stages:
    - documentation
    - docker
    - build
    - analysis
    - test
    - quality
    - security
    - release
    - deploy

include:
    - local: .dev-env/ci/template-ci.yml
    - local: .dev-env/ci/app/*-ci.yml

# publish docker images
publish:
    stage: docker
    image: docker:stable
    services:
        - docker:18-dind
    rules:
        -  if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    when:
        manual
    script:
        - 'echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY'
        - sh .dev-env/docker-builder/build.sh php-base 8.1 .dev-env/docker-images/php-8.1/Dockerfile php-base
        - sh .dev-env/docker-builder/build.sh php-dev 8.1 .dev-env/docker-images/php-8.1/Dockerfile php-dev

pages:
    image: node:18.3.0-alpine
    stage: documentation
    script:
        - cd .dev-doc/
        - yarn
        - export NODE_OPTIONS='--openssl-legacy-provider' && yarn build
        - cd ..
        - mv .dev-doc/docs/.vuepress/dist ./public
    rules:
        - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    artifacts:
        paths:
            - public
    when: manual
