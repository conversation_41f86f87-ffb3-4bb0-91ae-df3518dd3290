{"name": "bafv", "version": "0.1.0", "homepage": "/front/vendor/dashboard/", "private": true, "dependencies": {"@izberg/izberg-ui-beta": "^1.0.0-alpha.5", "@material-ui/core": "^3.9.3", "@material-ui/icons": "^3.0.2", "axios": "^0.19.0", "i18next": "^17.0.16", "js-file-download": "^0.4.12", "react": "^16.9.0", "react-dom": "^16.9.0", "react-i18next": "^10.13.0", "react-router-dom": "^5.1.0", "react-scripts": "3.1.2", "react-spinners": "^0.8.0", "typeface-roboto": "0.0.75"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "postbuild": "node postbuild.js", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"ncp": "^2.0.0"}}