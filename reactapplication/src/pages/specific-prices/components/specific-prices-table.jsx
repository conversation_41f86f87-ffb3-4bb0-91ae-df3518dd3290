import React from "react";
import { DataGridBase } from "@izberg/izberg-ui-beta";
import i18n from "../../../i18n/init";
import DefaultSpinner from "../../../components/spinners/default-spinner";
import {ExportRowButton, ExportAllButton} from "./export-button";

export default function SpecificPricesTable(props) {
    const columns = [
        { name: "companyCode", label: i18n.t("specificPricesDashboard.tableItems.columns.companyCode"), active: true },
        { name: "companyName", label: i18n.t("specificPricesDashboard.tableItems.columns.companyName"), active: true },
        { name: "lastUpload", label: i18n.t("specificPricesDashboard.tableItems.columns.lastUpload"), active: true },
        { name: "nbProduct", label: i18n.t("specificPricesDashboard.tableItems.columns.nbProduct"), active: true },
        { name: "companyCode", label: i18n.t("specificPricesDashboard.tableItems.columns.action"), active: true, component: ExportRowButton },
    ];

    if (!props.dataLoaded) {
        return <DefaultSpinner size={60}/>
    }

    return (
        <>
            <DataGridBase
                columns={columns}
                data={props.items}
                toolbar={{
                    title: (
                        <div className="titleTable">
                            {i18n.t("specificPricesDashboard.tableItems.title", {productsCount: props.productsCount, buyersCount: props.buyersCount})}
                        </div>
                    )
                }}
            />
            <ExportAllButton />
        </>
    );
}
