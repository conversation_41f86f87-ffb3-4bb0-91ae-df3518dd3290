import React from "react";
import i18next from "i18next";
import {Button} from "@material-ui/core";

const styles = {
    container: {
        display: "flex",
        marginBottom: "15px"
    },
    description: {
        objectFit: "contain",
        fontSize: "14px",
        fontWeight: "500",
        color: "#343a40",
        margin: "10px 15px 15px 0"
    },
    fileInput: {
        display: "none"
    },
    action: {
        backgroundColor: "#9600ff"
    }
}

export default function SpecificPricesUpload (props) {
    let fileInputElement = null;
    const handleClick = () => {
        fileInputElement.click();
    }

    const handleInputChange = (event) => {
        props.fileUpload(event.target.files[0]);
    }

    return (
        <div style={styles.container}>
            <div style={styles.description}>
               <span dangerouslySetInnerHTML={{__html: i18next.t('specificPricesDashboard.fileUpload.description', {fileTemplateLink: props.fileTemplateLink})}}></span>
            </div>
            <div>
                <input type="file" style={styles.fileInput} ref={input => fileInputElement = input} onChange={handleInputChange}/>
                <Button variant="contained" color="primary" style={styles.action} onClick={handleClick}>
                    {i18next.t('specificPricesDashboard.fileUpload.action')}
                </Button>
            </div>
        </div>
    );
}