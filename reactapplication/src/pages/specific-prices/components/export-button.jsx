import React from "react";
import {Button} from "@material-ui/core";
import i18n from "../../../i18n/init";
import axios from "axios";
import {uri} from '../../../config/services';
import fileDownload from 'js-file-download';

const DEFAULT_FILE_NAME = 'specific-prices.csv';
const styles = {
    row: {
        backgroundColor: "#9600ff"
    },
    all: {
        backgroundColor: "#9600ff",
        marginTop: "10px",
        float: "right"
    }
}

export function ExportButton(props) {
    const handleClick = () => {
        const config = {
            responseType: 'blob'
        }
        const data = {
            'companyCode': props.companyCode
        }

        axios.post(uri('SPECIFIC_PRICES_EXPORT'), data, config).then(response => {
            fileDownload(response.data, DEFAULT_FILE_NAME);
        });
    }

    return (
        <Button variant="contained" color="primary" style={props.style} onClick={handleClick}>
            {i18n.t(props.label)}
        </Button>
    );
}

export function ExportAllButton(props) {
    return <ExportButton companyCode={null} label={'specificPricesDashboard.export.all'} style={styles.all} />
}

export function ExportRowButton(props) {
    const {companyCode} = props.params;

    return <ExportButton companyCode={companyCode} label={'specificPricesDashboard.export.row'} style={styles.row} />
}
