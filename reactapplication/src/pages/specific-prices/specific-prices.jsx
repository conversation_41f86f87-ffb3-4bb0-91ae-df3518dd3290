import React, {useEffect, useState} from "react";
import SpecificPricesTable from "./components/specific-prices-table";
import i18next from "i18next";
import SpecificPricesUpload from "./components/specific-prices-upload";
import {FILE_TEMPLATE_LINK} from '../../config/const';
import {uri} from '../../config/services';
import axios from "axios";

const styles = {
    title: {
        marginBottom: "15px",
        objectFit: "contain",
        fontSize: "30px",
        fontWeight: "bold",
        color: "#343a40"
    },
    error: {
        textAlign: 'center',
        color: 'red'
    }
}

export default function SpecificPrices() {
    const [error, setError] = useState('');
    const [dataLoaded, setDataLoaded] = useState(false);
    const [productsCount, setProductsCount] = useState(0);
    const [buyersCount, setBuyersCount] = useState(0);
    const [items, setItems] = useState([]);

    useEffect(() => {
        setError('');
        axios.get(uri('SPECIFIC_PRICES')).then(response => {
            const {buyersCount, productsCount, items} = response.data;
            setBuyersCount(buyersCount);
            setProductsCount(productsCount);
            setItems(items);
            setDataLoaded(true);
        });
    }, []);

    const fileUpload = (selectedFile) => {
        setError('');
        const formData = new FormData();
        formData.append('specific_prices', selectedFile, selectedFile.name);
        axios.post(uri('SPECIFIC_PRICES_UPDATE'), formData)
            .then(response => {
                const {buyersCount, productsCount, items} = response.data;
                setBuyersCount(buyersCount);
                setProductsCount(productsCount);
                setItems(items);
            })
            .catch(error => setError(error.response.data.message))
        ;
    }

    return (
        <>
            <div style={styles.title}>{i18next.t('specificPricesDashboard.title')}</div>
            <SpecificPricesUpload fileTemplateLink={FILE_TEMPLATE_LINK} fileUpload={fileUpload}/>
            {error && <div style={styles.error}>{error}</div>}
            <SpecificPricesTable dataLoaded={dataLoaded} productsCount={productsCount} buyersCount={buyersCount}
                                 items={items}/>
        </>
    );
}