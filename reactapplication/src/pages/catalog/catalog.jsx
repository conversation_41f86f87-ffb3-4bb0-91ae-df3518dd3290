import React from "react";
import i18next from "i18next";
import {uri} from '../../config/services';
import axios from "axios";
import {
    <PERSON><PERSON>,
    Card,
    CardContent,
    FormControl,
    Grid,
    InputLabel, MenuItem,
    Select,
    Typography
} from "@material-ui/core";
import i18n from "../../i18n/init";

const styles = {
    title: {
        marginBottom: "15px",
        objectFit: "contain",
        fontSize: "30px",
        fontWeight: "bold",
        color: "#343a40"
    },
    error: {
        textAlign: 'center',
        color: 'red'
    }
}

class Catalog extends React.Component
{

    state = {
        language: window.lang,
        status: 'active',
    };

    handleChange = event => {
        this.setState({ [event.target.name]: event.target.value });
    };

    export = () =>  {
        axios({
            method: 'get',
            url: uri('CATALOG_EXPORT') + '/' + this.state.status + '/' + this.state.language,
        }).then(function(response) {
            let blob = new Blob([response.data], { type: 'text/csv' });
            let link = document.createElement('a');
            link.href = window.URL.createObjectURL(blob);
            link.download = 'offer-catalog.csv';
            link.click();
        })
    }

    render() {
        return (
            <>
                <div style={styles.title}>{i18next.t('catalogPage.title')}</div>
                <p dangerouslySetInnerHTML={{__html: i18n.t("catalogPage.description")}}></p>
                <div className="titleTable" style={{fontSize: "1.25rem"}}>
                    {i18n.t("catalogPage.sub_title")}
                </div>
                <Card style={{marginTop: 10, marginBottom: 30}}>
                    <CardContent>
                        <Typography variant="body2" component="p">
                            <form autoComplete="off">
                                <p style={{marginTop:0}}>
                                    <b>{i18n.t("catalogPage.label.language")}</b>
                                </p>
                                <FormControl>
                                    <InputLabel htmlFor="language">{i18next.t('catalogPage.language')}</InputLabel>
                                    <Select
                                        value={this.state.language}
                                        onChange={this.handleChange}
                                        inputProps={{
                                            name: 'language',
                                            id: 'language',
                                        }}
                                    >
                                        <MenuItem value={'fr'}>{i18next.t('language.fr')}</MenuItem>
                                        <MenuItem value={'en'}>{i18next.t('language.en')}</MenuItem>
                                        <MenuItem value={'es'}>{i18next.t('language.es')}</MenuItem>
                                        <MenuItem value={'it'}>{i18next.t('language.it')}</MenuItem>
                                        <MenuItem value={'de'}>{i18next.t('language.de')}</MenuItem>
                                    </Select>
                                </FormControl>
                                <p style={{marginTop:30}}>
                                    <b>{i18n.t("catalogPage.label.status")}</b>
                                </p>
                                <FormControl>
                                    <InputLabel htmlFor="age-simple">{i18next.t('catalogPage.status')}</InputLabel>
                                    <Select
                                        value={this.state.status}
                                        onChange={this.handleChange}
                                        inputProps={{
                                            name: 'status',
                                            id: 'status',
                                        }}
                                    >
                                        <MenuItem value={'draft'}>{i18next.t('catalogPage.draft')}</MenuItem>
                                        <MenuItem value={'active'}>{i18next.t('catalogPage.active')}</MenuItem>
                                        <MenuItem value={'inactive'}>{i18next.t('catalogPage.inactive')}</MenuItem>
                                    </Select>
                                </FormControl>
                            </form>
                        </Typography>
                    </CardContent>

                </Card>
                <Grid
                    container
                    direction="row"
                    justify="flex-end"
                    alignItems="flex-end"
                >
                    <Button variant="contained" color="primary" style={{backgroundColor: "#9600ff"}} onClick={this.export}>
                        {i18next.t('catalogPage.export')}
                    </Button>
                </Grid>
            </>
        );
    }
}

export default Catalog;
