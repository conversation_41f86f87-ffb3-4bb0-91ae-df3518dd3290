import React, {Component} from "react";
import {<PERSON><PERSON><PERSON><PERSON>outer as Router, Route, Switch} from "react-router-dom";
import axios from "axios";
import { css } from "@emotion/core";
import { <PERSON><PERSON>oader } from "react-spinners";
import {NavBar} from "./components/navbar/navbar";
import Table from "./components/table/table";
import {uri} from "./config/services"
import SpecificPrices from "./pages/specific-prices/specific-prices";
import "./App.css";
import Catalog from "./pages/catalog/catalog";

const override = css`
  display: block;
  position: fixed;
  top: 50%;
  left: 50%;
  margin-top: -30px;
  margin-left: -30px;
  z-index: 99999;
`;

class App extends Component {
    constructor(props) {
        super(props);
        this.state = {
            loading: false,
        };
        axios.interceptors.request.use(
            (config) => {
                if (config.loader !== false) {
                    this.setState({ loading: true });
                }
                return config;
            },
            (error) => {
                this.setState({ loading: false });
                return Promise.reject(error);
            }
        );
        axios.interceptors.response.use(
            (data) => {
                this.setState({ loading: false });
                return data;
            },
            (error) => {
                this.setState({ loading: false });
                return Promise.reject(error);
            }
        );
    }

    render() {
        return (
            <Router>
                <NavBar/>
                <div style={{padding: "20px 32px 32px"}}>
                    <Switch>
                        <Route exact path={uri('HOME_PAGE')} component={Table}/>
                        <Route path={uri('SPECIFIC_PRICES_PAGE')} component={SpecificPrices}/>
                        <Route path={uri('CATALOG')} component={Catalog}/>
                    </Switch>
                </div>
                <MoonLoader
                    css={override}
                    size={60}
                    color={"#134391"}
                    loading={this.state.loading}
                />
                {this.state.loading && <div className="loader-bg"></div>}
            </Router>
        );
    }
}

export default App;
