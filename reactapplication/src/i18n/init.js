import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import EN from "./en.json";
import FR from "./fr.json";
import IT from "./it.json";
import ES from "./es.json";
import DE from "./de.json";

if (window.lang === '__LANG__') {window.lang = 'en';}

i18n.use(initReactI18next).init({
  resources: {
    en: {
      translation: EN
    },
    fr: {
      translation: FR
    },
    it: {
      translation: IT
    },
    es: {
      translation: ES
    },
    de: {
      translation: DE
    }
  },
  lng: window.lang,
  fallbackLng: "en",

  interpolation: {
    escapeValue: false
  }
});

export default i18n;
