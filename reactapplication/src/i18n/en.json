{"home": "Dashboard", "orders": "Orders", "catalog": "Catalog", "messages": "Messages", "settings": "Settings", "Withdrawals": "<PERSON><PERSON><PERSON><PERSON>", "Transactions": "Transactions", "Balances": "Balances", "logistic": "Logistic", "invoices": "Invoices", "credit": "Credit", "Refunds": "Refunds", "offers": "Offers", "addOffers": "Create an offer", "gallery": "Gallery", "import": "Import", "priceFile": "Dedicated Price File Template", "inbox": "Inbox", "guide": "Vendor User Guide", "terms": "Station One General terms of use", "agreement": "Payment Service Agreement", "toolbox": "Vendor Communication Toolbox", "profile": "Profile", "company": "Company", "documents": "Documents", "permissions": "Permissions", "account": "Account", "logout": "Logout", "title": "Buyer Account for Vendor permissions", "subtitle": "Manage buyer account for vendors permissions to access your catalog prices", "Name": "Company Code", "Enterprise": "Company Name", "Create date": "Request submitted on", "Status": "Status", "Action": "Action", "All": "All", "Pending": "Pending", "Accepted": "Accepted", "Rejected": "Rejected", "Reject": "Reject", "Accept": "Accept", "Status modification": "Status modification", "Are you sure you want to change the status": "Are you sure you want to change the status ?", "Yes": "Yes", "No": "No", "buyer management": "Buyer management", "specific prices": "Specific prices", "specificPricesDashboard": {"title": "Specific prices management", "export": {"row": "Export", "all": "Export all"}, "fileUpload": {"description": "Click <a href='{{fileTemplateLink}}'>here</a> to download the specific price file. <br/><br/>To apply a specific price for a given buyer, add his company code in the \"Buyer Identification Number\" column (available upon request to StationOne), then enter the concerned product information in Vendor Reference, Incoterm and Country columns. Finally, enter the new price to apply in the \"Unit Price\" column. <br/><br/>To remove all specific prices applied to a company, import the specific prices file and only populate the “Buyer Identification Number” on a single line. <br/>To delete one/several specific prices, load the specific prices file by populating the “Buyer Identification Number” of the buyer and the following information : Vendor reference, Incoterm and Country. All other columns remain empty.<br/><br/>To set up the validity date for each specific price, the format of the date has to be DD/MM/YYYY such as \"02/01/2030\" for 2nd of January 2030<br/><br/>In the table below, you can see specific prices status of your buyers on StationOne.", "action": "Import"}, "tableItems": {"title": "There are {{productsCount}} products with specific prices applied for {{buyersCount}} buyers", "columns": {"companyCode": "Company Code", "companyName": "Company Name", "lastUpload": "Last update", "nbProduct": "Number of products", "action": "Action"}}}, "catalogPage": {"title": "Export catalog management", "sub_title": "Catalog export criteria", "export": "Export", "label": {"language": "Choose the language", "status": "Choose the Status"}, "language": "Language", "status": "Status", "draft": "draft", "active": "active", "inactive": "inactive", "description": "To export your catalog to an Excel file (.csv), please select the catalog language and status of the offers which you would like to export.<br><br>From this file, you can select the offers (rows) and the attributes (columns) that you want to modify.<br>Be careful not to modify or delete the first row (StationOne keys) and the first column (sku of your offers) in the exported file.<br>Once the modifications have been made, you can follow the process of import of a new catalog and select \"Update only\".<br><br>Example: Updating your prices<br>1) Retrieve only the \"sku\" and \"price\" columns from the exported file<br>2) Modify the prices in the Excel file and save the file on your computer (in .csv)<br>3) Catalog / Import a feed, \"Create\" a new import and follow the import instructions by selecting \"update only\""}, "language": {"fr": "French", "en": "English", "es": "Spain", "it": "Italian", "de": "De<PERSON>ch"}}