import React from "react";
import { Trans } from "react-i18next";
import DashboardIcon from "@material-ui/icons/Dashboard";
import ViewListIcon from "@material-ui/icons/ViewList";
import SendIcon from "@material-ui/icons/Send";
import FolderIcon from "@material-ui/icons/Folder";
import CreditCardIcon from "@material-ui/icons/CreditCard";
import SearchIcon from "@material-ui/icons/Search";
import AddCircleIcon from "@material-ui/icons/AddCircle";
import PanoramaIcon from "@material-ui/icons/Panorama";
import ViewCompactIcon from "@material-ui/icons/ViewCompact";
import GetAppIcon from "@material-ui/icons/GetApp";
import MailIcon from "@material-ui/icons/Mail";
import StarIcon from "@material-ui/icons/Star";
import FileCopyIcon from "@material-ui/icons/FileCopy";
import ImportContactsIcon from "@material-ui/icons/ImportContacts";
import AccountBoxIcon from "@material-ui/icons/AccountBox";
import InfoIcon from "@material-ui/icons/Info";
import FolderOpenIcon from "@material-ui/icons/FolderOpen";
import PersonIcon from "@material-ui/icons/Person";
import ExitToAppIcon from "@material-ui/icons/ExitToApp";
import LocalAtmIcon from "@material-ui/icons/LocalAtm";
import SyncAltIcon from "@material-ui/icons/Sync";
import SupervisorAccountIcon from "@material-ui/icons/SupervisorAccount";
import MoneyIcon from "@material-ui/icons/Money";
import { menuUri, uri } from "./services";

const menu = {
  home: [
    {
      label: <Trans>home</Trans>,
      icon: <DashboardIcon />,
      href: menuUri("/"),
      target: "_self"
    },
    {
      label: <Trans>Withdrawals</Trans>,
      icon: <LocalAtmIcon />,
      href: menuUri("/sellersWithdrawals/"),
      target: "_self"
    },
    {
      label: <Trans>Transactions</Trans>,
      icon: <SyncAltIcon />,
      href: menuUri("/sellersTransactions/"),
      target: "_self"
    },
    {
      label: <Trans>Balances</Trans>,
      icon: <LocalAtmIcon />,
      href: menuUri("/balances/"),
      target: "_self"
    },
  ],
  orders: [
    {
      label: <Trans>orders</Trans>,
      icon: <ViewListIcon />,
      href: menuUri("/orders/manage/"),
      target: "_self"
    },
    {
      label: <Trans>logistic</Trans>,
      icon: <SendIcon />,
      href: menuUri("/orders/logistic/"),
      target: "_self"
    },
    {
      label: <Trans>invoices</Trans>,
      icon: <FolderIcon />,
      href: menuUri("/orders/invoices/"),
      target: "_self"
    },
    {
      label: <Trans>credit</Trans>,
      icon: <CreditCardIcon />,
      href: menuUri("/orders/credit-notes/"),
      target: "_self"
    },
    {
      label: <Trans>Refunds</Trans>,
      icon: <CreditCardIcon />,
      href: menuUri("/orders/refunds/"),
      target: "_self"
    }
  ],
  catalog: [
    {
      label: <Trans>offers</Trans>,
      icon: <SearchIcon />,
      href: menuUri("/offers/"),
      target: "_self"
    },
    {
      label: <Trans>addOffers</Trans>,
      icon: <AddCircleIcon />,
      href: menuUri("/offers/product_create/"),
      target: "_self"
    },
    {
      label: <Trans>gallery</Trans>,
      icon: <PanoramaIcon />,
      href: menuUri("/offers/gallery/"),
      target: "_self"
    },
    {
      label: <Trans>import</Trans>,
      icon: <ViewCompactIcon />,
      href: menuUri("/imports/"),
      target: "_self"
    },
    {
      label: <Trans>priceFile</Trans>,
      icon: <GetAppIcon />,
      href: "https://www.station-one.com/docs/StationOne_Dedicated_Prices.csv"
    },
    {
      label: <Trans>export</Trans>,
      icon: <GetAppIcon />,
      href: uri("CATALOG")
    }
  ],
  messages: [
    {
      label: <Trans>inbox</Trans>,
      icon: <MailIcon />,
      href: menuUri("/messages/"),
      target: "_self"
    }
  ],
  settings: [
    {
      label: <Trans>guide</Trans>,
      icon: <StarIcon />,
      href: "https://www.station-one.com/docs/StationOne_Vendor_User_Guide.pdf"
    },
    {
      label: <Trans>terms</Trans>,
      icon: <FileCopyIcon />,
      href: "https://www.station-one.com/docs/StationOne_Vendor_GTU.pdf"
    },
    {
      label: <Trans>agreement</Trans>,
      icon: <ImportContactsIcon />,
      href:
        "https://www.station-one.com/docs/StationOne_Payment_Service_Agreement.pdf"
    },
    {
      label: <Trans>toolbox</Trans>,
      icon: <GetAppIcon />,
      href:
        "https://www.station-one.com/docs/StationOne_Vendor_Communication_Toolbox.zip",
      target: "_self"
    },
    {
      label: <Trans>profile</Trans>,
      icon: <AccountBoxIcon />,
      href: menuUri("/settings/"),
      target: "_self"
    },
    {
      label: <Trans>company</Trans>,
      icon: <InfoIcon />,
      href: menuUri("/settings/company/"),
      target: "_self"
    },
    {
      label: <Trans>documents</Trans>,
      icon: <FolderOpenIcon />,
      href: menuUri("/settings/documents/"),
      target: "_self"
    },
    {
      label: <Trans>permissions</Trans>,
      icon: <PersonIcon />,
      href: menuUri("/settings/permissions/"),
      target: "_self"
    },
    {
      label: <Trans>buyer management</Trans>,
      icon: <SupervisorAccountIcon />,
      href: uri("HOME_PAGE"),
      target: "_self"
    },
    {
      label: <Trans>specific prices</Trans>,
        icon: <MoneyIcon />,
      href: uri("SPECIFIC_PRICES_PAGE"),
      target: "_self"
    }
  ],
  user: [
    {
      label: <Trans>account</Trans>,
      icon: <PersonIcon />,
      href: menuUri("/"),
      target: "_self"
    },
    {
      label: <Trans>logout</Trans>,
      icon: <ExitToAppIcon />,
      href: "/front/vendor/logout",
      target: "_self"
    }
  ]
};

export default menu;
