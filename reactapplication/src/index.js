import React from "react";
import ReactDOM from "react-dom";
import App from "./App";
import "./index.css";
// import "typeface-roboto";
import { MuiThemeProvider, createMuiTheme } from "@material-ui/core/styles";
import { getTheme } from "@izberg/izberg-ui-beta/theme";
import "./i18n/init";
import palette from "./config/theme";

const theme = createMuiTheme(getTheme({ palette: palette }));

ReactDOM.render(
  <MuiThemeProvider theme={theme}>
    <App />
  </MuiThemeProvider>,
  document.getElementById("root")
);
