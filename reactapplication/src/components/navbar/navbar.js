import React, { Component } from "react";
import AppBar from "@material-ui/core/AppBar";
import Toolbar from "@material-ui/core/Toolbar";
import Dropdown from "@izberg/izberg-ui-beta/Dropdown";
import { Button } from "@material-ui/core";
import { Trans } from "react-i18next";
import Menu from "../../config/menu";
import AccountCircleIcon from "@material-ui/icons/AccountCircle";

import "./navbar.css";

export class NavBar extends Component {
  state = {
    anchorEl: null,
    menu: null
  };

  openMenu(e, menu) {
    this.setState({
      anchorEl: e.currentTarget,
      menu: menu
    });
  }

  render() {
    const { anchorEl, menu } = this.state;
    return (
      <AppBar position="sticky" color="inherit">
        <Toolbar style={{ minHeight: 70 }}>
          <div style={{ display: "flex", flex: 1, alignItems: "center" }}>
            <a
              href="https://stationone.merchant.izberg-marketplace.com/"
              style={{ display: "flex", marginRight: 150 }}
            >
              <img
                src="https://izberg-backoffices.s3.amazonaws.com/media/themefile/2018/9/logotype_stationone_rvb_34432cb.png"
                style={{ height: 30 }}
                alt="Station one"
              />
            </a>
            <div style={{ display: "flex", flex: 1 }}>
              <Button onClick={e => this.openMenu(e, Menu.home)} class="menu">
                <Trans>home</Trans>
              </Button>
              <Button onClick={e => this.openMenu(e, Menu.orders)} class="menu">
                <Trans>orders</Trans>
              </Button>
              <Button
                onClick={e => this.openMenu(e, Menu.catalog)}
                class="menu"
              >
                <Trans>catalog</Trans>
              </Button>
              <Button
                onClick={e => this.openMenu(e, Menu.messages)}
                class="menu"
              >
                <Trans>messages</Trans>
              </Button>
              <Button
                onClick={e => this.openMenu(e, Menu.settings)}
                class="menu"
              >
                <Trans>settings</Trans>
              </Button>
            </div>
            <div>
              <Button onClick={e => this.openMenu(e, Menu.user)}>
                <AccountCircleIcon />
                &nbsp; {window.userName}
              </Button>
            </div>
            <Dropdown
              actions={menu}
              classes={{}}
              anchorEl={anchorEl}
              onClose={() => this.setState({ anchorEl: null })}
              position={"bottom"}
            />
          </div>
        </Toolbar>
      </AppBar>
    );
  }
}
