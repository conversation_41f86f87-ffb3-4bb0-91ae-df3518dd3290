import React, { Component } from "react";
import { But<PERSON> } from "@material-ui/core";
import { Modal } from "@izberg/izberg-ui-beta";
import axios from "axios";
import { uri } from "../../config/services";
import { Trans } from "react-i18next";
import TextField from "@izberg/izberg-ui-beta/TextField";
import * as CONSTANT from "../../config/const";

export default class ButtonState extends Component {
  constructor() {
    super();
    this.state = {
      rejectModal: false,
      acceptModal: false,
      rejectCommentError: true,
      comment: ""
    };
  }

  toggleModal(type, open) {
    this.setState({ ...this.state, [type]: open, comment: "" });
  }

  updateStatus(props, status) {
    axios
      .get(uri("BAFVUPDATE"), {
        params: {
          id: props.params.id,
          status: status,
          rejectionReason: this.state.comment
        }
      })
      .then(() => {
        document.dispatchEvent(new CustomEvent("update_table"));
      });
  }

  setComment(e) {
      this.setState({ ...this.state, comment: e });
  }

  submitReject() {
      if (this.state.comment.trim() !== "") {
          this.setState({...this.state, rejectCommentError: false})
          this.updateStatus(this.props, CONSTANT.REJECTED_STATUS);
          this.toggleModal("rejectModal", false);
      } else {
          this.setState({...this.state, rejectCommentError: true})
      }
  }

  render() {
    return (
      <>
        {(this.props.params.status === CONSTANT.ACCEPTED_STATUS ||
          this.props.params.status === CONSTANT.PENDING_STATUS) && (
          <Button
            color="default"
            variant="contained"
            size="medium"
            onClick={() => {
              this.toggleModal("rejectModal", true);
            }}
          >
            <Trans>Reject</Trans>
          </Button>
        )}
        {(this.props.params.status === CONSTANT.REJECTED_STATUS ||
          this.props.params.status === CONSTANT.PENDING_STATUS) && (
          <Button
            color="primary"
            variant="contained"
            size="medium"
            onClick={() => {
              this.toggleModal("acceptModal", true);
            }}
          >
            <Trans>Accept</Trans>
          </Button>
        )}
        <Modal
          cancelLabel={<Trans>No</Trans>}
          fullWidth
          maxWidth="sm"
          onClose={() => {
            this.toggleModal("rejectModal", false);
          }}
          onSubmit={() => {
            this.submitReject()
          }}
          open={this.state.rejectModal}
          submitLabel={<Trans>Yes</Trans>}
          title={<Trans>Status modification</Trans>}
        >
          <Trans>Are you sure you want to change the status</Trans>
          <div style={{ marginTop: 30 }}>
            <TextField
              fullWidth
              multiline
              label="rejection motivation"
              rows="6"
              rowsMax="6"
              variant="outlined"
              required={true}
              onChange={e => {
                this.setComment(e);
              }}
            />
          </div>
        </Modal>
        <Modal
          cancelLabel={<Trans>No</Trans>}
          fullWidth
          maxWidth="sm"
          onClose={() => {
            this.toggleModal("acceptModal", false);
          }}
          onSubmit={() => {
            this.updateStatus(this.props, CONSTANT.ACCEPTED_STATUS);
            this.toggleModal("acceptModal", false);
          }}
          open={this.state.acceptModal}
          submitLabel={<Trans>Yes</Trans>}
          title={<Trans>Status modification</Trans>}
        >
          <Trans>Are you sure you want to change the status</Trans>
        </Modal>
      </>
    );
  }
}
