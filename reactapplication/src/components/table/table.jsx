import React, {Component} from "react";
import {DataGridBase} from "@izberg/izberg-ui-beta";
import axios from "axios";
import {uri} from "../../config/services";
import tableButton from "./tableButtonState";
import statusState from "./status";
import {Trans} from "react-i18next";
import i18n from "../../i18n/init";
import * as CONSTANT from "../../config/const";
import DateState from "./date";

const columns = [
    {name: "identification", label: <Trans>Name</Trans>, active: true},
    {
        name: "name",
        label: <Trans>Enterprise</Trans>,
        active: true
    },
    {
        name: "createdAt",
        label: <Trans>Create date</Trans>,
        active: true,
        component: DateState
    },
    {
        name: "status",
        label: <Trans>Status</Trans>,
        active: true,
        type: "status",
        component: statusState
    },
    {
        name: "status",
        label: <Trans>Action</Trans>,
        active: true,
        component: tableButton
    }
];

const filtersGroups = [
    {
        label: <Trans>All</Trans>,
        value: "all",
        active: true,
        queryParams: {type: "all"}
    },
    {
        label: <Trans>Pending</Trans>,
        value: CONSTANT.PENDING_STATUS,
        active: false,
        queryParams: {type: "Pending"}
    },
    {
        label: <Trans>Accepted</Trans>,
        value: CONSTANT.ACCEPTED_STATUS,
        active: false,
        queryParams: {type: "Accepted"}
    },
    {
        label: <Trans>Rejected</Trans>,
        value: CONSTANT.REJECTED_STATUS,
        active: false,
        queryParams: {type: "Rejected"}
    }
];

export default class SimpleTable extends Component {
    allData = [];

    constructor() {
        super();
        this.state = {
            tableData: [],
            filters: filtersGroups,
            currentFilter: "all",
            currentFind: ""
        };
        this.getFilteredData.bind(this);
        this.findName.bind(this);
    }

    componentDidMount() {
        this.getTableData();
        document.addEventListener("update_table", this.getTableData.bind(this));
    }

    getFilteredData(type) {
        const data = this.allData;

        let newData = {};

        if (type !== "all") {
            newData.tableData = data.filter(d => d.status === type);
        } else {
            newData.tableData = data;
        }

        if (this.state.currentFind !== "") {
            newData.tableData = newData.tableData.filter(
                d =>
                    d.name.toLowerCase().includes(this.state.currentFind) ||
                    d.identification.toLowerCase().includes(this.state.currentFind)
            );
        }

        newData.currentFilter = type;

        const newState = Object.assign({}, this.state, newData);
        this.setState(newState);
    }

    findName(name) {
        let data = this.allData;
        let state = {};

        state.tableData = data.filter(
            d =>
                d.name.toLowerCase().includes(name[0].value) ||
                d.identification.toLowerCase().includes(name[0].value)
        );
        if (this.state.currentFilter !== "all") {
            state.tableData = state.tableData.filter(
                d => d.status === this.state.currentFilter
            );
        }

        state.currentFind = name[0].value;

        const newState = Object.assign({}, this.state, state);
        this.setState(newState);
    }

    getTableData() {
        if (!process.env.NODE_ENV || process.env.NODE_ENV === "development") {
            this.allData = [
                {
                    identification: "greg",
                    name: "Greg",
                    createdAt: "2019-10-22T08:33:07+00:00",
                    status: 1,
                    id: 1
                },
                {
                    identification: "greg",
                    name: "COLLOT",
                    createdAt: "2019-10-22T08:33:07+00:00",
                    status: 2,
                    id: 2
                },
                {
                    identification: "greg",
                    name: "COLLOT",
                    createdAt: "2019-10-22T08:33:07+00:00",
                    status: 0,
                    id: 2
                }
            ];
            const newState = Object.assign({}, this.state, {
                tableData: this.allData
            });
            this.setState(newState);
        } else {
            axios.get(uri("BAFVLIST")).then(response => {
                const newState = Object.assign({}, this.state, {
                    tableData: response.data
                });
                this.setState(newState);
                this.allData = response.data;
            });
        }
    }

    render() {
        return (
            <DataGridBase
                columns={columns}
                data={this.state.tableData}
                filtersGroups={this.state.filters}
                appliedFiltersGroup={this.state.filters.filter(f => f.active)}
                onFiltersGroupChange={e => this.getFilteredData(e)}
                filterSearch={{label: i18n.t("User")}}
                onSearchSubmit={e => this.findName(e)}
                totalCount={this.state.tableData.length}
                toolbar={{
                    title: (
                        <div className="titleTable">
                            <Trans>title</Trans>
                            <br/>
                            <small>
                                <Trans>subtitle</Trans>
                            </small>
                        </div>
                    )
                }}
            />
        );
    }
}
