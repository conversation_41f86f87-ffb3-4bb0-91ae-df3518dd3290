import React from 'react';
import {css} from "@emotion/core";
import {<PERSON><PERSON>oa<PERSON>} from "react-spinners";

const override = css`
  display: block;
  position: fixed;
  top: 50%;
  left: 50%;
  margin-top: -30px;
  margin-left: -30px;
  z-index: 99999;
`;

export default function DefaultSpinner (props) {
    return <MoonLoader
        css={override}
        size={props.size}
        color={"#134391"}
    /> ;
}
