version: "3.8"

services:
    app:
        image: glregistry.boost.open.global/izberg/cubotoo/cubotoo/php:8.1-latest
        user: '1000'
        working_dir: /var/www/symfony4
        container_name: station-one-app
        env_file:
            - .env
        volumes:
            - ./symfony4:/var/www/symfony4
            - ./php/app.ini:/usr/local/etc/php/conf.d/local.ini
        depends_on:
            - db
            - redis
            - yarn-encore
            - blackfire
            - mailcatcher
        networks:
            - default

    nginx:
        image: nginx
        restart: on-failure
        ports:
            - "80:80"
            - "443:443"
        volumes:
            - ./symfony4:/var/www/symfony4
            - ./nginx/templates:/etc/nginx/templates
            - ./nginx/certs:/etc/nginx/certs
        networks:
            - default
        depends_on:
            - app

    db:
        image: mysql:5.6.49
        command: --default-authentication-plugin=mysql_native_password
        ports:
            - "3306:3306"
        env_file:
            - .env
        volumes:
            - database:/var/lib/mysql
        networks:
            - default

    redis:
        image: redis
        networks:
            - default

    yarn-encore:
        image: glregistry.boost.open.global/izberg/station_one/docker/yarn:latest
        volumes:
            - ./symfony4:/app

    mailcatcher:
        image: glregistry.boost.open.global/izberg/station_one/docker/mailcatcher:latest
        ports:
            - '1080:1080'
        networks:
            - default
volumes:
    database:
    database_test:
