<?php

namespace Tests\Open\LogBundle\Utils;

use Open\LogBundle\Utils\LogUtil;
use PHPUnit\Framework\TestCase;

/**
 * Class LogUtilTest
 */
class LogUtilTest extends TestCase
{

    /**
     * @param array $context
     * @param array $expected
     * @dataProvider buildContextProvider
     */
    public function testBuildContext(array $context, array $expected)
    {
        $this->assertEquals($expected, LogUtil::buildContext($context));
    }

    /**
     * @return \Generator
     */
    public function buildContextProvider(){
        $context1 = [LogUtil::EVENT_NAME => 'test',
            LogUtil::USER_NAME => 'test'];
        yield[$context1,$context1];

        $context2 = [];
        yield [$context2, [
            LogUtil::EVENT_NAME => LogUtil::DEFAULT_VALUE,
            LogUtil::USER_NAME => LogUtil::DEFAULT_VALUE
        ]];

        $context3 = [
            LogUtil::EVENT_NAME => 'test',
            'toto' => 'test'
            ];

        yield [$context3,[
            LogUtil::EVENT_NAME => 'test',
            LogUtil::USER_NAME => LogUtil::DEFAULT_VALUE,
            'toto' => 'test'
        ]];
    }
}
