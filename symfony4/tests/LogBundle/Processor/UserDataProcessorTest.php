<?php

namespace Tests\Open\LogBundle\Processor;

use AppBundle\Entity\Company;
use AppBundle\Entity\Contact;
use AppBundle\Entity\User;
use AppBundle\Services\SecurityService;
use Open\LogBundle\Processor\UserDataProcessor;
use Open\LogBundle\Utils\LogUtil;
use PHPUnit\Framework\TestCase;
use Prophecy\Prophecy\ObjectProphecy;
use Prophecy\Prophet;

/**
 * Class UserDataProcessorTest
 */
class UserDataProcessorTest extends TestCase
{
    public static int $USER_STATE = 0;
    private Prophet $prophet;
    private UserDataProcessor $userDataProcessor;
    private ObjectProphecy $securityService;

    /**
     * @param array $record
     * @param array $expected
     * @dataProvider __invokeProvider
     */
    public function test__invoke(array $record, array $expected, int $userValue):void
    {
        self::$USER_STATE = $userValue;
        $this->assertEquals($expected, $this->userDataProcessor->__invoke($record));
    }

    /**
     * @return \Generator
     */
    public function __invokeProvider(){
        $record = ['context'=>[]];
        yield [$record, ['context'=>[
            LogUtil::USER_EMAIL=>sha1('<EMAIL>'),
            LogUtil::COMPANY_EMAIL=>sha1('<EMAIL>')]],0];

        yield [$record, ['context'=>[
            LogUtil::USER_EMAIL=>sha1('<EMAIL>')]],1];

        yield [$record, ['context'=>[
            LogUtil::USER_EMAIL=>sha1('<EMAIL>')]],2];

        yield [$record, ['context'=>[]],3];
    }

    /**
     *
     */
    protected function setUp():void{
        $this->prophet = new Prophet;
        $this->securityService = $this->prophet->prophesize(SecurityService::class);

        $this->securityService->getUser()->will(function ($args){
            if(UserDataProcessorTest::$USER_STATE === 0){
                return UserDataProcessorTest::buildFakeUser();
            }else if(UserDataProcessorTest::$USER_STATE === 1){
                return UserDataProcessorTest::buildFakeUser(true);
            }else if(UserDataProcessorTest::$USER_STATE === 2){
                return UserDataProcessorTest::buildFakeUser(true,true);
            }else {
                return null;
            }

        });
        $this->userDataProcessor = new UserDataProcessor($this->securityService->reveal());


    }

    /**
     *
     */
    protected function tearDown():void
    {
        $this->prophet->checkPredictions();
    }

    /**
     * @param bool $noContact
     * @param bool $noCompany
     *
     * @return User
     */
    public static function buildFakeUser(bool $noContact = false, bool $noCompany = false, string $userEmail = "<EMAIL>" , string $contactEmail = "<EMAIL>"):User{
        $user = new User();
        $user->setEmail($userEmail);

        $contact = new Contact();
        $contact->setEmail($contactEmail);

        $company = new Company();
        if($noCompany){
            return $user;
        }
        $user->setCompany($company);
        if($noContact){
            return $user;
        }
        $company->setMainContact($contact);
        return $user;
    }


}
