<?php

declare(strict_types=1);

namespace Tests\Open\Model;

use PHPUnit\Framework\TestCase;
use Tests\DummyFactory\DummyUserIdentityFactory;

final class UserIdentityTest extends TestCase
{
    public function testGetMerchantScopes(): void
    {
        $userIdentity = DummyUserIdentityFactory::createUserIdentity();

        $this->assertCount(2, $userIdentity->getMerchantScopes());
        $this->assertSame(1, $userIdentity->getMerchantScopes()[0]->id);
    }

    public function testGetMerchantScopesIfEmpty(): void
    {
        $userIdentity = DummyUserIdentityFactory::createUserIdentity([
            'merchant_scopes' => []
        ]);

        $this->assertCount(0, $userIdentity->getMerchantScopes());
    }
}
