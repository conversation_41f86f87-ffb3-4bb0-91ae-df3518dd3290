<?php

declare(strict_types=1);

namespace Tests\Open\BackBundle\EventSubscriber;

use Doctrine\ORM\EntityManagerInterface;
use <PERSON>ip\TestFixturesBundle\Services\DatabaseToolCollection;
use Liip\TestFixturesBundle\Services\DatabaseTools\AbstractDatabaseTool;
use Open\BackBundle\Events\DeactivationUserEvent;
use Open\BackBundle\EventSubscriber\DeactivationUserSubscriber;
use Open\BackBundle\UseCase\DetachUserOfSites;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\EventDispatcher\EventDispatcher;

final class DeactivationUserSubscriberTest extends KernelTestCase
{
    private AbstractDatabaseTool $databaseTool;

    protected function setUp(): void
    {
        static::bootKernel();

        $this->databaseTool = static::$container->get(DatabaseToolCollection::class)->get();
    }

    public function testDetachUserOfSiteIfItDeactivate(): void
    {
        $results = $this->databaseTool->loadAliceFixture([
            __DIR__ . '/../UserSiteFixtures.yaml'
        ]);
        /** @var EntityManagerInterface $em */
        $em = static::$container->get(EntityManagerInterface::class);

        $subscriber = new DeactivationUserSubscriber($em, new DetachUserOfSites());
        $event = new DeactivationUserEvent($results['user1']);

        $dispatcher = new EventDispatcher();
        $dispatcher->addSubscriber($subscriber);
        $dispatcher->dispatch($event);

        $this->assertNull($results['site1']->getDefaultUser());
    }

    public function testGetSubscribedEvents(): void
    {
        $this->assertArrayHasKey(
            DeactivationUserEvent::class,
            DeactivationUserSubscriber::getSubscribedEvents()
        );
    }
}
