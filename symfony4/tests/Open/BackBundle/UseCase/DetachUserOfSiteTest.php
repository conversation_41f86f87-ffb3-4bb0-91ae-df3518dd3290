<?php

declare(strict_types=1);

namespace Tests\Open\BackBundle\UseCase;

use Doctrine\ORM\EntityManagerInterface;
use <PERSON>ip\TestFixturesBundle\Services\DatabaseToolCollection;
use Liip\TestFixturesBundle\Services\DatabaseTools\AbstractDatabaseTool;
use Open\BackBundle\UseCase\DetachUserOfSites;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

final class DetachUserOfSiteTest extends KernelTestCase
{
    private AbstractDatabaseTool $databaseTool;

    protected function setUp(): void
    {
        static::bootKernel();

        $this->databaseTool = static::$container->get(DatabaseToolCollection::class)->get();
    }

    public function testShouldDetachUserOfAllSiteIfDeactivate(): void
    {
        $results = $this->databaseTool->loadAliceFixture([
            __DIR__ . '/../UserSiteFixtures.yaml'
        ]);

        /** @var EntityManagerInterface $em */
        $em = static::$container->get(EntityManagerInterface::class);
        $siteRepository = $em->getRepository('AppBundle:Site');

        $useCase = new DetachUserOfSites();

        $this->assertNotNull($results['site1']->getDefaultUser());

        $expected = $useCase->execute($results['user1'], $siteRepository);

        $this->assertNotEmpty($expected);
        $this->assertNull($expected[0]->getDefaultUser());
    }

    public function testShouldDetachUserButItNotSites(): void
    {
        $results = $this->databaseTool->loadAliceFixture([
            __DIR__ . '/../UserSiteFixtures.yaml'
        ]);

        /** @var EntityManagerInterface $em */
        $em = static::$container->get(EntityManagerInterface::class);
        $siteRepository = $em->getRepository('AppBundle:Site');

        $useCase = new DetachUserOfSites();

        $expected = $useCase->execute($results['user2'], $siteRepository);

        $this->assertEmpty($expected);
    }
}
