<?php

declare(strict_types=1);

namespace Tests\Open\FrontBundle\ProformaPdf\Builder;

use AppBundle\Entity\Company;
use AppBundle\Model\Cart\Cart;
use AppBundle\Model\Cart\CartItem;
use AppBundle\Model\DetailedOffer;
use AppBundle\Model\Merchant;
use AppBundle\Model\Offer;
use AppBundle\Services\OfferService;
use Open\FrontBundle\Exception\OfferNotFoundException;
use Open\FrontBundle\ProformaPdf\Builder\ProformaPdfBuilder;
use Open\FrontBundle\ProformaPdf\MerchantData;
use Open\IzbergBundle\Api\MerchantApi;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class ProformaPdfBuilderTest extends TestCase
{
    private readonly MerchantApi|MockObject $merchantApiMock;

    private readonly OfferService|MockObject $offerServiceMock;

    private readonly ProformaPdfBuilder $builder;

    protected function setUp(): void
    {
        parent::setUp();


        $this->merchantApiMock = $this->createMock(MerchantApi::class);
        $this->offerServiceMock = $this->createMock(OfferService::class);

        $this->builder = new ProformaPdfBuilder(
            merchantApi: $this->merchantApiMock,
            offerService: $this->offerServiceMock,
        );
    }

    public function testCreateProformaPdfDataSuccessful(): void
    {
        $this->offerServiceMock->expects($this->once())
            ->method('findDetailedOfferById')
            ->willReturnCallback(function () {
                $merchant = new Merchant();
                $merchant->setId(1);
                $offer = new Offer();
                $offer->setProductId(1);
                $offer->setMerchant($merchant);

                $detailedOffer = new DetailedOffer();
                $detailedOffer->setOffer($offer);
                return $detailedOffer;
            })
        ;

        $this->merchantApiMock->expects($this->once())->method('getMerchant')->willReturn((object)[
            'id' => 1,
            'name' => 'Merchant name',
        ]);

        $data = $this->builder->create(cart: $this->makeCartModel(), company: new Company());

        $expected = (array)$data;

        $this->assertArrayHasKey(1, $expected);
        $this->assertArrayHasKey('offers', $expected[1]);
        $this->assertArrayHasKey('merchant', $expected[1]);
        $this->assertInstanceOf(MerchantData::class, $expected[1]['merchant']);
        $this->assertCount(1, $expected[1]['offers']);
    }

    public function testFailCreatePdfDataIfOfferNotFound(): void
    {
        $this->expectException(OfferNotFoundException::class);
        $this->expectExceptionMessage('No offer with id 1 was found');

        $this->offerServiceMock->expects($this->once())
            ->method('findDetailedOfferById')
            ->willReturn(null)
        ;

        $this->merchantApiMock->expects($this->never())->method('getMerchant');

        $this->builder->create(cart: $this->makeCartModel(), company: new Company());
    }

    public function testFailCreatePdfDataIfCartHasNotItems(): void
    {
        $this->offerServiceMock->expects($this->never())->method('findDetailedOfferById');

        $this->merchantApiMock->expects($this->never())->method('getMerchant');

        $pdfData = $this->builder->create(cart: $this->makeCartModel()->setItems([]), company: new Company());

        $this->assertEmpty($pdfData);
    }

    private function makeCartModel(): Cart
    {
        $cartItem = new CartItem();
        $cartItem->setId(1);
        $cartItem->setOfferId(1);
        $cartItem->setName('Product 1');
        $cartItem->setTotalItem(200);

        $cart = new Cart();
        $cart->setId(1);
        $cart->setCurrency('EUR');
        $cart->setItems([
            $cartItem
        ]);
        $cart->setTotal(200);

        return $cart;
    }
}
