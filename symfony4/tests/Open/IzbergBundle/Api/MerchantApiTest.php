<?php

declare(strict_types=1);

namespace Tests\Open\IzbergBundle\Api;

use AppBundle\Services\SecurityService;
use J<PERSON>\Serializer\SerializerInterface;
use Open\IzbergBundle\Api\ApiClientManager;
use Open\IzbergBundle\Api\MerchantApi;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Tests\DummyFactory\DummyAlstomCustomAttributesFactory;
use Tests\DummyFactory\DummyApiConfiguratorFactory;

final class MerchantApiTest extends TestCase
{
    private readonly MerchantApi $merchantApi;
    private readonly ApiClientManager $apiClientManager;

    private TokenStorageInterface|MockObject $tokenStorage;

    private MockObject|SerializerInterface $serializer;

    private SecurityService|MockObject $securityService;

    private SessionInterface|MockObject $session;

    protected function setUp(): void
    {
        parent::setUp();

        $this->apiClientManager = new ApiClientManager();
        $this->apiClientManager->setConfiguration(DummyApiConfiguratorFactory::createApiConfiguration());
        $this->tokenStorage = $this->createMock(TokenStorageInterface::class);
        $this->serializer = $this->createMock(SerializerInterface::class);
        $this->securityService = $this->createMock(SecurityService::class);
        $this->session = $this->createMock(SessionInterface::class);

        $this->merchantApi = new MerchantApi(
            apiClientManager: $this->apiClientManager,
            tokenStorage: $this->tokenStorage,
            session: $this->session,
            serializer: $this->serializer,
            securityService: $this->securityService,
            customAttributes: DummyAlstomCustomAttributesFactory::createCustomAttribute(),
        );
    }

    public function testGetItemApiClass(): void
    {
        $this->assertSame('Open\IzbergBundle\Model\Merchant', $this->merchantApi->getItemClass());
    }
}
