<?php

declare(strict_types=1);

namespace Tests\Open\IzbergBundle\Api;

use Open\IzbergBundle\Api\ApiConfiguration;
use Open\IzbergBundle\Api\ApiConfigurator;
use Open\IzbergBundle\Api\ApiConfiguratorFactory;
use PHPUnit\Framework\TestCase;

final class ApiConfiguratorFactoryTest extends TestCase
{
    public function testBuildDefaultConfiguration(): void
    {
        $config = [
            'default_connection' => [
                'domain' => 'domain',
                'domain_seller' => 'domain_seller',
                'version' => 'version',
                'protocol' => 'protocol',
                'access_token' => 'access_token',
                'application_id' => 'application_id',
                'application_namespace' => 'application_namespace',
                'secret_key' => 'secret_key',
                'email' => 'email',
                'username' => 'username',
                'first_name' => 'first_name',
                'last_name' => 'last_name',
                'jwt_secret' => 'jwt_secret',
                'seller_email_domain' => 'seller_email_domain',
                'create_merchant_url' => 'create_merchant_url',
                'identity_api_url' => 'identity_api_url',
                'client_id' => 1,
                'domain_id' => 1,
                'audience' => 'audience',
            ],
            'other_connections' => [],
        ];
        $apiConfig = ApiConfiguratorFactory::createApiConfigurator(config: $config);

        $this->assertInstanceOf(ApiConfigurator::class, $apiConfig);
        $this->assertTrue(in_array('default', array_keys($apiConfig->getConfigurations())));
        $this->assertInstanceOf(ApiConfiguration::class, $apiConfig->findConfiguration('default'));
        $this->assertSame('domain', $apiConfig->findConfiguration('default')->getDomain());
    }
}
