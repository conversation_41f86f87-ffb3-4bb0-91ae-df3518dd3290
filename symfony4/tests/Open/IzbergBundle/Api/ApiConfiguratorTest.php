<?php

declare(strict_types=1);

namespace Tests\Open\IzbergBundle\Api;

use Open\IzbergBundle\Api\ApiConfiguration;
use Open\IzbergBundle\Api\ApiConfigurator;
use Open\IzbergBundle\Exception\InvalidApiConfigurationNameException;
use PHPUnit\Framework\TestCase;

final class ApiConfiguratorTest extends TestCase
{
    public function testConstructAddDefaultConfiguration(): void
    {
        $apiConfigurator = new ApiConfigurator(defaultConfiguration: $this->makeApiConfiguration());

        $this->assertInstanceOf(ApiConfigurator::class, $apiConfigurator);
        $this->assertCount(1, $apiConfigurator->getConfigurations());
    }

    public function testAddConfiguration(): void
    {
        $apiConfigurationDefault = $this->makeApiConfiguration();
        $apiConfigurationConsole = $this->makeApiConfiguration('console');

        $apiConfigurator = new ApiConfigurator(defaultConfiguration: $apiConfigurationDefault);

        $this->assertInstanceOf(ApiConfigurator::class, $apiConfigurator);
        $this->assertCount(1, $apiConfigurator->getConfigurations());

        $apiConfigurator->addConfiguration($apiConfigurationConsole);

        $this->assertCount(2, $apiConfigurator->getConfigurations());
    }

    public function testFindConfigurationIfExist(): void
    {
        $apiConfigurationDefault = $this->makeApiConfiguration();
        $apiConfigurationConsole = $this->makeApiConfiguration('console');

        $apiConfigurator = new ApiConfigurator(defaultConfiguration: $apiConfigurationDefault);
        $apiConfigurator->addConfiguration($apiConfigurationConsole);

        $this->assertInstanceOf(ApiConfiguration::class, $apiConfigurator->findConfiguration(connection: 'default'));
        $this->assertInstanceOf(ApiConfiguration::class, $apiConfigurator->findConfiguration(connection: 'console'));

        // The connection is not defined :
        $this->assertSame('default', $apiConfigurator->findConfiguration(connection: 'unknown')->getConfigurationName());
    }

    public function testAddConfigurationWithUnknownConfigName(): void
    {
        $this->expectException(InvalidApiConfigurationNameException::class);
        $this->expectExceptionMessage('The unknownName api configuration name is not supported in (default | console | webhook');

        $apiConfigurationDefault = $this->makeApiConfiguration('unknownName');

        new ApiConfigurator(defaultConfiguration: $apiConfigurationDefault);
    }

    private function makeApiConfiguration(string $configName = 'default'): ApiConfiguration
    {
        return (new ApiConfiguration())
            ->setDomain('domain')
            ->setDomainSeller('domain_seller')
            ->setVersion('version')
            ->setProtocol('protocol')
            ->setAccessToken('access_token')
            ->setApplicationId('application_id')
            ->setApplicationNamespace('application_namespace')
            ->setSecretKey('secret_key')
            ->setEmail('email')
            ->setUsername('username')
            ->setFirstName('first_name')
            ->setLastName('last_name')
            ->setJwtSecret('jwt_secret')
            ->setSellerEmailDomain('seller_email_domain')
            ->setCreateMerchantUrl('create_merchant_url')
            ->setIdentityApiUrl('identity_api_url')
            ->setClientId("1F")
            ->setDomainId("1A")
            ->setAudience('audience')
            ->setConfigurationName($configName)
        ;
    }
}
