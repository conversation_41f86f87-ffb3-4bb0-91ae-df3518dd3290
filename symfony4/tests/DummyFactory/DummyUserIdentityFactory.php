<?php

declare(strict_types=1);

namespace Tests\DummyFactory;

use Open\IzbergBundle\Model\UserIdentity;

final class DummyUserIdentityFactory
{
    public static function createUserIdentity(array $data = []): UserIdentity
    {
        $properties = $data + [
            'uuid' => '3da79982-ccaf-46a2-a8a0-94eae408d445',
            'domain_id' => '120112B18E',
            'domain_alias' => 'domain_alias',
            'application' => 'application',
            'email' => 'email',
            'first_name' => 'john',
            'last_name' => 'doe',
            'status' => 'status',
            'created_at' => 'created_at',
            'last_login' => 'last_login',
            'last_token_refresh' => 'last_token_refresh',
            'created_by' => 'created_by',
            'merchant_scopes' => [
                1 => ['*' => 'admin'],
                2 => ['*' => 'admin'],
            ],
            'user_type' => 'merchant',
        ];

        return new UserIdentity(...$properties);
    }
}
