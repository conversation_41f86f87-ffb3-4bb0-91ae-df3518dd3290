<?php

declare(strict_types=1);

namespace Tests\DummyFactory;

use AppBundle\Services\AlstomCustomAttributes;

final class DummyAlstomCustomAttributesFactory
{
    public static function createCustomAttribute(array $attributes = []): AlstomCustomAttributes
    {
        return new AlstomCustomAttributes($attributes + [
            'associated_products' => '',
            'associated_services' => '',
            'manufacturer_reference' => '',
            'incoterm' => '',
            'country_of_delivery' => '',
            'supplier_product_compatibilities' => '',
            'threshold_1' => '',
            'threshold_1_price' => '',
            'threshold_2' => '',
            'threshold_2_price' => '',
            'threshold_3' => '',
            'threshold_3_price' => '',
            'threshold_4' => '',
            'threshold_4_price' => '',
            'moq' => '',
            'total_delay_for_customer' => '',
            'standard' => '',
            'fit_form_functions' => '',
            'manufacturer_obsolete_references' => '',
            'unspsc_code' => '',
            'tax_group' => '',
            'batch_size' => '',
            'stock_availability' => '',
            'stock_management' => '',
            'psp_merchant_id' => '',
            'vendor_reference' => '',
            'delivery_time' => '',
            'data_sheet' => '',
            'quantity_per_sku' => '',
            'sku_unit' => '',
            'fca_address' => '',
            'fca_zip_code' => '',
            'fca_zip_town' => '',
            'company_identification_number' => '',
            'merchant_legal_notice' => '',
            'minimum_order_amount' => '',
            'dangerous_product' => '',
            'package_height_moq' => '',
            'package_width_moq' => '',
            'package_length_moq' => '',
            'package_weight_moq' => '',
            'stackability' => '',
            'transport_type' => '',
            'custom_tariff_code' => '',
            'upela_is_active' => '',
            'made_in' => '',
            'delay_before_shipping' => '',
            'last_buy_order_date' => '',
            'restricted_product_boolean' => '',
            'restricted_product_field' => '',
            'restricted_product_fields_count' => '',
            'fire_smoke' => '',
            'share_capital' => '',
            'vendor_vat_number' => '',
            'merchant_order_url_pdf' => '',
            'price_validity_date' => '',
            'warranty_period' => '',
        ]);
    }
}
