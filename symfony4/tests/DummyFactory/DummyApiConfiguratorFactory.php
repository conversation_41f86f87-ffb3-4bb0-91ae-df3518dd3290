<?php

declare(strict_types=1);

namespace Tests\DummyFactory;

use Open\IzbergBundle\Api\ApiConfiguration;
use Open\IzbergBundle\Api\ApiConfiguratorFactory;

final class DummyApiConfiguratorFactory
{
    public static  function createApiConfiguration(string $connection = 'default', array $defaultConnection = []): ApiConfiguration
    {
        $config = [
            'default_connection' => $defaultConnection + [
                'domain' => 'domain',
                'domain_seller' => 'domain_seller',
                'version' => 'version',
                'protocol' => 'protocol',
                'access_token' => 'access_token',
                'application_id' => 'application_id',
                'application_namespace' => 'application_namespace',
                'secret_key' => 'secret_key',
                'email' => 'email',
                'username' => 'username',
                'first_name' => 'first_name',
                'last_name' => 'last_name',
                'jwt_secret' => 'jwt_secret',
                'seller_email_domain' => 'seller_email_domain',
                'create_merchant_url' => 'create_merchant_url',
                'identity_api_url' => 'http://identity.api/',
                'client_id' => 1,
                'domain_id' => 1,
                'audience' => 'audience',
            ],
            'other_connections' => [],
        ];

        return ApiConfiguratorFactory::createApiConfigurator($config)->findConfiguration($connection);
    }
}
