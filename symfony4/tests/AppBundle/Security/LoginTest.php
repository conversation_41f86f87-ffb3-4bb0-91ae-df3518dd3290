<?php

declare(strict_types=1);

namespace Tests\AppBundle\Security;

use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Tests\Api\Traits\FixturesTrait;

abstract class LoginTest extends WebTestCase
{
    use FixturesTrait;

    public function testIfLoginIsSuccessful(): void
    {
        $this->getDatabaseTool()->loadAliceFixture([
            __DIR__ . '/AuthenticateUserLoginFixtures.yaml'
        ]);

        $client = static::createClient();

        /** @var UrlGeneratorInterface $router */
        $router = static::$container->get(UrlGeneratorInterface::class);

        $client->request('GET', $router->generate('login'));
        $client->submitForm('Log in', [
           '_username' => '<EMAIL>',
           '_password' => 'password'
        ]);

        $this->assertResponseStatusCodeSame(302);
    }

    public function testLoginWithApiRole(): void
    {
        $this->getDatabaseTool()->loadAliceFixture([
            __DIR__ . '/AuthenticateUserLoginFixtures.yaml'
        ]);

        $client = static::createClient();

        /** @var UrlGeneratorInterface $router */
        $router = static::$container->get(UrlGeneratorInterface::class);

        $client->request('GET', $router->generate('login'));
        $client->submitForm('Log in', [
           '_username' => '<EMAIL>',
           '_password' => 'password'
        ]);

        $this->assertResponseStatusCodeSame(302);

        $crawler = $client->followRedirect();
        $crawler = $client->click($crawler->filter('a')->link());

        $this->assertStringContainsString('Buyer Login', $crawler->filter('h2.LoginForm-header')->text());
    }
}
