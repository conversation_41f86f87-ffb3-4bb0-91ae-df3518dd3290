<?php

declare(strict_types=1);

namespace Tests\AppBundle\ValueObject;

use Api\Core\Exception\InvalidArgumentException;
use AppBundle\ValueObject\PaymentMethod;
use PHPUnit\Framework\TestCase;

final class PaymentMethodTest extends TestCase
{
    public function testConstructPaymentMethod(): void
    {
        $paymentMethod = PaymentMethod::createToLabel('preTransferWire');

        $this->assertInstanceOf(PaymentMethod::class, $paymentMethod);
        $this->assertSame('preTransferWire', (string) $paymentMethod);
    }

    public function testConstructPaymentMethodFail(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('The fail payment method is not supported : preCreditCard, preTransferWire, termTransferWire');

        PaymentMethod::createToLabel('fail');
    }
}
