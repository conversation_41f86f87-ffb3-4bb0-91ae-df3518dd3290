<?php

namespace Tests\AppBundle\Services;

use AppBundle\Message\SendEmailMessage;
use AppBundle\Services\MailService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Messenger\Transport\InMemoryTransport;

class MailServiceAsyncTest extends TestCase
{
    private $messageBus;
    private $mailService;

    protected function setUp(): void
    {
        $this->messageBus = $this->createMock(MessageBusInterface::class);
        
        // Mock other dependencies (simplified for testing)
        $parameters = [
            'fosuser_from_email' => '<EMAIL>',
            'fosuser_from_name' => 'Test Sender',
            'force_locale' => false
        ];
        
        $this->mailService = $this->getMockBuilder(MailService::class)
            ->setConstructorArgs([
                $parameters,
                'example.com',
                'https://',
                'test',
                $this->createMock(\AppBundle\Services\SecurityService::class),
                $this->createMock(\Symfony\Component\Mailer\MailerInterface::class),
                $this->createMock(\Doctrine\ORM\EntityManagerInterface::class),
                $this->createMock(\Twig\Environment::class),
                $this->createMock(\Symfony\Component\Routing\RouterInterface::class),
                $this->createMock(\AppBundle\Repository\CompanyRepository::class),
                $this->createMock(\AppBundle\Repository\OrderRepository::class),
                $this->messageBus
            ])
            ->onlyMethods(['sendEmailMessageSync'])
            ->getMock();
    }

    public function testSendEmailMessageAsync()
    {
        // Test that async email sending dispatches a message to the bus
        $this->messageBus->expects($this->once())
            ->method('dispatch')
            ->with($this->isInstanceOf(SendEmailMessage::class))
            ->willReturn(new Envelope(new SendEmailMessage('TEST_EMAIL', 'en', '<EMAIL>', [])));

        $result = $this->mailService->sendEmailMessage(
            'TEST_EMAIL',
            'en',
            '<EMAIL>',
            ['test' => 'data'],
            null,
            null,
            [],
            true // async = true
        );

        $this->assertTrue($result);
    }

    public function testSendEmailMessageSync()
    {
        // Test that sync email sending calls the sync method
        $this->mailService->expects($this->once())
            ->method('sendEmailMessageSync')
            ->with('TEST_EMAIL', 'en', '<EMAIL>', ['test' => 'data'], null, null, [])
            ->willReturn(true);

        $result = $this->mailService->sendEmailMessage(
            'TEST_EMAIL',
            'en',
            '<EMAIL>',
            ['test' => 'data'],
            null,
            null,
            [],
            false // async = false
        );

        $this->assertTrue($result);
    }

    public function testSendEmailMessagePriority()
    {
        // Test priority classification
        $highPriorityMessage = new SendEmailMessage('USER_RESET_MDP', 'en', '<EMAIL>', []);
        $this->assertTrue($highPriorityMessage->isHighPriority());
        $this->assertEquals('high', $highPriorityMessage->getPriority());

        $lowPriorityMessage = new SendEmailMessage('SOME_BULK_EMAIL', 'en', '<EMAIL>', []);
        $this->assertFalse($lowPriorityMessage->isHighPriority());
        $this->assertEquals('low', $lowPriorityMessage->getPriority());
    }
}
