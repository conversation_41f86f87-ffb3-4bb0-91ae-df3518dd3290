<?php

namespace Tests\AppBundle\Services;

use AppBundle\Entity\MerchantOrder;
use AppBundle\Entity\Order;
use AppBundle\Repository\MerchantOrderRepository;
use AppBundle\Services\MerchantOrderSyncService;
use AppBundle\Services\OrderItemService;
use Open\IzbergBundle\Api\OrderApi;
use Open\IzbergBundle\Model\OrderItem;
use Open\IzbergBundle\Model\OrderMerchant;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophet;

class MerchantOrderSyncServiceTest extends TestCase
{

    private MerchantOrderSyncService $merchantORderSyncService;

    /**
     * @param OrderMerchant $merchant
     * @param Order $orderEntity
     * @dataProvider provideSyncMerchantOrder
     */
    public function testSyncMerchantOrder(OrderMerchant $merchant, \AppBundle\Entity\Order $orderEntity)
    {
        $this->assertTrue($this->merchantORderSyncService->syncMerchantOrder($merchant, $orderEntity));
    }

    /**
     * @return \Generator
     */
    public function provideSyncMerchantOrder(){
        $merchant =  new OrderMerchant();
        $merchant->setId(0);
        $entity = new Order();
        yield [$merchant, $entity];
        $merchant =  new OrderMerchant();
        $merchant->setId(12);
        yield [$merchant, $entity];
    }

    /**
     *
     */
    public function setUp(): void
    {
        $prophet = new Prophet();
        $merchantOrderRepository = $prophet->prophesize(MerchantOrderRepository::class);
        $merchantOrderRepository->findOneBy(Argument::type("array"))->will(function ($args) {
            if ($args[0]["izbergId"] === 12){
                //build fake merchantOrder
                return new MerchantOrder();
            }

            return null;
        });
        $orderItemService = $prophet->prophesize(OrderItemService::class);
        $orderItemService->syncOrderItem(Argument::type(OrderItem::class),Argument::type(MerchantOrder::class))->willReturn(true);
        $orderApi = $prophet->prophesize(OrderApi::class);
        $orderApi->fetchOrdersItemByMerchantOrder(Argument::type("int"))->will(function ($args) {
            $orderItem = new OrderItem();
            return [$orderItem];
        });
        $this->merchantORderSyncService = new MerchantOrderSyncService($merchantOrderRepository->reveal(), $orderItemService->reveal(), $orderApi->reveal());
    }
}
