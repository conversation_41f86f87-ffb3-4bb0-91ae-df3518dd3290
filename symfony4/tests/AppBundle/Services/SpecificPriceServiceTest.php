<?php

namespace AppBundle\Services;

use AppBundle\Command\CsvToArrayService;
use AppBundle\Entity\Company;
use AppBundle\Entity\SpecificPrice;
use AppBundle\Model\Merchant;
use AppBundle\Model\Offer;
use AppBundle\Repository\CompanyRepository;
use AppBundle\Repository\SpecificPriceRepository;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Generator;
use Open\BCEBundle\Service\BCEService;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophecy\ObjectProphecy;
use Prophecy\Prophet;

class SpecificPriceServiceTest extends TestCase
{
    private Prophet $prophet;
    private ObjectProphecy $entityManager;
    private ObjectProphecy $BCEService;
    private ObjectProphecy $userService;
    private ObjectProphecy $csvToArrayService;
    private ObjectProphecy $specificPriceMapper;
    private ObjectProphecy $companyRepository;
    private ObjectProphecy $specificPriceRepository;

    /**
     * @dataProvider updateOfferSpecificPricesDateValidityControlProvider
     * @param DateTime|null $validityDate
     * @param array $specificPriceThresholds
     * @param array $expectedThresholds
     * @param int $delayOfDelivery
     * @param int $expectedDelayOfDelivery
     */
    public function testUpdateOfferSpecificPricesDateValidityControl(
        ?DateTime $validityDate,
        array $specificPriceThresholds,
        array $expectedThresholds,
        int $delayOfDelivery,
        int $expectedDelayOfDelivery
    )
    {
        $company = new Company();
        $offer = $this->buildOffer();

        [$t1, $t2, $t3, $t4] = array_keys($specificPriceThresholds);
        [$p1, $p2, $p3, $p4] = array_values($specificPriceThresholds);

        $specificPrice = (new SpecificPrice())
            ->setValidityDate($validityDate)
            ->setDelayOfDelivery($delayOfDelivery)
            ->setMoq(5)
            ->setBasicPrice(5)
            ->setThreshold1($t1)
            ->setPrice1($p1)
            ->setThreshold2($t2)
            ->setPrice2($p2)
            ->setThreshold3($t3)
            ->setPrice3($p3)
            ->setThreshold4($t4)
            ->setPrice4($p4)
            ->setFrameContract('GTHUINDT');

        $this->specificPriceRepository->findOneBy(Argument::type('array'))->willReturn($specificPrice);

        $specificPriceService = $this->buildSpecificPriceService();
        $specificPriceService->updateOfferSpecificPrices($company, $offer);

        foreach($offer->getThresholds() as $threshold => $price) {
            $this->assertArrayHasKey($threshold, $expectedThresholds);
            $expectedPrice = $expectedThresholds[$threshold];
            $this->assertEquals($expectedPrice, $price);
        }

        $this->assertEquals($expectedDelayOfDelivery, $offer->getDeliveryTime());
    }

    public function updateOfferSpecificPricesDateValidityControlProvider(): Generator
    {
        $specificPriceThresholds = $expectedThresholds = [
            10 => 60,
            20 => 70,
            30 => 80,
            40 => 90,
        ];

        $validityDate = new DateTime();
        $validityDate->add(new \DateInterval('P2D'));

        $deliveryTime = $expectedDeliveryTime = 5;

        yield [$validityDate, $specificPriceThresholds, $expectedThresholds, $deliveryTime, $expectedDeliveryTime];

        $validityDate = null;

        yield [$validityDate, $specificPriceThresholds, $expectedThresholds, $deliveryTime, $expectedDeliveryTime];

        $validityDate = new DateTime();
        $validityDate->sub(new \DateInterval('P2D'));
        $expectedThresholds = $this->defaultOfferThresholds();
        $expectedDeliveryTime = $this->defaultDeliveryTime();

        yield [$validityDate, $specificPriceThresholds, $expectedThresholds, $deliveryTime, $expectedDeliveryTime];
    }

    private function buildOffer(): Offer
    {

        $merchant = (new Merchant())
            ->setId(1);
        $incoterm = 'DAP';
        $country = 'France';
        $currency = 'EUR';
        $prices = ['EUR' => 5];

        return (new Offer())
            ->setThresholds($this->defaultOfferThresholds())
            ->setMerchant($merchant)
            ->setIncoterm($incoterm)
            ->setIncotermCountry($country)
            ->setCurrency($currency)
            ->setPrices($prices)
            ->setDeliveryTime($this->defaultDeliveryTime());
    }

    private function defaultOfferThresholds() : array
    {
        return [
            10 => 10,
            20 => 20,
            30 => 30,
            40 => 40,
        ];
    }

    private function defaultDeliveryTime() : int
    {
        return 42;
    }

    private function buildSpecificPriceService(): SpecificPriceService
    {
        return new SpecificPriceService(
            $this->entityManager->reveal(),
            $this->BCEService->reveal(),
            $this->userService->reveal(),
            $this->csvToArrayService->reveal(),
            $this->specificPriceMapper->reveal(),
            $this->companyRepository->reveal()
        );
    }

    protected function setUp(): void
    {
        $this->prophet = new Prophet;

        $this->entityManager = $this->prophet->prophesize(EntityManagerInterface::class);
        $this->BCEService = $this->prophet->prophesize(BCEService::class);
        $this->userService = $this->prophet->prophesize(UserBddService::class);
        $this->csvToArrayService = $this->prophet->prophesize(CsvToArrayService::class);
        $this->specificPriceMapper = $this->prophet->prophesize(SpecificPriceMapper::class);
        $this->companyRepository = $this->prophet->prophesize(CompanyRepository::class);
        $this->specificPriceRepository = $this->prophet->prophesize(SpecificPriceRepository::class);

        $this->entityManager->getRepository(SpecificPrice::class)->willReturn($this->specificPriceRepository->reveal());
    }
}
