<?php

namespace Tests\AppBundle\Services;

use AppBundle\Services\MessengerProgressionService;
use Open\IzbergBundle\Service\RedisService;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophet;

class MessengerProgressionServiceTest extends TestCase
{

    private MessengerProgressionService $messengerProgressionService;

    private Prophet $prophet;

    /**
     * This function will test the initProgression function.
     */
    public function testInitProgression()
    {
        $result = $this->messengerProgressionService->initProgression(MessengerProgressionService::CATALOG_MESSENGER, 0, 10);
        $this->assertTrue($result);
    }

    /**
     * @param int  $companyId
     * @param int  $limit
     * @param bool $expected
     * @dataProvider setProgressionLimitProvider
     */
    public function testSetProgressionLimit(int $companyId, int $limit, bool $expected)
    {
        $result = $this->messengerProgressionService->setProgressionLimit(MessengerProgressionService::CATALOG_MESSENGER, $companyId, $limit);
        $this->assertEquals($expected, $result);
    }

    /**
     * This function will provide data for testGetProgression.
     * @return \Generator
     */
    public function setProgressionLimitProvider(){

        yield [0,10, true];

        yield [5,10, false];
    }

    /**
     * This function will test the hasMessageInProgress.
     *
     * @param int $companyId
     * @param bool $expected
     *
     * @dataProvider hasMessageInProgressProvider
     */
    public function testHasMessageInProgress(int $companyId, bool $expected)
    {
        $result = $this->messengerProgressionService->hasMessageInProgress(MessengerProgressionService::CATALOG_MESSENGER, $companyId);
        $this->assertEquals($expected, $result);
    }

    /**
     * This function will provide data for testHasMessageInProgress.
     * @return \Generator
     */
    public function hasMessageInProgressProvider(){
        yield [0, true];

        yield [5, false];
    }

    /**
     * this function will test the getProgression function.
     *
     * @dataProvider getProgressionProvider
     */
    public function testGetProgression(int $companyId, ?array $expected)
    {
        $result = $this->messengerProgressionService->getProgression(MessengerProgressionService::CATALOG_MESSENGER, $companyId);
        $this->assertEquals($expected, $result);
    }

    /**
     * This function will provide data for testGetProgression.
     * @return \Generator
     */
    public function getProgressionProvider(){

        yield [0, [MessengerProgressionService::CURRENT_ITEM=>9, MessengerProgressionService::MAX_ITEM=>10]];

        yield [5, null];
    }


    /**
     * This function will test the incrementProgression function.
     *
     * @param int $companyId
     * @param bool $expected
     *
     * @dataProvider incrementProgressionProvider
     */
    public function testIncrementProgression(int $companyId, bool $expected)
    {
        $result = $this->messengerProgressionService->incrementProgression(MessengerProgressionService::CATALOG_MESSENGER, $companyId);
        $this->assertEquals($expected, $result);
    }

    /**
     * This function will provide data for the testIncrementProgression.
     *
     * @return \Generator
     */
    public function incrementProgressionProvider(){
        yield [0, true];

        yield [1, true];
    }

    protected function setUp():void{
        $this->prophet = new Prophet;
        $redisService = $this->prophet->prophesize(RedisService::class);
        $redisService->saveItem(Argument::type('string'), Argument::any(), Argument::type('int'))->willReturn(true);
        $redisService->removeItem(Argument::type('string'))->willReturn();
        $redisService->getItem(Argument::type('string'))->will(function ($args) {
            if($args[0] === 'CATALOG_MESSENGER_0'){
                return [MessengerProgressionService::CURRENT_ITEM=>9, MessengerProgressionService::MAX_ITEM=>10];
            }else if ($args[0] === 'CATALOG_MESSENGER_1'){
                return [MessengerProgressionService::CURRENT_ITEM=>0, MessengerProgressionService::MAX_ITEM=>10];
            }
            return null;
        });
        $this->messengerProgressionService = new MessengerProgressionService($redisService->reveal());
    }

    protected function tearDown():void
    {
        $this->prophet->checkPredictions();
    }

}
