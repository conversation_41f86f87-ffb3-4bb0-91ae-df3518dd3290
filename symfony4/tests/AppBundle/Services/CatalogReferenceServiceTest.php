<?php

namespace Tests\AppBundle\Services;

use AppBundle\Services\AlstomCustomAttributes;
use AppBundle\Services\CatalogReferenceService;
use Open\IzbergBundle\Algolia\AlgoliaServiceEn;
use Open\IzbergBundle\Service\RedisService;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophet;

class CatalogReferenceServiceTest extends TestCase
{
    private CatalogReferenceService $catalogReferenceService;

    private Prophet $prophet;

    /**
     * @param string $reference
     * @param string $country
     * @param bool $expected
     *
     * @dataProvider referenceExistsProvider
     */
    public function testReferenceExists(string $reference, string $country, bool $expected)
    {
        $referenceExists = $this->catalogReferenceService->referenceExists($reference, $country);
        $this->assertEquals($expected, $referenceExists);
    }

    public function referenceExistsProvider()
    {
        // test dap country true
        $reference = '45654546';
        $country = 'france';
        $expected = true;

        yield [$reference, $country, $expected];

        // test dap country false
        $reference = 'trucmuche';
        $country = 'france';
        $expected = false;

        yield [$reference, $country, $expected];

        // test fca true
        $reference = 'foobar';
        $country = 'france';
        $expected = true;

        yield [$reference, $country, $expected];
    }

    protected function setUp(): void
    {
        $this->prophet = new Prophet;

        $redisService = $this->prophet->prophesize(RedisService::class);
        $redisService->getItem(Argument::type('string'))->willReturn($this->listOfReferences());
        $algoliaServiceEn = $this->prophet->prophesize(AlgoliaServiceEn::class);
        $customAttributes = $this->prophet->prophesize(AlstomCustomAttributes::class);

        $this->catalogReferenceService = new CatalogReferenceService(
            $redisService->reveal(),
            $algoliaServiceEn->reveal(),
            $customAttributes->reveal()
        );
    }

    private function listOfReferences(): array
    {
        return [
            '45654546_dap_france',
            'trucmuche_dap_italy',
            'foobar_fca',
        ];
    }
}
