<?php

namespace Tests\AppBundle\Services;

use AppBundle\Repository\CountryRepository;
use AppBundle\Services\AlstomCustomAttributes;
use AppBundle\Services\CustomsService;
use AppBundle\Services\FeesOperatorShippingService;
use AppBundle\Services\OfferService;
use AppBundle\Services\SerializerService;
use AppBundle\Services\ShippingService;
use AppBundle\Services\UpelaApiService;
use DG\BypassFinals;
use Doctrine\Common\Collections\ArrayCollection;
use Generator;
use Open\IzbergBundle\Api\CartApi;
use Open\IzbergBundle\Api\ProductOfferApi;
use Open\IzbergBundle\Model\OrderItem;
use Open\IzbergBundle\Model\OrderMerchant;
use Open\IzbergBundle\Service\RedisService;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophecy\ObjectProphecy;
use Prophecy\Prophet;

class ShippingServiceTest extends TestCase
{
    private ShippingService $shippingService;
    private ObjectProphecy $productOfferApi;
    private Prophet $prophet;

    /**
     * @param array $merchantOrders
     * @param int $offerId
     *
     * @dataProvider removeShippingOfferFromMerchantOrdersProvider
     */
    public function testRemoveShippingOfferFromMerchantOrders(array $merchantOrders, int $offerId)
    {
        $this->productOfferApi->deactivateProduct(Argument::exact($offerId))->shouldBeCalled();
        $this->productOfferApi->trashProduct(Argument::exact($offerId))->shouldBeCalled();
        $this->productOfferApi->removeOfferProduct(Argument::exact($offerId))->shouldBeCalled();
        $succeed = $this->shippingService->removeShippingOfferFromMerchantOrders($merchantOrders);

        $this->assertTrue($succeed);
    }


    public function removeShippingOfferFromMerchantOrdersProvider(): Generator
    {
        $offerId = 456;
        $externalOfferId = 'shipment-42';
        $merchantOrder = new OrderMerchant();
        $orderItem = new OrderItem();
        $orderItem->setOfferId($offerId);
        $orderItem->setOfferExternalId($externalOfferId);

        $orderItems = new ArrayCollection([$orderItem]);

        $merchantOrder->setItems($orderItems);

        $merchantOrders = [$merchantOrder];

        yield [$merchantOrders, $offerId];
    }

    protected function setup(): void
    {
        BypassFinals::enable();

        $this->prophet = new Prophet();

        $offerService = $this->prophet->prophesize(OfferService::class);
        $upelaApiService = $this->prophet->prophesize(UpelaApiService::class);
        $redisService = $this->prophet->prophesize(RedisService::class);
        $serializer = $this->prophet->prophesize(SerializerService::class);
        $countryRepository = $this->prophet->prophesize(CountryRepository::class);
        $cartApi = $this->prophet->prophesize(CartApi::class);
        $customsService = $this->prophet->prophesize(CustomsService::class);
        $this->productOfferApi = $this->prophet->prophesize(ProductOfferApi::class);
        $customAttributes = $this->prophet->prophesize(AlstomCustomAttributes::class);
        $feeOperatorService = $this->prophet->prophesize(FeesOperatorShippingService::class);

        $this->shippingService = new ShippingService(
            $offerService->reveal(),
            $upelaApiService->reveal(),
            $redisService->reveal(),
            $serializer->reveal(),
            $countryRepository->reveal(),
            $cartApi->reveal(),
            $customsService->reveal(),
            $this->productOfferApi->reveal(),
            $customAttributes->reveal(),
            $feeOperatorService->reveal()
        );
    }

    protected function tearDown(): void
    {
        $this->prophet->checkPredictions();
    }


}
