<?php

declare(strict_types=1);

namespace Tests\AppBundle\Services;

use AppBundle\Entity\Company;
use AppBundle\Factory\PaymentModeFactoryInterface;
use AppBundle\Model\PaymentMode\PaymentModeInterface;
use AppBundle\Services\PaymentModes\PaymentModesService;
use AppBundle\Services\PaymentService;
use Open\IzbergBundle\Model\PaymentTerm;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;

final class PaymentModesServiceTest extends TestCase
{
    private PaymentModesService $service;

    protected function setUp(): void
    {
        $request = new Request();
        $request->setLocale('FR');

        $requestStack = $this->createMock(RequestStack::class);
        $requestStack->expects($this->any())
            ->method('getCurrentRequest')
            ->willReturn($request)
        ;
        $paymentTerm = new PaymentTerm();
        $paymentTerm->setId("2");

        $paymentService = $this->createMock(PaymentService::class);
        $paymentService->expects($this->any())
            ->method('fetchPaymentTerm')
            ->willReturn($paymentTerm);

        $paymentModeFactory = new class implements PaymentModeFactoryInterface {
            public function toArray(PaymentModeInterface $paymentMode): array
            {
                return [
                    'name' => $paymentMode->getName(),
                ];
            }
        };
        $this->service = new PaymentModesService(
            $requestStack,
            $paymentModeFactory
        );
    }

    public function testShouldHaveThePaymentModeListOfCompanyGiven(): void
    {
        $company = new Company();
        $company->setPrepaymentCreditcardEnabled(false);
        $company->setPrepaymentMoneyTransfertEnabled(true);
        $company->setTermpaymentMoneyTransfertEnabled(true);

        $expected = $this->service->computePaymentModesOfCompany($company);
        $this->assertEquals([
            [
                'name' => 'preTransferWire'
            ],
            [
                'name' => 'termTransferWire'
            ]
        ], $expected);
    }
}
