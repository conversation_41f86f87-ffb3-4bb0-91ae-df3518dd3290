<?php

namespace Tests\AppBundle\Services;

use AppBundle\Entity\SpecificPrice;
use AppBundle\Exception\SpecificPriceMapperException;
use AppBundle\Services\SpecificPriceMapper;
use Generator;
use PHPUnit\Framework\TestCase;

class SpecificPriceMapperTest extends TestCase
{
    /**
     * @dataProvider mapToSpecificPriceEntityProvider
     * @param array $rowSet
     * @param array $expected
     */
    public function testMapToSpecificPriceEntity(array $rowSet, array $expected): void
    {
        $specificPriceMapper = new SpecificPriceMapper();

        $specificPriceSet = $specificPriceMapper->mapToSpecificPriceEntity($rowSet);
        $specificPriceSet = array_map(
            fn(SpecificPrice $specificPrice): SpecificPrice => $specificPrice->setCreatedAt($this->clock()),
            $specificPriceSet
        );

        $this->assertEquals($expected, $specificPriceSet);
    }

    public function mapToSpecificPriceEntityProvider(): Generator
    {
        $rowSet = [];
        $row = [
            "Buyer Identification Number" => 'FR51306138900',
            "Vendor Reference" => 'EQTRAM001A',
            "Incoterm" => 'FCA',
            "Country" => 'France',
            "Unit Price" => '2',
            "Threshold 1" => '5',
            "Price 1" => '5',
            "Threshold 2" => '10',
            "Price 2" => '10',
            "Threshold 3" => '15',
            "Price 3" => '15',
            "Threshold 4" => '20',
            "Price 4" => '20',
            "MOQ" => '2',
            "Validity Date" => '02/02/2030',
            "Total leadtime for customer" => '5',
            "Frame Contract" => 'DGTEHN4586'
        ];

        $entity = (new SpecificPrice())
            ->setCompanyIdentification('FR51306138900')
            ->setVendorReference('EQTRAM001A')
            ->setIncoterm('FCA')
            ->setCountry('France')
            ->setBasicPrice(2.0)
            ->setThreshold1(5)
            ->setPrice1(5.0)
            ->setThreshold2(10)
            ->setPrice2(10.0)
            ->setThreshold3(15)
            ->setPrice3(15.0)
            ->setThreshold4(20)
            ->setPrice4(20.0)
            ->setMoq(2)
            ->setDelayOfDelivery(5)
            ->setValidityDate(\DateTime::createFromFormat('d/m/Y', '02/02/2030')->setTime(0, 0))
            ->setFrameContract('DGTEHN4586')
            ->setCreatedAt($this->clock());

        $rowSet[] = $row;
        $entitySet[] = $entity;

        yield [$rowSet, $entitySet];
    }

    public function testMapToSpecificPriceEntityWithWrongValidityDateFormat()
    {
        $rowSet = [];
        $row = [
            "Buyer Identification Number" => 'FR51306138900',
            "Vendor Reference" => 'EQTRAM001A',
            "Incoterm" => 'FCA',
            "Country" => 'France',
            "Unit Price" => '2',
            "Threshold 1" => '5',
            "Price 1" => '5',
            "Threshold 2" => '10',
            "Price 2" => '10',
            "Threshold 3" => '15',
            "Price 3" => '15',
            "Threshold 4" => '20',
            "Price 4" => '20',
            "MOQ" => '2',
            "Validity Date" => '2030-02-02',
            "Total leadtime for customer" => '5',
            "Frame Contract" => 'DGTEHN4586'

        ];

        $rowSet[] = $row;

        $specificPriceMapper = new SpecificPriceMapper();

        $this->expectException(SpecificPriceMapperException::class);
        $specificPriceMapper->mapToSpecificPriceEntity($rowSet);
    }

    private function clock(): \DateTime
    {
        $date = \DateTime::createFromFormat('d/m/Y', '01/01/2021');
        $date->setTime(0,0);
        return $date;
    }
}
