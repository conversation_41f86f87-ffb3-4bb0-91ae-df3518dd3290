<?php

declare(strict_types=1);

namespace Tests\AppBundle\Services\Payload;

use AppBundle\Entity\Middleware\AbstractPayload;
use AppBundle\Services\Payload\PayloadTypeService;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ObjectManager;
use Liip\TestFixturesBundle\Services\DatabaseToolCollection;
use Liip\TestFixturesBundle\Services\DatabaseTools\AbstractDatabaseTool;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Contracts\Translation\TranslatorInterface;

final class PayloadTypeServiceTest extends KernelTestCase
{
    /**
     * @var EntityManagerInterface|MockObject
     */
    private EntityManagerInterface $emMock;

    private ?ObjectManager $entityManager;

    private AbstractDatabaseTool $databaseTool;

    /**
     * @var MockObject|TranslatorInterface
     */
    private TranslatorInterface $translator;

    protected function setUp(): void
    {
        parent::setUp();

        $kernel = static::bootKernel();

        $this->entityManager = $kernel->getContainer()->get('doctrine')->getManager();
        $this->databaseTool = $kernel->getContainer()->get(DatabaseToolCollection::class)->get();

        $this->emMock = $this->createMock(EntityManagerInterface::class);
        $this->translator = $this->createMock(TranslatorInterface::class);
    }

    public function testGetPayloadTypesToString(): void
    {
        $this->databaseTool->loadAliceFixture([
            __DIR__ . '/PayloadFixtures.yaml'
        ]);

        $this->translator->expects($this->any())
            ->method('trans')
            ->willReturn('PurchaseRequestItemPayload');

        $this->emMock->expects($this->once())
            ->method('getRepository')
            ->willReturn($this->entityManager->getRepository(AbstractPayload::class))
        ;
        $payloadTypeService = new PayloadTypeService($this->emMock, $this->translator);

        $actual = $payloadTypeService->getPayloadTypesToString();

        $this->assertCount(1, $actual);
        $this->assertEquals([
            'PurchaseRequestItemPayload' => 'AppBundle\Entity\Middleware\PurchaseRequestItemPayload'
        ], $actual);
    }
}
