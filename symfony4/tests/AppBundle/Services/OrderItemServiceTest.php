<?php

namespace Tests\AppBundle\Services;

use AppBundle\Entity\MerchantOrder;
use AppBundle\Entity\OrderItem;
use AppBundle\Model\Offer;
use Open\IzbergBundle\Model\OrderItem as OrderItemModel;
use AppBundle\Repository\OrderItemRepository;
use AppBundle\Services\OfferService;
use AppBundle\Services\OrderItemService;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophet;

class OrderItemServiceTest extends TestCase
{
    public OrderItemService $orderItemService;

    /**
     * @param OrderItemModel $orderItem
     * @param MerchantOrder $merchantOrderEntity
     * @param int $lineId
     * @dataProvider provideSyncOrderItem
     */
    public function testSyncOrderItem(OrderItemModel $orderItem, MerchantOrder $merchantOrderEntity)
    {
        $result = $this->orderItemService->syncOrderItem($orderItem, $merchantOrderEntity);
        $this->assertTrue($result);
    }

    /**
     * @return \Generator
     */
    public function provideSyncOrderItem()
    {
        //offer null, no orderItem, no delivry date
        $orderItem = new OrderItemModel();
        $orderItem->setOfferId(0);
        $orderItem->setId(0);
        $merchantOrder = new MerchantOrder();
        yield [$orderItem,$merchantOrder];

        $orderItem = new OrderItemModel();
        $orderItem->setOfferId(12);
        $orderItem->setId(12);
        $orderItem->setDeliveryDates([0=>["expected_delivery_date"=>"now"]]);
        $merchantOrder = new MerchantOrder();
        yield [$orderItem,$merchantOrder];
    }

    /**
     *
     */
    public function setUp(): void
    {
        $prophet = new Prophet();
        $offerService = $prophet->prophesize(OfferService::class);
        $offerService->findOfferById(Argument::type("int"))->will(function ($args) {
            if ($args[0] === 12){
                return self::buildFakeOffer();
            }

            return null;
        });
        $orderItemRepository = $prophet->prophesize(OrderItemRepository::class);
        $orderItemRepository->findOneBy(Argument::type("array"))->will(function ($args) {
            if ($args[0]["izbergId"] === 12){
                return self::buildFakeOrderItemEntity();
            }

            return null;
        });
        $this->orderItemService = new OrderItemService($orderItemRepository->reveal(), $offerService->reveal());
    }

    private static function buildFakeOrderItemEntity(){
        $orderItem = new OrderItem();
        return $orderItem;
    }

    private static function buildFakeOffer(){
        $offer = new Offer();
        $offer->setSellerRef("sellerRef");
        $offer->setOfferTitle("title");
        return $offer;
    }
}
