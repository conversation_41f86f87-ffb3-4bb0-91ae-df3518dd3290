test_name;test_value;regex_tested
test_australia_KO;12345678;^[0-9]{9}$
test_australia_OK;*********;^[0-9]{9}$
test_austria_KO;ATU*********;^ATU[0-9]{8}$
test_austria_OK;ATU12345678;^ATU[0-9]{8}$
test_bahrain_KO;*********01234A;^[0-9]{15}$
test_bahrain_OK;*********012345;^[0-9]{15}$
test_belgium_KO;BE*********;^BE[0-9]{10}$
test_belgium_OK;BE*********9;^BE[0-9]{10}$
test_brasil_KO;*********0123;^[0-9]{14}$
test_brasil_OK;*********01234;^[0-9]{14}$
test_bulgaria_KO;BG12345678;^BG[0-9]{9,10}$
test_bulgaria_KO2;BG*********01;^BG[0-9]{9,10}$
test_bulgaria_OK;BG*********;^BG[0-9]{9,10}$
test_bulgaria_OK2;BG*********0;^BG[0-9]{9,10}$
test_canada_KO;*********0;^[0-9]{9}$
test_canada_OK;*********;^[0-9]{9}$
test_canary_islands_KO;B12345678;^A[0-9]{8}$
test_canary_islands_OK;A12345678;^A[0-9]{8}$
test_china_KO;*********0AZERTYUIX;^[0-9,A-Z]{1,18}$
test_china_OK;*********0AZERTYUI;^[0-9,A-Z]{1,18}$
test_cyprus_KO;CYAZer12345a;^CY[0-9,A-Z,a-z]{9}$
test_cyprus_OK;CYAZer12345;^CY[0-9,A-Z,a-z]{9}$
test_czech_republic_KO;CZ1234567;^CZ[0-9]{8,10}$
test_czech_republic_KO2;CZ*********01;^CZ[0-9]{8,10}$
test_czech_republic_OK;CZ12345678;^CZ[0-9]{8,10}$
test_czech_republic_OK2;CZ*********;^CZ[0-9]{8,10}$
test_czech_republic_OK3;CZ*********0;^CZ[0-9]{8,10}$
test_denmark_KO;DK1234567;^DK[0-9]{8}$
test_denmark_OK;DK12345678;^DK[0-9]{8}$
test_estonia_KO;E*********;^EE[0-9]{9}$
test_estonia_OK;EE*********;^EE[0-9]{9}$
test_finland_KO;FI1234567;^FI[0-9]{8}$
test_finland_OK;FI12345678;^FI[0-9]{8}$
test_france_KO;FR*********012;^FR[0-9]{11}$
test_france_OK;FR*********01;^FR[0-9]{11}$
test_germany_KO;*********;^DE[0-9]{9}$
test_germany_OK;DE*********;^DE[0-9]{9}$
test_greece_KO;EL*********0;^EL[0-9]{9}$
test_greece_OK;EL*********;^EL[0-9]{9}$
test_hongkong_KO;123;^[0-9]{4,8}$
test_hongkong_KO2;*********;^[0-9]{4,8}$
test_hongkong_OK;1234;^[0-9]{4,8}$
test_hongkong_OK2;12345678;^[0-9]{4,8}$
test_hungary_KO;HU*********;^HU[0-9]{8}$
test_hungary_OK;HU12345678;^HU[0-9]{8}$
test_india_KO;1AZERTazert123;^[0-9]{2}[0-9,A-Z,a-z]{13}$
test_india_KO2;12AZERTazert1234;^[0-9]{2}[0-9,A-Z,a-z]{13}$
test_india_OK;12AZERTazert123;^[0-9]{2}[0-9,A-Z,a-z]{13}$
test_ireland_KO;IE123456A;^IE[0-9]{7}[A-Z]{1,2}$
test_ireland_KO2;**********ZE;^IE[0-9]{7}[A-Z]{1,2}$
test_ireland_OK;**********;^IE[0-9]{7}[A-Z]{1,2}$
test_ireland_OK2;**********Z;^IE[0-9]{7}[A-Z]{1,2}$
test_ireland_KO;IE123456A;^IE[0-9]{7}[A-Z]{1,2}$
test_ireland_KO2;**********ZE;^IE[0-9]{7}[A-Z]{1,2}$
test_italy_KO;*************2;^IT[0-9]{11}$
test_italy_OK;*************;^IT[0-9]{11}$
test_latvia_KO;*********01;^LV[0-9]{11}$
test_latvia_OK;LV*********01;^LV[0-9]{11}$
test_lithuania_KO;LT12345678;^LT[0-9]{9,12}$
test_lithuania_KO2;LT*********0123;^LT[0-9]{9,12}$
test_lithuania_OK;LT*********;^LT[0-9]{9,12}$
test_lithuania_OK2;LT*********012;^LT[0-9]{9,12}$
test_luxembourg_KO;*********;^LU[0-9]{8}$
test_luxembourg_OK;*********8;^LU[0-9]{8}$
test_malta_KO;*********;^MT[0-9]{8}$
test_malta_OK;*********8;^MT[0-9]{8}$
test_mexico_KO;*********0A;^[0-9,A-Z,a-z]{12,13}$
test_mexico_KO2;*********0AZe2;^[0-9,A-Z,a-z]{12,13}$
test_mexico_OK;*********0Az;^[0-9,A-Z,a-z]{12,13}$
test_mexico_OK2;*********0AZe;^[0-9,A-Z,a-z]{12,13}$
test_morocco_KO;*********0AZERTYUIOPqsdfghjklm123456;^[0-9,A-Z,a-z]{1,35}$
test_morocco_OK;*********0AZERTYUIOPqsdfghjklm12345;^[0-9,A-Z,a-z]{1,35}$
test_netherlands_KO;NL12345678B12;^NL[0-9]{9}B[0-9]{2}$
test_netherlands_KO2;NL*********A12;^NL[0-9]{9}B[0-9]{2}$
test_netherlands_KO3;NL*********B321;^NL[0-9]{9}B[0-9]{2}$
test_netherlands_OK;NL*********B12;^NL[0-9]{9}B[0-9]{2}$
test_norway_KO;12345678MVA;^[0-9]{9}MVA$
test_norway_OK;*********MVA;^[0-9]{9}MVA$
test_poland_KO;PL*********01;^PL[0-9]{10}$
test_poland_OK;PL*********0;^PL[0-9]{10}$
test_portugal_KO;PT12345678;^PT[0-9]{9}$
test_portugal_OK;PT*********;^PT[0-9]{9}$
test_qatar_KO;1234A;^[0-9]{5}$
test_qatar_OK;12345;^[0-9]{5}$
test_russia_KO;*********01234;^(?=[0-9]*$)(?:.{13}|.{15})$
test_russia_OK;*********0123;^(?=[0-9]*$)(?:.{13}|.{15})$
test_russia_OK2;*********012345;^(?=[0-9]*$)(?:.{13}|.{15})$
test_saudi_arabia_KO;AA*********012345;^AS[0-9]{15}$
test_saudi_arabia_OK;AS*********012345;^AS[0-9]{15}$
test_senegal_KO;AZEt1234a12345;^[A-Z,a-z]{5}[0-9]{4}[A-Z,a-z]{1}[0-9]{5}$
test_senegal_KO2;AZErt12354a12345;^[A-Z,a-z]{5}[0-9]{4}[A-Z,a-z]{1}[0-9]{5}$
test_senegal_KO3;AZErt1234012345;^[A-Z,a-z]{5}[0-9]{4}[A-Z,a-z]{1}[0-9]{5}$
test_senegal_KO4;AZErt1234a123456;^[A-Z,a-z]{5}[0-9]{4}[A-Z,a-z]{1}[0-9]{5}$
test_senegal_OK;AZErt1234a12345;^[A-Z,a-z]{5}[0-9]{4}[A-Z,a-z]{1}[0-9]{5}$
test_singapore_KO;*********0A;^[0-9]{1,9}[A-Z,a-z]{1}$
test_singapore_KO2;1234567aZ;^[0-9]{1,9}[A-Z,a-z]{1}$
test_singapore_OK;*********A;^[0-9]{1,9}[A-Z,a-z]{1}$
test_singapore_Ok2;1234567a;^[0-9]{1,9}[A-Z,a-z]{1}$
test_slovak_republic_KO;SK*********A;^SK[0-9]{10}$
test_slovak_republic_OK;SK*********0;^SK[0-9]{10}$
test_slovenia_KO;SI1234567a;^SI[0-9]{8}$
test_slovenia_OK;SI12345678;^SI[0-9]{8}$
test_spain_KO;ES*********0;^ES[A-Z,0-9]{9}$
test_spain_OK;ES12345AZER;^ES[A-Z,0-9]{9}$
test_sweden_KO;SE*********11;^SE[0-9]{10}[0-1]{2}$
test_sweden_KO2;SE*********013;^SE[0-9]{10}[0-1]{2}$
test_sweden_KO3;SE*********0110;^SE[0-9]{10}[0-1]{2}$
test_sweden_OK;SE*********011;^SE[0-9]{10}[0-1]{2}$
test_switzerland_KO;CHE*********;^CHE-[0-9]{9}$
test_switzerland_OK;CHE-*********;^CHE-[0-9]{9}$
test_taiwan_KO;*********;^[0-9]{8}$
test_taiwan_OK;12345678;^[0-9]{8}$
test_thailand_KO;*********012;^[0-9]{13}$
test_thailand_OK;*********0123;^[0-9]{13}$
test_tunisia_KO;12345AZer;^[0-9,A-Z,a-z]{10,11}$
test_tunisia_KO2;12345AZer012;^[0-9,A-Z,a-z]{10,11}$
test_tunisia_OK;12345AZer0;^[0-9,A-Z,a-z]{10,11}$
test_tunisia_OK2;12345AZer01;^[0-9,A-Z,a-z]{10,11}$
test_turkey_KO;*********0a;^[0-9]{10}$
test_turkey_OK;*********0;^[0-9]{10}$
test_united_arab_emirates_KO;*********0AZErty;^[0-9,A-Z,a-z]{1,15}$
test_united_arab_emirates_OK;*********0AZErt;^[0-9,A-Z,a-z]{1,15}$
test_united_kingdom_KO;GB*********a;^GB[0-9]{9}$
test_united_kingdom_OK;GB*********;^GB[0-9]{9}$
test_united_states_KO;*********0;^[0-9]{9}$
test_united_states_OK;*********;^[0-9]{9}$
