<?php

declare(strict_types=1);

namespace Tests\AppBundle\Entity;

use AppBundle\Entity\Company;
use AppBundle\Entity\User;
use Doctrine\Common\Collections\ArrayCollection;
use PHPUnit\Framework\TestCase;

final class CompanyTest extends TestCase
{
    public function testIfCompanyHasUserApi(): void
    {
        $userApi = new User();
        $userApi->setRoles([User::ROLE_API]);
        $userApi->setFirstname('api');
        $userApi->setLastname('api');
        $userApi->setEnabled(true);

        $userBuyer = new User();
        $userBuyer->setRoles([User::ROLE_BUYER_BUYER]);
        $userBuyer->setFirstname('buyer');
        $userBuyer->setLastname('buyer');
        $company = new Company();
        $company->setUsers(new ArrayCollection([$userApi, $userBuyer]));

        $this->assertTrue($company->hasUserApi());
    }

    public function testIfCompanyHasNoUserApi(): void
    {
        $userBuyer = new User();
        $userBuyer->setRoles([User::ROLE_BUYER_BUYER]);
        $userBuyer->setFirstname('buyer');
        $userBuyer->setLastname('buyer');

        $userApiButNotEnable = new User();
        $userApiButNotEnable->setRoles([User::ROLE_API]);
        $userApiButNotEnable->setFirstname('api');
        $userApiButNotEnable->setLastname('api');
        $userApiButNotEnable->setEnabled(false);
        $company = new Company();
        $company->setUsers(new ArrayCollection([$userBuyer]));

        $this->assertFalse($company->hasUserApi());
    }
}
