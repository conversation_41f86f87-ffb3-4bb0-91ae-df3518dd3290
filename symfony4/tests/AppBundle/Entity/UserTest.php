<?php

declare(strict_types=1);

namespace Tests\AppBundle\Entity;

use AppBundle\Entity\User;
use PHPUnit\Framework\TestCase;

final class UserTest extends TestCase
{
    public function testDisplay(): void
    {
        $userBuyer = new User();
        $userBuyer->setRoles([User::ROLE_BUYER_BUYER]);
        $userBuyer->setFirstname('user');
        $userBuyer->setLastname('buyer');
        $userBuyer->setEmail('<EMAIL>');

        $this->assertSame('user buyer (<EMAIL>)', $userBuyer->display());
    }
}
