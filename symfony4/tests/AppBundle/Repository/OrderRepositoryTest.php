<?php

declare(strict_types=1);

namespace Tests\AppBundle\Repository;

use AppBundle\Repository\OrderRepository;
use Liip\TestFixturesBundle\Services\DatabaseToolCollection;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

final class OrderRepositoryTest extends KernelTestCase
{
    public function testFindLastOrder(): void
    {
        static::bootKernel();

        $databaseTool = static::$container->get(DatabaseToolCollection::class)->get();
        $databaseTool->loadAliceFixture([__DIR__ . '/OrderRepositoryFixtures.yaml']);
        $orderRepository = static::$container->get(OrderRepository::class);

        $orders = $orderRepository->fetchLastOrders(2);
        $this->assertCount(3, $orders);

        $orders = $orderRepository->fetchLastOrders(2, 1);
        $this->assertCount(1, $orders);
    }

    public function testWithEmptyResult(): void
    {
        static::bootKernel();

        $databaseTool = static::$container->get(DatabaseToolCollection::class)->get();
        $databaseTool->loadAliceFixture([__DIR__ . '/OrderRepositoryFixtures.yaml']);
        $orderRepository = static::$container->get(OrderRepository::class);

        $orders = $orderRepository->fetchLastOrders(3);
        $this->assertEmpty($orders);
    }
}
