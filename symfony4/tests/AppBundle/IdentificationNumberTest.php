<?php

namespace Tests\AppBundle;

use PHPUnit\Framework\TestCase;
use Symfony\Component\Filesystem\Exception\FileNotFoundException;

class IdentificationNumberTest extends TestCase
{
    /**
     * @dataProvider identificationRegexDataProvider
     */
    public function testRegexIdentificationNumber($identificationNumber, $regex, $expected)
    {
        $pregmatchResult = preg_match('#' . $regex . '#', $identificationNumber);
        $result = ($pregmatchResult === 1);
        $this->assertEquals($expected, $result);
    }

    public function identificationRegexDataProvider()
    {
        $data = [];

        $csvFilePath = __DIR__ . '/regex_identification_country.csv';

        if (($csvFilePointer = fopen($csvFilePath, 'r')) === false) {
            throw new FileNotFoundException(sprintf('Cannot open %s file for testing identification number', $csvFilePath));
        }

        // ignore CSV header
        fgets($csvFilePointer);
        $delimiter = ';';

        while(($row = fgetcsv($csvFilePointer, 500, $delimiter)) !== false) {
            [
                $testName,
                $identificationNumber,
                $regex,
            ] = array_pad($row, 3, null);

            $testResult = true;
            if (strpos($testName, 'KO') !== false) {
                $testResult = false;
            }

            $data[$testName] = [$identificationNumber, $regex, $testResult];
        }
        fclose($csvFilePointer);

        return $data;
    }
}
