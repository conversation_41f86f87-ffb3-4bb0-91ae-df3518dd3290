<?php

declare(strict_types=1);

namespace Tests\Api\Security;

use ApiPlatform\Core\Bridge\Symfony\Bundle\Test\ApiTestCase;
use ApiPlatform\Core\Bridge\Symfony\Bundle\Test\Client;
use Liip\TestFixturesBundle\Services\DatabaseToolCollection;
use Liip\TestFixturesBundle\Services\DatabaseTools\AbstractDatabaseTool;

final class AuthenticateTest extends ApiTestCase
{
    protected AbstractDatabaseTool $databaseTool;
    private Client $client;

    public function setUp(): void
    {
        parent::setUp();

        $this->client = static::createClient();

        $this->databaseTool = static::$container->get(DatabaseToolCollection::class)->get();
    }

    public function testLogin(): void
    {
        $this->databaseTool->loadAliceFixture([
            __DIR__ . '/AuthenticateUserLoginFixtures.yaml'
        ]);

        // retrieve a token
        $response = $this->client->request('POST', '/middleware/api/login_check', [
            'headers' => ['Content-Type' => 'application/json'],
            'json' => [
                'email' => '<EMAIL>',
                'password' => 'password',
            ],
        ]);

        $json = $response->toArray();
        $this->assertResponseIsSuccessful();
        $this->assertArrayHasKey('token', $json);
    }

    public function testWithBVadCredentials(): void
    {
        $this->databaseTool->loadAliceFixture([
            __DIR__ . '/AuthenticateUserLoginFixtures.yaml'
        ]);

        // retrieve a token
        $response = $this->client->request('POST', '/middleware/api/login_check', [
            'headers' => ['Content-Type' => 'application/json'],
            'json' => [
                'email' => '<EMAIL>',
                'password' => 'fail',
            ],
        ]);

        $this->assertEquals([
            'code' => 401,
            'message' => 'Invalid credentials.'
        ],
            json_decode($response->getContent(false), true)
        );
        $this->assertResponseStatusCodeSame(401);
    }

    public function testWithUserUnAuthorized(): void
    {
        $this->databaseTool->loadAliceFixture([
            __DIR__ . '/AuthenticateUserLoginFixtures.yaml'
        ]);

        // retrieve a token
        $response = $this->client->request('POST', '/middleware/api/login_check', [
            'headers' => ['Content-Type' => 'application/json'],
            'json' => [
                'email' => '<EMAIL>',
                'password' => 'password',
            ],
        ]);

        $this->assertEquals([
            'code' => 401,
            'message' => 'Access denied.'
        ],
            json_decode($response->getContent(false), true)
        );
        $this->assertResponseStatusCodeSame(401);
    }
}
