AppBundle\Entity\User:
  user1:
    email: user+1\@email.com
    email_canonical: user+1\@email.com
    password: \$2y\$13\$LvRb6ZGTKhG5h6DSlLEIoe5gnURQosGUoPDppN6BA7eK6ICkttK8u
    roles: [ROLE_API]
    username: '<username()>'
    firstname: '<firstname()>'
    lastname: 'lastname()'
    username_canonical: user1_canonical
    enabled: 1
    function_: 'admin'
    locale: 'FR'
    civ: 'MR'
    login_attempt: 0
  user2:
    email: user+2\@email.com
    email_canonical: user+2\@email.com
    password: \$2y\$13\$LvRb6ZGTKhG5h6DSlLEIoe5gnURQosGUoPDppN6BA7eK6ICkttK8u
    roles: [ROLE_OPERATOR]
    username: '<username()>'
    firstname: '<firstname()>'
    lastname: '<lastname()>'
    username_canonical: '<username()>'
    enabled: 1
    function_: 'admin'
    locale: 'FR'
    civ: 'MR'
    login_attempt: 0




