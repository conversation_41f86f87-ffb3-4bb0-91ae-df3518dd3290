<?php

declare(strict_types=1);

namespace Tests\Api\Traits;

use ApiPlatform\Core\Bridge\Symfony\Bundle\Test\Client;
use Symfony\Component\HttpFoundation\Request;

trait AuthenticateTrait
{
    private function getUserToken(Client $client): string
    {
        // retrieve a token
        $response = $client->request('POST', '/middleware/api/login_check', [
            'headers' => ['Content-Type' => 'application/json'],
            'json' => [
                'email' => '<EMAIL>',
                'password' => 'Password1234',
            ],
        ]);

        return $response->toArray()['token'] ?? '';
    }

    public static function createAuthenticatedClient(string $email = '<EMAIL>'): Client
    {
        $client = self::createClient([
            'base_uri' => 'http://dummy.local',
        ]);

        $response = $client->request(
            Request::METHOD_POST,
            '/middleware/api/login_check',
            [
                'json' => [
                    'email' => $email,
                    'password' => 'password',
                ],
                'headers' => [
                    'accept' => ['application/json'],
                ],
            ]
        );

        $content = $response->toArray();

        $client->setDefaultOptions([
            'headers' => ['authorization' => sprintf('Bearer %s', $content['token'])],
        ]);

        return $client;
    }
}
