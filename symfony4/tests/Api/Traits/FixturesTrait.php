<?php

declare(strict_types=1);

namespace Tests\Api\Traits;

use Liip\TestFixturesBundle\Services\DatabaseToolCollection;
use Liip\TestFixturesBundle\Services\DatabaseTools\ORMSqliteDatabaseTool;

trait FixturesTrait
{
    private ?ORMSqliteDatabaseTool $databaseTool = null;

    public function getDatabaseTool(): ORMSqliteDatabaseTool
    {
        static::createClient();
        if ($this->databaseTool === null) {
            $this->databaseTool = static::$container->get(DatabaseToolCollection::class)->get();
        }

        return $this->databaseTool;
    }
}
