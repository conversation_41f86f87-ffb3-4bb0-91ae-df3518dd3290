<?php

declare(strict_types=1);

namespace Tests\Api\Core\Utils;

use Api\Core\Utils\ErrorFormatter;
use PHPUnit\Framework\TestCase;

final class ErrorFormatterTest extends TestCase
{
    public function testFormattedArrayErrorsEmpty(): void
    {
        $this->assertEmpty(ErrorFormatter::formatErrors([]));
    }

    public function testFormattedArrayErrors(): void
    {
        $errors = ErrorFormatter::formatErrors([
            12 => ['Error message 1', 'Error message 2'],
            13 => ['Error message 13', 'Error message 14'],
        ]);

        $this->assertNotEmpty($errors);
        $this->assertEquals([
            '12: Error message 1, Error message 2',
            '13: Error message 13, Error message 14',
        ], $errors);
    }
}
