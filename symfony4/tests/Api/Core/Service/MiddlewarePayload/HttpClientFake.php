<?php

declare(strict_types=1);

namespace Tests\Api\Core\Service\MiddlewarePayload;

use Symfony\Component\HttpClient\Exception\TransportException;
use Symfony\Component\HttpClient\MockHttpClient;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;
use Symfony\Contracts\HttpClient\ResponseStreamInterface;

/**
 * @method  withOptions(array $options)
 */
final class HttpClientFake implements HttpClientInterface
{
    private array $responses = [];

    public function __construct(array $responses = [])
    {
        $this->responses = $responses;
    }

    /**
     * @inheritDoc
     */
    public function request(string $method, string $url, array $options = []): ResponseInterface
    {
        $response = $this->responses[$url] ?? null;

        if ($response === null) {
            throw new TransportException(sprintf('There is no response  for url: %s', $url));
        }

        return (new MockHttpClient($response, 'https://middleware.api.fake'))
            ->request($method, $url, $options);
    }

    /**
     * @inheritDoc
     */
    public function stream($responses, float $timeout = null): ResponseStreamInterface
    {
        throw new \LogicException('No implement this method ' . __METHOD__);
    }
}
