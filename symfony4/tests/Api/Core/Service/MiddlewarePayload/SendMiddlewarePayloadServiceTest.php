<?php

declare(strict_types=1);

namespace Tests\Api\Core\Service\MiddlewarePayload;

use Api\Core\Service\MiddlewarePayload\SendMiddlewarePayloadService;
use Generator;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpClient\Response\MockResponse;

final class SendMiddlewarePayloadServiceTest extends TestCase
{
    /**
     * @dataProvider provideStatusCode
     */
    public function testSendMiddlewarePayload(int $statusCode, string $expected): void
    {
        $service = new SendMiddlewarePayloadService(new HttpClientFake([
            '/middleware/' => new MockResponse('', ['http_code' => $statusCode])]));

        $payload = DummyPayload::createFromData([], 'create', 1);
        $service->send($payload,  '/middleware/');

        $this->assertSame($expected, $payload->getStatus());
    }

    public function testFailSendMiddlewarePayload(): void
    {
        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->once())
            ->method('error')
            ->with($this->callback(fn (string $message) => $message === 'fail message'))
        ;
        $service = new SendMiddlewarePayloadService(new HttpClientFake([
            '/middleware/' => new MockResponse('')]));

        $service->setLogger($logger);
        $payload = DummyPayload::createFromData([], 'create', 1);
        $service->errorMessage('fail message')->send($payload,  '/fail/');

        $this->assertSame('STATUS_FAILED', $payload->getStatus());
    }

    public function provideStatusCode(): Generator
    {
        yield [200, 'STATUS_SUCCESS'];
        yield [400, 'STATUS_FAILED'];
    }
}
