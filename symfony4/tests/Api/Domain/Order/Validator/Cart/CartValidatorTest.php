<?php

declare(strict_types=1);

namespace Tests\Api\Domain\Order\Validator\Cart;

use Api\Domain\Order\Validator\Cart\CartValidator;
use PHPUnit\Framework\TestCase;

final class CartValidatorTest extends TestCase
{
    private CartValidator $cartValidator;

    protected function setUp(): void
    {
        $this->cartValidator = new CartValidator();
    }

    public function testMoqValidationSuccessFul(): void
    {
        // Successful
        $this->cartValidator->validateMoq(0, 2);

        $this->assertCount(0, $this->cartValidator);
    }

    public function testMoqValidationError(): void
    {
        $this->cartValidator->validateMoq(4, 2);

        $this->assertFail('moq : The quantity shall be higher or equal to MOQ 4');
    }

    public function testQuantityValidationSuccessFul(): void
    {
        // Successful
        $this->cartValidator->validateQuantity(1, 10);

        $this->assertCount(0, $this->cartValidator);
    }

    public function testQuantityValidationError(): void
    {
        $this->cartValidator->validateQuantity(4, 2);

        $this->assertFail('quantity : The quantity not must be greater to 2');
    }

    public function testOfferLimitValidationSuccessFul(): void
    {
        // Successful
        $this->cartValidator->validateOfferLimit(false);

        $this->assertCount(0, $this->cartValidator);
    }

    public function testOfferLimitValidationError(): void
    {
        $this->cartValidator->validateOfferLimit(true);

        $this->assertFail('offerLimit : Cannot add offer to cart - offer is limited');
    }

    public function testHasNoPriceValidationSuccessFul(): void
    {
        // Successful
        $this->cartValidator->validateOfferHasNoPrice(false);

        $this->assertCount(0, $this->cartValidator);
    }

    public function testHasNoPriceValidationError(): void
    {
        $this->cartValidator->validateOfferHasNoPrice(true);

        $this->assertFail('hasNoPrice : Cannot add offer to cart - offer has no price');
    }

    public function testBatchSizeValidationSuccessFul(): void
    {
        // Successful
        $this->cartValidator->validateBatchSize(1, 1);

        $this->assertCount(0, $this->cartValidator);
    }

    public function testBatchSizeValidationError(): void
    {
        $this->cartValidator->validateBatchSize(2, 3);

        $this->assertFail('batchSize : Quantity does not match with batchsize');
    }

    public function testErrorsWithProductId(): void
    {
        $this->cartValidator
            ->withProduct(10)
            ->validateBatchSize(2, 3)
            ->validateQuantity(4, 2)
        ;

        $this->cartValidator
            ->withProduct(11)
            ->validateBatchSize(2, 3)
            ->validateQuantity(4, 2)
        ;

        $this->assertEquals([
            10 => [
                'batchSize : Quantity does not match with batchsize',
                'quantity : The quantity not must be greater to 2',
            ],
            11 => [
                'batchSize : Quantity does not match with batchsize',
                'quantity : The quantity not must be greater to 2',
            ]
        ], $this->cartValidator->getIterator());
    }

    private function assertFail(string $message, int $expectedCount = 1): void
    {
        $this->assertCount($expectedCount, $this->cartValidator);
        $this->assertSame($message, $this->cartValidator->current());
    }
}
