<?php

declare(strict_types=1);

namespace Tests\Api\Domain\Order\Service;

use Api\Domain\Order\Service\DataOrder\GetOrderDataService;
use AppBundle\Services\OrderService;
use Open\IzbergBundle\Model\Order;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class GetOrderDataServiceTest extends TestCase
{
    private readonly MockObject|OrderService $orderService;

    private readonly GetOrderDataService $service;

    protected function setUp(): void
    {
        parent::setUp();

        $this->orderService = $this->createMock(OrderService::class);
        $this->service = new GetOrderDataService(orderService: $this->orderService);
    }

    public function testGetReconciliationKeyWithoutDefinedTheOrderObject(): void
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('The izberg order must be defined by GetOrderDataService::izbergOrder method.');

        $this->service->reconciliationKey();
    }

    public function testGetReconciliationKeyData(): void
    {
        $this->orderService->expects($this->once())->method('fetchOrderById')->willReturn($this->makeIzbergOrder());
        $this->orderService->expects($this->once())->method('getReconciliationKeyFromOrder')->willReturn('foo');

        $reconciliationKey = $this->service->order(externalOrderId: 1)->reconciliationKey();

        $this->assertSame('foo', $reconciliationKey);
    }

    private function makeIzbergOrder(): Order
    {
        $order = new Order();
        $order->setId(1);
        $order->setAmount(10);


        return $order;
    }
}
