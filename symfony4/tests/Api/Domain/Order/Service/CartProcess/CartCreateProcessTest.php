<?php

declare(strict_types=1);

namespace Tests\Api\Domain\Order\Service\CartProcess;

use Api\Core\Exception\InvalidArgumentException;
use Api\Domain\Order\Dto\ProductData;
use Api\Domain\Order\Exception\CannotCreateCartException;
use Api\Domain\Order\Exception\CartMinimumAccountException;
use Api\Domain\Order\Service\CartProcess\CartCreateProcess;
use Api\Domain\Order\Validator\Cart\CartValidator;
use AppBundle\Entity\Address;
use AppBundle\Entity\Cart;
use AppBundle\Entity\Category;
use AppBundle\Entity\Company;
use AppBundle\Entity\Country;
use AppBundle\Entity\ShippingPoint;
use AppBundle\Entity\Site;
use AppBundle\Entity\User;
use AppBundle\Model\Cart\Cart as CartModel;
use AppBundle\Model\Cart\Cart as ModelCart;
use AppBundle\Model\Cart\CartItem;
use AppBundle\Model\Cart\CartMerchant;
use AppBundle\Model\CartShippingOption;
use AppBundle\Model\DetailedOffer;
use AppBundle\Model\Merchant;
use AppBundle\Model\Offer;
use AppBundle\Repository\CartRepository;
use AppBundle\Services\AddressService;
use AppBundle\Services\CartService;
use AppBundle\Services\CompanyCatalogService;
use AppBundle\Services\OfferService;
use AppBundle\Services\ShippingPointService;
use AppBundle\Services\ShippingService;
use AppBundle\ValueObject\PaymentMethod;
use Generator;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Tests\Api\Domain\Order\DummyFactory\DummyProductDataFactory;

final class CartCreateProcessTest extends TestCase
{
    private CartCreateProcess $cartProcess;

    /**
     * @var CartService|MockObject
     */
    private CartService $cartService;

    /**
     * @var OfferService|MockObject
     */
    private OfferService $offerService;

    /**
     * @var MockObject|RequestStack
     */
    private RequestStack $requestStack;

    /**
     * @var CompanyCatalogService|MockObject
     */
    private CompanyCatalogService $companyCatalogService;

    /**
     * @var CartRepository|MockObject
     */
    private CartRepository $cartRepository;

    /**
     * @var ShippingService|MockObject
     */
    private ShippingService $shippingService;

    /**
     * @var AddressService|MockObject
     */
    private AddressService $addressService;

    private ShippingPointService $shippingPointService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->cartService = $this->createMock(CartService::class);
        $this->offerService = $this->createMock(OfferService::class);
        $this->requestStack = $this->createMock(RequestStack::class);
        $this->companyCatalogService = $this->createMock(CompanyCatalogService::class);
        $this->cartRepository = $this->createMock(CartRepository::class);
        $this->shippingService = $this->createMock(ShippingService::class);
        $this->addressService = $this->createMock(AddressService::class);
        $this->shippingPointService = $this->createMock(ShippingPointService::class);

        $request = new Request();
        $request->setLocale('FR');
        $this->requestStack->expects($this->any())->method('getCurrentRequest')->willReturn($request);

        $this->cartProcess = new CartCreateProcess(
            $this->offerService,
            $this->cartService,
            $this->requestStack,
            $this->companyCatalogService,
            $this->cartRepository,
            $this->shippingService,
            $this->addressService,
            new CartValidator(),
            $this->shippingPointService
        );
    }

    public function testCreateCart(): void
    {
        $country = new Country();
        $country->setId(1);
        $country->setCode("FR");
        $country->setBusinessEverywhere(true);
        $country->setIzbFcaCountry('france');
        $address = new Address();
        $address->setId(1);
        $address->setAddress('address 1');
        $address->setCountry($country);
        $modelCart = new ModelCart();
        $modelCart->setId(1);
        $cartItem = new CartItem();
        $cartItem->setName('product1');
        $modelCart->setItems([$cartItem]);
        $category = new Category();
        $category->setLabel('category_new');
        $company = new Company();
        $company->setId(1);
        $company->setMainAddress($address);
        $company->setCategory($category);
        $defaultUser = new User();
        $defaultUser->setId(1);
        $defaultUser->setCompany($company);
        $merchant = new Merchant();
        $merchant->setId(1);
        $merchant->setCountry($country);
        $this->cartService->expects($this->once())->method('addOfferWithCart')->willReturn(1);
        $this->cartService->expects($this->once())->method('updateQuantity')->willReturn(1);
        $this->cartService->expects($this->once())->method('createUserCart')->willReturn($modelCart);
        $this->cartRepository->expects($this->once())->method('find')->willReturn(new Cart());
        $this->offerService
            ->expects($this->exactly(2))
            ->method('findDetailedOfferById')
            ->willReturn(
                (new DetailedOffer())
                    ->setOffer((new Offer())
                        ->setQuantity(10)
                        ->setMoq(1)
                        ->setCurrency('EUR')
                        ->setStatus('active')
                        ->setSellerRef('vendorRef')
                        ->setIncoterm('DAP')
                        ->setIncotermCountry('France')
                        ->setUser($defaultUser)
                        ->setMerchant($merchant)
                    )
            )
        ;
        $this->cartRepository
            ->expects($this->once())
            ->method('save')
            ->with($this->callback(fn (Cart $cart): bool => $cart->getCreationSource() === 'api'))
        ;

        $this->mockShippingAddress(false);

        $this->cartService
            ->expects($this->never())
            ->method('assignCart');

        $user = new User();
        $user->setId(1);
        $user->setCompany($company);

        $this->cartProcess
            ->withUser($user)
            ->createCart([DummyProductDataFactory::createProductData()], 1, '23')
        ;
    }

    public function testCreateCartWithDAPIsNotValid(): void
    {
        $this->expectException(CannotCreateCartException::class);
        $this->expectExceptionMessage('The cart could not be created successful');

        $country = new Country();
        $country->setId(1);
        $country->setCode("FR");
        $country->setBusinessEverywhere(true);
        $country->setIzbFcaCountry('france');
        $address = new Address();
        $address->setId(1);
        $address->setAddress('address 1');
        $address->setCountry($country);
        $modelCart = new ModelCart();
        $modelCart->setId(1);
        $cartItem = new CartItem();
        $cartItem->setName('product1');
        $modelCart->setItems([]);
        $category = new Category();
        $category->setLabel('category_new');
        $company = new Company();
        $company->setId(1);
        $company->setMainAddress($address);
        $company->setCategory($category);
        $defaultUser = new User();
        $defaultUser->setId(1);
        $defaultUser->setCompany($company);
        $merchant = new Merchant();
        $merchant->setId(1);
        $merchant->setCountry($country);
        $this->cartService->expects($this->never())->method('addOfferWithCart');
        $this->cartService->expects($this->never())->method('updateQuantity');
        $this->cartService->expects($this->once())->method('createUserCart')->willReturn($modelCart);
        $this->cartRepository->expects($this->never())->method('find');
        $this->offerService
            ->expects($this->exactly(2))
            ->method('findDetailedOfferById')
            ->willReturn(
                (new DetailedOffer())
                    ->setOffer((new Offer())
                        ->setQuantity(10)
                        ->setMoq(1)
                        ->setCurrency('EUR')
                        ->setStatus('active')
                        ->setSellerRef('vendorRef')
                        ->setIncoterm('DAP')
                        ->setIncotermCountry('Germany')
                        ->setUser($defaultUser)
                        ->setMerchant($merchant)
                    )
            )
        ;

        $this->mockShippingAddress(false);

        $this->cartService
            ->expects($this->never())
            ->method('assignCart');

        $user = new User();
        $user->setId(1);
        $user->setCompany($company);
        $this->cartProcess
            ->withUser($user)
            ->createCart([new ProductData(2, 4)], 1, '23')
        ;
    }

    public function testIfEmptyDeleteCart(): void
    {
        $country = new Country();
        $country->setId(1);
        $country->setCode("FR");
        $country->setBusinessEverywhere(true);
        $country->setIzbFcaCountry('france');
        $address = new Address();
        $address->setId(1);
        $address->setAddress('address 1');
        $address->setCountry($country);
        $modelCart = new ModelCart();
        $modelCart->setId(1);
        $modelCart->setItems([]);
        $category = new Category();
        $category->setLabel('category_new');
        $company = new Company();
        $company->setId(1);
        $company->setMainAddress($address);
        $company->setCategory($category);
        $defaultUser = new User();
        $defaultUser->setId(1);
        $defaultUser->setCompany($company);
        $merchant = new Merchant();
        $merchant->setId(1);
        $merchant->setCountry($country);
        $this->cartService->expects($this->any())->method('addOfferWithCart')->willReturn(1);
        $this->cartService->expects($this->once())->method('updateQuantity')->willReturn(1);
        $this->cartService->expects($this->once())->method('createUserCart')->willReturn($modelCart);
        $this->cartService->expects($this->once())->method('clearUserCurrentCarts');
        $this->cartRepository->expects($this->once())->method('find')->willReturn(new Cart());
        $this->offerService
            ->expects($this->exactly(2))
            ->method('findDetailedOfferById')
            ->willReturn(
                (new DetailedOffer())
                    ->setOffer((new Offer())
                        ->setQuantity(10)
                        ->setMoq(1)
                        ->setCurrency('EUR')
                        ->setStatus('active')
                        ->setSellerRef('vendorRef')
                        ->setIncoterm('DAP')
                        ->setIncotermCountry('France')
                        ->setUser($defaultUser)
                        ->setMerchant($merchant)
                    )
            )
        ;
        $this->cartRepository
            ->expects($this->once())
            ->method('save')
            ->with($this->callback(fn (Cart $cart) => $cart->getCreationSource() === 'api'))
        ;

        $this->mockShippingAddress(false);

        $this->cartService
            ->expects($this->never())
            ->method('assignCart');

        $this->expectException(CannotCreateCartException::class);
        $this->expectExceptionMessage('The cart could not be created successful');

        $user = new User();
        $user->setId(1);
        $user->setCompany($company);
        $this->cartProcess
            ->withUser($user)
            ->createCart([new ProductData(2, 0)], 1, '23')
        ;
    }

    public function testCreateCartWithExceptionOfAddOfferToCart(): void
    {
        $country = new Country();
        $country->setId(1);
        $country->setCode("FR");
        $country->setBusinessEverywhere(true);
        $country->setIzbFcaCountry('france');
        $address = new Address();
        $address->setId(1);
        $address->setAddress('address 1');
        $address->setCountry($country);
        $modelCart = new ModelCart();
        $modelCart->setId(1);
        $cartItem = new CartItem();
        $cartItem->setName('product1');
        $modelCart->setItems([$cartItem]);
        $category = new Category();
        $category->setLabel('category_new');
        $company = new Company();
        $company->setId(1);
        $company->setMainAddress($address);
        $company->setCategory($category);
        $defaultUser = new User();
        $defaultUser->setId(1);
        $defaultUser->setCompany($company);
        $merchant = new Merchant();
        $merchant->setId(1);
        $merchant->setCountry($country);
        $this->cartService->expects($this->once())->method('addOfferWithCart')->willReturn(1);
        $this->cartService->expects($this->once())->method('updateQuantity')->willReturn(1);
        $this->cartService->expects($this->once())->method('createUserCart')->willReturn($modelCart);
        $this->cartRepository->expects($this->once())->method('find')->willReturn(new Cart());
        $this->offerService
            ->expects($this->exactly(2))
            ->method('findDetailedOfferById')
            ->willReturn(
                (new DetailedOffer())
                    ->setOffer((new Offer())
                        ->setQuantity(10)
                        ->setMoq(1)
                        ->setCurrency('EUR')
                        ->setStatus('active')
                        ->setSellerRef('vendorRef')
                        ->setIncoterm('DAP')
                        ->setIncotermCountry('France')
                        ->setUser($defaultUser)
                        ->setMerchant($merchant)
                    )
            )
        ;

        $this->cartRepository
            ->expects($this->once())
            ->method('save')
            ->willReturn(true)
        ;

        $this->mockShippingAddress();

        $this->cartService
            ->expects($this->once())
            ->method('assignCart');

        $this->expectException(CannotCreateCartException::class);

        $this->cartProcess
            ->withUser($defaultUser)
            ->createCart([new ProductData(2, 0)], 1, '23')
        ;
    }

    public function testCreateCartWithProductsArrayEmpty(): void
    {
        $country = new Country();
        $country->setId(1);
        $country->setCode("FR");
        $country->setBusinessEverywhere(true);
        $country->setIzbFcaCountry('france');
        $address = new Address();
        $address->setId(1);
        $address->setAddress('address 1');
        $address->setCountry($country);
        $modelCart = new ModelCart();
        $modelCart->setId(1);
        $cartItem = new CartItem();
        $cartItem->setName('product1');
        $modelCart->setItems([$cartItem]);
        $category = new Category();
        $category->setLabel('category_new');
        $company = new Company();
        $company->setId(1);
        $company->setMainAddress($address);
        $company->setCategory($category);
        $defaultUser = new User();
        $defaultUser->setId(1);
        $defaultUser->setCompany($company);

        $this->cartService->expects($this->never())->method('addOfferToCart')->willReturn(1);
        $this->cartProcess
            ->withUser($defaultUser)
            ->createCart([], 1, null)
        ;
    }

    /**
     * @dataProvider provideOfferQuantity
     */
    public function testShouldHaveTheQuantityErrorWhenIWantCreateTheCartWithBadQuantity(int $quantity): void
    {
        $country = new Country();
        $country->setId(1);
        $country->setCode("FR");
        $country->setBusinessEverywhere(true);
        $country->setIzbFcaCountry('france');
        $address = new Address();
        $address->setId(1);
        $address->setAddress('address 1');
        $address->setCountry($country);
        $modelCart = new ModelCart();
        $modelCart->setId(1);
        $cartItem = new CartItem();
        $cartItem->setName('product1');
        $modelCart->setItems([$cartItem]);
        $category = new Category();
        $category->setLabel('category_new');
        $company = new Company();
        $company->setId(1);
        $company->setMainAddress($address);
        $company->setCategory($category);
        $defaultUser = new User();
        $defaultUser->setId(1);
        $defaultUser->setCompany($company);
        $merchant = new Merchant();
        $merchant->setId(1);
        $merchant->setCountry($country);
        $this->cartService->expects($this->once())->method('addOfferWithCart')->willReturn(1);
        $this->cartService->expects($this->once())->method('updateQuantity')->willReturn(1);
        $this->cartService->expects($this->once())->method('createUserCart')->willReturn($modelCart);
        $this->cartRepository->expects($this->once())->method('find')->willReturn(new Cart());
        $this->offerService
            ->expects($this->exactly(2))
            ->method('findDetailedOfferById')
            ->willReturn(
                (new DetailedOffer())
                    ->setOffer((new Offer())
                        ->setQuantity(10)
                        ->setMoq(1)
                        ->setCurrency('EUR')
                        ->setStatus('active')
                        ->setSellerRef('vendorRef')
                        ->setIncoterm('DAP')
                        ->setIncotermCountry('France')
                        ->setUser($defaultUser)
                        ->setMerchant($merchant)
                    )
            )
        ;

        $this->cartRepository
            ->expects($this->once())
            ->method('save')
            ->willReturn(true)
        ;

       $this->mockShippingAddress();

        $this->cartService
            ->expects($this->once())
            ->method('assignCart');

        $this->expectException(CannotCreateCartException::class);
        $this->expectExceptionMessage('An error has occurred, the cart has been assigned to your default user');

        $this->cartProcess
            ->withUser($defaultUser)
            ->createCart([new ProductData(2, $quantity)], 1, '23')
        ;
    }

    public function testSelectedCheapestShipping(): void
    {
        $country = new Country();
        $country->setId(1);
        $country->setCode("FR");
        $country->setBusinessEverywhere(true);
        $country->setIzbFcaCountry('france');
        $address = new Address();
        $address->setId(1);
        $address->setAddress('address 1');
        $address->setCountry($country);
        $modelCart = new ModelCart();
        $modelCart->setId(1);
        $cartItem = new CartItem();
        $cartItem->setName('product1');
        $modelCart->setItems([$cartItem]);
        $category = new Category();
        $category->setLabel('category_new');
        $company = new Company();
        $company->setId(1);
        $company->setMainAddress($address);
        $company->setCategory($category);
        $defaultUser = new User();
        $defaultUser->setId(1);
        $defaultUser->setCompany($company);
        $merchant = new Merchant();
        $merchant->setId(1);
        $merchant->setCountry($country);
        $this->cartService->expects($this->once())->method('addOfferWithCart')->willReturn(1);
        $this->cartService->expects($this->once())->method('updateQuantity')->willReturn(1);
        $this->cartService->expects($this->once())->method('createUserCart')->willReturn($modelCart);
        $this->cartRepository->expects($this->once())->method('find')->willReturn(new Cart());
        $this->offerService
            ->expects($this->exactly(2))
            ->method('findDetailedOfferById')
            ->willReturn(
                (new DetailedOffer())
                    ->setOffer((new Offer())
                        ->setQuantity(10)
                        ->setMoq(1)
                        ->setCurrency('EUR')
                        ->setStatus('active')
                        ->setSellerRef('vendorRef')
                        ->setIncoterm('DAP')
                        ->setIncotermCountry('France')
                        ->setUser($defaultUser)
                        ->setMerchant($merchant)
                    )
            )
        ;
        $cart = new CartModel();
        $cartMerchant = new CartMerchant();
        $cartMerchant->setId(1);
        $cart->setMerchants([$cartMerchant]);

        $this->cartService->expects($this->once())->method('addOfferWithCart')->willReturn(1);
        $this->cartService->expects($this->once())->method('findCart')->willReturn($cart);
        $this->cartRepository->expects($this->once())->method('find')->willReturn(new Cart());

        $this->shippingService->expects($this->once())
            ->method('buildCartMerchantShippingOptions')
            ->willReturn(new CartShippingOption())
        ;

        $this->shippingService->expects($this->once())->method('saveSelectedMerchantShippingOptions');

        $this->cartProcess->withUser($defaultUser)
            ->createCart([new ProductData(2, 4)], 1, '23')
            ->selectedCheapestShipping();
    }

    public function testSelectedCheapestShippingWithCartFail(): void
    {
        $country = new Country();
        $country->setId(1);
        $country->setCode("FR");
        $country->setBusinessEverywhere(true);
        $country->setIzbFcaCountry('france');
        $address = new Address();
        $address->setId(1);
        $address->setAddress('address 1');
        $address->setCountry($country);
        $modelCart = new ModelCart();
        $modelCart->setId(1);
        $cartItem = new CartItem();
        $cartItem->setName('product1');
        $modelCart->setItems([$cartItem]);
        $category = new Category();
        $category->setLabel('category_new');
        $company = new Company();
        $company->setId(1);
        $company->setMainAddress($address);
        $company->setCategory($category);
        $defaultUser = new User();
        $defaultUser->setId(1);
        $defaultUser->setCompany($company);
        $merchant = new Merchant();
        $merchant->setId(1);
        $merchant->setCountry($country);
        $this->cartService->expects($this->once())->method('addOfferWithCart')->willReturn(1);
        $this->cartService->expects($this->once())->method('updateQuantity')->willReturn(1);
        $this->cartService->expects($this->once())->method('createUserCart')->willReturn($modelCart);
        $this->cartRepository->expects($this->once())->method('find')->willReturn(new Cart());
        $this->offerService
            ->expects($this->exactly(2))
            ->method('findDetailedOfferById')
            ->willReturn(
                (new DetailedOffer())
                    ->setOffer((new Offer())
                        ->setQuantity(10)
                        ->setMoq(1)
                        ->setCurrency('EUR')
                        ->setStatus('active')
                        ->setSellerRef('vendorRef')
                        ->setIncoterm('DAP')
                        ->setIncotermCountry('France')
                        ->setUser($defaultUser)
                        ->setMerchant($merchant)
                    )
            )
        ;
        $this->cartService->expects($this->once())->method('findCart')->willReturn(null);

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Cart does not exist');

        $this->cartProcess->withUser($defaultUser)
            ->createCart([new ProductData(2, 4)], 1, '23')
            ->selectedCheapestShipping();
    }

    public function testCheckoutCartSuccessful(): void
    {
        $country = new Country();
        $country->setId(1);
        $country->setCode("FR");
        $country->setBusinessEverywhere(true);
        $country->setIzbFcaCountry('france');
        $address = new Address();
        $address->setId(1);
        $address->setAddress('address 1');
        $address->setCountry($country);
        $modelCart = new ModelCart();
        $modelCart->setId(1);
        $cartItem = new CartItem();
        $cartItem->setName('product1');
        $modelCart->setItems([$cartItem]);
        $category = new Category();
        $category->setLabel('category_new');
        $company = new Company();
        $company->setId(1);
        $company->setMainAddress($address);
        $company->setCategory($category);
        $defaultUser = new User();
        $defaultUser->setId(1);
        $defaultUser->setCompany($company);
        $merchant = new Merchant();
        $merchant->setId(1);
        $merchant->setCountry($country);
        $this->cartService->expects($this->once())->method('addOfferWithCart')->willReturn(1);
        $this->cartService->expects($this->once())->method('updateQuantity')->willReturn(1);
        $this->cartService->expects($this->once())->method('createUserCart')->willReturn($modelCart);
        $this->cartRepository->expects($this->once())->method('find')->willReturn(new Cart());
        $this->offerService
            ->expects($this->exactly(2))
            ->method('findDetailedOfferById')
            ->willReturn(
                (new DetailedOffer())
                    ->setOffer((new Offer())
                        ->setQuantity(10)
                        ->setMoq(1)
                        ->setCurrency('EUR')
                        ->setStatus('active')
                        ->setSellerRef('vendorRef')
                        ->setIncoterm('DAP')
                        ->setIncotermCountry('France')
                        ->setUser($defaultUser)
                        ->setMerchant($merchant)
                    )
            )
        ;
        $cartModel = new CartModel();
        $cartModel->setTotal(21);
        $this->cartService->expects($this->once())->method('findCart')->willReturn($cartModel);
        $this->cartService->expects($this->once())->method('checkoutCart');

        $actual = $this->cartProcess->withUser($defaultUser)
            ->createCart([new ProductData(2, 4)], 1, '23')
            ->checkoutCart(
                1,
                PaymentMethod::createToLabel('termTransferWire'),
                20.99,
                null
            );

        $this->assertInstanceOf(CartModel::class, $actual);
    }

    public function testCheckoutCartFail(): void
    {
        $country = new Country();
        $country->setId(1);
        $country->setCode("FR");
        $country->setBusinessEverywhere(true);
        $country->setIzbFcaCountry('france');
        $address = new Address();
        $address->setId(1);
        $address->setAddress('address 1');
        $address->setCountry($country);
        $modelCart = new ModelCart();
        $modelCart->setId(1);
        $cartItem = new CartItem();
        $cartItem->setName('product1');
        $modelCart->setItems([$cartItem]);
        $category = new Category();
        $category->setLabel('category_new');
        $company = new Company();
        $company->setId(1);
        $company->setMainAddress($address);
        $company->setCategory($category);
        $defaultUser = new User();
        $defaultUser->setId(1);
        $defaultUser->setCompany($company);
        $merchant = new Merchant();
        $merchant->setId(1);
        $merchant->setCountry($country);
        $this->cartService->expects($this->once())->method('addOfferWithCart')->willReturn(1);
        $this->cartService->expects($this->once())->method('updateQuantity')->willReturn(1);
        $this->cartService->expects($this->once())->method('createUserCart')->willReturn($modelCart);
        $this->cartRepository->expects($this->once())->method('find')->willReturn(new Cart());
        $this->offerService
            ->expects($this->exactly(2))
            ->method('findDetailedOfferById')
            ->willReturn(
                (new DetailedOffer())
                    ->setOffer((new Offer())
                        ->setQuantity(10)
                        ->setMoq(1)
                        ->setCurrency('EUR')
                        ->setStatus('active')
                        ->setSellerRef('vendorRef')
                        ->setIncoterm('DAP')
                        ->setIncotermCountry('France')
                        ->setUser($defaultUser)
                        ->setMerchant($merchant)
                    )
            )
        ;
        $cartModel = new CartModel();
        $cartModel->setTotal(20);
        $this->cartService->expects($this->once())->method('findCart')->willReturn($cartModel);

        $this->cartService
            ->expects($this->once())
            ->method('checkoutCart')
            ->willThrowException(new \InvalidArgumentException());

        $this->cartService
            ->expects($this->once())
            ->method('assignCart');

        $this->mockShippingAddress();

        $this->expectException(InvalidArgumentException::class);

        $actual = $this->cartProcess->withUser($defaultUser)
            ->createCart([new ProductData(2, 4)], 1, '23')
            ->checkoutCart(
                1,
                PaymentMethod::createToLabel('termTransferWire'),
                20,
                null
            );

        $this->assertInstanceOf(CartModel::class, $actual);
    }

    public function testCheckoutCartTotalPriceSuccess(): void
    {
        $country = new Country();
        $country->setId(1);
        $country->setCode("FR");
        $country->setBusinessEverywhere(true);
        $country->setIzbFcaCountry('france');
        $address = new Address();
        $address->setId(1);
        $address->setAddress('address 1');
        $address->setCountry($country);
        $modelCart = new ModelCart();
        $modelCart->setId(1);
        $cartItem = new CartItem();
        $cartItem->setName('product1');
        $modelCart->setItems([$cartItem]);
        $category = new Category();
        $category->setLabel('category_new');
        $company = new Company();
        $company->setId(1);
        $company->setMainAddress($address);
        $company->setCategory($category);
        $defaultUser = new User();
        $defaultUser->setId(1);
        $defaultUser->setCompany($company);
        $merchant = new Merchant();
        $merchant->setId(1);
        $merchant->setCountry($country);
        $this->cartService->expects($this->once())->method('addOfferWithCart')->willReturn(1);
        $this->cartService->expects($this->once())->method('updateQuantity')->willReturn(1);
        $this->cartService->expects($this->once())->method('createUserCart')->willReturn($modelCart);
        $this->cartRepository->expects($this->once())->method('find')->willReturn(new Cart());
        $this->offerService
            ->expects($this->exactly(2))
            ->method('findDetailedOfferById')
            ->willReturn(
                (new DetailedOffer())
                    ->setOffer((new Offer())
                        ->setQuantity(10)
                        ->setMoq(1)
                        ->setCurrency('EUR')
                        ->setStatus('active')
                        ->setSellerRef('vendorRef')
                        ->setIncoterm('DAP')
                        ->setIncotermCountry('France')
                        ->setUser($defaultUser)
                        ->setMerchant($merchant)
                    )
            )
        ;
        $cartModel = new CartModel();
        $cartModel->setTotal(21);

        $this->cartService->expects($this->once())->method('findCart')->willReturn($cartModel);

        $this->cartService
            ->expects($this->once())
            ->method('checkoutCart');

        $shippingPoint = new ShippingPoint();
        $site = new Site();
        $site->setId(1);
        $shippingPoint->setSite($site->setDefaultUser($defaultUser));

        $actual = $this->cartProcess->withUser($defaultUser)
            ->createCart([new ProductData(2, 4)], 1, '23')
            ->checkoutCart(
                shippingAddressId: 1,
                paymentMethod: PaymentMethod::createToLabel('termTransferWire'),
                totalPrice: 20.99,
                buyerOrderNumber: null
            );

        $this->assertInstanceOf(CartModel::class, $actual);
    }

    public function testCheckoutCartTotalPriceFail(): void
    {
        $country = new Country();
        $country->setId(1);
        $country->setCode("FR");
        $country->setBusinessEverywhere(true);
        $country->setIzbFcaCountry('france');
        $address = new Address();
        $address->setId(1);
        $address->setAddress('address 1');
        $address->setCountry($country);
        $modelCart = new ModelCart();
        $modelCart->setId(1);
        $cartItem = new CartItem();
        $cartItem->setName('product1');
        $modelCart->setItems([$cartItem]);
        $category = new Category();
        $category->setLabel('category_new');
        $company = new Company();
        $company->setId(1);
        $company->setMainAddress($address);
        $company->setCategory($category);
        $defaultUser = new User();
        $defaultUser->setId(1);
        $defaultUser->setCompany($company);
        $merchant = new Merchant();
        $merchant->setId(1);
        $merchant->setCountry($country);
        $this->cartService->expects($this->once())->method('addOfferWithCart')->willReturn(1);
        $this->cartService->expects($this->once())->method('updateQuantity')->willReturn(1);
        $this->cartService->expects($this->once())->method('createUserCart')->willReturn($modelCart);
        $this->cartRepository->expects($this->once())->method('find')->willReturn(new Cart());
        $this->offerService
            ->expects($this->exactly(2))
            ->method('findDetailedOfferById')
            ->willReturn(
                (new DetailedOffer())
                    ->setOffer((new Offer())
                        ->setQuantity(10)
                        ->setMoq(1)
                        ->setCurrency('EUR')
                        ->setStatus('active')
                        ->setSellerRef('vendorRef')
                        ->setIncoterm('DAP')
                        ->setIncotermCountry('France')
                        ->setUser($defaultUser)
                        ->setMerchant($merchant)
                    )
            )
        ;
        $cartModel = new CartModel();
        $cartModel->setTotal(30);

        $this->cartService->expects($this->once())->method('findCart')->willReturn($cartModel);

        $this->cartService
            ->expects($this->never())
            ->method('checkoutCart');

        $this->cartService
            ->expects($this->never())
            ->method('clearUserCurrentCarts');

        $this->mockShippingAddress();

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage(
            'Specific total price 20.00 is not correspond to the total price 30.00 of the cart'
        );

        $this->cartProcess->withUser($defaultUser)
            ->createCart([new ProductData(2, 4)], 1, '23')
            ->checkoutCart(
                1,
                PaymentMethod::createToLabel('termTransferWire'),
                20,
                null
            );
    }

    public function testSelectedCheapestShippingFail(): void
    {
        $country = new Country();
        $country->setId(1);
        $country->setCode("FR");
        $country->setBusinessEverywhere(true);
        $country->setIzbFcaCountry('france');
        $address = new Address();
        $address->setId(1);
        $address->setAddress('address 1');
        $address->setCountry($country);
        $modelCart = new ModelCart();
        $modelCart->setId(1);
        $cartItem = new CartItem();
        $cartItem->setName('product1');
        $modelCart->setItems([$cartItem]);
        $category = new Category();
        $category->setLabel('category_new');
        $company = new Company();
        $company->setId(1);
        $company->setMainAddress($address);
        $company->setCategory($category);
        $defaultUser = new User();
        $defaultUser->setId(1);
        $defaultUser->setCompany($company);
        $merchant = new Merchant();
        $merchant->setId(1);
        $merchant->setCountry($country);
        $this->cartService->expects($this->once())->method('addOfferWithCart')->willReturn(1);
        $this->cartService->expects($this->once())->method('updateQuantity')->willReturn(1);
        $this->cartService->expects($this->once())->method('createUserCart')->willReturn($modelCart);
        $this->cartRepository->expects($this->once())->method('find')->willReturn(new Cart());
        $this->offerService
            ->expects($this->exactly(2))
            ->method('findDetailedOfferById')
            ->willReturn(
                (new DetailedOffer())
                    ->setOffer((new Offer())
                        ->setQuantity(10)
                        ->setMoq(1)
                        ->setCurrency('EUR')
                        ->setStatus('active')
                        ->setSellerRef('vendorRef')
                        ->setIncoterm('DAP')
                        ->setIncotermCountry('France')
                        ->setUser($defaultUser)
                        ->setMerchant($merchant)
                    )
            )
        ;
        $this->cartService->expects($this->once())->method('findCart')->willReturn(new CartModel());

        $this->shippingService->expects($this->once())
            ->method('buildCartMerchantShippingOptions')
            ->willReturn(null)
        ;

        $this->shippingService->expects($this->never())->method('saveSelectedMerchantShippingOptions');

        $this->cartProcess->withUser($defaultUser)
            ->createCart([new ProductData(2, 4)], 1, '23')
            ->selectedCheapestShipping();
    }

    public function testSetRequestedDocumentationSuccess(): void
    {
        $shippingPoint = new ShippingPoint();
        $shippingPoint->setId(1);

        $country = new Country();
        $country->setId(1);
        $country->setCode("FR");
        $country->setBusinessEverywhere(true);
        $country->setIzbFcaCountry('france');
        $address = new Address();
        $address->setId(1);
        $address->setAddress('address 1');
        $address->setCountry($country);
        $modelCart = new ModelCart();
        $modelCart->setId(1);
        $modelCart->setShippingPoint($shippingPoint);
        $cartItem = new CartItem();
        $cartItem->setName('product1');
        $modelCart->setItems([$cartItem]);
        $category = new Category();
        $category->setLabel('category_new');
        $company = new Company();
        $company->setId(1);
        $company->setMainAddress($address);
        $company->setCategory($category);
        $defaultUser = new User();
        $defaultUser->setId(1);
        $defaultUser->setCompany($company);
        $merchant = new Merchant();
        $merchant->setId(1);
        $merchant->setCountry($country);
        $this->cartService->expects($this->once())->method('addOfferWithCart')->willReturn(1);
        $this->cartService->expects($this->once())->method('updateQuantity')->willReturn(1);
        $this->cartService->expects($this->once())->method('createUserCart')->willReturn($modelCart);
        $this->cartService->expects($this->once())->method('getCartDb')->willReturn(new Cart());
        $this->cartRepository->expects($this->once())->method('find')->willReturn(new Cart());
        $this->offerService
            ->expects($this->exactly(2))
            ->method('findDetailedOfferById')
            ->willReturn(
                (new DetailedOffer())
                    ->setOffer((new Offer())
                        ->setQuantity(10)
                        ->setMoq(1)
                        ->setCurrency('EUR')
                        ->setStatus('active')
                        ->setSellerRef('vendorRef')
                        ->setIncoterm('DAP')
                        ->setIncotermCountry('France')
                        ->setUser($defaultUser)
                        ->setMerchant($merchant)
                    )
            )
        ;

        $this->cartService->expects($this->once())->method('findCart')->willReturn($modelCart);

        $this->shippingPointService->expects($this->once())
            ->method('isValidShippingPointDocumentsRequests')
            ->willReturn(true)
        ;

        $this->cartService->expects($this->once())->method('saveDatabaseCart');

       $this->cartProcess->withUser($defaultUser)
            ->createCart([new ProductData(2, 4)], 1, '23')
            ->setRequestedDocumentation(['Document 1']);
    }

    public function testSetRequestedDocumentationFail(): void
    {
        $shippingPoint = new ShippingPoint();
        $shippingPoint->setId(1);

        $country = new Country();
        $country->setId(1);
        $country->setCode("FR");
        $country->setBusinessEverywhere(true);
        $country->setIzbFcaCountry('france');
        $address = new Address();
        $address->setId(1);
        $address->setAddress('address 1');
        $address->setCountry($country);
        $modelCart = new ModelCart();
        $modelCart->setId(1);
        $modelCart->setShippingPoint($shippingPoint);
        $cartItem = new CartItem();
        $cartItem->setName('product1');
        $modelCart->setItems([$cartItem]);
        $category = new Category();
        $category->setLabel('category_new');
        $company = new Company();
        $company->setId(1);
        $company->setMainAddress($address);
        $company->setCategory($category);
        $defaultUser = new User();
        $defaultUser->setId(1);
        $defaultUser->setCompany($company);
        $merchant = new Merchant();
        $merchant->setId(1);
        $merchant->setCountry($country);
        $this->cartService->expects($this->once())->method('addOfferWithCart')->willReturn(1);
        $this->cartService->expects($this->once())->method('updateQuantity')->willReturn(1);
        $this->cartService->expects($this->once())->method('createUserCart')->willReturn($modelCart);
        $this->cartService->expects($this->never())->method('getCartDb');
        $this->cartRepository->expects($this->once())->method('find')->willReturn(new Cart());
        $this->offerService
            ->expects($this->exactly(2))
            ->method('findDetailedOfferById')
            ->willReturn(
                (new DetailedOffer())
                    ->setOffer((new Offer())
                        ->setQuantity(10)
                        ->setMoq(1)
                        ->setCurrency('EUR')
                        ->setStatus('active')
                        ->setSellerRef('vendorRef')
                        ->setIncoterm('DAP')
                        ->setIncotermCountry('France')
                        ->setUser($defaultUser)
                        ->setMerchant($merchant)
                    )
            )
        ;

        $this->cartService->expects($this->once())->method('findCart')->willReturn($modelCart);

        $this->shippingPointService->expects($this->once())
            ->method('isValidShippingPointDocumentsRequests')
            ->willReturn(false)
        ;

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Requested documents are not valid !');

        $this->cartProcess->withUser($defaultUser)
            ->createCart([new ProductData(2, 4)], 1, '23')
            ->setRequestedDocumentation(['Document 1']);
    }

    public function testMinimumAmountFail(): void
    {
        $shippingPoint = new ShippingPoint();
        $shippingPoint->setId(1);

        $country = new Country();
        $country->setId(1);
        $country->setCode("FR");
        $country->setBusinessEverywhere(true);
        $country->setIzbFcaCountry('france');
        $address = new Address();
        $address->setId(1);
        $address->setAddress('address 1');
        $address->setCountry($country);
        $modelCart = new ModelCart();
        $modelCart->setId(1);
        $cartItem = new CartItem();
        $cartItem->setName('product1');
        $modelCart->setItems([$cartItem]);
        $category = new Category();
        $category->setLabel('category_new');
        $company = new Company();
        $company->setId(1);
        $company->setMainAddress($address);
        $company->setCategory($category);
        $defaultUser = new User();
        $defaultUser->setId(1);
        $defaultUser->setCompany($company);
        $merchant = new Merchant();
        $merchant->setId(1);
        $merchant->setCountry($country);
        $cartMerchant = new CartMerchant();
        $cartMerchant->setId(123);
        $cartMerchant->setMinimumOrderAmount(1000);
        $cartMerchant->setSubTotalWithoutVat(900);
        $cartMerchant->setItems([$cartItem]);
        $modelCart->setMerchants([$cartMerchant]);

        $this->cartService->expects($this->once())->method('addOfferWithCart')->willReturn(1);
        $this->cartService->expects($this->once())->method('updateQuantity')->willReturn(1);
        $this->cartService->expects($this->once())->method('createUserCart')->willReturn($modelCart);
        $this->cartRepository->expects($this->once())->method('find')->willReturn(new Cart());
        $this->offerService
            ->expects($this->exactly(2))
            ->method('findDetailedOfferById')
            ->willReturn(
                (new DetailedOffer())
                    ->setOffer((new Offer())
                        ->setQuantity(10)
                        ->setMoq(1)
                        ->setCurrency('EUR')
                        ->setStatus('active')
                        ->setSellerRef('vendorRef')
                        ->setIncoterm('DAP')
                        ->setIncotermCountry('France')
                        ->setUser($defaultUser)
                        ->setMerchant($merchant)
                    )
            )
        ;

        $this->cartService->expects($this->once())->method('findCart')->willReturn($modelCart);

        $this->mockShippingAddress();

        $this->expectException(CartMinimumAccountException::class);
        $this->expectExceptionMessage('the following merchant cart(s) has not reach the minimum amount');

        $this->cartProcess->withUser($defaultUser)
            ->createCart([new ProductData(2, 4)], 1, '23')
            ->validateMinimumAmount(shippingAddressId: 1, buyerOrderNumber: '23')
        ;
    }

    public function testMinimumAmountSuccess(): void
    {
        $shippingPoint = new ShippingPoint();
        $shippingPoint->setId(1);

        $country = new Country();
        $country->setId(1);
        $country->setCode("FR");
        $country->setBusinessEverywhere(true);
        $country->setIzbFcaCountry('france');
        $address = new Address();
        $address->setId(1);
        $address->setAddress('address 1');
        $address->setCountry($country);
        $modelCart = new ModelCart();
        $modelCart->setId(1);
        $cartItem = new CartItem();
        $cartItem->setName('product1');
        $modelCart->setItems([$cartItem]);
        $category = new Category();
        $category->setLabel('category_new');
        $company = new Company();
        $company->setId(1);
        $company->setMainAddress($address);
        $company->setCategory($category);
        $defaultUser = new User();
        $defaultUser->setId(1);
        $defaultUser->setCompany($company);
        $merchant = new Merchant();
        $merchant->setId(1);
        $merchant->setCountry($country);
        $cartMerchant = new CartMerchant();
        $cartMerchant->setId(123);
        $cartMerchant->setMinimumOrderAmount(1000);
        $cartMerchant->setSubTotalWithoutVat(1100);
        $cartMerchant->setItems([$cartItem]);
        $modelCart->setMerchants([$cartMerchant]);

        $this->cartService->expects($this->once())->method('addOfferWithCart')->willReturn(1);
        $this->cartService->expects($this->once())->method('updateQuantity')->willReturn(1);
        $this->cartService->expects($this->once())->method('createUserCart')->willReturn($modelCart);
        $this->cartRepository->expects($this->once())->method('find')->willReturn(new Cart());
        $this->offerService
            ->expects($this->exactly(2))
            ->method('findDetailedOfferById')
            ->willReturn(
                (new DetailedOffer())
                    ->setOffer((new Offer())
                        ->setQuantity(10)
                        ->setMoq(1)
                        ->setCurrency('EUR')
                        ->setStatus('active')
                        ->setSellerRef('vendorRef')
                        ->setIncoterm('DAP')
                        ->setIncotermCountry('France')
                        ->setUser($defaultUser)
                        ->setMerchant($merchant)
                    )
            )
        ;

        $this->cartService->expects($this->once())->method('findCart')->willReturn($modelCart);

        $this->mockShippingAddress(false);

        $this->cartProcess->withUser($defaultUser)
            ->createCart([new ProductData(2, 4)], 1, '23')
            ->validateMinimumAmount(shippingAddressId: 1, buyerOrderNumber: '23')
        ;
    }


    public function provideOfferQuantity(): Generator
    {
        yield [0];
        yield [11];
    }

    private function mockShippingAddress(bool $isCalled = true): void
    {
        $shippingPoint = new ShippingPoint();
        $defaultUser = new User();
        $defaultUser->setId(1);
        $site = new Site();
        $site->setId(1);
        $shippingPoint->setSite($site->setDefaultUser($defaultUser));

        $this->addressService
            ->expects($isCalled ? $this->once() : $this->never())
            ->method('get')
            ->willReturn((new Address())
                ->addShippingPoint($shippingPoint)
            )
        ;
    }
}
