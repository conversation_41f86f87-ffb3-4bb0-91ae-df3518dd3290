<?php

declare(strict_types=1);

namespace Tests\Api\Domain\Order;

use ApiPlatform\Core\Bridge\Symfony\Bundle\Test\ApiTestCase;
use ApiPlatform\Core\Bridge\Symfony\Bundle\Test\Client;
use AppBundle\Entity\Order;
use Tests\Api\Traits\AuthenticateTrait;
use Tests\Api\Traits\FixturesTrait;

abstract class OrderTest extends ApiTestCase
{
    use AuthenticateTrait;
    use FixturesTrait;

    private Client $client;

    public function setUp(): void
    {
        $this->client = static::createClient([
            'base_uri' => 'https://alstom.local',
        ]);
    }

    public function testRetrieveOrderByIzbergId(): void
    {
        $this->getDatabaseTool()->loadAliceFixture([
            __DIR__ . '/Resources/OrderFixtures.yaml'
        ]);

        $client = $this->createAuthenticatedClient('<EMAIL>');

        $client->request('GET', '/middleware/api/orders/1');

        $this->assertResponseIsSuccessful();
        $this->assertJsonContains(['@id' => '/middleware/api/orders/1']);
    }

    public function testRetrieveOrderByIzbergIdDoesNotExist(): void
    {
        $this->getDatabaseTool()->loadAliceFixture([
            __DIR__ . '/Resources/OrderFixtures.yaml'
        ]);

        $client = $this->createAuthenticatedClient('<EMAIL>');

        $client->request('GET', '/middleware/api/orders/1000');

        $this->assertResponseStatusCodeSame(404);
        $this->assertJsonContains(['The 1000 order does not exist.']);
    }
}
