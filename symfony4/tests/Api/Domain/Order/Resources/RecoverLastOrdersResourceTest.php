<?php

declare(strict_types=1);

namespace Tests\Api\Domain\Order\Resources;

use ApiPlatform\Core\Bridge\Symfony\Bundle\Test\ApiTestCase;
use AppBundle\Entity\Order;
use Tests\Api\Traits\AuthenticateTrait;
use Tests\Api\Traits\FixturesTrait;

final class RecoverLastOrdersResourceTest extends ApiTestCase
{
    use AuthenticateTrait;
    use FixturesTrait;

    public function testRetrieveLastOrdersOfCompany(): void
    {
        $this->getDatabaseTool()->loadAliceFixture([
            __DIR__ . '/../../Fixtures/ApiUserLoginFixtures.yaml',
            __DIR__ . '/OrderFixtures.yaml',
        ]);

        $client = $this->createAuthenticatedClient('<EMAIL>');

        $client->request('GET', '/middleware/api/orders/last', [
            'headers' => ['Content-Type' => 'application/json'],
        ]);

        $this->assertResponseIsSuccessful();
        $this->assertMatchesResourceItemJsonSchema(Order::class);
        $this->assertJsonContains(['hydra:totalItems' => 3]);
    }

    public function testRetrieveLastOrdersOfCompanyWithLimitArgument(): void
    {
        $this->getDatabaseTool()->loadAliceFixture([
            __DIR__ . '/../../Fixtures/ApiUserLoginFixtures.yaml',
            __DIR__ . '/OrderFixtures.yaml',
        ]);

        $client = $this->createAuthenticatedClient('<EMAIL>');

        $client->request('GET', '/middleware/api/orders/last?limit=2', [
            'headers' => ['Content-Type' => 'application/json'],
        ]);

        $this->assertResponseIsSuccessful();
        $this->assertMatchesResourceItemJsonSchema(Order::class);
        $this->assertJsonContains(['hydra:totalItems' => 2]);
    }
}
