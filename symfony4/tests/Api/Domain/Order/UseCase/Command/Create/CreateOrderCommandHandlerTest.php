<?php

declare(strict_types=1);

namespace Tests\Api\Domain\Order\UseCase\Command\Create;

use Api\Core\Exception\InvalidArgumentException;
use Api\Domain\Order\Dto\ProductData;
use Api\Domain\Order\Message\OrderPayloadMessage;
use Api\Domain\Order\Payment\PaymentMethodHandler;
use Api\Domain\Order\UseCase\Command\Create\CreateOrderCommandHandler;
use Api\Domain\Order\UseCase\Command\Create\CreateOrderInput;
use AppBundle\Entity\Address;
use AppBundle\Entity\Company;
use AppBundle\Entity\Country;
use AppBundle\Entity\Order;
use AppBundle\Entity\ShippingPoint;
use AppBundle\Entity\User;
use AppBundle\Model\AddressCore;
use AppBundle\Repository\OrderRepository;
use AppBundle\Repository\ShippingPointRepository;
use AppBundle\Services\OfferService;
use AppBundle\Services\OrderService;
use Open\IzbergBundle\Model\Order as IzbergOrder;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Security\Core\Security;
use Tests\Api\Domain\Order\DummyFactory\DummyProductDataFactory;

final class CreateOrderCommandHandlerTest extends TestCase
{
    private CreateOrderCommandHandler $useCase;

    /**
     * @var OfferService|MockObject
     */
    private OfferService $offerService;

    /**
     * @var MockObject|Security
     */
    private Security $security;

    /**
     * @var OrderRepository|MockObject
     */
    private OrderRepository $orderRepository;

    /**
     * @var OrderService|MockObject
     */
    private OrderService $orderService;

    /**
     * @var MockObject|MessageBusInterface
     */
    private MessageBusInterface $messengerBus;

    /**
     * @var PaymentMethodHandler|mixed|MockObject
     */
    private PaymentMethodHandler $paymentMethodHandler;

    private ShippingPointRepository|MockObject $shippingPointRepository;

    protected function setUp(): void
    {
        $this->offerService = $this->createMock(OfferService::class);
        $this->security = $this->createMock(Security::class);
        $this->orderRepository = $this->createMock(OrderRepository::class);
        $this->orderService = $this->createMock(OrderService::class);
        $this->messengerBus = $this->createMock(MessageBusInterface::class);
        $this->paymentMethodHandler = $this->createMock(PaymentMethodHandler::class);
        $this->shippingPointRepository = $this->createMock(ShippingPointRepository::class);

        $this->useCase = new CreateOrderCommandHandler(
            $this->security,
            $this->orderService,
            $this->orderRepository,
            new CartCreateProcessFake(),
            $this->messengerBus,
            $this->paymentMethodHandler,
            $this->shippingPointRepository,
        );
    }

    public function testShouldHaveAnOrderToCreate(): void
    {
        $user = $this->instantiateUser();

        $this->security
            ->expects($this->once())
            ->method('getUser')
            ->willReturn($user)
        ;

        $order = new Order();
        $order->setIzbergId(1);
        $this->orderRepository
            ->expects($this->once())
            ->method('fetchByCartId')
            ->willReturn($order);

        $this->messengerBus
            ->expects($this->once())
            ->method('dispatch')
            ->with($this->isInstanceOf(OrderPayloadMessage::class))
            ->willReturn(new Envelope(new OrderPayloadMessage(new IzbergOrder())))
        ;

        $billingAddress = new AddressCore();
        $billingAddress->setAddress('rue du test');
        $billingAddress->setAddressComplement('');
        $billingAddress->setCity('Paris');
        $billingAddress->setZipCode('75001');
        $billingAddress->setArea('idf');

        $input = CreateOrderInput::create(
            products: [DummyProductDataFactory::createProductData()],
            costCenterId: 2,
            paymentMethod: 'preTransferWire',
            totalPriceTaxIncl: 0,
            deliveryAddressId: 2,
            accountingEmail: '<EMAIL>',
            billingAddress: $billingAddress
        );

        $order = $this->useCase->__invoke($input);

        $this->assertInstanceOf(Order::class, $order);
        $this->assertSame('<EMAIL>', $order->getAccountingEmail());
    }

    public function testCreateOrderWithAccountingEmail(): void
    {
        $user = $this->instantiateUser();

        $this->security
            ->expects($this->once())
            ->method('getUser')
            ->willReturn($user)
        ;

        $order = new Order();
        $order->setIzbergId(1);
        $this->orderRepository
            ->expects($this->once())
            ->method('fetchByCartId')
            ->willReturn($order);

        $this->messengerBus
            ->expects($this->once())
            ->method('dispatch')
            ->with($this->isInstanceOf(OrderPayloadMessage::class))
            ->willReturn(new Envelope(new OrderPayloadMessage(new IzbergOrder())))
        ;

        $this->shippingPointRepository->expects($this->any())->method('byId')->willReturnCallback(function  () {
            $shippingPoint = new ShippingPoint();
            $shippingPoint->setAccountantEmail('<EMAIL>');

            return $shippingPoint;
        });

        $billingAddress = new AddressCore();
        $billingAddress->setAddress('rue du test');
        $billingAddress->setAddressComplement('');
        $billingAddress->setCity('Paris');
        $billingAddress->setZipCode('75001');
        $billingAddress->setArea('idf');

        $input = CreateOrderInput::create(
            products: [DummyProductDataFactory::createProductData()],
            costCenterId: 2,
            paymentMethod: 'preTransferWire',
            totalPriceTaxIncl: 0,
            deliveryAddressId: 2,
            accountingEmail: '',
            billingAddress: $billingAddress
        );

        $order = $this->useCase->__invoke($input);

        $this->assertInstanceOf(Order::class, $order);
        $this->assertSame('<EMAIL>', $order->getAccountingEmail());
    }

    public function testShouldHaveTheThrowExceptionWithCreditCardPayment(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Creating an order is not allowed with credit card payment');

        $this->security
            ->expects($this->once())
            ->method('getUser')
            ->willReturn(new User())
        ;

        $input = CreateOrderInput::create(
            products: [new ProductData(2, 4)],
            costCenterId: 2,
            paymentMethod: 'preCreditCard',
            totalPriceTaxIncl: 0,
            deliveryAddressId: 2
        );

        $this->useCase->__invoke($input);
    }

    public function testShouldHaveErrorIfNotUser(): void
    {
        $this->security->expects($this->once())->method('getUser')->willReturn(null);

        $input = CreateOrderInput::create([], 2, '', 0, 2);
        $this->assertNull($this->useCase->__invoke($input));
    }

    private function instantiateUser(): User
    {
        $country = new Country();
        $country->setId(1);
        $country->setCode("FR");
        $country->setBusinessEverywhere(true);
        $country->setIzbFcaCountry('france');
        $address = new Address();
        $address->setId(1);
        $address->setAddress('address 1');
        $address->setCountry($country);
        $company = new Company();
        $company->setMainAddress($address);
        $user = new User();
        $user->setCompany($company);

        return $user;
    }
}
