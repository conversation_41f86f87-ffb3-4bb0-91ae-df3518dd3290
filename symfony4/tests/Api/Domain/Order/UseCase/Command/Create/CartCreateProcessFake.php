<?php

declare(strict_types=1);

namespace Tests\Api\Domain\Order\UseCase\Command\Create;

use Api\Domain\Order\Service\CartProcess\CartCreateProcessInterface;
use AppBundle\Entity\Address;
use AppBundle\Entity\User;
use AppBundle\Model\Cart\Cart;
use AppBundle\ValueObject\PaymentMethod;

final class CartCreateProcessFake implements CartCreateProcessInterface
{
    private User $user;

    public function withUser(User $user): CartCreateProcessInterface
    {
        $this->user = (new User())->setEmail('<EMAIL>');

        return $this;
    }

    /**
     * @inheritDoc
     */
    public function createCart(
        array $products,
        int $shippingAddressId,
        ?string $buyerOrderNumber
    ): CartCreateProcessInterface {
        return $this;
    }

    public function selectedCheapestShipping(): CartCreateProcessInterface
    {
        return $this;
    }

    /**
     * @inheritDoc
     */
    public function checkoutCart(
        int $shippingAddressId,
        PaymentMethod $paymentMethod,
        float $totalPrice,
        ?string $buyerOrderNumber,
        ?string $accountingEmail = null,
        ?Address $billingAddress = null
    ): Cart {
        $cart = new Cart();
        $cart->setId(1);
        return $cart;
    }

    public function setRequestedDocumentation(array $requestedDocumentation): CartCreateProcessInterface
    {
        return $this;
    }

    public function validateMinimumAmount(int $shippingAddressId, ?string $buyerOrderNumber,): CartCreateProcessInterface
    {
        return $this;
    }
}
