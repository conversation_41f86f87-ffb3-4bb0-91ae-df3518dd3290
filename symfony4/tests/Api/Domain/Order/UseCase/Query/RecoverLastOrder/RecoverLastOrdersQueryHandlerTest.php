<?php

declare(strict_types=1);

namespace Tests\Api\Domain\Order\UseCase\Query\RecoverLastOrder;

use Api\Domain\Order\UseCase\Query\RecoverLastOrders\RecoverLastOrdersQuery;
use Api\Domain\Order\UseCase\Query\RecoverLastOrders\RecoverLastOrdersQueryHandler;
use AppBundle\Entity\Order;
use AppBundle\Repository\OrderRepository;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class RecoverLastOrdersQueryHandlerTest extends TestCase
{
    /**
     * @var OrderRepository|MockObject
     */
    private OrderRepository $orderRepository;

    protected function setUp(): void
    {
        parent::setUp();

        $this->orderRepository = $this->createMock(OrderRepository::class);
    }

    public function testRecoverLastOfOrders(): void
    {
        $this->orderRepository
            ->expects($this->once())->method('fetchLastOrders')
            ->willReturn([
                $this->makeOrder(),
                $this->makeOrder()->setId(2),
                $this->makeOrder()->setId(3),
        ]);

        $query = new RecoverLastOrdersQueryHandler($this->orderRepository);

        $orders = $query->__invoke(new RecoverLastOrdersQuery(1, 3));

        $this->assertNotEmpty($orders);
        $this->assertContainsOnlyInstancesOf(Order::class, $orders);
    }

    private function makeOrder(): Order
    {
        $order = new Order();
        $order->setId(1);
        $order->setIzbergId(1);
        $order->setAmount(100);
        $order->setCartId(1);

        return $order;
    }
}
