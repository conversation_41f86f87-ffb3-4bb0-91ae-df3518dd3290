<?php

declare(strict_types=1);

namespace Tests\Api\Domain\Order\Status;

use Api\Core\Exception\InvalidArgumentException;
use Api\Domain\Order\Status\Status;
use PHPUnit\Framework\TestCase;

final class StatusTest extends TestCase
{
    /**
     * @dataProvider provideIzbergCode
     */
    public function testIzbergCodeToActionLabel(int $izbergCode, string $expected): void
    {
        $this->assertSame($expected, Status::izbergCodeTOAction($izbergCode));
    }

    public function testIzbergCodeToActionLabelFail(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('The izbergCode is invalid: 0, 60, 80, 85, 2000');

        Status::izbergCodeTOAction(10);
    }

    public function provideIzbergCode(): iterable
    {
        yield [0, 'INITIAL'];
        yield [60, 'ORDER_AUTHORIZED'];
        yield [80, 'ORDER_CONFIRMED'];
        yield [85, 'ORDER_PROCESSED'];
        yield [2000, 'ORDER_CANCELED'];
    }
}
