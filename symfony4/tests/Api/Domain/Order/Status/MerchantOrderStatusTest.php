<?php

declare(strict_types=1);

namespace Tests\Api\Domain\Order\Status;

use Api\Core\Exception\InvalidArgumentException;
use Api\Domain\Order\Status\MerchantOrderStatus;
use PHPUnit\Framework\TestCase;

final class MerchantOrderStatusTest extends TestCase
{
    /**
     * @dataProvider provideIzbergStatus
     */
    public function testIzbergStatusToLabelSuccessful(int $izbergStatus, string $expected): void
    {
        $label = MerchantOrderStatus::izbergToLabel($izbergStatus);

        $this->assertSame($expected, $label);
    }

    public function testIzbergStatusToLabelFail(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('The 10 izberg status code does not exist.');

        MerchantOrderStatus::izbergToLabel(10);
    }

    public function provideIzbergStatus(): iterable
    {
        yield [0, 'merchant_order.status.initial'];
        yield [60, 'merchant_order.status.payment_authorized'];
        yield [80, 'merchant_order.status.confirmed'];
        yield [2000, 'merchant_order.status.canceled'];
        yield [85, 'merchant_order.status.processed'];
    }
}
