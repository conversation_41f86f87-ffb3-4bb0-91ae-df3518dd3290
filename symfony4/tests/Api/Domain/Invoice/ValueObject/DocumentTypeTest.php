<?php

declare(strict_types=1);

namespace Tests\Api\Domain\Invoice\ValueObject;

use Api\Core\Exception\InvalidArgumentException;
use Api\Domain\Invoice\ValueObject\DocumentType;
use PHPUnit\Framework\TestCase;

final class DocumentTypeTest extends TestCase
{
    public function testCreateDocumentType(): void
    {
        $documentType = DocumentType::create('invoice');
        $this->assertInstanceOf(DocumentType:: class, $documentType);
        $this->assertSame('invoice', (string) $documentType);

        $documentType = DocumentType::create('credit_note');
        $this->assertInstanceOf(DocumentType:: class, $documentType);
        $this->assertSame('credit_note', (string) $documentType);
    }

    public function testThrowExceptionWithBadDocumentType(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('The unknown document type is not supported: credit_note, invoice');

        DocumentType::create('unknown');
    }
}
