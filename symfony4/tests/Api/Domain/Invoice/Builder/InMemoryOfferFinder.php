<?php

declare(strict_types=1);

namespace Tests\Api\Domain\Invoice\Builder;

use AppBundle\Model\Offer;
use AppBundle\Services\Offer\OfferFinderInterface;

final class InMemoryOfferFinder implements OfferFinderInterface
{
    public function __construct(private array $offers = [])
    {
    }

    public function findOfferById(int $offerId): ?Offer
    {
        return $this->offers[$offerId] ?? null;
    }
}
