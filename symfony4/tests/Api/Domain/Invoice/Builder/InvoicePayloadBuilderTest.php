<?php

declare(strict_types=1);

namespace Tests\Api\Domain\Invoice\Builder;

use Api\Domain\Invoice\Builder\BuyerInvoicePayloadIzbergBuilder;
use Api\Domain\Invoice\Builder\InvoiceLinesPayloadIzbergBuilder;
use Api\Domain\Invoice\Builder\InvoicePayloadIzbergBuilder;
use Api\Domain\Invoice\Builder\VendorInvoicePayloadIzbergBuilder;
use Api\Domain\Invoice\Mapper\CreditNoteToInvoiceMapper;
use Api\Domain\Invoice\ValueObject\DocumentType;
use AppBundle\Entity\OrderItem as OrderItemEntity;
use AppBundle\Model\Offer;
use AppBundle\Repository\OrderItemRepository;
use AppBundle\Services\AlstomCustomAttributes;
use AppBundle\Services\OfferService;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Open\IzbergBundle\Api\MerchantApi;
use Open\IzbergBundle\Model\CreditNote;
use Open\IzbergBundle\Model\CreditNoteLine;
use Open\IzbergBundle\Model\Invoice;
use Open\IzbergBundle\Model\InvoiceLine;
use Open\IzbergBundle\Model\IzbergUser;
use Open\IzbergBundle\Model\Merchant;
use Open\IzbergBundle\Model\OrderItem;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class InvoicePayloadBuilderTest extends TestCase
{
    /**
     * @var MerchantApi|MockObject
     */
    private MerchantApi $merchantApiMock;

    /**
     * @var AlstomCustomAttributes|MockObject
     */
    private AlstomCustomAttributes $customAttributes;

    /**
     * @var EntityManagerInterface|MockObject
     */
    private EntityManagerInterface $entityManager;

    /**
     * @var OrderItemRepository|MockObject
     */
    private OrderItemRepository $orderItemRepository;

    protected function setUp(): void
    {
        parent::setUp();

        $this->merchantApiMock = $this->createMock(MerchantApi::class);

        $this->customAttributes = $this->createMock(AlstomCustomAttributes::class);
        $this->orderItemRepository = $this->createMock(OrderItemRepository::class);

        $merchantApi = new \stdClass();
        $merchantApi->id = 1;
        $merchantApi->addresses[] = (object) [
            'address' => 'address 1',
            'zipcode' => '22200',
            'city' => 'Lannion',
            'country' => (object) ['name' => 'France']
        ];

        $this->merchantApiMock
            ->expects($this->any())
            ->method('getMerchantAllCustomAttributes')
            ->willReturn(['key' => '121212', 'vatnumber' => 'FR00000000190']);

        $this->merchantApiMock
            ->expects($this->any())
            ->method('getMerchant')
            ->willReturn($merchantApi);

        $bankAccount = (object) [
            'objects' => [
                (object) ['account_BIC' => 'SOGEFRPP', 'account_IBAN' => "***************************"]
            ]
        ];
        $this->merchantApiMock
            ->expects($this->any())
            ->method('getMerchantStoreBankAccount')
            ->willReturn($bankAccount);

        $this->customAttributes
            ->expects($this->any())
            ->method('getShareCapital')
            ->willReturn('key');

        $this->customAttributes
            ->expects($this->any())
            ->method('getVendorVatNumber')
            ->willReturn('vatnumber');

        $orderItem = new OrderItemEntity();
        $orderItem->setVendorReference('vendor_ref');
        $orderItem->setBuyerReference('buyer_ref');
        $this->orderItemRepository
            ->expects($this->any())
            ->method('getByIzbergId')
            ->willReturn($orderItem);
    }

    public function testBuildInvoicePayloadDataFromIzbergData(): void
    {
        $offer = new Offer();
        $offer
            ->setProductId(1)
            ->setIncoterm('')
            ->setCustomTariffCode('')
        ;
        $offer2 = new Offer();
        $offer2
            ->setProductId(10)
            ->setIncoterm('')
            ->setCustomTariffCode('')
        ;

        $offerFinder = new InMemoryOfferFinder([1000 => $offer, 10 => $offer2]);
        $builder = new InvoicePayloadIzbergBuilder();
        $builder = new VendorInvoicePayloadIzbergBuilder(
            $builder,
            $this->merchantApiMock,
            $this->customAttributes
        );
        $builder = new BuyerInvoicePayloadIzbergBuilder($builder);
        $builder = new InvoiceLinesPayloadIzbergBuilder(
            $builder,
            $this->orderItemRepository,
            $offerFinder,
        );

        $actual = $builder->build($this->makeInvoice());

        $this->assertEquals([
            'buyer_order_number' => 10,
            'capital_of_stationone' => '89.000 Euros',
            'due_on' => '12/12/2022',
            'external_order_id' => '12',
            'invoice_status' => 'emitted',
            'izb_invoice_id' => 1,
            'izb_order_id' => 10,
            'invoice_lines' => [
                [
                    'vendor_reference' => 'vendor_ref',
                    'buyer_reference' => 'buyer_ref',
                    'product_name' => 'product name',
                    'order_line' => '',
                    'made_in' => '',
                    'unit' => 20,
                    'quantity' => 1,
                    'price_exclude_vat' => 10,
                    'amount_exclude_vat' => 5,
                    'vat_applied' => 3,
                    'incoterm' => '',
                    'custom_tarif_code' => '',
                ],
                [
                    'vendor_reference' => 'vendor_ref',
                    'buyer_reference' => 'buyer_ref',
                    'product_name' => 'product name 2',
                    'order_line' => '',
                    'made_in' => '',
                    'unit' => 20,
                    'quantity' => 1,
                    'price_exclude_vat' => 10,
                    'amount_exclude_vat' => 5,
                    'vat_applied' => 3,
                    'incoterm' => '',
                    'custom_tarif_code' => '',
                ],
            ],
            'legal_notice' => 'notice',
            'pdf_file' => null,
            'reconciliation_key' => 'payment_details',
            'stationone_vat_number' => 'FR18752364885',
            'supplier_address' => '69/73 Boulevard Victor Hugo, 93400 Saint-Ouen (France)',
            'supplier_bic' => 'SOGEFRPP',
            'supplier_iban' => '***************************',
            'supplier_name' => 'StationOne',
            'total_vat_excluded' => 192000.000000,
            'total_vat_included' => 230400.0,
            'type_of_document' => 'invoice',
            'vendor_address' => 'address 1 22200 Lannion France',
            'vendor_capital' => '121212',
            'vendor_name' => 'societé nkev2611a',
            'vendor_siret' => '',
            'vendor_vat_number' => 'FR00000000190',
        ], $actual);
    }

    public function testBuildInvoicePayloadDataFromCreditNoteIzbergData(): void
    {
        $offer = new Offer();
        $offer
            ->setProductId(1)
            ->setIncoterm('')
            ->setCustomTariffCode('')
        ;

        $offerFinder = new InMemoryOfferFinder([1000 => $offer]);
        $builder = new InvoicePayloadIzbergBuilder();
        $builder = new VendorInvoicePayloadIzbergBuilder(
            $builder,
            $this->merchantApiMock,
            $this->customAttributes
        );
        $builder = new BuyerInvoicePayloadIzbergBuilder($builder);
        $builder = new InvoiceLinesPayloadIzbergBuilder(
            $builder,
            $this->orderItemRepository,
            $offerFinder
        );

        $mapper = new CreditNoteToInvoiceMapper();
        $actual = $builder->build($mapper($this->makeCreditNote()));

        $this->assertEquals([
            'buyer_order_number' => 10,
            'capital_of_stationone' => '89.000 Euros',
            'due_on' => '',
            'external_order_id' => null,
            'invoice_lines' => [
                [
                    'vendor_reference' => 'vendor_ref',
                    'buyer_reference' => 'buyer_ref',
                    'product_name' => 'product name',
                    'order_line' => '',
                    'made_in' => '',
                    'unit' => 20,
                    'quantity' => 1,
                    'price_exclude_vat' => 10,
                    'amount_exclude_vat' => 5,
                    'vat_applied' => 3,
                    'incoterm' => '',
                    'custom_tarif_code' => '',
                ],
            ],
            'invoice_status' => 'emitted',
            'izb_invoice_id' => 1,
            'izb_order_id' => null,
            'legal_notice' => 'notice',
            'pdf_file' => null,
            'reconciliation_key' => 'payment_details',
            'stationone_vat_number' => 'FR18752364885',
            'supplier_address' => '69/73 Boulevard Victor Hugo, 93400 Saint-Ouen (France)',
            'supplier_bic' => 'SOGEFRPP',
            'supplier_iban' => '***************************',
            'supplier_name' => 'StationOne',
            'total_vat_excluded' => null,
            'total_vat_included' => 230400.0,
            'type_of_document' => 'credit_note',
            'vendor_address' => 'address 1 22200 Lannion France',
            'vendor_capital' => '121212',
            'vendor_name' => 'societé nkev2611a',
            'vendor_siret' => '',
            'vendor_vat_number' => 'FR00000000190',
        ], $actual);
    }

    public function testBuilderInvoiceWithoutOfferInAlgolia(): void
    {
        $offer = new Offer();
        $offer
            ->setIncoterm('')
            ->setCustomTariffCode('')
        ;

        $offerFinder = new InMemoryOfferFinder([1000 => $offer]);
        $builder = new InvoicePayloadIzbergBuilder();
        $builder = new VendorInvoicePayloadIzbergBuilder(
            $builder,
            $this->merchantApiMock,
            $this->customAttributes
        );
        $builder = new BuyerInvoicePayloadIzbergBuilder($builder);
        $builder = new InvoiceLinesPayloadIzbergBuilder(
            $builder,
            $this->orderItemRepository,
            $offerFinder,
        );

        $actual = $builder->build($this->makeInvoice());

        $this->assertEquals([
            'buyer_order_number' => 10,
            'capital_of_stationone' => '89.000 Euros',
            'due_on' => '12/12/2022',
            'external_order_id' => '12',
            'invoice_lines' => [
                [
                    'vendor_reference' => 'vendor_ref',
                    'buyer_reference' => 'buyer_ref',
                    'product_name' => 'product name',
                    'order_line' => '',
                    'made_in' => '',
                    'unit' => 20,
                    'quantity' => 1,
                    'price_exclude_vat' => 10,
                    'amount_exclude_vat' => 5,
                    'vat_applied' => 3,
                    'incoterm' => '',
                    'custom_tarif_code' => '',
                ],
            ],
            'invoice_status' => 'emitted',
            'izb_invoice_id' => 1,
            'izb_order_id' => 10,
            'legal_notice' => 'notice',
            'pdf_file' => null,
            'reconciliation_key' => 'payment_details',
            'stationone_vat_number' => 'FR18752364885',
            'supplier_address' => '69/73 Boulevard Victor Hugo, 93400 Saint-Ouen (France)',
            'supplier_bic' => 'SOGEFRPP',
            'supplier_iban' => '***************************',
            'supplier_name' => 'StationOne',
            'total_vat_excluded' => 192000.000000,
            'total_vat_included' => 230400.0,
            'type_of_document' => 'invoice',
            'vendor_address' => 'address 1 22200 Lannion France',
            'vendor_capital' => '121212',
            'vendor_name' => 'societé nkev2611a',
            'vendor_siret' => '',
            'vendor_vat_number' => 'FR00000000190'
        ], $actual);
    }

    private function makeInvoice(): Invoice
    {
        $merchant = new Merchant();
        $merchant->setId(1);
        $merchant->setName('societé nkev2611a');

        $user = new IzbergUser();
        $user->setId(1);
        $user->setFirstName('firstname');
        $user->setLastName('lastname');

        $invoice = new Invoice();
        $invoice->setId(1);
        $invoice->setStatus('emitted');
        $invoice->setIssuerName('societé nkev2611a');
        $invoice->setCreatedOn((new \DateTime('2022-12-12'))->format('Y-m-d'));
        $invoice->setDueOn((new \DateTime('2022-12-12'))->format('Y-m-d'));
        $invoice->setOrderId(10);
        $invoice->setIdNumber(10);
        $invoice->setCurrency('EUR');
        $invoice->setLanguage('FR');
        $invoice->setNumOrder('12');
        $invoice->setIssuer($merchant);
        $invoice->setReceiver($user);
        $invoice->setIssuer($merchant);
        $invoice->setDueOn('12/12/2022');
        $invoice->setLegalNotices('notice');
        $invoice->setTotalAmountWithTaxes(230400.00);
        $invoice->setTotalAmountWithoutTaxes(192000.000000);
        $invoice->setDocumentType(DocumentType::create('invoice'));
        $invoice->setPaymentDetails('payment_details');
        $orderItem = new OrderItem();
        $orderItem->setId(10);
        $orderItem->setName('product name');
        $orderItem->setPrice(20);
        $orderItem->setQuantity(1);
        $orderItem->setAmount(10);
        $orderItem->setVat(3);
        $orderItem->setAmountVatIncluded(5);
        $orderItem->setOfferId(1000);
        $invoiceLine = new InvoiceLine();
        $invoiceLine->setOrderItem($orderItem);
        $orderItem2 = new OrderItem();
        $orderItem2->setId(10);
        $orderItem2->setName('product name 2');
        $orderItem2->setPrice(20);
        $orderItem2->setQuantity(1);
        $orderItem2->setAmount(10);
        $orderItem2->setVat(3);
        $orderItem2->setAmountVatIncluded(5);
        $orderItem2->setOfferId(10);
        $invoiceLine2 = new InvoiceLine();
        $invoiceLine2->setOrderItem($orderItem2);
        $invoice->setInvoiceLines(new ArrayCollection([$invoiceLine, $invoiceLine2]));

        return $invoice;
    }

    private function makeCreditNote(): CreditNote
    {
        $merchant = new Merchant();
        $merchant->setId(1);
        $merchant->setName('societé nkev2611a');

        $user = new IzbergUser();
        $user->setId(1);
        $user->setFirstName('firstname');
        $user->setLastName('lastname');

        $creditNote = new CreditNote();
        $creditNote->setId(1);
        $creditNote->setStatus('emitted');
        $creditNote->setIssuerName('societé nkev2611a');
        $creditNote->setCreatedOn((new \DateTime('2022-12-12'))->format('Y-m-d'));
        $creditNote->setIdNumber(10);
        $creditNote->setCurrency('EUR');
        $creditNote->setIssuer($merchant);
        $creditNote->setReceiver($user);
        $creditNote->setIssuer($merchant);
        $creditNote->setLegalNotices('notice');
        $creditNote->setTotalAmountWithTaxes(230400.00);
        $creditNote->setPaymentDetails('payment_details');
        $orderItem = new OrderItem();
        $orderItem->setId(10);
        $orderItem->setName('product name');
        $orderItem->setPrice(20);
        $orderItem->setQuantity(1);
        $orderItem->setAmount(10);
        $orderItem->setVat(3);
        $orderItem->setAmountVatIncluded(5);
        $orderItem->setOfferId(1000);
        $creditNoteLine = new CreditNoteLine();
        $creditNoteLine->setOrderItem($orderItem);
        $creditNote->setCreditNoteLines(new ArrayCollection([$creditNoteLine]));

        return $creditNote;
    }
}
