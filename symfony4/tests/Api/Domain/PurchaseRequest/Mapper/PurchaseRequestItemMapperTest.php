<?php

declare(strict_types=1);

namespace Tests\Api\Domain\PurchaseRequest\Mapper;

use Api\Domain\PurchaseRequest\Mapper\PurchaseRequestItemMapper;
use AppBundle\Entity\PurchaseRequestItem;
use AppBundle\Entity\SpecificPrice;
use AppBundle\Model\Merchant;
use AppBundle\Model\Offer;
use PHPUnit\Framework\TestCase;

final class PurchaseRequestItemMapperTest extends TestCase
{
    private PurchaseRequestItemMapper $mapper;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mapper = new PurchaseRequestItemMapper();
    }

    public function testMapPayloadDataWithSpecificPriceAtNull(): void
    {
        $merchant = new Merchant();
        $merchant->setName('merchant name');

        $purchaseRequest = new PurchaseRequestItem();
        $purchaseRequest->setQuantityExpected(2);
        $purchaseRequest->setManufacturerReference('manufacturer ref');
        $purchaseRequest->setBuyerReference('buyer ref');

        $offer = new Offer();
        $offer->setPrices(['EUR' => 20]);
        $offer->setIzbergReference("1");
        $offer->setBuyerRef("offer buyer ref");
        $offer->setSellerRef("seller ref");
        $offer->setOfferTitle("offer title");
        $offer->setShortDescription("short description");
        $offer->setThresholds([]);
        $offer->setMoq(1);
        $offer->setBatchSize(2);
        $offer->setDeliveryTime(1);
        $offer->setQuantity(1);
        $offer->setIncoterm("incoterm");
        $offer->setIncotermCountry("France");
        $offer->setCurrency("EUR");
        $offer->setMerchant($merchant);
        $offer->setManufacturerName('manufacturer name');
        $offer->setMinimumOrderAmount(12);

        $actual = $this->mapper->mapPayloadData(
            $offer,
            1,
            $purchaseRequest,
            null,
        );

        $this->assertEquals([
            'izb_product_id' => "1",
            'vendor_reference' => "seller ref",
            'expected_buyer_reference' => 'buyer ref',
            'expected_vendor_reference' => 'manufacturer ref',
            'product_name' => "offer title",
            'product_description' => "short description",
            'unit_price' => 20,
            'thresholds' => [],
            'moq' => 1,
            'batch_size' => 2,
            'delivery_delay' => 1,
            'stock' => 1,
            'incoterm' => "incoterm",
            'country_of_delivery' => 'France',
            'currency' => 'EUR',
            'transport_service_availability' => 'no',
            'vat_is_applicable' => 'yes',
            'vendor_name' => 'merchant name',
            'manufacturer_name' => 'manufacturer name',
            'minimum_order_amount' => 12.0,
            'contract_number' => null,
            'validity_duration' => null,
            'vat_rate' => '1%'
        ], $actual);
    }

    public function testMapPayloadDataWithSpecificPrice(): void
    {
        $merchant = new Merchant();
        $merchant->setName('merchant name');

        $specificPrice = new SpecificPrice();
        $specificPrice->setFrameContract('frame contract');
        $specificPrice->setValidityDate(new \DateTime('12/12/2022'));
        $specificPrice->setPrice1(10);
        $specificPrice->setThreshold1(5);

        $purchaseRequest = new PurchaseRequestItem();
        $purchaseRequest->setQuantityExpected(5);
        $purchaseRequest->setManufacturerReference('manufacturer ref');
        $purchaseRequest->setBuyerReference('buyer ref');

        $offer = new Offer();
        $offer->setPrices(['EUR' => 20]);
        $offer->setIzbergReference("1");
        $offer->setBuyerRef("offer buyer ref");
        $offer->setSellerRef("seller ref");
        $offer->setOfferTitle("offer title");
        $offer->setShortDescription("short description");
        $offer->setThresholds([]);
        $offer->setMoq(1);
        $offer->setBatchSize(2);
        $offer->setDeliveryTime(1);
        $offer->setQuantity(1);
        $offer->setIncoterm("incoterm");
        $offer->setIncotermCountry("France");
        $offer->setCurrency("EUR");
        $offer->setMerchant($merchant);
        $offer->setManufacturerName('manufacturer name');
        $offer->setMinimumOrderAmount(12);

        $actual = $this->mapper->mapPayloadData(
            $offer,
            1,
            $purchaseRequest,
            $specificPrice,
        );

        $this->assertEquals([
            'izb_product_id' => "1",
            'vendor_reference' => "seller ref",
            'expected_buyer_reference' => 'buyer ref',
            'expected_vendor_reference' => 'manufacturer ref',
            'product_name' => "offer title",
            'product_description' => "short description",
            'unit_price' => 10.0,
            'thresholds' => [],
            'moq' => 1,
            'batch_size' => 2,
            'delivery_delay' => 1,
            'stock' => 1,
            'incoterm' => "incoterm",
            'country_of_delivery' => 'France',
            'currency' => 'EUR',
            'transport_service_availability' => 'no',
            'vat_is_applicable' => 'yes',
            'vendor_name' => 'merchant name',
            'manufacturer_name' => 'manufacturer name',
            'minimum_order_amount' => 12.0,
            'contract_number' => 'frame contract',
            'validity_duration' => new \DateTime('12/12/2022'),
            'vat_rate' => '1%'
        ], $actual);
    }
}
