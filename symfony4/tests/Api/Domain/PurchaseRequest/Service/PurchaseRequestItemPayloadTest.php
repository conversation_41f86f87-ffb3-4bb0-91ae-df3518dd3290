<?php

declare(strict_types=1);

namespace Tests\Api\Domain\PurchaseRequest\Service;

use Api\Domain\PurchaseRequest\Service\PurchaseRequestItemPayload;
use AppBundle\Entity\Company;
use AppBundle\Entity\PurchaseRequest;
use AppBundle\Entity\PurchaseRequestItem;
use AppBundle\Entity\SearchResult;
use AppBundle\Entity\SpecificPrice;
use AppBundle\Entity\User;
use AppBundle\Model\Merchant;
use AppBundle\Model\Offer;
use AppBundle\Repository\PurchaseRequestItemRepository;
use AppBundle\Services\AlstomCustomAttributes;
use AppBundle\Services\BafvService;
use AppBundle\Services\CustomsService;
use AppBundle\Services\PurchaseRequestService;
use AppBundle\Services\SpecificPriceService;
use Doctrine\ORM\EntityManagerInterface;
use Open\IzbergBundle\Api\MerchantApi;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use AppBundle\Entity\Middleware\PurchaseRequestItemPayload as PurchaseRequestItemPayloadEntity;
use Psr\Log\LoggerInterface;
use RuntimeException;

final class PurchaseRequestItemPayloadTest extends TestCase
{
    /**
     * @var PurchaseRequestService|MockObject
     */
    private PurchaseRequestService $purchaseRequestService;

    /**
     * @var SpecificPriceService|MockObject
     */
    private SpecificPriceService $specificPriceService;

    /**
     * @var CustomsService|MockObject
     */
    private CustomsService $customService;

    /**
     * @var EntityManagerInterface|MockObject
     */
    private EntityManagerInterface $em;

    private PurchaseRequestItemPayload $service;

    /**
     * @var MockObject|LoggerInterface
     */
    private LoggerInterface $logger;

    public function setUp(): void
    {
        parent::setUp();

        $this->purchaseRequestService = $this->createMock(PurchaseRequestService::class);
        $this->specificPriceService = $this->createMock(SpecificPriceService::class);
        $this->customService = $this->createMock(CustomsService::class);
        $this->em = $this->createMock(EntityManagerInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->merchantApi = $this->createMock(MerchantApi::class);
        $this->alstomCustomAttributes = $this->createMock(AlstomCustomAttributes::class);
        $this->bafvService = $this->createMock(BafvService::class);

        $this->service = new PurchaseRequestItemPayload(
            $this->purchaseRequestService,
            $this->specificPriceService,
            $this->customService,
            $this->em,
            $this->merchantApi,
            $this->alstomCustomAttributes,
            $this->bafvService
        );
        $this->service->setLogger($this->logger);
    }

    public function testCreateThePurchaseRequestItemPayload(): void
    {
        $company = new Company();
        $company->setName('Company name');
        $user = new User();
        $user->setCompany($company);
        $purchaseRequest = new PurchaseRequest();
        $purchaseRequest->setId(1);
        $purchaseRequest->setUser($user);
        $purchaseRequestItem = new PurchaseRequestItem();
        $purchaseRequestItem->setCartId(1);
        $purchaseRequestItem->setPurchaseRequest($purchaseRequest);
        $purchaseRequestItem->setQuantityExpected(5);
        $purchaseRequest->addItem($purchaseRequestItem);

        $this->em->expects($this->once())
            ->method('find')
            ->willReturn($purchaseRequestItem)
        ;

        $specificPrice = new SpecificPrice();
        $specificPrice->setPrice1(1);
        $specificPrice->setThreshold1(5);
        $this->specificPriceService
            ->expects($this->once())
            ->method('getOfferPrice')
            ->willReturn($specificPrice)
        ;

        $this->customService
            ->expects($this->once())
            ->method('taxRateToApply')
            ->willReturn(19)
        ;

        $merchant = new Merchant();
        $merchant->setName('merchant name');
        $merchant->setId(1);

        $offer = new Offer();
        $offer->setOfferTitle('offer title');
        $offer->setProductId(1);
        $offer->setCurrency('EUR');
        $offer->setPrices(['EUR' => 15]);
        $offer->setThresholds([]);
        $offer->setMoq(1);
        $offer->setIncoterm('incoterm');
        $offer->setMerchant($merchant);
        $offer->setQuantity(6);
        $searchResult = new SearchResult();
        $searchResult->setOffers([$offer]);

        $this->purchaseRequestService
            ->expects($this->once())
            ->method('searchOffers')
            ->willReturn($searchResult)
       ;

        $actual = $this->service->create(1, 2, 'FR');

        $expected = PurchaseRequestItemPayloadEntity::create(1, 2, 20, [], 'companyName');

        $this->assertInstanceOf(PurchaseRequestItemPayloadEntity::class, $actual);
        $this->assertSame(1, $actual->getEntityId());
        $this->assertEquals('companyName', $expected->getUserType());
    }

    public function testCreateThePurchaseRequestItemPayloadWithNotSearchResult(): void
    {
        $company = new Company();
        $company->setName('Company name');
        $user = new User();
        $user->setCompany($company);
        $purchaseRequest = new PurchaseRequest();
        $purchaseRequest->setId(1);
        $purchaseRequest->setUser($user);
        $purchaseRequestItem = new PurchaseRequestItem();
        $purchaseRequestItem->setCartId(1);
        $purchaseRequestItem->setPurchaseRequest($purchaseRequest);
        $purchaseRequestItem->setQuantityExpected(5);
        $purchaseRequest->addItem($purchaseRequestItem);

        $this->em->expects($this->once())
            ->method('find')
            ->willReturn($purchaseRequestItem)
        ;

        $searchResult = new SearchResult();
        $searchResult->setOffers([]);

        $this->purchaseRequestService
            ->expects($this->once())
            ->method('searchOffers')
            ->willReturn($searchResult)
       ;

        $this->assertNull($this->service->create(1, 2, 'FR'));
    }

    public function testCreateThePayloadWithPurchaseRequestItemError(): void
    {
        $this->em->expects($this->once())->method('find')->willReturn(null);

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('purchase request item [id: 1] not fount ! in purchase.request.creation.payload.sending');

        $this->logger->expects($this->once())
            ->method('error')
            ->with(
                $this->callback(fn (string $message) => $message === 'purchase request item [id: 1] not fount ! in purchase.request.creation.payload.sending')
            )
        ;

        $this->service->create(1, 2, 'FR');
    }
}
