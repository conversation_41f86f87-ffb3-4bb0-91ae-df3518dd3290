<?php

declare(strict_types=1);

namespace Tests\Api\Domain\PurchaseRequest\Service;

use Api\Domain\PurchaseRequest\Input\PurchaseRequestItemInput;
use Api\Domain\PurchaseRequest\Service\ConvertPayloadToFileObject;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpKernel\KernelInterface;

final class ConvertPayloadToFileObjectTest extends TestCase
{
    protected function tearDown(): void
    {
        parent::tearDown();

        foreach (glob(__DIR__ . '/tmp_*.csv') as $file) {
            @unlink($file);
        }
    }

    public function testShouldHaveAnUploadedFileObjectWithPayloadData(): void
    {
        $kernel = $this->createMock(KernelInterface::class);
        $kernel->expects($this->any())->method('getCacheDir')->willReturn(__DIR__);
        $convertPayloadService = new ConvertPayloadToFileObject($kernel);

        $purchaseRequestItem = new PurchaseRequestItemInput();
        $purchaseRequestItem->buyerReference = 'buyer_ref';
        $purchaseRequestItem->vendorReference = 'vendor_ref';
        $purchaseRequestItem->productName = 'product_name';
        $purchaseRequestItem->vendorName = 'vendor_name';

        $actual = $convertPayloadService->convert([$purchaseRequestItem]);
        $this->assertInstanceOf(UploadedFile::class, $actual);
        $this->assertEquals(
            file_get_contents(__DIR__ . '/StationOne_Purchase_Request_dummy.csv'),
            file_get_contents(__DIR__ . '/'.$actual->getClientOriginalName())
        );
    }
}
