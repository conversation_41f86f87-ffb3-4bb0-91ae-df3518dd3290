<?php

declare(strict_types=1);

namespace Tests\Api\Domain\PurchaseRequest\Resource;

use ApiPlatform\Core\Bridge\Symfony\Bundle\Test\ApiTestCase;
use AppBundle\Repository\PurchaseRequestItemRepository;
use Tests\Api\Traits\AuthenticateTrait;
use Tests\Api\Traits\FixturesTrait;

final class PurchaseRequestResourceTest extends ApiTestCase
{
    use AuthenticateTrait;
    use FixturesTrait;

    public function testCreatePurchaseRequestSuccessful(): void
    {
        $this->getDatabaseTool()->loadAliceFixture([
            __DIR__ . '/../../Fixtures/ApiUserLoginFixtures.yaml'
        ]);

        $client = $this->createAuthenticatedClient('<EMAIL>');
        $purchaseRequestRepository = static::$container->get(PurchaseRequestItemRepository::class);

        $client->request('POST', '/middleware/api/purchase-request', [
            'headers' => ['Content-Type' => 'application/json'],
            'json' => [
                'purchaseRequestItems' => [
                    [
                        'buyerReference' => "un test",
                        'vendorReference' => "",
                        'quantityRequested' => 0,
                        'vendorName' => "",
                        'productName' => ""
                    ]
                ]
            ]
        ]);


        $this->assertSame(1, $purchaseRequestRepository->count([]));

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(201);
    }

    public function testCreatePurchaseRequestWithBuyerAndVendorReferenceFieldEmpty(): void
    {
        $this->getDatabaseTool()->loadAliceFixture([
            __DIR__ . '/../../Fixtures/ApiUserLoginFixtures.yaml'
        ]);

        $client = $this->createAuthenticatedClient('<EMAIL>');

        $response = $client->request('POST', '/middleware/api/purchase-request', [
            'headers' => ['Content-Type' => 'application/json'],
            'json' => [
                'purchaseRequestItems' => [
                    [
                        'buyerReference' => "",
                        'vendorReference' => "",
                        'quantityRequested' => 0,
                        'vendorName' => "",
                        'productName' => ""
                    ]
                ]
            ]
        ]);

        $this->assertResponseStatusCodeSame(422);
        $this->assertJsonContains([
            '@context' => '/middleware/api/contexts/ConstraintViolationList',
            '@type' => 'ConstraintViolationList',
            'hydra:title' => 'An error occurred',
            'hydra:description' => 'buyerReference.vendorReference: The purchase request properties is mandatory.',
            'violations' =>
                [
                    0 =>
                        [
                            'propertyPath' => 'buyerReference.vendorReference',
                            'message' => 'The purchase request properties is mandatory.',
                            'code' => NULL,
                        ],
                ],
        ]);
    }
}
