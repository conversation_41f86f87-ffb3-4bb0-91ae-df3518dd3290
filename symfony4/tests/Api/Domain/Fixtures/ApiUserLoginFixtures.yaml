AppBundle\Entity\ZipCode:
  zipCode1:
    id: 1
    zipCode: '22200'
    city: 'Lannion'
    label: ''

AppBundle\Entity\Region:
  region1:
    id: 1
    code: 'Bretagne'

AppBundle\Entity\Country:
  country1:
    id: 1
    code: 'france'
    izbFcaCountry: 'France'
    companyIdentRegex: ''
    buyer: 1
    vendor: 1
    locale: 'fr'
    izbergCode: 'FR'
    izbergId: 72
    inEU: true

AppBundle\Entity\Category:
  category1:
    id: 1
    label: test

AppBundle\Entity\Address:
  address1:
    address: 'address test'
    country: '@country1'
  address2:
    address: 'address test 2'
    country: '@country1'

AppBundle\Entity\Contact:
  contact1:
    firstname: '<firstname()>'
    lastname: '<lastname()>'
  contact2:
    firstname: '<firstname()>'
    lastname: '<lastname()>'

AppBundle\Entity\Company:
  company1:
    name: Open
    enabled: 1
    identification: 'FR11500569405'
    izbergEmail: '<EMAIL>'
    izbergUsername: 'open'
    izbergUserId: 1067013
    mainAddress: '@address1'
    cgu: 2
    mainContact: '@contact1'
    category: '@category1'
    endpointUrl: 'http://request-bin:8000/stationone'
  company2:
    name: Open2
    enabled: 1
    identification: 'FR11500569405'
    izbergEmail: '<EMAIL>'
    izbergUsername: 'open2'
    mainAddress: '@address2'
    izbergUserId: 1067013
    cgu: 2
    mainContact: '@contact2'
    category: '@category1'
    endpointUrl: 'http://request-bin:8000/stationone'

AppBundle\Entity\Site:
  site1:
    name: 'Main Cost Center'
    defaultUser: '@user1'
    company: '@company1'
  site2:
    name: 'Main Cost Center 2'
    company: '@company2'


AppBundle\Entity\User:
  user1:
    email: user+1\@email.com
    email_canonical: user+1\@email.com
    password: \$2y\$13\$LvRb6ZGTKhG5h6DSlLEIoe5gnURQosGUoPDppN6BA7eK6ICkttK8u
    roles: [ROLE_API]
    username: '<username()>'
    firstname: '<firstname()>'
    lastname: '<lastname()>'
    username_canonical: user1_canonical
    enabled: 1
    function_: 'admin'
    locale: 'FR'
    civ: 'MR'
    login_attempt: 0
    company: '@company1'
  user2:
    email: user+2\@email.com
    email_canonical: user+2\@email.com
    password: \$2y\$13\$LvRb6ZGTKhG5h6DSlLEIoe5gnURQosGUoPDppN6BA7eK6ICkttK8u
    roles: [ROLE_OPERATOR]
    username: '<username()>'
    firstname: '<firstname()>'
    lastname: '<lastname()>'
    username_canonical: '<username()>'
    enabled: 1
    function_: 'admin'
    locale: 'FR'
    civ: 'MR'
    login_attempt: 0
    company: '@company2'
