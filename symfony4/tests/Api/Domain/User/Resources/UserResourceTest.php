<?php

declare(strict_types=1);

namespace Tests\Api\Domain\User\Resources;

use ApiPlatform\Symfony\Bundle\Test\ApiTestCase;
use Tests\Api\Traits\AuthenticateTrait;
use Tests\Api\Traits\FixturesTrait;

final class UserResourceTest extends ApiTestCase
{
    use AuthenticateTrait;
    use FixturesTrait;

    public function testRetrieveOwnedUserInformation(): void
    {
        $this->getDatabaseTool()->loadAliceFixture([
            __DIR__ . '/UserCompanyFixtures.yaml'
        ]);

        $client = $this->createAuthenticatedClient('<EMAIL>');

        $client->request('GET', '/middleware/api/users/2', [
            'headers' => ['Content-Type' => 'application/json'],
        ]);

        $this->assertResponseIsSuccessful();
        $this->assertJsonContains([
            '@context' => '/middleware/api/contexts/User',
            '@id' => '/middleware/api/users/2',
        ]);
    }

    public function testRetrieveOwnedUserInformationWithNotSameCompany(): void
    {
        $this->getDatabaseTool()->loadAliceFixture([
            __DIR__ . '/UserCompanyFixtures.yaml'
        ]);

        $client = $this->createAuthenticatedClient('<EMAIL>');

        $client->request('GET', '/middleware/api/users/3', [
            'headers' => ['Content-Type' => 'application/json'],
        ]);

        $this->assertResponseStatusCodeSame(404);
    }
}
