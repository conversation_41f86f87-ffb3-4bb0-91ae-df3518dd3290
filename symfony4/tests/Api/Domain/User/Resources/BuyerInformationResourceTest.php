<?php

declare(strict_types=1);

namespace Tests\Api\Domain\User\Resources;

use ApiPlatform\Core\Bridge\Symfony\Bundle\Test\ApiTestCase;
use Open\IzbergBundle\EventSubscriber\AuthenticationSubscriber;
use Tests\Api\Traits\AuthenticateTrait;
use Tests\Api\Traits\FixturesTrait;

final class BuyerInformationResourceTest extends ApiTestCase
{
    use AuthenticateTrait;
    use FixturesTrait;

    protected function setUp(): void
    {
        parent::setUp();
        $container = self::createClient()->getContainer();
        $authSubscriberIzberg = $this->createMock(AuthenticationSubscriber::class);
        $container->set(AuthenticationSubscriber::class, $authSubscriberIzberg);
    }

    public function testGetAllBuyerInformation(): void
    {
        $this->getDatabaseTool()->loadAliceFixture([
            __DIR__ . '/UserCompanyFixtures.yaml'
        ]);

        $client = $this->createAuthenticatedClient('<EMAIL>');

        $client->request('GET', '/middleware/api/buyer-information', [
            'headers' => ['Content-Type' => 'application/json'],
        ]);

        $this->assertResponseIsSuccessful();
    }

    public function testGetIfUserHasNotDefaultSites(): void
    {
        $this->getDatabaseTool()->loadAliceFixture([
            __DIR__ . '/UserCompanyFixtures.yaml'
        ]);

        $client = $this->createAuthenticatedClient('<EMAIL>');

        $client->request('GET', '/middleware/api/buyer-information', [
            'headers' => ['Content-Type' => 'application/json'],
        ]);

        $this->assertResponseIsSuccessful();
    }
}
