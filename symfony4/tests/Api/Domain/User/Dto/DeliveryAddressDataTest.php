<?php

declare(strict_types=1);

namespace Tests\Api\Domain\User\Dto;

use Api\Domain\User\Dto\DeliveryAddressData;
use AppBundle\Entity\Address;
use AppBundle\Entity\ShippingPoint;
use PHPUnit\Framework\TestCase;

final class DeliveryAddressDataTest extends TestCase
{
    public function testCreateDeliveryAddressFromShippingPoints(): void
    {
        $address = new Address();
        $address->setId(1);

        $shippingPoints = new ShippingPoint();
        $shippingPoints->setId(2);
        $shippingPoints->setName('Shipping point');
        $shippingPoints->setDocumentationRequest1('documentationRequest 1');
        $shippingPoints->setDocumentationRequest2('documentationRequest 2');
        $shippingPoints->setAddress($address);

        $expected = DeliveryAddressData::createFromShippingPoints($shippingPoints);

        $this->assertInstanceOf(DeliveryAddressData::class, $expected);
        $this->assertEquals([
            ['name' => 'documentationRequest 1'],
            ['name' => 'documentationRequest 2'],
        ], $expected->documentationRequested);
    }
}
