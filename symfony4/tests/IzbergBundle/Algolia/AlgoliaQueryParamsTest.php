<?php

namespace Tests\IzbergBundle\Algolia;

use Open\IzbergBundle\Algolia\AlgoliaQueryParams;
use PHPUnit\Framework\TestCase;

class AlgoliaQueryParamsTest extends TestCase
{
    /**
     * @dataProvider buildQueryParamsDataProvider
     */
    public function testBuildQueryParams($input, $expected)
    {
        $algoliaQueryParams = new AlgoliaQueryParams();

        foreach($input as [$method, $args]) {
            if (is_array($args)) {
                $algoliaQueryParams->$method(...$args);
            } else {
                $algoliaQueryParams->$method($args);
            }
        }

        $this->assertEquals($expected, $algoliaQueryParams->toArray());
    }

    public function buildQueryParamsDataProvider()
    {
        return [
            'testAddFilters' => [
                [
                    [
                        'addFilters',
                        ['merchant.name:pouf'],
                    ],
                    [
                        'addFilters',
                        ['status:active', 'merchant.name:pouf'],
                    ],
                    [
                        'addFilters',
                        [['truc:muche']],
                    ],
                    [
                        'addFilters',
                        ['and:also', ['blah:blah', 'bi:bi']],
                    ],
                    [
                        'addFilters',
                        ['and:also this that'],
                    ],
                    [
                        'addNotFilters',
                        ['foo:bar'],
                    ],
                ],
                [
                    'filters' => 'merchant.name:"pouf" AND status:"active" AND truc:"muche" AND and:"also" AND (blah:"blah" OR bi:"bi") AND and:"also this that" AND NOT foo:"bar"',
                ]
            ],

            'testAddDisjunctiveFilters' => [
                [
                    [
                        'addFacetFilters',
                        ['status:active'],
                    ],
                    [
                        'addDisjunctiveFilters',
                        ['merchant.name:pouf'],
                    ],
                    [
                        'addDisjunctiveFilters',
                        ['merchant.name:pif', 'merchant.name:paf', 'merchant.name:puf'],
                    ],
                    [
                        'addDisjunctiveFilters',
                        ['merchant.name:pouf'],
                    ],
                ],
                [
                    'facetFilters' => [
                        'status:active',
                        ['merchant.name:pouf', 'merchant.name:pif', 'merchant.name:paf', 'merchant.name:puf'],
                    ],
                ]
            ],

            'testAddNumericFilters' => [
                [
                    [
                        'addNumericFilters',
                        ['pif=100', 'paf>0', ['foo=200', 'bar>250']],
                    ],
                    [
                        'addNumericFilters',
                        ['foo=10', 'puf>0'],
                    ]
                ],
                [
                    'numericFilters' => ['pif=100', 'paf>0', ['foo=200', 'bar>250'], 'foo=10', 'puf>0'],
                ],
            ],
            'testAddFacets' => [
                [
                    [
                        'addFacets',
                        ['pif', 'paf', 'pouf'],
                    ],
                    [
                        'addFacets',
                        ['pif'],
                    ],
                ],
                [
                    'facets' => ['pif', 'paf', 'pouf']
                ],
            ],
            'testAddFacetFilters' => [
                [
                    [
                        'addFacetFilters',
                        ['status:active'],
                    ],
                    [
                        'addFacetFilters',
                        ['name:truc', 'foo:bar', 'why:not'],
                    ],
                    [
                        'addFacetFilters',
                        ['status:active'],
                    ],
                    [
                        'addFacetFilters',
                        [['truc:muche']],
                    ],
                    [
                        'addFacetFilters',
                        ['and:also', ['blah:blah', 'bi:bi']],
                    ],
                ],
                [
                    'facetFilters' => [
                        'status:active',
                        'name:truc',
                        'foo:bar',
                        'why:not',
                        ['truc:muche'],
                        'and:also',
                        ['blah:blah', 'bi:bi']
                    ],
                ]
            ],
            'testAddRestrictSearchableAttributes' => [
                [
                    [
                        'addRestrictSearchableAttributes',
                        ['pouf'],
                    ],
                    [
                        'addRestrictSearchableAttributes',
                        ['pif', 'paf', 'pouf'],
                    ],
                ],
                [
                    'restrictSearchableAttributes' => ['pouf', 'pif', 'paf']
                ]
            ],
            'testAddFacetFilters_AND_AddRestrictSearchableAttributes' => [
                [
                    [
                        'addFacetFilters',
                        ['status:active'],
                    ],
                    [
                        'addFacetFilters',
                        ['name:truc', 'foo:bar', 'why:not'],
                    ],
                    [
                        'addFacetFilters',
                        ['status:active'],
                    ],
                    [
                        'addRestrictSearchableAttributes',
                        ['pif', 'paf', 'pouf'],
                    ],
                    [
                        'addFacetFilters',
                        [['truc:muche']],
                    ],
                    [
                        'addFacetFilters',
                        ['and:also', ['blah:blah', 'bi:bi']],
                    ],
                ],
                [
                    'restrictSearchableAttributes' => ['pif', 'paf', 'pouf'],
                    'facetFilters' => [
                        'status:active',
                        'name:truc',
                        'foo:bar',
                        'why:not',
                        ['truc:muche'],
                        'and:also',
                        ['blah:blah', 'bi:bi']
                    ]
                ]
            ],
            'testAddFacets_AND_offset_And_length' => [
                [
                    [
                        'addFacets',
                        ['pif', 'paf', 'pouf'],
                    ],
                    [
                        'addFacets',
                        ['pif'],
                    ],
                    [
                        'offset',
                        0
                    ],
                    [
                        'length',
                        1
                    ]
                ],
                [
                    'offset' => 0,
                    'length' => 1,
                    'facets' => ['pif', 'paf', 'pouf']
                ],
            ],
            'testAddFacets_AND_typoTolerance_And_offset_And_length_And_orQuery' => [
                [
                    [
                        'addFacets',
                        ['pif', 'paf', 'pouf'],
                    ],
                    [
                        'addFacets',
                        ['pif'],
                    ],
                    [
                        'offset',
                        0
                    ],
                    [
                        'length',
                        1
                    ],
                    [
                        'typoTolerance',
                        false
                    ],
                    [
                        'orQuery',
                        'this is the query'
                    ]
                ],
                [
                    'typoTolerance' => false,
                    'offset' => 0,
                    'length' => 1,
                    'facets' => ['pif', 'paf', 'pouf'],
                    'optionalWords' => 'this is the query',
                ],
            ],
        ];
    }
}
