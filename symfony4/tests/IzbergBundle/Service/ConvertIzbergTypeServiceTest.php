<?php

declare(strict_types=1);

namespace Tests\IzbergBundle\Service;

use Open\IzbergBundle\Dto\AttributeTypeDto;
use Open\IzbergBundle\Service\ConvertIzbergTypeService;
use PHPUnit\Framework\TestCase;

final class ConvertIzbergTypeServiceTest extends TestCase
{
    /**
     * @dataProvider provideIzbergAttributes
     */
    public function testConvertTheIzbergTypeToValidType(mixed $value, string $valueType, mixed $expected): void
    {
        $actual = ConvertIzbergTypeService::convertValue(
            new AttributeTypeDto(value: $value, valueType: $valueType)
        );

        $this->assertSame($expected, $actual);
    }

    public function testWithInvalidType(): void
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage(
            'The Open\IzbergBundle\Service\ConvertIzbergTypeService service can\'t convert the value to unknown type.'
        );

        ConvertIzbergTypeService::convertValue(
            new AttributeTypeDto(value: 'foo', valueType: 'unknown')
        );
    }

    public function provideIzbergAttributes(): iterable
    {
        yield 'Convert to float' => ['10', 'decimal', 10.0];
        yield 'Convert to string with text type' => ['long text', 'text', 'long text'];
        yield 'Convert to string with str type' => ['string', 'str', 'string'];
        yield 'Convert to int' => ['20', 'whale_number', 20];
        yield 'Convert to boolean' => ['1', 'bool', true];
    }
}
