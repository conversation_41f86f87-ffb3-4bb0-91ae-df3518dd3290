<?php
/**
 * Xdebug Verification Script for Station One
 * 
 * This script helps verify that Xdebug is properly configured and working.
 * Access this file through your web browser at: http://localhost/xdebug_verification.php
 */

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Xdebug Verification - Station One</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; border-bottom: 3px solid #007cba; padding-bottom: 10px; }
        h2 { color: #555; margin-top: 30px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        ul { margin: 10px 0; padding-left: 20px; }
        li { margin: 5px 0; }
        code { background-color: #f8f9fa; padding: 2px 4px; border-radius: 3px; font-family: monospace; }
        .breakpoint-test { background-color: #fffbf0; padding: 15px; border: 1px solid #ffd700; border-radius: 4px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐛 Xdebug Verification for Station One</h1>
        
        <h2>📋 Xdebug Status</h2>
        <?php
        if (extension_loaded('xdebug')) {
            echo '<div class="status success">✅ Xdebug extension is loaded</div>';
            echo '<p><strong>Xdebug Version:</strong> ' . phpversion('xdebug') . '</p>';
        } else {
            echo '<div class="status error">❌ Xdebug extension is NOT loaded</div>';
        }
        ?>

        <h2>⚙️ Xdebug Configuration</h2>
        <?php
        if (extension_loaded('xdebug')) {
            $xdebug_info = [
                'xdebug.mode' => ini_get('xdebug.mode'),
                'xdebug.start_with_request' => ini_get('xdebug.start_with_request'),
                'xdebug.client_host' => ini_get('xdebug.client_host'),
                'xdebug.client_port' => ini_get('xdebug.client_port'),
                'xdebug.idekey' => ini_get('xdebug.idekey'),
                'xdebug.log_level' => ini_get('xdebug.log_level'),
                'xdebug.discover_client_host' => ini_get('xdebug.discover_client_host') ? 'On' : 'Off',
            ];

            echo '<ul>';
            foreach ($xdebug_info as $setting => $value) {
                $status_class = 'info';
                if ($setting === 'xdebug.mode' && $value !== 'debug') {
                    $status_class = 'warning';
                }
                if ($setting === 'xdebug.client_host' && empty($value)) {
                    $status_class = 'error';
                }
                echo "<li class=\"status {$status_class}\"><strong>{$setting}:</strong> " . ($value ?: 'not set') . "</li>";
            }
            echo '</ul>';
        }
        ?>

        <h2>🌍 Environment Variables</h2>
        <?php
        $env_vars = [
            'XDEBUG_MODE',
            'XDEBUG_START_WITH_REQUEST',
            'XDEBUG_CLIENT_HOST',
            'XDEBUG_CLIENT_PORT',
            'XDEBUG_IDEKEY',
            'XDEBUG_LOG_LEVEL'
        ];

        echo '<ul>';
        foreach ($env_vars as $env_var) {
            $value = getenv($env_var);
            $status_class = $value !== false ? 'success' : 'warning';
            echo "<li class=\"status {$status_class}\"><strong>{$env_var}:</strong> " . ($value !== false ? $value : 'not set') . "</li>";
        }
        echo '</ul>';
        ?>

        <h2>🔧 Debugging Test</h2>
        <div class="breakpoint-test">
            <p><strong>Breakpoint Test:</strong> Set a breakpoint on the next line in your IDE and reload this page:</p>
            <?php
            $test_variable = "This is a test variable for debugging - " . date('Y-m-d H:i:s');
            $test_array = [
                'name' => 'Station One',
                'framework' => 'Symfony 4',
                'xdebug_version' => extension_loaded('xdebug') ? phpversion('xdebug') : 'Not loaded',
                'timestamp' => time()
            ];
            
            // This line should trigger a breakpoint if debugging is active
            echo "<p><strong>Test Variable:</strong> {$test_variable}</p>";
            echo "<p><strong>Test Array:</strong> " . json_encode($test_array, JSON_PRETTY_PRINT) . "</p>";
            ?>
        </div>

        <h2>📖 IDE Setup Instructions</h2>
        <div class="info">
            <h3>PhpStorm Setup:</h3>
            <ol>
                <li>Go to <strong>File → Settings → PHP → Debug</strong></li>
                <li>Set <strong>Xdebug port</strong> to <code>9003</code></li>
                <li>Check <strong>"Can accept external connections"</strong></li>
                <li>Go to <strong>PHP → Servers</strong></li>
                <li>Add a new server:
                    <ul>
                        <li><strong>Name:</strong> <code>station-one</code></li>
                        <li><strong>Host:</strong> <code>localhost</code></li>
                        <li><strong>Port:</strong> <code>80</code></li>
                        <li><strong>Debugger:</strong> <code>Xdebug</code></li>
                        <li>Check <strong>"Use path mappings"</strong></li>
                        <li>Map your local <code>symfony4</code> folder to <code>/var/www/symfony4</code></li>
                    </ul>
                </li>
                <li>Start listening for debug connections (phone icon in toolbar)</li>
                <li>Set a breakpoint in this file and reload the page</li>
            </ol>
        </div>

        <h2>🚀 Quick Test Commands</h2>
        <div class="info">
            <p>Run these commands to test Xdebug from the command line:</p>
            <ul>
                <li><code>make symfony4.console</code> - Access the PHP container</li>
                <li><code>php -v</code> - Check PHP version and Xdebug</li>
                <li><code>php -m | grep xdebug</code> - Verify Xdebug is loaded</li>
                <li><code>env | grep XDEBUG</code> - Check environment variables</li>
            </ul>
        </div>

        <h2>🔍 Troubleshooting</h2>
        <div class="warning">
            <p>If debugging is not working:</p>
            <ul>
                <li>Ensure your IDE is listening on port <strong>9003</strong></li>
                <li>Check that <code>host.docker.internal</code> resolves to your host machine</li>
                <li>Verify firewall settings allow connections on port 9003</li>
                <li>Enable Xdebug logging by setting <code>XDEBUG_LOG_LEVEL=7</code> in <code>.dev-env/docker-compose/.env</code></li>
                <li>Restart containers after configuration changes: <code>make stop && make start</code></li>
            </ul>
        </div>

        <p><em>Generated on <?php echo date('Y-m-d H:i:s'); ?></em></p>
    </div>
</body>
</html>
