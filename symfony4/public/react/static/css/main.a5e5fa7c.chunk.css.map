{"version": 3, "sources": ["navbar.css", "App.css", "index.css"], "names": [], "mappings": "AAAA,oCACE,WAAY,CACZ,wEAEF,CAEA,MACE,aAAc,CACd,cAAe,CACf,gBAAiB,CACjB,eAAgB,CAEhB,WAAoC,CAApC,mCAAoC,CACpC,eAAiB,CACjB,kBACF,CACA,YACE,cAAe,CACf,2BACF,CCnBA,GACE,UAAW,CACX,QAAS,CACT,WAAY,CACZ,aAAc,CACd,gCACF,CAEA,QACE,aACF,CAEA,YACE,eAAiB,CACjB,aACF,CAEA,kBACE,eAAmB,CACnB,cACF,CCpBA,KACE,QAAS,CACT,SAAU,CACV,4BAAgC,CAChC,kBACF", "file": "main.a5e5fa7c.chunk.css", "sourcesContent": [".MuiAppBar-root.MuiPaper-elevation4 {\n  height: 70px;\n  box-shadow: 0 4px 5px -2px rgba(0, 0, 0, 0.22),\n    0 2px 1px -1px rgba(0, 0, 0, 0.05);\n}\n\n.menu {\n  color: #8038b1;\n  padding: 0 15px;\n  line-height: 70px;\n  background: none;\n  border: none;\n  border-bottom: 1px solid transparent;\n  font-weight: bold;\n  font-family: \"Roboto\";\n}\n.menu:hover {\n  cursor: pointer;\n  border-bottom-color: #6e7285;\n}\n", "hr {\n  height: 1px;\n  margin: 0;\n  border: none;\n  flex-shrink: 0;\n  background-color: rgba(0, 0, 0, 0.12);\n}\n\n.scroll {\n  overflow: auto;\n}\n\n.titleTable {\n  font-weight: bold;\n  line-height: 1;\n}\n\n.titleTable small {\n  font-weight: normal;\n  font-size: 12px;\n}\n", "body {\n  margin: 0;\n  padding: 0;\n  font-family: \"Roboto\" !important;\n  background: #f4f5fa;\n}\n"]}