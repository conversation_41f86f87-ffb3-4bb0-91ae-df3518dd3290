{"version": 3, "sources": ["codemirror.css"], "names": [], "mappings": "AAEA,YAEE,qBAAsB,CACtB,YAAa,CACb,UAAY,CACZ,aACF,CAIA,kBACE,aACF,CACA,qEAEE,aACF,CAEA,uDACE,qBACF,CAIA,oBACE,2BAA4B,CAC5B,wBAAyB,CACzB,kBACF,CAEA,uBACE,mBAAoB,CACpB,cAAe,CACf,gBAAiB,CACjB,UAAW,CACX,kBACF,CAEA,yBAA2B,UAAc,CACzC,gCAAkC,UAAa,CAI/C,mBACE,0BAA4B,CAC5B,iBAAkB,CAClB,OACF,CAEA,2CACE,4BACF,CACA,kCACE,UAAW,CACX,kBAAoB,CACpB,eACF,CACA,sCACE,SACF,CACA,oBACE,mCAIF,CACA,2CAJE,+CAAgD,CAEhD,uCASF,CAPA,uBACE,UAAW,CACX,QAAS,CAIT,qBACF,CAMA,yBAEE,IAAM,4BAA+B,CAEvC,CACA,iBAEE,IAAM,4BAA+B,CAEvC,CAKA,QAAU,oBAAqB,CAAE,uBAA0B,CAE3D,mBACE,iBAAkB,CAClB,MAAO,CAAE,OAAQ,CAAE,SAAU,CAAE,QAAS,CACxC,eACF,CACA,kBACE,0BAA2B,CAC3B,KAAM,CAAE,QAAS,CACjB,iBACF,CAIA,yBAA0B,UAAY,CACtC,wBAAyB,UAAY,CACrC,aAAc,UAAY,CAC1B,aAAc,UAAY,CAC1B,sBAAwB,eAAkB,CAC1C,OAAQ,iBAAmB,CAC3B,SAAU,yBAA2B,CACrC,kBAAmB,4BAA8B,CAEjD,0BAA2B,UAAY,CACvC,uBAAwB,UAAY,CACpC,yBAA0B,UAAY,CACtC,sBAAuB,UAAY,CAKnC,6BAA8B,UAAY,CAC1C,oDAAsD,UAAY,CAClE,0BAA2B,UAAY,CACvC,yBAA0B,UAAY,CACtC,2BAA4B,UAAY,CAExC,mDAA6B,UAAY,CACzC,0BAA2B,UAAY,CACvC,0BAA2B,UAAY,CACvC,sBAAuB,UAAY,CACnC,4BAA6B,UAAY,CACzC,qBAAsB,UAAY,CAClC,uBAAwB,UAAY,CAGpC,wCAAiB,SAAY,CAE7B,sBAAwB,uBAA0B,CAIlD,+CAAgD,UAAY,CAC5D,kDAAmD,UAAY,CAC/D,wBAA0B,6BAAmC,CAC7D,kCAAmC,kBAAoB,CAOvD,YACE,iBAAkB,CAClB,eAAgB,CAChB,eACF,CAEA,mBACE,yBAA2B,CAG3B,mBAAoB,CAAE,kBAAmB,CACzC,mBAAoB,CACpB,WAAY,CACZ,YAAa,CACb,iBACF,CACA,kBACE,iBAAkB,CAClB,mCACF,CAKA,qGACE,iBAAkB,CAClB,SAAU,CACV,YACF,CACA,uBACE,OAAQ,CAAE,KAAM,CAChB,iBAAkB,CAClB,iBACF,CACA,uBACE,QAAS,CAAE,MAAO,CAClB,iBAAkB,CAClB,iBACF,CACA,6BACE,OAAQ,CAAE,QACZ,CACA,0BACE,MAAO,CAAE,QACX,CAEA,oBACE,iBAAkB,CAAE,MAAO,CAAE,KAAM,CACnC,eAAgB,CAChB,SACF,CACA,mBACE,kBAAmB,CACnB,WAAY,CACZ,oBAAqB,CACrB,kBAAmB,CACnB,mBACF,CACA,2BACE,iBAAkB,CAClB,SAAU,CACV,yBAA2B,CAC3B,qBACF,CACA,8BACE,iBAAkB,CAClB,KAAM,CAAE,QAAS,CACjB,SACF,CACA,uBACE,iBAAkB,CAClB,cAAe,CACf,SACF,CACA,uCAAyC,4BAA8B,CACvE,4CAA8C,4BAA8B,CAE5E,kBACE,WAAY,CACZ,cACF,CACA,qEAGmD,eAAgB,CACjE,cAAe,CACf,sBAAuB,CACvB,mBAAoB,CACpB,iBAAkB,CAClB,QAAS,CACT,eAAgB,CAChB,gBAAiB,CACjB,mBAAoB,CACpB,aAAc,CACd,SAAU,CACV,iBAAkB,CAClB,gBAAiB,CACjB,uCAAwC,CACxC,yCAA0C,CAC1C,4BAAkC,CAAlC,iCACF,CACA,+EAEE,oBAAqB,CACrB,oBAAqB,CACrB,iBACF,CAEA,2BACE,iBAAkB,CAClB,MAAO,CAAE,OAAQ,CAAE,KAAM,CAAE,QAAS,CACpC,SACF,CAEA,uBACE,iBAAkB,CAClB,SAAU,CACV,YACF,CAIA,oBAAsB,aAAgB,CAEtC,iBACE,YACF,CAGA,mGAME,sBACF,CAEA,oBACE,iBAAkB,CAClB,UAAW,CACX,QAAS,CACT,eAAgB,CAChB,iBACF,CAEA,mBACE,iBAAkB,CAClB,mBACF,CACA,wBAA0B,eAAkB,CAE5C,uBACE,iBAAkB,CAClB,iBAAkB,CAClB,SACF,CAKA,sEACE,kBACF,CAEA,qBAAuB,kBAAqB,CAC5C,yCAA2C,kBAAqB,CAChE,sBAAwB,gBAAmB,CAC3C,mGAA6G,kBAAqB,CAClI,kHAA4H,kBAAqB,CAEjJ,cACE,qBAAsB,CACtB,mCACF,CAGA,iBAAmB,kBAAqB,CAExC,aAEE,mCACE,iBACF,CACF,CAGA,wBAA0B,UAAa,CAGvC,6BAA+B,eAAkB", "file": "2.7fd08c2a.chunk.css", "sourcesContent": ["/* BASICS */\n\n.CodeMirror {\n  /* Set height, width, borders, and global font properties here */\n  font-family: monospace;\n  height: 300px;\n  color: black;\n  direction: ltr;\n}\n\n/* PADDING */\n\n.CodeMirror-lines {\n  padding: 4px 0; /* Vertical padding around content */\n}\n.CodeMirror pre.CodeMirror-line,\n.CodeMirror pre.CodeMirror-line-like {\n  padding: 0 4px; /* Horizontal padding of content */\n}\n\n.CodeMirror-scrollbar-filler, .CodeMirror-gutter-filler {\n  background-color: white; /* The little square between H and V scrollbars */\n}\n\n/* GUTTER */\n\n.CodeMirror-gutters {\n  border-right: 1px solid #ddd;\n  background-color: #f7f7f7;\n  white-space: nowrap;\n}\n.CodeMirror-linenumbers {}\n.CodeMirror-linenumber {\n  padding: 0 3px 0 5px;\n  min-width: 20px;\n  text-align: right;\n  color: #999;\n  white-space: nowrap;\n}\n\n.CodeMirror-guttermarker { color: black; }\n.CodeMirror-guttermarker-subtle { color: #999; }\n\n/* CURSOR */\n\n.CodeMirror-cursor {\n  border-left: 1px solid black;\n  border-right: none;\n  width: 0;\n}\n/* Shown when moving in bi-directional text */\n.CodeMirror div.CodeMirror-secondarycursor {\n  border-left: 1px solid silver;\n}\n.cm-fat-cursor .CodeMirror-cursor {\n  width: auto;\n  border: 0 !important;\n  background: #7e7;\n}\n.cm-fat-cursor div.CodeMirror-cursors {\n  z-index: 1;\n}\n.cm-fat-cursor-mark {\n  background-color: rgba(20, 255, 20, 0.5);\n  -webkit-animation: blink 1.06s steps(1) infinite;\n  -moz-animation: blink 1.06s steps(1) infinite;\n  animation: blink 1.06s steps(1) infinite;\n}\n.cm-animate-fat-cursor {\n  width: auto;\n  border: 0;\n  -webkit-animation: blink 1.06s steps(1) infinite;\n  -moz-animation: blink 1.06s steps(1) infinite;\n  animation: blink 1.06s steps(1) infinite;\n  background-color: #7e7;\n}\n@-moz-keyframes blink {\n  0% {}\n  50% { background-color: transparent; }\n  100% {}\n}\n@-webkit-keyframes blink {\n  0% {}\n  50% { background-color: transparent; }\n  100% {}\n}\n@keyframes blink {\n  0% {}\n  50% { background-color: transparent; }\n  100% {}\n}\n\n/* Can style cursor different in overwrite (non-insert) mode */\n.CodeMirror-overwrite .CodeMirror-cursor {}\n\n.cm-tab { display: inline-block; text-decoration: inherit; }\n\n.CodeMirror-rulers {\n  position: absolute;\n  left: 0; right: 0; top: -50px; bottom: 0;\n  overflow: hidden;\n}\n.CodeMirror-ruler {\n  border-left: 1px solid #ccc;\n  top: 0; bottom: 0;\n  position: absolute;\n}\n\n/* DEFAULT THEME */\n\n.cm-s-default .cm-header {color: blue;}\n.cm-s-default .cm-quote {color: #090;}\n.cm-negative {color: #d44;}\n.cm-positive {color: #292;}\n.cm-header, .cm-strong {font-weight: bold;}\n.cm-em {font-style: italic;}\n.cm-link {text-decoration: underline;}\n.cm-strikethrough {text-decoration: line-through;}\n\n.cm-s-default .cm-keyword {color: #708;}\n.cm-s-default .cm-atom {color: #219;}\n.cm-s-default .cm-number {color: #164;}\n.cm-s-default .cm-def {color: #00f;}\n.cm-s-default .cm-variable,\n.cm-s-default .cm-punctuation,\n.cm-s-default .cm-property,\n.cm-s-default .cm-operator {}\n.cm-s-default .cm-variable-2 {color: #05a;}\n.cm-s-default .cm-variable-3, .cm-s-default .cm-type {color: #085;}\n.cm-s-default .cm-comment {color: #a50;}\n.cm-s-default .cm-string {color: #a11;}\n.cm-s-default .cm-string-2 {color: #f50;}\n.cm-s-default .cm-meta {color: #555;}\n.cm-s-default .cm-qualifier {color: #555;}\n.cm-s-default .cm-builtin {color: #30a;}\n.cm-s-default .cm-bracket {color: #997;}\n.cm-s-default .cm-tag {color: #170;}\n.cm-s-default .cm-attribute {color: #00c;}\n.cm-s-default .cm-hr {color: #999;}\n.cm-s-default .cm-link {color: #00c;}\n\n.cm-s-default .cm-error {color: #f00;}\n.cm-invalidchar {color: #f00;}\n\n.CodeMirror-composing { border-bottom: 2px solid; }\n\n/* Default styles for common addons */\n\ndiv.CodeMirror span.CodeMirror-matchingbracket {color: #0b0;}\ndiv.CodeMirror span.CodeMirror-nonmatchingbracket {color: #a22;}\n.CodeMirror-matchingtag { background: rgba(255, 150, 0, .3); }\n.CodeMirror-activeline-background {background: #e8f2ff;}\n\n/* STOP */\n\n/* The rest of this file contains styles related to the mechanics of\n   the editor. You probably shouldn't touch them. */\n\n.CodeMirror {\n  position: relative;\n  overflow: hidden;\n  background: white;\n}\n\n.CodeMirror-scroll {\n  overflow: scroll !important; /* Things will break if this is overridden */\n  /* 30px is the magic margin used to hide the element's real scrollbars */\n  /* See overflow: hidden in .CodeMirror */\n  margin-bottom: -30px; margin-right: -30px;\n  padding-bottom: 30px;\n  height: 100%;\n  outline: none; /* Prevent dragging from highlighting the element */\n  position: relative;\n}\n.CodeMirror-sizer {\n  position: relative;\n  border-right: 30px solid transparent;\n}\n\n/* The fake, visible scrollbars. Used to force redraw during scrolling\n   before actual scrolling happens, thus preventing shaking and\n   flickering artifacts. */\n.CodeMirror-vscrollbar, .CodeMirror-hscrollbar, .CodeMirror-scrollbar-filler, .CodeMirror-gutter-filler {\n  position: absolute;\n  z-index: 6;\n  display: none;\n}\n.CodeMirror-vscrollbar {\n  right: 0; top: 0;\n  overflow-x: hidden;\n  overflow-y: scroll;\n}\n.CodeMirror-hscrollbar {\n  bottom: 0; left: 0;\n  overflow-y: hidden;\n  overflow-x: scroll;\n}\n.CodeMirror-scrollbar-filler {\n  right: 0; bottom: 0;\n}\n.CodeMirror-gutter-filler {\n  left: 0; bottom: 0;\n}\n\n.CodeMirror-gutters {\n  position: absolute; left: 0; top: 0;\n  min-height: 100%;\n  z-index: 3;\n}\n.CodeMirror-gutter {\n  white-space: normal;\n  height: 100%;\n  display: inline-block;\n  vertical-align: top;\n  margin-bottom: -30px;\n}\n.CodeMirror-gutter-wrapper {\n  position: absolute;\n  z-index: 4;\n  background: none !important;\n  border: none !important;\n}\n.CodeMirror-gutter-background {\n  position: absolute;\n  top: 0; bottom: 0;\n  z-index: 4;\n}\n.CodeMirror-gutter-elt {\n  position: absolute;\n  cursor: default;\n  z-index: 4;\n}\n.CodeMirror-gutter-wrapper ::selection { background-color: transparent }\n.CodeMirror-gutter-wrapper ::-moz-selection { background-color: transparent }\n\n.CodeMirror-lines {\n  cursor: text;\n  min-height: 1px; /* prevents collapsing before first draw */\n}\n.CodeMirror pre.CodeMirror-line,\n.CodeMirror pre.CodeMirror-line-like {\n  /* Reset some styles that the rest of the page might have set */\n  -moz-border-radius: 0; -webkit-border-radius: 0; border-radius: 0;\n  border-width: 0;\n  background: transparent;\n  font-family: inherit;\n  font-size: inherit;\n  margin: 0;\n  white-space: pre;\n  word-wrap: normal;\n  line-height: inherit;\n  color: inherit;\n  z-index: 2;\n  position: relative;\n  overflow: visible;\n  -webkit-tap-highlight-color: transparent;\n  -webkit-font-variant-ligatures: contextual;\n  font-variant-ligatures: contextual;\n}\n.CodeMirror-wrap pre.CodeMirror-line,\n.CodeMirror-wrap pre.CodeMirror-line-like {\n  word-wrap: break-word;\n  white-space: pre-wrap;\n  word-break: normal;\n}\n\n.CodeMirror-linebackground {\n  position: absolute;\n  left: 0; right: 0; top: 0; bottom: 0;\n  z-index: 0;\n}\n\n.CodeMirror-linewidget {\n  position: relative;\n  z-index: 2;\n  padding: 0.1px; /* Force widget margins to stay inside of the container */\n}\n\n.CodeMirror-widget {}\n\n.CodeMirror-rtl pre { direction: rtl; }\n\n.CodeMirror-code {\n  outline: none;\n}\n\n/* Force content-box sizing for the elements where we expect it */\n.CodeMirror-scroll,\n.CodeMirror-sizer,\n.CodeMirror-gutter,\n.CodeMirror-gutters,\n.CodeMirror-linenumber {\n  -moz-box-sizing: content-box;\n  box-sizing: content-box;\n}\n\n.CodeMirror-measure {\n  position: absolute;\n  width: 100%;\n  height: 0;\n  overflow: hidden;\n  visibility: hidden;\n}\n\n.CodeMirror-cursor {\n  position: absolute;\n  pointer-events: none;\n}\n.CodeMirror-measure pre { position: static; }\n\ndiv.CodeMirror-cursors {\n  visibility: hidden;\n  position: relative;\n  z-index: 3;\n}\ndiv.CodeMirror-dragcursors {\n  visibility: visible;\n}\n\n.CodeMirror-focused div.CodeMirror-cursors {\n  visibility: visible;\n}\n\n.CodeMirror-selected { background: #d9d9d9; }\n.CodeMirror-focused .CodeMirror-selected { background: #d7d4f0; }\n.CodeMirror-crosshair { cursor: crosshair; }\n.CodeMirror-line::selection, .CodeMirror-line > span::selection, .CodeMirror-line > span > span::selection { background: #d7d4f0; }\n.CodeMirror-line::-moz-selection, .CodeMirror-line > span::-moz-selection, .CodeMirror-line > span > span::-moz-selection { background: #d7d4f0; }\n\n.cm-searching {\n  background-color: #ffa;\n  background-color: rgba(255, 255, 0, .4);\n}\n\n/* Used to force a border model for a node */\n.cm-force-border { padding-right: .1px; }\n\n@media print {\n  /* Hide the cursor when printing */\n  .CodeMirror div.CodeMirror-cursors {\n    visibility: hidden;\n  }\n}\n\n/* See issue #2901 */\n.cm-tab-wrap-hack:after { content: ''; }\n\n/* Help users use markselection to safely style text background */\nspan.CodeMirror-selectedtext { background: none; }\n"]}