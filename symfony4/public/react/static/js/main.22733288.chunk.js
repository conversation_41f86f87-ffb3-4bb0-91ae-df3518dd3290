(window.webpackJsonpbafv=window.webpackJsonpbafv||[]).push([[0],{5989:function(e){e.exports=JSON.parse('{"home":"Dashboard","orders":"Orders","catalog":"Catalog","messages":"Messages","settings":"Settings","Withdrawals":"Withdrawals","Transactions":"Transactions","Balances":"Balances","logistic":"Logistic","invoices":"Invoices","credit":"Credit","Refunds":"Refunds","offers":"Offers","addOffers":"Create an offer","gallery":"Gallery","import":"Import","priceFile":"Dedicated Price File Template","inbox":"Inbox","guide":"Vendor User Guide","terms":"Station One General terms of use","agreement":"Payment Service Agreement","toolbox":"Vendor Communication Toolbox","profile":"Profile","company":"Company","documents":"Documents","permissions":"Permissions","account":"Account","logout":"Logout","title":"Buyer Account for Vendor permissions","subtitle":"Manage buyer account for vendors permissions to access your catalog prices","Name":"Company Code","Enterprise":"Company Name","Create date":"Request submitted on","Status":"Status","Action":"Action","All":"All","Pending":"Pending","Accepted":"Accepted","Rejected":"Rejected","Reject":"Reject","Accept":"Accept","Status modification":"Status modification","Are you sure you want to change the status":"Are you sure you want to change the status ?","Yes":"Yes","No":"No","buyer management":"Buyer management","specific prices":"Specific prices","specificPricesDashboard":{"title":"Specific prices management","export":{"row":"Export","all":"Export all"},"fileUpload":{"description":"Click <a href=\'{{fileTemplateLink}}\'>here</a> to download the specific price file. <br/><br/>To apply a specific price for a given buyer, add his company code in the \\"Buyer Identification Number\\" column (available upon request to StationOne), then enter the concerned product information in Vendor Reference, Incoterm and Country columns. Finally, enter the new price to apply in the \\"Unit Price\\" column. <br/><br/>To remove all specific prices applied to a company, import the specific prices file and only populate the \u201cBuyer Identification Number\u201d on a single line. <br/>To delete one/several specific prices, load the specific prices file by populating the \u201cBuyer Identification Number\u201d of the buyer and the following information : Vendor reference, Incoterm and Country. All other columns remain empty.<br/><br/>To set up the validity date for each specific price, the format of the date has to be DD/MM/YYYY such as \\"02/01/2030\\" for 2nd of January 2030<br/><br/>In the table below, you can see specific prices status of your buyers on StationOne.","action":"Import"},"tableItems":{"title":"There are {{productsCount}} products with specific prices applied for {{buyersCount}} buyers","columns":{"companyCode":"Company Code","companyName":"Company Name","lastUpload":"Last update","nbProduct":"Number of products","action":"Action"}}},"catalogPage":{"title":"Export catalog management","sub_title":"Catalog export criteria","export":"Export","label":{"language":"Choose the language","status":"Choose the Status"},"language":"Language","status":"Status","draft":"draft","active":"active","inactive":"inactive","description":"To export your catalog to an Excel file (.csv), please select the catalog language and status of the offers which you would like to export.<br><br>From this file, you can select the offers (rows) and the attributes (columns) that you want to modify.<br>Be careful not to modify or delete the first row (StationOne keys) and the first column (sku of your offers) in the exported file.<br>Once the modifications have been made, you can follow the process of import of a new catalog and select \\"Update only\\".<br><br>Example: Updating your prices<br>1) Retrieve only the \\"sku\\" and \\"price\\" columns from the exported file<br>2) Modify the prices in the Excel file and save the file on your computer (in .csv)<br>3) Catalog / Import a feed, \\"Create\\" a new import and follow the import instructions by selecting \\"update only\\""},"language":{"fr":"French","en":"English","es":"Spain","it":"Italian","de":"Deutsch"}}')},5990:function(e){e.exports=JSON.parse('{"home":"Tableau de bord","orders":"Commandes","catalog":"Catalogue","messages":"Messages","settings":"Param\xe8tres","Withdrawals":"Retraits","Transactions":"Transactions","Balances":"Balances","logistic":"Logistique","invoices":"Factures","credit":"Avoirs","Refunds":"Remboursements","offers":"Offres","addOffers":"Cr\xe9er une offre","gallery":"Galerie","import":"Import","priceFile":"Template du fichier des prix sp\xe9cifiques","inbox":"Bo\xeete de r\xe9ception","guide":"Guide d\'utilisation vendeur","terms":"Conditions G\xe9n\xe9rales d\'utilisation de Station One","agreement":"Contrat Service de Paiement","toolbox":"Kit de communication vendeur","profile":"Profil de l\'entreprise","company":"Informations de l\'entreprise","documents":"Documents","permissions":"Permissions","account":"Mon compte","logout":"Se d\xe9connecter","title":"Permissions des comptes acheteurs pour les vendeurs","subtitle":"G\xe9rez les autorisations des comptes acheteurs pour les vendeurs pour acc\xe9der aux prix de vos catalogues","Name":"Code de la soci\xe9t\xe9","Enterprise":"Nom de la soci\xe9t\xe9","Create date":"Demande soumise le","Status":"Statut","Action":"Action","All":"Tout","Pending":"En attente","Accepted":"Accept\xe9e","Rejected":"Rejet\xe9e","Reject":"Rejeter","Accept":"Accepter","Status modification":"Modification du statut","Are you sure you want to change the status":"\xcates-vous s\xfbr(e) de vouloir modifier le statut ?","Yes":"Oui","No":"Non","specific prices":"Prix sp\xe9cifiques","specificPricesDashboard":{"title":"Gestion des prix sp\xe9cifiques","export":{"row":"Exporter","all":"Exporter tous"},"fileUpload":{"description":"Cliquez <a href=\'{{fileTemplateLink}}\'>ici</a> pour t\xe9l\xe9charger le fichier des prix sp\xe9cifiques. <br/><br/>Afin d\'appliquer un prix sp\xe9cifique pour un acheteur, entrez son num\xe9ro de TVA (disponible sur demande aupr\xe8s de StationOne) dans la colonne \\"Buyer Identification Number\\", puis entrez les informations relatives au produit dans les colonnes Vendor reference, Incoterm et Country. Entrez finalement le prix sp\xe9cifique \xe0 appliquer pour cet acheteur dans la colonne \\"Unit Price\\". <br/><br/>Pour retirer l\u2019ensemble des prix sp\xe9cifiques d\u2019un acheteur, il faut charger le fichier d\u2019import des prix sp\xe9cifiques en renseignant uniquement le num\xe9ro de TVA de l\u2019acheteur sur 1 ligne.<br/>Pour supprimer un/plusieurs prix sp\xe9cifique(s), il faut charger le fichier d\u2019import des prix<br/><br/>Pour d\xe9finir la date de validit\xe9 de chaque prix sp\xe9cifique, le format de la date doit \xeatre JJ / MM / AAAA tel que \\"02/01/2030\\" pour le 2 Janvier 2030","action":"Importer"},"tableItems":{"title":"{{productsCount}} offres ont un prix sp\xe9cifique appliqu\xe9 pour {{buyersCount}} acheteurs","columns":{"companyCode":"Code TVA de la soci\xe9t\xe9","companyName":"Nom de la soci\xe9t\xe9","lastUpload":"Derni\xe8re mise \xe0 jour","nbProduct":"Nombre d\'offres avec un prix sp\xe9cifique","action":"Action"}}},"catalogPage":{"title":"Export catalog management","sub_title":"Crit\xe8res d\'exportation du catalogue","export":"Export","label":{"language":"Choisissez la langue","status":"Choisissez le statut"},"language":"Langue","status":"Statut","draft":"Brouillon","active":"Actif","inactive":"Inactif","description":"Pour exporter votre catalogue vers un fichier Excel (.csv), veuillez s\xe9lectionner la langue du catalogue et le statut des offres que vous souhaitez exporter.<br><br>A partir de ce fichier, vous pouvez s\xe9lectionner les offres (lignes) et les attributs (colonnes) que vous souhaitez modifier.<br>Attention \xe0 ne pas modifier ou supprimer la premi\xe8re ligne (cl\xe9s StationOne) et la premi\xe8re colonne (sku de vos offres) dans le fichier export\xe9.<br>Une fois les modifications effectu\xe9es, vous pouvez suivre le processus d\'importation d\'un nouveau catalogue et s\xe9lectionner \\"Mettre \xe0 jour uniquement\\".<br><br>Exemple: mise \xe0 jour de vos prix<br>1) R\xe9cup\xe9rer uniquement les colonnes \\"sku\\" et \\"price\\" du fichier export\xe9<br>2) Modifiez les prix dans le fichier Excel et enregistrez le fichier sur votre ordinateur (en .csv)<br>3) Cataloguer / Importer un flux, \\"Cr\xe9er\\" une nouvelle importation et suivre les instructions d\'importation en s\xe9lectionnant \\"Mettre \xe0 jour uniquement\\""},"language":{"fr":"French","en":"English","es":"Spain","it":"Italian","de":"Deutsch"}}')},5991:function(e){e.exports=JSON.parse('{"home":"Dashboard","orders":"Ordini","catalog":"Catalogo","messages":"Messagi","settings":"Parametri","Withdrawals":"Trasferimenti di denaro","Transactions":"Transazioni","Balances":"Saldi","logistic":"Logistica","invoices":"Fatturas","credit":"Fatturas da avere","Refunds":"Rimborsi","offers":"Offerte","addOffers":"Crea una offerta","gallery":"Galleria","import":"Importare uno Feed","priceFile":"Dedicated Price File template","inbox":"Inbox","guide":"Vendor User Guide","terms":"Termini e condizioni d\'uso di Station One","agreement":"Accordo sul servizio di pagamento","toolbox":"Kit di comunicazione del venditore","profile":"Profilo","company":"Societa","documents":"Documenti","permissions":"Autorizzazione","account":"Account","logout":"Disconnettersi","title":"Autorizzazioni account acquirente per venditori","subtitle":"Gestisci le autorizzazioni dell\'account acquirente per i venditori per accedere ai prezzi dei tuoi cataloghi","Name":"Codice dell\'azienda","Enterprise":"Nome dell\'azienda","Create date":"Domanda presentata il","Status":"Status","Action":"Azione","All":"Tutte","Pending":"In attesa di","Accepted":"Accettato","Rejected":"Respinto","Reject":"Rifiutare","Accept":"Accettare","Status modification":"Modifica dello stato","Are you sure you want to change the status":"Sei sicuro di voler cambiare lo stato?","Yes":"S\xec","No":"No","buyer management":"Gestione dell\'acquirente","specific prices":"Prezzi specifici","specificPricesDashboard":{"title":"Gestione di prezzi specifici","export":{"row":"Esportare","all":"Esporta tutto"},"fileUpload":{"description":"Clicca <a href=\'{{fileTemplateLink}}\'>qui</a> per scaricare il file di prezzo specifico.<br/><br/>Per applicare un prezzo specifico per un acquirente, inserisci il suo numero di partita IVA (disponibile su richiesta presso StationOne) nella colonna \\"Buyer Identification Number\\", quindi inserisci le informazioni sul prodotto nel Vendor Reference, Incoterm e Country. Inserisci infine il prezzo specifico da applicare per questo acquirente nella colonna \\"Unit Price\\".<br/><br/>Per ritirare tutti i prezzi specifici di un acquirente, \xe8 necessario caricare il file di importazione di prezzi specifici inserendo il Codice dell\'azienda su 1 riga.<br/>Per eliminare uno / pi\xf9 prezzi specifici, \xe8 necessario caricare il file di importazione di prezzi specifici inserendo il Codice dell\'azienda e indicando le seguenti informazioni: Vendor Reference, Incoterm e Country \\". Le altre colonne devono rimanere vuote.<br/><br/>Per impostare la data di validit\xe0 per ogni prezzo specifico, il formato della data deve essere GG / MM / AAAA come \\"02/01/2030\\" per il 2 Gennaio 2030<br/><br/>Nella tabella seguente, puoi vedere lo stato dei prezzi specifici applicati ai tuoi acquirenti su StationOne.","action":"Importare"},"tableItems":{"title":"{{productsCount}} prodotti hanno un prezzo specifico applicato agli {{buyersCount}} acquirenti","columns":{"companyCode":"Codice dell\'azienda","companyName":"Nome dell\'azienda","lastUpload":"Ultimo aggiornamento","nbProduct":"Quantit\xe0 di prodotti","action":"Azione"}}},"catalogPage":{"title":"Export catalog management","sub_title":"Criteri di esportazione del catalogo","export":"Export","label":{"language":"Scegli la lingua","status":"Scegli lo stato"},"language":"Linguaggio","status":"Stato","draft":"Bozza","active":"Attivo","inactive":"Disattivato","description":"Per esportare il tuo catalogo in un file Excel (.csv), seleziona la lingua del catalogo e lo stato delle offerte che desideri esportare.<br><br>Da questo file \xe8 possibile selezionare le offerte (righe) e gli attributi (colonne) che si desidera modificare.<br>Fai attenzione a non modificare o eliminare la prima riga (chiavi StationOne) e la prima colonna (sku delle tue offerte) nel file esportato.<br>Una volta apportate le modifiche, puoi seguire il processo di importazione di un nuovo catalogo e selezionare \\"Solo aggiornamento\\".<br><br>Esempio: aggiornamento dei prezzi<br>1) Recupera solo le colonne \\"sku\\" e \\"price\\" dal file esportato<br>2) Modifica i prezzi nel file Excel e salva il file sul tuo computer (in .csv)<br>3) Cataloga / Importa un feed, \\"Crea\\" una nuova importazione e segui le istruzioni per l\'importazione selezionando \\"solo aggiornamento\\""},"language":{"fr":"French","en":"English","es":"Spain","it":"Italian","de":"Deutsch"}}')},5992:function(e){e.exports=JSON.parse('{"home":"Panel de control","orders":"Pedidos","catalog":"Catalogo","messages":"Mensajes","settings":"Ajustes","Withdrawals":"Retiros de dinero","Transactions":"Transacciones","Balances":"Ingresos","logistic":"Log\xedstica","invoices":"Facturas","credit":"Saldo a favor","Refunds":"Reembolsos","offers":"Mis ofertas","addOffers":"A\xf1adir oferta","gallery":"Galeria","import":"Importac\xedon","priceFile":"Plantilla de precios espec\xedficos","inbox":"Bandeja de entrada","guide":" Manual del vendedor","terms":"T\xe9rminos generales de utilizaci\xf3n de StationOne","agreement":"Acuerdo de servicio de pago","toolbox":"Herramienta de comunicacion del vendedor","profile":"Perfil de la Empresa","company":"Informaci\xf3n de la Empresa","documents":"Documentos","permissions":"Permisos del usuario","account":"Cuenta","logout":"Cerrar sesi\xf3n","title":"Autorizaciones de cuentas de compradores para los vendedores","subtitle":"Administrar las autorizaciones de cuentas de compradores para los vendodores para acceder a los precios de sus cat\xe1logos.","Name":"N\xfamero de identificaci\xf3n de la empresa","Enterprise":"Nombre de la empresa","Create date":"Solicitud presentada el","Status":"Estatus","Action":"Acci\xf3n","All":"Todos","Pending":"Pendiente","Accepted":"Aceptada","Rejected":"Rechazada","Reject":"Rechazar","Accept":"Aceptar","Status modification":"Modificaci\xf3n de estado","Are you sure you want to change the status":"\xbfSeguro que quieres cambiar el estado?","Yes":"Si","No":"No","buyer management":"Gesti\xf3n del comprador","specific prices":"Precios espec\xedficos","specificPricesDashboard":{"title":"Gesti\xf3n de precios espec\xedficos","export":{"row":"Exportar","all":"Exportar todo"},"fileUpload":{"description":"Haga clic <a href=\'{{fileTemplateLink}}\'>aqu\xed</a> para descargar el archivo de precio espec\xedfico.<br/><br/>Para aplicar un precio espec\xedfico para un comprador, ingrese su C\xf3digo de la empresa (disponible a pedido de StationOne) en la columna \\"Buyer Identification Number\\", luego ingrese la informaci\xf3n del producto en Vendor Reference, Incoterm y Country. Finalmente ingrese el precio espec\xedfico para solicitar este comprador en la columna \\"Unit Price\\".<br/><br/>Para retirar todos los precios espec\xedficos de un comprador, debe cargar el archivo de importaci\xf3n de precios espec\xedficos ingresando solo el C\xf3digo de la empresa en 1 l\xednea.<br/>Para eliminar uno o m\xe1s precios espec\xedficos, debe cargar el archivo de importaci\xf3n de precios espec\xedficos ingresando el C\xf3digo de la empresa e indicando la siguiente informaci\xf3n: Vendor Reference, Incoterm y Country. Las otras columnas deben permanecer vac\xedas.<br/><br/>Para configurar la fecha de validez de cada precio espec\xedfico, el formato de la fecha debe ser DD / MM / AAAA, como \\"02/01/2030\\" para el 2 de Enero de 2030.<br/><br/>Aqu\xed, puede ver el estado de los precios espec\xedficos aplicados a sus compradores en StationOne.","action":"Importar"},"tableItems":{"title":"{{productsCount}} productos tienen un precio espec\xedfico aplicado para {{buyersCount}} compradores","columns":{"companyCode":"C\xf3digo de la empresa","companyName":"Nombre de la compa\xf1\xeda","lastUpload":"\xdaltima actualizaci\xf3n","nbProduct":"Cantidad de productos","action":"Acci\xf3n"}}},"catalogPage":{"title":"Export catalog management","sub_title":"Criterios de exportaci\xf3n de cat\xe1logos","export":"Export","label":{"language":"Elige el idioma","status":"Elija el estado"},"language":"Idioma","status":"Estado","draft":"Borrador","active":"Activo","inactive":"Inactivo","description":"Para exportar su cat\xe1logo a un archivo de Excel (.csv), seleccione el idioma del cat\xe1logo y el estado de las ofertas que desea exportar.<br><br>Desde este archivo, puede seleccionar las ofertas (filas) y los atributos (columnas) que desea modificar.<br>Tenga cuidado de no modificar o eliminar la primera fila (claves StationOne) y la primera columna (sku de sus ofertas) en el archivo exportado.<br>Una vez realizadas las modificaciones, puede seguir el proceso de importaci\xf3n de un nuevo cat\xe1logo y seleccionar \\"S\xf3lo actualizar\\".<br><br>Ejemplo: actualizar sus precios<br>1) Recupere solo las columnas \\"sku\\" y \\"price\\" del archivo exportado<br>2) Modifique los precios en el archivo de Excel y guarde el archivo en su computadora (en .csv)<br>3) Catalogar / importar un feed, \\"Crear\\" una nueva importaci\xf3n y seguir las instrucciones de importaci\xf3n seleccionando \\"solo actualizar\\""},"language":{"fr":"French","en":"English","es":"Spain","it":"Italian","de":"Deutsch"}}')},5993:function(e){e.exports=JSON.parse('{"home":"\xdcbersicht","orders":"Auftr\xe4ge","catalog":"Katalog","messages":"Mitteilungen","settings":"Einstellungen","Withdrawals":"Abhebungen","Transactions":"Transaktionen","Balances":"Saldo","logistic":"Logistik","invoices":"Rechnungen","credit":"Gutschriften","Refunds":"R\xfcckerstattungen","offers":"Angebote","addOffers":"Angebot hinzuf\xfcgen","gallery":"Galerie","import":"Import","priceFile":"Vorlage f\xfcr Preisdateien","inbox":"Posteingang","guide":"Lieferanten Besucherhandbuch","terms":"Station One Allgemeine Nutzungsbedingungen","agreement":"Zahlungsservice Vereinbarung","toolbox":"Lieferanten Kommunikationsbox","profile":"Unternehmensprofil","company":"Unternehmensinformation","documents":"Dokumente","permissions":"Berechtigungen","account":"Benutzerkonto","logout":"Logout","title":"K\xe4uferkontoberechtigungen","subtitle":"Verwalten Sie die K\xe4uferkontoberechtigungen, um auf die Preise Ihrer Kataloge zugreifen zu k\xf6nnen","Name":"Umsatzsteuer-Identifikationsnummer","Enterprise":"Unternehmensname","Create date":"Antrag eingereicht am","Status":"Status","Action":"Aktion","All":"Alle","Pending":"Warten","Accepted":"Akzeptiert","Rejected":"Abgelehnt","Reject":"Ablehnen","Accept":"Akzeptieren"," Status modification":"Status\xe4nderung","Are you sure you want to change the status":"M\xf6chten Sie den Status wirklich \xe4ndern?","Yes":"Ja","No":"Nein","buyer management":"K\xe4ufermanagement","specific prices":"Spezifische Preise","specificPricesDashboard":{"title":"Spezifisches Preismanagement","export":{"row":"Export","all":"Alle exportieren"},"fileUpload":{"description":"Klicken Sie <a href=\'{{fileTemplateLink}}\'>hier</a>, um die Preistabelle  zur Bearbeitung herunterzuladen.<br/><br/>Damit Sie eine spezifische Preisliste f\xfcr einen Kunden einpflegen k\xf6nnen, f\xfcllen Sie die Tabelle bitte gem\xe4\xdf folgenden Anweisungen aus:<br/>\u2022&nbsp;&nbsp;&nbsp;\u201eBuyer identification number\u201c: Einf\xfcgen der Umsatzsteueridentifikationsnummer des K\xe4ufers (auf Anfrage bei StationOne erh\xe4ltlich).<br/>\u2022&nbsp;&nbsp;&nbsp;\u201eVendor reference\u201c: Eingabe der Lieferantenmaterialnummer<br/>\u2022&nbsp;&nbsp;&nbsp;\u201eIncoterm\u201c: FCA oder DAP <br/>\u2022&nbsp;&nbsp;&nbsp;\u201eCountry\u201c: FCA (Herkunftsland), DAP (Bestimmungsland)<br/>\u2022&nbsp;&nbsp;&nbsp;\u201cUnit price\u201d: Preis pro St\xfcck<br/>\u2022&nbsp;&nbsp;&nbsp;\u201cThreshold 1/2/3/4 + Price 1/2/3/4\u201d: Staffelmengen + Staffelpreise<br/>\u2022&nbsp;&nbsp;&nbsp;\u201cMOQ\u201d:Mindestbestellmenge<br/>Die Tabelle anschlie\xdfend auf StationOne hochladen, um die Datens\xe4tze zu aktualisieren.<br/><br/><br/>Alle spezifischen Preise eines Kunden entfernen: <br/>Geben Sie in die A2 Zelle dieser Tabelle die Umsatzsteueridentifikationsnummer des Kunden ein.<br/>Alle anderen Zellen bleiben leer. <br/>Die Tabelle anschlie\xdfend auf StationOne hochladen, um die Datens\xe4tze zu aktualisieren.<br/><br/>Einzelne spezifischen Preise entfernen: <br/>Laden Sie die Datei von Ihrer Backoffice Seite herunter.<br/>Entfernen Sie bei den betroffenen Produkten die Preise bzw. Staffelmengen. <br/>Die Spalten A bis D bleiben unver\xe4ndert. <br/><br/>Um das G\xfcltigkeitsdatum f\xfcr jeden bestimmten Preis festzulegen, muss das Format des Datums TT / MM / JJJJ sein, z. B. \\"02/01/2030\\" f\xfcr den 2 Januar 2030<br/>","action":"Importieren"},"tableItems":{"title":"Es gibt {{productsCount}}-Produkte mit spezifischen Preisen f\xfcr {{buyersCount}}-K\xe4ufer","columns":{"companyCode":"Unternehmenscode","companyName":"Unternehmensname","lastUpload":"Letztes Update","nbProduct":"Anzahl der Produkte","action":"Aktion"}}},"catalogPage":{"title":"Export catalog management","sub_title":"Katalog export kriterien","export":"Export","label":{"language":"W\xe4hlen Sie die Sprache","status":"W\xe4hlen Sie den Status"},"language":"Sprache","status":"Status","draft":"Entwurf","active":"Aktiv","inactive":"Inaktiv","description":"Um Ihren Katalog in eine Excel-Datei (.csv) zu exportieren, w\xe4hlen Sie bitte die Katalogsprache und den Status der Angebote aus, die Sie exportieren m\xf6chten.<br><br>Aus dieser Datei k\xf6nnen Sie die Angebote (Zeilen) und Attribute (Spalten) ausw\xe4hlen, die Sie \xe4ndern m\xf6chten.<br>Achten Sie darauf, die erste Zeile (StationOne-Schl\xfcssel) und die erste Spalte (Artikel Ihrer Angebote) in der exportierten Datei nicht zu \xe4ndern oder zu l\xf6schen.<br>Sobald die \xc4nderungen vorgenommen wurden, k\xf6nnen Sie den Importvorgang eines neuen Katalogs verfolgen und \\"Nur aktualisieren\\" ausw\xe4hlen.<br><br>Beispiel: Aktualisieren Sie Ihre Preise<br>1) Rufen Sie nur die Spalten \\"sku\\" und \\"price\\" aus der exportierten Datei ab<br>2) \xc4ndern Sie die Preise in der Excel-Datei und speichern Sie die Datei auf Ihrem Computer (in .csv).<br>3) Katalogisieren / Importieren eines Feeds, \\"Erstellen\\" eines neuen Imports und Befolgen der Importanweisungen durch Auswahl von \\"Nur aktualisieren\\"."},"language":{"fr":"French","en":"English","es":"Spain","it":"Italian","de":"Deutsch"}}')},6064:function(e,t,a){e.exports=a(7069)},6166:function(e,t,a){},7064:function(e,t,a){},7066:function(e,t,a){},7069:function(e,t,a){"use strict";a.r(t);var n=a(4),r=a.n(n),i=a(68),o=a.n(i),l=a(45),s=a(46),c=a(53),u=a(55),d=a(56),m=a(268),p=a(5995),f=a(120),g=a(73),b=a.n(g),h=a(40),E=a(269),v=a(266),y=a.n(v),C=a(198),S=a.n(C),A=a(199),P=a.n(A),x=a(33),z=a(24),O=a(5959),j=a.n(O),k=a(5962),w=a.n(k),I=a(5963),D=a.n(I),T=a(5964),_=a.n(T),F=a(441),M=a.n(F),R=a(5965),N=a.n(R),q=a(5966),B=a.n(q),L=a(5967),U=a.n(L),G=a(5968),V=a.n(G),K=a(275),W=a.n(K),J=a(5969),Y=a.n(J),H=a(5970),Z=a.n(H),X=a(5971),Q=a.n(X),$=a(5972),ee=a.n($),te=a(5973),ae=a.n(te),ne=a(5974),re=a.n(ne),ie=a(5975),oe=a.n(ie),le=a(442),se=a.n(le),ce=a(5978),ue=a.n(ce),de=a(440),me=a.n(de),pe=a(5961),fe=a.n(pe),ge=a(5976),be=a.n(ge),he=a(5977),Ee=a.n(he),ve={BASE:"/",IZBERGBASE:"https://stationone.merchant.izberg-marketplace.com/",HOME_PAGE:"front/vendor/dashboard",SPECIFIC_PRICES_PAGE:"front/vendor/specific-prices",BAFVLIST:"front/vendor/price-request",BAFVUPDATE:"front/vendor/price-request-update-status",SPECIFIC_PRICES:"front/vendor/specific-prices-request",SPECIFIC_PRICES_UPDATE:"front/vendor/specific-prices-update-request",SPECIFIC_PRICES_EXPORT:"front/vendor/specific-prices-export-request",CATALOG:"front/vendor/catalog",CATALOG_EXPORT:"front/vendor/offer-catalog/export"};function ye(e){return ve.BASE+ve[e]}function Ce(e){return ve.IZBERGBASE+window.slug+e}var Se={home:[{label:r.a.createElement(z.a,null,"home"),icon:r.a.createElement(j.a,null),href:Ce("/"),target:"_self"},{label:r.a.createElement(z.a,null,"Withdrawals"),icon:r.a.createElement(me.a,null),href:Ce("/sellersWithdrawals/"),target:"_self"},{label:r.a.createElement(z.a,null,"Transactions"),icon:r.a.createElement(fe.a,null),href:Ce("/sellersTransactions/"),target:"_self"},{label:r.a.createElement(z.a,null,"Balances"),icon:r.a.createElement(me.a,null),href:Ce("/balances/"),target:"_self"}],orders:[{label:r.a.createElement(z.a,null,"orders"),icon:r.a.createElement(w.a,null),href:Ce("/orders/manage/"),target:"_self"},{label:r.a.createElement(z.a,null,"logistic"),icon:r.a.createElement(D.a,null),href:Ce("/orders/logistic/"),target:"_self"},{label:r.a.createElement(z.a,null,"invoices"),icon:r.a.createElement(_.a,null),href:Ce("/orders/invoices/"),target:"_self"},{label:r.a.createElement(z.a,null,"credit"),icon:r.a.createElement(M.a,null),href:Ce("/orders/credit-notes/"),target:"_self"},{label:r.a.createElement(z.a,null,"Refunds"),icon:r.a.createElement(M.a,null),href:Ce("/orders/refunds/"),target:"_self"}],catalog:[{label:r.a.createElement(z.a,null,"offers"),icon:r.a.createElement(N.a,null),href:Ce("/offers/"),target:"_self"},{label:r.a.createElement(z.a,null,"addOffers"),icon:r.a.createElement(B.a,null),href:Ce("/offers/product_create/"),target:"_self"},{label:r.a.createElement(z.a,null,"gallery"),icon:r.a.createElement(U.a,null),href:Ce("/offers/gallery/"),target:"_self"},{label:r.a.createElement(z.a,null,"import"),icon:r.a.createElement(V.a,null),href:Ce("/imports/"),target:"_self"},{label:r.a.createElement(z.a,null,"priceFile"),icon:r.a.createElement(W.a,null),href:"https://www.station-one.com/docs/StationOne_Dedicated_Prices.csv"},{label:r.a.createElement(z.a,null,"export"),icon:r.a.createElement(W.a,null),href:ye("CATALOG")}],messages:[{label:r.a.createElement(z.a,null,"inbox"),icon:r.a.createElement(Y.a,null),href:Ce("/messages/"),target:"_self"}],settings:[{label:r.a.createElement(z.a,null,"guide"),icon:r.a.createElement(Z.a,null),href:"https://www.station-one.com/docs/StationOne_Vendor_User_Guide.pdf"},{label:r.a.createElement(z.a,null,"terms"),icon:r.a.createElement(Q.a,null),href:"https://www.station-one.com/docs/StationOne_Vendor_GTU.pdf"},{label:r.a.createElement(z.a,null,"agreement"),icon:r.a.createElement(ee.a,null),href:"https://www.station-one.com/docs/StationOne_Payment_Service_Agreement.pdf"},{label:r.a.createElement(z.a,null,"toolbox"),icon:r.a.createElement(W.a,null),href:"https://www.station-one.com/docs/StationOne_Vendor_Communication_Toolbox.zip",target:"_self"},{label:r.a.createElement(z.a,null,"profile"),icon:r.a.createElement(ae.a,null),href:Ce("/settings/"),target:"_self"},{label:r.a.createElement(z.a,null,"company"),icon:r.a.createElement(re.a,null),href:Ce("/settings/company/"),target:"_self"},{label:r.a.createElement(z.a,null,"documents"),icon:r.a.createElement(oe.a,null),href:Ce("/settings/documents/"),target:"_self"},{label:r.a.createElement(z.a,null,"permissions"),icon:r.a.createElement(se.a,null),href:Ce("/settings/permissions/"),target:"_self"},{label:r.a.createElement(z.a,null,"buyer management"),icon:r.a.createElement(be.a,null),href:ye("HOME_PAGE"),target:"_self"},{label:r.a.createElement(z.a,null,"specific prices"),icon:r.a.createElement(Ee.a,null),href:ye("SPECIFIC_PRICES_PAGE"),target:"_self"}],user:[{label:r.a.createElement(z.a,null,"account"),icon:r.a.createElement(se.a,null),href:Ce("/"),target:"_self"},{label:r.a.createElement(z.a,null,"logout"),icon:r.a.createElement(ue.a,null),href:"/front/vendor/logout",target:"_self"}]},Ae=a(5979),Pe=a.n(Ae),xe=(a(6166),function(e){function t(){var e,a;Object(l.a)(this,t);for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(a=Object(c.a)(this,(e=Object(u.a)(t)).call.apply(e,[this].concat(r)))).state={anchorEl:null,menu:null},a}return Object(d.a)(t,e),Object(s.a)(t,[{key:"openMenu",value:function(e,t){this.setState({anchorEl:e.currentTarget,menu:t})}},{key:"render",value:function(){var e=this,t=this.state,a=t.anchorEl,n=t.menu;return r.a.createElement(y.a,{position:"sticky",color:"inherit"},r.a.createElement(S.a,{style:{minHeight:70}},r.a.createElement("div",{style:{display:"flex",flex:1,alignItems:"center"}},r.a.createElement("a",{href:"https://stationone.merchant.izberg-marketplace.com/",style:{display:"flex",marginRight:150}},r.a.createElement("img",{src:"https://izberg-backoffices.s3.amazonaws.com/media/themefile/2018/9/logotype_stationone_rvb_34432cb.png",style:{height:30},alt:"Station one"})),r.a.createElement("div",{style:{display:"flex",flex:1}},r.a.createElement(x.a,{onClick:function(t){return e.openMenu(t,Se.home)},class:"menu"},r.a.createElement(z.a,null,"home")),r.a.createElement(x.a,{onClick:function(t){return e.openMenu(t,Se.orders)},class:"menu"},r.a.createElement(z.a,null,"orders")),r.a.createElement(x.a,{onClick:function(t){return e.openMenu(t,Se.catalog)},class:"menu"},r.a.createElement(z.a,null,"catalog")),r.a.createElement(x.a,{onClick:function(t){return e.openMenu(t,Se.messages)},class:"menu"},r.a.createElement(z.a,null,"messages")),r.a.createElement(x.a,{onClick:function(t){return e.openMenu(t,Se.settings)},class:"menu"},r.a.createElement(z.a,null,"settings"))),r.a.createElement("div",null,r.a.createElement(x.a,{onClick:function(t){return e.openMenu(t,Se.user)}},r.a.createElement(Pe.a,null),"\xa0 ",window.userName)),r.a.createElement(P.a,{actions:n,classes:{},anchorEl:a,onClose:function(){return e.setState({anchorEl:null})},position:"bottom"}))))}}]),t}(n.Component)),ze=a(69),Oe=a(90),je=a(119),ke=a(438),we=a.n(ke),Ie="/docs/StationOne_Dedicated_Prices.csv";function De(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function Te(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?De(a,!0).forEach((function(t){Object(je.a)(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):De(a).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}var _e=function(e){function t(){var e;return Object(l.a)(this,t),(e=Object(c.a)(this,Object(u.a)(t).call(this))).state={rejectModal:!1,acceptModal:!1,rejectCommentError:!0,comment:""},e}return Object(d.a)(t,e),Object(s.a)(t,[{key:"toggleModal",value:function(e,t){var a;this.setState(Te({},this.state,(a={},Object(je.a)(a,e,t),Object(je.a)(a,"comment",""),a)))}},{key:"updateStatus",value:function(e,t){b.a.get(ye("BAFVUPDATE"),{params:{id:e.params.id,status:t,rejectionReason:this.state.comment}}).then((function(){document.dispatchEvent(new CustomEvent("update_table"))}))}},{key:"setComment",value:function(e){this.setState(Te({},this.state,{comment:e}))}},{key:"submitReject",value:function(){""!==this.state.comment.trim()?(this.setState(Te({},this.state,{rejectCommentError:!1})),this.updateStatus(this.props,0),this.toggleModal("rejectModal",!1)):this.setState(Te({},this.state,{rejectCommentError:!0}))}},{key:"render",value:function(){var e=this;return r.a.createElement(r.a.Fragment,null,(2===this.props.params.status||1===this.props.params.status)&&r.a.createElement(x.a,{color:"default",variant:"contained",size:"medium",onClick:function(){e.toggleModal("rejectModal",!0)}},r.a.createElement(z.a,null,"Reject")),(0===this.props.params.status||1===this.props.params.status)&&r.a.createElement(x.a,{color:"primary",variant:"contained",size:"medium",onClick:function(){e.toggleModal("acceptModal",!0)}},r.a.createElement(z.a,null,"Accept")),r.a.createElement(Oe.Modal,{cancelLabel:r.a.createElement(z.a,null,"No"),fullWidth:!0,maxWidth:"sm",onClose:function(){e.toggleModal("rejectModal",!1)},onSubmit:function(){e.submitReject()},open:this.state.rejectModal,submitLabel:r.a.createElement(z.a,null,"Yes"),title:r.a.createElement(z.a,null,"Status modification")},r.a.createElement(z.a,null,"Are you sure you want to change the status"),r.a.createElement("div",{style:{marginTop:30}},r.a.createElement(we.a,{fullWidth:!0,multiline:!0,label:"rejection motivation",rows:"6",rowsMax:"6",variant:"outlined",required:!0,onChange:function(t){e.setComment(t)}}))),r.a.createElement(Oe.Modal,{cancelLabel:r.a.createElement(z.a,null,"No"),fullWidth:!0,maxWidth:"sm",onClose:function(){e.toggleModal("acceptModal",!1)},onSubmit:function(){e.updateStatus(e.props,2),e.toggleModal("acceptModal",!1)},open:this.state.acceptModal,submitLabel:r.a.createElement(z.a,null,"Yes"),title:r.a.createElement(z.a,null,"Status modification")},r.a.createElement(z.a,null,"Are you sure you want to change the status")))}}]),t}(n.Component),Fe=function(e){function t(){return Object(l.a)(this,t),Object(c.a)(this,Object(u.a)(t).apply(this,arguments))}return Object(d.a)(t,e),Object(s.a)(t,[{key:"render",value:function(){return r.a.createElement(Oe.Themer,null,1===this.props.children&&r.a.createElement(Oe.Status,{type:"info"},r.a.createElement(z.a,null,"Pending")),2===this.props.children&&r.a.createElement(Oe.Status,{type:"success"},r.a.createElement(z.a,null,"Accepted")),0===this.props.children&&r.a.createElement(Oe.Status,{type:"error"},r.a.createElement(z.a,null,"Rejected")))}}]),t}(n.Component),Me=a(50),Re=a(5989),Ne=a(5990),qe=a(5991),Be=a(5992),Le=a(5993);"__LANG__"===window.lang&&(window.lang="en"),Me.a.use(z.b).init({resources:{en:{translation:Re},fr:{translation:Ne},it:{translation:qe},es:{translation:Be},de:{translation:Le}},lng:window.lang,fallbackLng:"en",interpolation:{escapeValue:!1}});var Ue=Me.a,Ge=function(e){function t(){return Object(l.a)(this,t),Object(c.a)(this,Object(u.a)(t).apply(this,arguments))}return Object(d.a)(t,e),Object(s.a)(t,[{key:"formatDate",value:function(e){return new Date(e).toLocaleDateString(window.lang)}},{key:"render",value:function(){return r.a.createElement(r.a.Fragment,null,this.formatDate(this.props.params.createdAt))}}]),t}(n.Component),Ve=[{name:"identification",label:r.a.createElement(z.a,null,"Name"),active:!0},{name:"name",label:r.a.createElement(z.a,null,"Enterprise"),active:!0},{name:"createdAt",label:r.a.createElement(z.a,null,"Create date"),active:!0,component:Ge},{name:"status",label:r.a.createElement(z.a,null,"Status"),active:!0,type:"status",component:Fe},{name:"status",label:r.a.createElement(z.a,null,"Action"),active:!0,component:_e}],Ke=[{label:r.a.createElement(z.a,null,"All"),value:"all",active:!0,queryParams:{type:"all"}},{label:r.a.createElement(z.a,null,"Pending"),value:1,active:!1,queryParams:{type:"Pending"}},{label:r.a.createElement(z.a,null,"Accepted"),value:2,active:!1,queryParams:{type:"Accepted"}},{label:r.a.createElement(z.a,null,"Rejected"),value:0,active:!1,queryParams:{type:"Rejected"}}],We=function(e){function t(){var e;return Object(l.a)(this,t),(e=Object(c.a)(this,Object(u.a)(t).call(this))).allData=[],e.state={tableData:[],filters:Ke,currentFilter:"all",currentFind:""},e.getFilteredData.bind(Object(ze.a)(e)),e.findName.bind(Object(ze.a)(e)),e}return Object(d.a)(t,e),Object(s.a)(t,[{key:"componentDidMount",value:function(){this.getTableData(),document.addEventListener("update_table",this.getTableData.bind(this))}},{key:"getFilteredData",value:function(e){var t=this,a=this.allData,n={};n.tableData="all"!==e?a.filter((function(t){return t.status===e})):a,""!==this.state.currentFind&&(n.tableData=n.tableData.filter((function(e){return e.name.toLowerCase().includes(t.state.currentFind)||e.identification.toLowerCase().includes(t.state.currentFind)}))),n.currentFilter=e;var r=Object.assign({},this.state,n);this.setState(r)}},{key:"findName",value:function(e){var t=this,a=this.allData,n={};n.tableData=a.filter((function(t){return t.name.toLowerCase().includes(e[0].value)||t.identification.toLowerCase().includes(e[0].value)})),"all"!==this.state.currentFilter&&(n.tableData=n.tableData.filter((function(e){return e.status===t.state.currentFilter}))),n.currentFind=e[0].value;var r=Object.assign({},this.state,n);this.setState(r)}},{key:"getTableData",value:function(){var e=this;b.a.get(ye("BAFVLIST")).then((function(t){var a=Object.assign({},e.state,{tableData:t.data});e.setState(a),e.allData=t.data}))}},{key:"render",value:function(){var e=this;return r.a.createElement(Oe.DataGridBase,{columns:Ve,data:this.state.tableData,filtersGroups:this.state.filters,appliedFiltersGroup:this.state.filters.filter((function(e){return e.active})),onFiltersGroupChange:function(t){return e.getFilteredData(t)},filterSearch:{label:Ue.t("User")},onSearchSubmit:function(t){return e.findName(t)},totalCount:this.state.tableData.length,toolbar:{title:r.a.createElement("div",{className:"titleTable"},r.a.createElement(z.a,null,"title"),r.a.createElement("br",null),r.a.createElement("small",null,r.a.createElement(z.a,null,"subtitle")))}})}}]),t}(n.Component),Je=a(110);function Ye(){var e=Object(m.a)(["\n  display: block;\n  position: fixed;\n  top: 50%;\n  left: 50%;\n  margin-top: -30px;\n  margin-left: -30px;\n  z-index: 99999;\n"]);return Ye=function(){return e},e}var He=Object(h.css)(Ye());function Ze(e){return r.a.createElement(E.MoonLoader,{css:He,size:e.size,color:"#134391"})}var Xe=a(5994),Qe=a.n(Xe),$e="specific-prices.csv",et={row:{backgroundColor:"#9600ff"},all:{backgroundColor:"#9600ff",marginTop:"10px",float:"right"}};function tt(e){return r.a.createElement(x.a,{variant:"contained",color:"primary",style:e.style,onClick:function(){var t={companyCode:e.companyCode};b.a.post(ye("SPECIFIC_PRICES_EXPORT"),t,{responseType:"blob"}).then((function(e){Qe()(e.data,$e)}))}},Ue.t(e.label))}function at(e){return r.a.createElement(tt,{companyCode:null,label:"specificPricesDashboard.export.all",style:et.all})}function nt(e){var t=e.params.companyCode;return r.a.createElement(tt,{companyCode:t,label:"specificPricesDashboard.export.row",style:et.row})}function rt(e){var t=[{name:"companyCode",label:Ue.t("specificPricesDashboard.tableItems.columns.companyCode"),active:!0},{name:"companyName",label:Ue.t("specificPricesDashboard.tableItems.columns.companyName"),active:!0},{name:"lastUpload",label:Ue.t("specificPricesDashboard.tableItems.columns.lastUpload"),active:!0},{name:"nbProduct",label:Ue.t("specificPricesDashboard.tableItems.columns.nbProduct"),active:!0},{name:"companyCode",label:Ue.t("specificPricesDashboard.tableItems.columns.action"),active:!0,component:nt}];return e.dataLoaded?r.a.createElement(r.a.Fragment,null,r.a.createElement(Oe.DataGridBase,{columns:t,data:e.items,toolbar:{title:r.a.createElement("div",{className:"titleTable"},Ue.t("specificPricesDashboard.tableItems.title",{productsCount:e.productsCount,buyersCount:e.buyersCount}))}}),r.a.createElement(at,null)):r.a.createElement(Ze,{size:60})}var it={container:{display:"flex",marginBottom:"15px"},description:{objectFit:"contain",fontSize:"14px",fontWeight:"500",color:"#343a40",margin:"10px 15px 15px 0"},fileInput:{display:"none"},action:{backgroundColor:"#9600ff"}};function ot(e){var t=null;return r.a.createElement("div",{style:it.container},r.a.createElement("div",{style:it.description},r.a.createElement("span",{dangerouslySetInnerHTML:{__html:Me.a.t("specificPricesDashboard.fileUpload.description",{fileTemplateLink:e.fileTemplateLink})}})),r.a.createElement("div",null,r.a.createElement("input",{type:"file",style:it.fileInput,ref:function(e){return t=e},onChange:function(t){e.fileUpload(t.target.files[0])}}),r.a.createElement(x.a,{variant:"contained",color:"primary",style:it.action,onClick:function(){t.click()}},Me.a.t("specificPricesDashboard.fileUpload.action"))))}var lt={title:{marginBottom:"15px",objectFit:"contain",fontSize:"30px",fontWeight:"bold",color:"#343a40"},error:{textAlign:"center",color:"red"}};function st(){var e=Object(n.useState)(""),t=Object(Je.a)(e,2),a=t[0],i=t[1],o=Object(n.useState)(!1),l=Object(Je.a)(o,2),s=l[0],c=l[1],u=Object(n.useState)(0),d=Object(Je.a)(u,2),m=d[0],p=d[1],f=Object(n.useState)(0),g=Object(Je.a)(f,2),h=g[0],E=g[1],v=Object(n.useState)([]),y=Object(Je.a)(v,2),C=y[0],S=y[1];Object(n.useEffect)((function(){i(""),b.a.get(ye("SPECIFIC_PRICES")).then((function(e){var t=e.data,a=t.buyersCount,n=t.productsCount,r=t.items;E(a),p(n),S(r),c(!0)}))}),[]);return r.a.createElement(r.a.Fragment,null,r.a.createElement("div",{style:lt.title},Me.a.t("specificPricesDashboard.title")),r.a.createElement(ot,{fileTemplateLink:Ie,fileUpload:function(e){i("");var t=new FormData;t.append("specific_prices",e,e.name),b.a.post(ye("SPECIFIC_PRICES_UPDATE"),t).then((function(e){var t=e.data,a=t.buyersCount,n=t.productsCount,r=t.items;E(a),p(n),S(r)})).catch((function(e){return i(e.response.data.message)}))}}),a&&r.a.createElement("div",{style:lt.error},a),r.a.createElement(rt,{dataLoaded:s,productsCount:m,buyersCount:h,items:C}))}a(7064);var ct={title:{marginBottom:"15px",objectFit:"contain",fontSize:"30px",fontWeight:"bold",color:"#343a40"},error:{textAlign:"center",color:"red"}},ut=function(e){function t(){var e,a;Object(l.a)(this,t);for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(a=Object(c.a)(this,(e=Object(u.a)(t)).call.apply(e,[this].concat(r)))).state={language:window.lang,status:"active"},a.handleChange=function(e){a.setState(Object(je.a)({},e.target.name,e.target.value))},a.export=function(){b()({method:"get",url:ye("CATALOG_EXPORT")+"/"+a.state.status+"/"+a.state.language}).then((function(e){var t=new Blob([e.data],{type:"text/csv"}),a=document.createElement("a");a.href=window.URL.createObjectURL(t),a.download="offer-catalog.csv",a.click()}))},a}return Object(d.a)(t,e),Object(s.a)(t,[{key:"render",value:function(){return r.a.createElement(r.a.Fragment,null,r.a.createElement("div",{style:ct.title},Me.a.t("catalogPage.title")),r.a.createElement("p",{dangerouslySetInnerHTML:{__html:Ue.t("catalogPage.description")}}),r.a.createElement("div",{className:"titleTable",style:{fontSize:"1.25rem"}},Ue.t("catalogPage.sub_title")),r.a.createElement(x.b,{style:{marginTop:10,marginBottom:30}},r.a.createElement(x.c,null,r.a.createElement(x.i,{variant:"body2",component:"p"},r.a.createElement("form",{autoComplete:"off"},r.a.createElement("p",{style:{marginTop:0}},r.a.createElement("b",null,Ue.t("catalogPage.label.language"))),r.a.createElement(x.d,null,r.a.createElement(x.f,{htmlFor:"language"},Me.a.t("catalogPage.language")),r.a.createElement(x.h,{value:this.state.language,onChange:this.handleChange,inputProps:{name:"language",id:"language"}},r.a.createElement(x.g,{value:"fr"},Me.a.t("language.fr")),r.a.createElement(x.g,{value:"en"},Me.a.t("language.en")),r.a.createElement(x.g,{value:"es"},Me.a.t("language.es")),r.a.createElement(x.g,{value:"it"},Me.a.t("language.it")),r.a.createElement(x.g,{value:"de"},Me.a.t("language.de")))),r.a.createElement("p",{style:{marginTop:30}},r.a.createElement("b",null,Ue.t("catalogPage.label.status"))),r.a.createElement(x.d,null,r.a.createElement(x.f,{htmlFor:"age-simple"},Me.a.t("catalogPage.status")),r.a.createElement(x.h,{value:this.state.status,onChange:this.handleChange,inputProps:{name:"status",id:"status"}},r.a.createElement(x.g,{value:"draft"},Me.a.t("catalogPage.draft")),r.a.createElement(x.g,{value:"active"},Me.a.t("catalogPage.active")),r.a.createElement(x.g,{value:"inactive"},Me.a.t("catalogPage.inactive")))))))),r.a.createElement(x.e,{container:!0,direction:"row",justify:"flex-end",alignItems:"flex-end"},r.a.createElement(x.a,{variant:"contained",color:"primary",style:{backgroundColor:"#9600ff"},onClick:this.export},Me.a.t("catalogPage.export"))))}}]),t}(r.a.Component);function dt(){var e=Object(m.a)(["\n  display: block;\n  position: fixed;\n  top: 50%;\n  left: 50%;\n  margin-top: -30px;\n  margin-left: -30px;\n  z-index: 99999;\n"]);return dt=function(){return e},e}var mt=Object(h.css)(dt()),pt=function(e){function t(e){var a;return Object(l.a)(this,t),(a=Object(c.a)(this,Object(u.a)(t).call(this,e))).state={loading:!1},b.a.interceptors.request.use((function(e){return!1!==e.loader&&a.setState({loading:!0}),e}),(function(e){return a.setState({loading:!1}),Promise.reject(e)})),b.a.interceptors.response.use((function(e){return a.setState({loading:!1}),e}),(function(e){return a.setState({loading:!1}),Promise.reject(e)})),a}return Object(d.a)(t,e),Object(s.a)(t,[{key:"render",value:function(){return r.a.createElement(p.a,null,r.a.createElement(xe,null),r.a.createElement("div",{style:{padding:"20px 32px 32px"}},r.a.createElement(f.c,null,r.a.createElement(f.a,{exact:!0,path:ye("HOME_PAGE"),component:We}),r.a.createElement(f.a,{path:ye("SPECIFIC_PRICES_PAGE"),component:st}),r.a.createElement(f.a,{path:ye("CATALOG"),component:ut}))),r.a.createElement(E.MoonLoader,{css:mt,size:60,color:"#134391",loading:this.state.loading}),this.state.loading&&r.a.createElement("div",{className:"loader-bg"}))}}]),t}(n.Component),ft=(a(7066),a(87)),gt=a(439),bt=Object(ft.createMuiTheme)(Object(gt.getTheme)({palette:{primary:{main:"#264685",dark:"#213460",light:"#6FA1C3",contrastText:"#fff"},secondary:{main:"#0078B2",dark:"#9AC6D8",light:"#3B61A6",contrastText:"#000"},grey:{light:"#C0C2C4",main:"#97999C",dark:"#636466"},warning:{dark:"#ffa000",main:"#F0A572",light:"#FFC7A3"},error:{dark:"#d32f2f",main:"#F1746C",light:"#FFA5A3"},success:{dark:"#278a2c",main:"#77CB7E",light:"#C6E6A1"},info:{dark:"#264685",main:"#6FA1C3"},yellow:{dark:"#f7a00e"},text:{onPrimary:"#FFFFFF"}}}));o.a.render(r.a.createElement(ft.MuiThemeProvider,{theme:bt},r.a.createElement(pt,null)),document.getElementById("root"))}},[[6064,1,2]]]);
//# sourceMappingURL=main.22733288.chunk.js.map