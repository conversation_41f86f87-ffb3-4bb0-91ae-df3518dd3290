{"algolia/algoliasearch-client-php": {"version": "1.28.1"}, "algolia/search-bundle": {"version": "6.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "3.0", "ref": "fd172e8feabf6b1c0818e18de90c2a3f82ea0a60"}, "files": ["config/packages/algolia_search.yaml"]}, "amphp/amp": {"version": "v2.5.1"}, "amphp/byte-stream": {"version": "v1.8.0"}, "api-platform/core": {"version": "2.7", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.5", "ref": "b86557ce5677fa855b1b2608f4a4bc4a8fed8be7"}, "files": ["config/packages/api_platform.yaml", "config/routes/api_platform.yaml", "src/Entity/.gitignore"]}, "brick/math": {"version": "0.9.3"}, "composer/package-versions-deprecated": {"version": "1.11.99"}, "composer/semver": {"version": "3.2.4"}, "composer/xdebug-handler": {"version": "1.4.5"}, "ddeboer/vatin": {"version": "2.2.1"}, "ddeboer/vatin-bundle": {"version": "dev-master"}, "dg/bypass-finals": {"version": "v1.3.1"}, "dnoegel/php-xdg-base-dir": {"version": "v0.1.1"}, "doctrine/annotations": {"version": "1.14", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.10", "ref": "64d8583af5ea57b7afa4aba4b159907f3a148b05"}, "files": []}, "doctrine/cache": {"version": "1.10.2"}, "doctrine/collections": {"version": "1.6.7"}, "doctrine/common": {"version": "3.0.2"}, "doctrine/data-fixtures": {"version": "1.5.1"}, "doctrine/dbal": {"version": "2.12.0"}, "doctrine/doctrine-bundle": {"version": "2.13", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.10", "ref": "c170ded8fc587d6bd670550c43dafcf093762245"}, "files": ["config/packages/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-fixtures-bundle": {"version": "3.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.0", "ref": "1f5514cfa15b947298df4d771e694e578d4c204d"}, "files": ["src/DataFixtures/AppFixtures.php"]}, "doctrine/doctrine-migrations-bundle": {"version": "2.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "2.2", "ref": "baaa439e3e3179e69e3da84b671f0a3e4a2f56ad"}, "files": ["config/packages/doctrine_migrations.yaml", "migrations/.gitignore"]}, "doctrine/event-manager": {"version": "1.1.1"}, "doctrine/inflector": {"version": "1.4.3"}, "doctrine/instantiator": {"version": "1.3.1"}, "doctrine/lexer": {"version": "1.2.1"}, "doctrine/migrations": {"version": "3.0.1"}, "doctrine/orm": {"version": "2.7.4"}, "doctrine/persistence": {"version": "2.1.0"}, "doctrine/reflection": {"version": "1.2.2"}, "doctrine/sql-formatter": {"version": "1.1.1"}, "egulias/email-validator": {"version": "2.1.23"}, "eightpoints/guzzle-bundle": {"version": "8.3", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "7.0", "ref": "7babb21a3928e44e485391907e3abc346804e0d2"}}, "excelwebzone/recaptcha-bundle": {"version": "1.5", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "1.5", "ref": "fd4da7bc71749db65bc83abf5d164bfa9c839cf4"}}, "fakerphp/faker": {"version": "v1.19.0"}, "felixfbecker/language-server-protocol": {"version": "v1.5.0"}, "fig/link-util": {"version": "0.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "0.0", "ref": ""}}, "firebase/php-jwt": {"version": "v5.2.0"}, "friendsofsymfony/ckeditor-bundle": {"version": "2.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "2.0", "ref": "8eb1cd0962ded6a6d6e1e5a9b6d3e888f9f94ff6"}}, "friendsofsymfony/jsrouting-bundle": {"version": "2.3", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "2.3", "ref": "a9f2e49180f75cdc71ae279a929c4b2e0638de84"}}, "friendsofsymfony/user-bundle": {"version": "v2.1.2"}, "google/recaptcha": {"version": "1.1", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "1.1", "ref": "d087df3e087f50da3955f2def05079380da5894b"}}, "guzzlehttp/guzzle": {"version": "7.2.0"}, "guzzlehttp/promises": {"version": "1.4.0"}, "guzzlehttp/psr7": {"version": "1.7.0"}, "illuminate/collections": {"version": "8.x-dev"}, "illuminate/contracts": {"version": "8.x-dev"}, "illuminate/encryption": {"version": "v8.14.0"}, "illuminate/macroable": {"version": "8.x-dev"}, "illuminate/support": {"version": "8.x-dev"}, "jasny/twig-extensions": {"version": "v1.3.0"}, "jms/metadata": {"version": "2.3.0"}, "jms/serializer": {"version": "3.10.0"}, "jms/serializer-bundle": {"version": "3.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "3.0", "ref": "384cec52df45f3bfd46a09930d6960a58872b268"}}, "justinrainbow/json-schema": {"version": "5.2.12"}, "knplabs/knp-components": {"version": "v2.4.2"}, "knplabs/knp-menu": {"version": "3.2-dev"}, "knplabs/knp-menu-bundle": {"version": "v3.0.0"}, "knplabs/knp-paginator-bundle": {"version": "v5.3.0"}, "knpuniversity/oauth2-client-bundle": {"version": "1.20", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "1.20", "ref": "80acc9223a205cbf5d9828fd616c82a374d281dd"}}, "laminas/laminas-code": {"version": "3.4.1"}, "laminas/laminas-eventmanager": {"version": "3.3.0"}, "laminas/laminas-zendframework-bridge": {"version": "1.1.1"}, "lcobucci/clock": {"version": "2.0.0"}, "lcobucci/jwt": {"version": "4.1.5"}, "league/oauth2-client": {"version": "2.6.0"}, "lexik/jwt-authentication-bundle": {"version": "2.15", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "2.5", "ref": "5b2157bcd5778166a5696e42f552ad36529a07a6"}, "files": ["config/packages/lexik_jwt_authentication.yaml"]}, "liip/test-fixtures-bundle": {"version": "2.3.0"}, "mashape/unirest-php": {"version": "v3.0.4"}, "monolog/monolog": {"version": "1.25.5"}, "mpdf/mpdf": {"version": "v8.0.7"}, "myclabs/deep-copy": {"version": "1.x-dev"}, "nelmio/alice": {"version": "3.9", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.2", "ref": "0b9900ece737bec7752e4155c0164639dd9b0cb0"}, "files": ["config/packages/dev/nelmio_alice.yaml", "config/packages/test/nelmio_alice.yaml"]}, "nelmio/cors-bundle": {"version": "1.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.5", "ref": "6bea22e6c564fba3a1391615cada1437d0bde39c"}, "files": ["config/packages/nelmio_cors.yaml"]}, "nesbot/carbon": {"version": "2.x-dev"}, "netresearch/jsonmapper": {"version": "v2.1.0"}, "nikic/php-parser": {"version": "v4.10.2"}, "ocramius/proxy-manager": {"version": "2.9.1"}, "phar-io/manifest": {"version": "2.0.1"}, "phar-io/version": {"version": "3.0.4"}, "phpdocumentor/reflection-common": {"version": "2.2.0"}, "phpdocumentor/reflection-docblock": {"version": "5.2.2"}, "phpdocumentor/type-resolver": {"version": "1.4.0"}, "phpspec/prophecy": {"version": "1.12.1"}, "phpstan/phpstan": {"version": "1.9", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.0", "ref": "5e490cc197fb6bb1ae22e5abbc531ddc633b6767"}}, "phpunit/php-code-coverage": {"version": "9.2.5"}, "phpunit/php-file-iterator": {"version": "3.0.5"}, "phpunit/php-invoker": {"version": "3.1.1"}, "phpunit/php-text-template": {"version": "2.0.4"}, "phpunit/php-timer": {"version": "5.0.3"}, "phpunit/phpunit": {"version": "4.7", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.7", "ref": "477e1387616f39505ba79715f43f124836020d71"}, "files": [".env.test", "phpunit.xml.dist", "tests/bootstrap.php"]}, "psalm/plugin-phpunit": {"version": "0.15.0"}, "psalm/plugin-symfony": {"version": "v2.0.2"}, "psr/cache": {"version": "1.0.1"}, "psr/container": {"version": "1.0.0"}, "psr/http-client": {"version": "1.0.1"}, "psr/http-factory": {"version": "1.0.1"}, "psr/http-message": {"version": "1.0.1"}, "psr/link": {"version": "1.0.0"}, "psr/log": {"version": "1.1.3"}, "psr/simple-cache": {"version": "1.0.x-dev"}, "ralouphie/getallheaders": {"version": "3.0.3"}, "ramsey/collection": {"version": "1.2.2"}, "ramsey/uuid": {"version": "4.2.3"}, "sebastian/cli-parser": {"version": "1.0.1"}, "sebastian/code-unit": {"version": "1.0.8"}, "sebastian/code-unit-reverse-lookup": {"version": "2.0.3"}, "sebastian/comparator": {"version": "4.0.6"}, "sebastian/complexity": {"version": "2.0.2"}, "sebastian/diff": {"version": "4.0.4"}, "sebastian/environment": {"version": "5.1.3"}, "sebastian/exporter": {"version": "4.0.3"}, "sebastian/global-state": {"version": "5.0.2"}, "sebastian/lines-of-code": {"version": "1.0.3"}, "sebastian/object-enumerator": {"version": "4.0.4"}, "sebastian/object-reflector": {"version": "2.0.4"}, "sebastian/recursion-context": {"version": "4.0.4"}, "sebastian/resource-operations": {"version": "3.0.3"}, "sebastian/type": {"version": "2.3.1"}, "sebastian/version": {"version": "3.0.2"}, "setasign/fpdi": {"version": "v2.3.4"}, "soundasleep/html2text": {"version": "1.1.0"}, "squizlabs/php_codesniffer": {"version": "3.7", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "3.6", "ref": "1019e5c08d4821cb9b77f4891f8e9c31ff20ac6f"}, "files": ["phpcs.xml.dist"]}, "symfony/asset": {"version": "v4.4.16"}, "symfony/browser-kit": {"version": "v4.4.16"}, "symfony/cache": {"version": "v4.4.16"}, "symfony/cache-contracts": {"version": "v2.2.0"}, "symfony/config": {"version": "v4.4.16"}, "symfony/console": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "1781ff40d8a17d87cf53f8d4cf0c8346ed2bb461"}, "files": ["bin/console"]}, "symfony/css-selector": {"version": "v4.4.16"}, "symfony/debug": {"version": "v4.4.16"}, "symfony/debug-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "5aa8aa48234c8eb6dbdd7b3cd5d791485d2cec4b"}, "files": ["config/packages/debug.yaml"]}, "symfony/dependency-injection": {"version": "v4.4.16"}, "symfony/deprecation-contracts": {"version": "v2.2.0"}, "symfony/doctrine-bridge": {"version": "v4.4.16"}, "symfony/dom-crawler": {"version": "v4.4.16"}, "symfony/dotenv": {"version": "v4.4.16"}, "symfony/error-handler": {"version": "v4.4.16"}, "symfony/event-dispatcher": {"version": "v4.4.16"}, "symfony/event-dispatcher-contracts": {"version": "v1.1.9"}, "symfony/expression-language": {"version": "v4.4.16"}, "symfony/filesystem": {"version": "v4.4.16"}, "symfony/finder": {"version": "v4.4.16"}, "symfony/flex": {"version": "2.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.4", "ref": "52e9754527a15e2b79d9a610f98185a1fe46622a"}, "files": [".env", ".env.dev"]}, "symfony/form": {"version": "v4.4.16"}, "symfony/framework-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "32126346f25e1cee607cc4aa6783d46034920554"}, "files": ["config/packages/cache.yaml", "config/packages/framework.yaml", "config/preload.php", "config/routes/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/http-client": {"version": "v4.4.16"}, "symfony/http-client-contracts": {"version": "v2.3.1"}, "symfony/http-foundation": {"version": "v4.4.16"}, "symfony/http-kernel": {"version": "v4.4.16"}, "symfony/inflector": {"version": "v4.4.16"}, "symfony/intl": {"version": "v4.4.16"}, "symfony/mailer": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.3", "ref": "09051cfde49476e3c12cd3a0e44289ace1c75a4f"}, "files": ["config/packages/mailer.yaml"]}, "symfony/maker-bundle": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/messenger": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.0", "ref": "ba1ac4e919baba5644d31b57a3284d6ba12d52ee"}, "files": ["config/packages/messenger.yaml"]}, "symfony/mime": {"version": "v4.4.16"}, "symfony/monolog-bridge": {"version": "v4.4.16"}, "symfony/monolog-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "d7249f7d560f6736115eee1851d02a65826f0a56"}, "files": ["config/packages/dev/monolog.yaml", "config/packages/prod/deprecations.yaml", "config/packages/prod/monolog.yaml", "config/packages/test/monolog.yaml"]}, "symfony/options-resolver": {"version": "v4.4.16"}, "symfony/phpunit-bridge": {"version": "4.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.3", "ref": "6d0e35f749d5f4bfe1f011762875275cd3f9874f"}, "files": [".env.test", "bin/phpunit", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/polyfill-intl-icu": {"version": "v1.20.0"}, "symfony/polyfill-intl-idn": {"version": "v1.20.0"}, "symfony/polyfill-intl-normalizer": {"version": "v1.20.0"}, "symfony/polyfill-mbstring": {"version": "v1.20.0"}, "symfony/polyfill-php72": {"version": "v1.20.0"}, "symfony/polyfill-php73": {"version": "v1.20.0"}, "symfony/polyfill-php80": {"version": "v1.20.0"}, "symfony/polyfill-php81": {"version": "v1.25.0"}, "symfony/process": {"version": "v4.4.16"}, "symfony/property-access": {"version": "v4.4.16"}, "symfony/property-info": {"version": "v4.4.16"}, "symfony/proxy-manager-bridge": {"version": "v4.4.18"}, "symfony/routing": {"version": "4.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.2", "ref": "683dcb08707ba8d41b7e34adb0344bfd68d248a7"}, "files": ["config/packages/prod/routing.yaml", "config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/security-bundle": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.4", "ref": "7b4408dc203049666fe23fabed23cbadc6d8440f"}, "files": ["config/packages/security.yaml"]}, "symfony/security-core": {"version": "v4.4.16"}, "symfony/security-csrf": {"version": "v4.4.16"}, "symfony/security-guard": {"version": "v4.4.16"}, "symfony/security-http": {"version": "v4.4.16"}, "symfony/serializer": {"version": "v4.4.16"}, "symfony/service-contracts": {"version": "v2.2.0"}, "symfony/stopwatch": {"version": "v4.4.16"}, "symfony/translation": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "2ad9d2545bce8ca1a863e50e92141f0b9d87ffcd"}, "files": ["config/packages/translation.yaml", "translations/.gitignore"]}, "symfony/translation-contracts": {"version": "v2.3.0"}, "symfony/twig-bridge": {"version": "v4.4.16"}, "symfony/twig-bundle": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.4", "ref": "15a41bbd66a1323d09824a189b485c126bbefa51"}, "files": ["config/packages/test/twig.yaml", "config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/validator": {"version": "4.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.3", "ref": "d902da3e4952f18d3bf05aab29512eb61cabd869"}, "files": ["config/packages/test/validator.yaml", "config/packages/validator.yaml"]}, "symfony/var-dumper": {"version": "v4.4.16"}, "symfony/var-exporter": {"version": "v4.4.16"}, "symfony/web-link": {"version": "v4.4.16"}, "symfony/web-profiler-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "6bdfa1a95f6b2e677ab985cd1af2eae35d62e0f6"}, "files": ["config/packages/dev/web_profiler.yaml", "config/packages/test/web_profiler.yaml", "config/routes/dev/web_profiler.yaml"]}, "symfony/webpack-encore-bundle": {"version": "1.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.6", "ref": "69e1d805ad95964088bd510c05995e87dc391564"}, "files": ["assets/app.js", "assets/styles/app.css", "config/packages/assets.yaml", "config/packages/prod/webpack_encore.yaml", "config/packages/test/webpack_encore.yaml", "config/packages/webpack_encore.yaml", "package.json", "webpack.config.js"]}, "symfony/yaml": {"version": "v4.4.16"}, "theofidry/alice-data-fixtures": {"version": "1.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "fe5a50faf580eb58f08ada2abe8afbd2d4941e05"}}, "theseer/tokenizer": {"version": "1.2.0"}, "twbs/bootstrap": {"version": "dev-main"}, "twig/extra-bundle": {"version": "v3.1.1"}, "twig/twig": {"version": "v3.1.1"}, "vimeo/psalm": {"version": "4.1.1"}, "voku/portable-ascii": {"version": "1.5.6"}, "webimpress/safe-writer": {"version": "2.1.0"}, "webmozart/assert": {"version": "1.9.1"}, "willdurand/jsonp-callback-validator": {"version": "v1.1.0"}, "willdurand/negotiation": {"version": "0.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "0.0", "ref": ""}}}