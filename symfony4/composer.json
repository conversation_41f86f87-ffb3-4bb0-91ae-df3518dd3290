{"type": "project", "license": "proprietary", "minimum-stability": "stable", "require": {"php": ">=8.2", "ext-ctype": "*", "ext-iconv": "*", "ext-json": "*", "ext-redis": "^6.1", "algolia/search-bundle": "^7.0", "api-platform/core": "^v4.0.17", "composer/package-versions-deprecated": "^1.11", "ddeboer/vatin-bundle": "9999999-dev", "doctrine/annotations": "^2.0", "doctrine/common": "^3.0", "doctrine/doctrine-bundle": "^2.5", "doctrine/doctrine-migrations-bundle": "^3.1", "doctrine/orm": "^2.11", "eightpoints/guzzle-bundle": "^8.3", "excelwebzone/recaptcha-bundle": "^1.5", "firebase/php-jwt": "^6.4", "friendsofsymfony/ckeditor-bundle": "*", "friendsofsymfony/jsrouting-bundle": "*", "friendsofsymfony/user-bundle": "*", "guzzlehttp/guzzle": "^7.4.5", "illuminate/encryption": "^8.75", "illuminate/support": "^8.75", "jasny/twig-extensions": "^1.3", "jms/serializer-bundle": "*", "knplabs/knp-menu-bundle": "3.3", "knplabs/knp-paginator-bundle": "^6.0", "knpuniversity/oauth2-client-bundle": "^2.5", "lexik/jwt-authentication-bundle": "*", "mashape/unirest-php": "^3.0", "mpdf/mpdf": "^8.0", "nelmio/cors-bundle": "^2.2", "phpdocumentor/reflection-docblock": "^5.2", "ramsey/uuid": "^4.2", "soundasleep/html2text": "1.1", "symfony/asset": "^7.2", "symfony/console": "^7.2", "symfony/doctrine-messenger": "7.2", "symfony/dotenv": "^7.2", "symfony/expression-language": "^7.2", "symfony/flex": "*", "symfony/form": "^7.2", "symfony/framework-bundle": "^7.2", "symfony/http-client": "^7.2", "symfony/intl": "^7.2", "symfony/mailer": "^7.2", "symfony/messenger": "^7.2", "symfony/monolog-bundle": "*", "symfony/process": "^7.2", "symfony/property-access": "^7.2", "symfony/property-info": "^7.2", "symfony/proxy-manager-bridge": "6.4.*", "symfony/runtime": "^7.2", "symfony/security-bundle": "^7.2", "symfony/serializer": "^7.2", "symfony/translation": "^6.0", "symfony/twig-bridge": "*", "symfony/twig-bundle": "^7.2", "symfony/validator": "^7.2", "symfony/web-link": "^7.2", "symfony/webpack-encore-bundle": "^2.2", "symfony/yaml": "^7.2", "twbs/bootstrap": "4.0.0", "twig/extra-bundle": "^2.12|^3.0", "twig/intl-extra": "^3.20", "twig/twig": "^3.12"}, "require-dev": {"dg/bypass-finals": "^1.3", "doctrine/doctrine-fixtures-bundle": "^3.4", "justinrainbow/json-schema": "^5.2", "liip/test-fixtures-bundle": "^2.0.0", "phpcompatibility/php-compatibility": "^9.3", "phpspec/prophecy": "^1.12", "phpunit/phpunit": "^9.5", "psalm/plugin-phpunit": "^0.19.2", "psalm/plugin-symfony": "*", "rector/rector": "*", "symfony/browser-kit": "^7.2", "symfony/css-selector": "^7.2", "symfony/debug-bundle": "^7.2", "symfony/maker-bundle": "*", "symfony/phpunit-bridge": "^7.2", "symfony/stopwatch": "^7.2", "symfony/var-dumper": "^7.2", "symfony/web-profiler-bundle": "^7.2", "theofidry/alice-data-fixtures": "^1.4.0", "vimeo/psalm": "^6.8.4"}, "config": {"preferred-install": {"*": "dist"}, "sort-packages": true, "allow-plugins": {"symfony/flex": true, "symfony/runtime": true}}, "autoload": {"psr-4": {"App\\": "src/App", "Api\\": "src/Api", "AppBundle\\": "src/AppBundle", "Open\\": "src/Open", "Upela\\": "src/Upela", "Boekkooi\\": "src/Boekkooi"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "replace": {"paragonie/random_compat": "2.*", "symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php71": "*", "symfony/polyfill-php70": "*", "symfony/polyfill-php56": "*", "twig/extensions": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.2"}}}