knpu_oauth2_client:
  clients:
    izberg_vendor_client:
      type: generic
      client_id: '%env(IZBERG_VENDOR_OPENIDCONNECT_CLIENT_ID)%'
      client_secret: '%env(IZBERG_VENDOR_OPENIDCONNECT_CLIENT_SECRET)%'
      provider_class: \AppBundle\Security\IzbergVendorProvider
      redirect_route: front_vendor_connect_check
      provider_options:
        urlAuthorize: '%env(IZBERG_VENDOR_OPENIDCONNECT_URL_AUTHORIZE)%'
        urlAccessToken: '%env(IZBERG_VENDOR_OPENIDCONNECT_URL_ACCESS_TOKEN)%'
        izbergApiDomain: '%env(IZBERG_API_DOMAIN)%'
