monolog:
    handlers:
        main:
            type: fingers_crossed
            action_level: notice
            handler: nested
            excluded_http_codes: [404, 405]
            buffer_size: 50 # How many messages should be saved? Prevent memory leaks
            formatter: monolog.formatter.user_data
        nested:
            type: stream
            path: "%env(LOGS_DIR)%/%kernel.environment%.log"
            level: notice
            formatter: monolog.formatter.user_data
        console:
            type: console
            process_psr_3_messages: false
            channels: ["!event", "!doctrine"]
            formatter: monolog.formatter.user_data
