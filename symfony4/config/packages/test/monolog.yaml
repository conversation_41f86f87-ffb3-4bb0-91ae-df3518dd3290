monolog:
    handlers:
        main:
            type: fingers_crossed
            action_level: error
            handler: nested
            excluded_http_codes: [404, 405]
            channels: ["!event"]
            formatter: monolog.formatter.user_data
        nested:
            type: stream
            path: "%env(LOGS_DIR)%/%kernel.environment%.log"
            level: debug
            formatter: monolog.formatter.user_data
