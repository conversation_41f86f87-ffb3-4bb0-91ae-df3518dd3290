open_izberg:
    default_connection:
        domain: "%env(IZBERG_API_DOMAIN)%"
        domain_seller: "%env(IZBERG_API_SELLER_DOMAIN)%"
        version: '%env(int:IZBERG_API_VERSION)%'
        protocol: "%env(IZBERG_API_PROTOCOL)%"
        access_token: "%env(IZBERG_ACCESS_TOKEN)%"
        application_id: "%env(IZBERG_APPLICATION_ID)%"
        application_namespace: "%env(IZBERG_APPLICATION_NAMESPACE)%"
        secret_key: "%env(IZBERG_SECRET_KEY)%"
        email: "%env(IZBERG_EMAIL)%"
        username: "%env(IZBERG_USERNAME)%"
        first_name: "%env(IZBERG_FIRST_NAME)%"
        last_name: "%env(IZBERG_LAST_NAME)%"
        jwt_secret: "%env(IZBERG_JWT_SECRET)%"
        seller_email_domain: '%env(IZBERG_EMAIL_SELLER_DOMAIN)%'
        create_merchant_url: '%env(IZBERG_CREATE_MERCHANT_USER_URL)%'
        identity_api_url: '%env(IZBERG_IDENTITY_API_URL)%'
        client_id: '%env(IZBERG_M2MOPERATOR_CLIENT_ID)%'
        client_secret: '%env(IZBERG_M2MOPERATOR_CLIENT_SECRET)%'
        domain_id: '%env(IZBERG_DOMAIN_ID)%'
        audience: '%env(IZBERG_AUDIENCE)%'
    other_connections:
        console:
            username: '%env(IZBERG_CONSOLE_USERNAME)%'
            access_token: '%env(IZBERG_CONSOLE_ACCESS_TOKEN)%'
        webhook:
            username: "%env(IZBERG_WEBHOOK_USERNAME)%"
            access_token: "%env(IZBERG_WEBHOOK_ACCESS_TOKEN)%"
        operator:
            username: "%env(IZBERG_USERNAME)%"
            access_token: "%env(IZBERG_ACCESS_TOKEN)%"
