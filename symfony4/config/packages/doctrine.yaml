doctrine:
    dbal:
        url: '%env(resolve:DATABASE_URL)%'

        # IMPORTANT: You MUST configure your server version,
        # either here or in the DATABASE_URL env var (see .env file)
        #server_version: '5.7'
    orm:
        auto_generate_proxy_classes: true
        naming_strategy: doctrine.orm.naming_strategy.underscore_number_aware
        auto_mapping: false
        mappings:
            App:
                is_bundle: false
                type: attribute
                dir: '%kernel.project_dir%/src/AppBundle/Entity'
                prefix: 'AppBundle\Entity'
                alias: App
            OpenTicketBundle:
                is_bundle: false
                type: attribute
                dir: '%kernel.project_dir%/src/Open/TicketBundle/Entity'
                prefix: 'Open\TicketBundle\Entity'
                alias: 'OpenTicketBundle'
        filters:
            published_node: AppBundle\Doctrine\NodeStatusFilter
            node_language: AppBundle\Doctrine\NodeLanguageFilter
