security:
    password_hashers:
        FOS\UserBundle\Model\UserInterface: bcrypt

    role_hierarchy:
        ROLE_BUYER_BUYER: ROLE_USER
        ROLE_BUYER_PAYER: ROLE_BUYER_BUYER
        ROLE_BUYER_ADMIN: ROLE_BUYER_PAYER

        ROLE_VENDOR: ROLE_VENDOR

        ROLE_API: [ ROLE_BUYER_ADMIN ]

        ROLE_OPERATOR: [ ROLE_BUYER_ADMIN ]
        ROLE_SUPER_ADMIN: [ ROLE_OPERATOR ]

    # https://symfony.com/doc/current/security.html#where-do-users-come-from-user-providers
    providers:
        fos_userbundle:
            id: fos_user.user_provider.username_email
        vendor_db:
            entity: { class: AppBundle\Entity\Vendor }

    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false
        login:
            pattern: ^/middleware/api/login
            stateless: true
            provider: fos_userbundle
            user_checker: Api\Core\Security\UserApiChecker
            json_login:
                check_path: api_login_check
                username_path: email
                password_path: password
                success_handler: lexik_jwt_authentication.handler.authentication_success
                failure_handler: lexik_jwt_authentication.handler.authentication_failure

        api:
            pattern: ^/middleware/api
            stateless: true
            provider: fos_userbundle
            entry_point: jwt # Ajoutez cette ligne pour spécifier l'authentificateur par défaut
            http_basic:
                realm: Secured Area
            jwt: ~ # enables the jwt authenticator
        vendor:
            pattern: ^/front/vendor
            provider: vendor_db
            logout:
                path: vendor.logout
                target: vendor.logout.page
                invalidate_session: true
            custom_authenticator: AppBundle\Security\IzbergVendorAuthenticator

        admin:
            context: my_context
            pattern: /admin(.*)
            form_login:
                provider: fos_userbundle
                #csrf_token_generator: security.csrf.token_manager
                login_path: /admin/login
                check_path: /admin/login_check
                default_target_path: /admin
            switch_user: { role: ROLE_SUPER_ADMIN, parameter: _piggy, provider: fos_userbundle }
            logout:
                path: /admin/logout
                target: /
                invalidate_session: true
            stateless: false

        main:
            lazy: true
            context: my_context
            pattern: ^/
            user_checker: AppBundle\Security\UserChecker\UserChecker
            form_login:
                provider: fos_userbundle
                login_path: /login
                check_path: /login_check
                failure_path: /login/failure
                default_target_path: /login/success
                # csrf_token_generator: security.csrf.token_manager
                #always_use_default_target_path: false
                #target_path_parameter: _target_path
                #use_referer: true
            switch_user: { role: ROLE_OPERATOR , parameter: _piggy, provider: fos_userbundle }
            logout:
                path: fos_user_security_logout
                target: /
                invalidate_session: true

            # activate different ways to authenticate
            # https://symfony.com/doc/current/security.html#firewalls-authentication

            # https://symfony.com/doc/current/security/impersonating_user.html
            # switch_user: true

    # Easy way to control access for large sections of your site
    # Note: Only the *first* access control that matches will be used
    access_control:
        - { path: ^/attachment, role: PUBLIC_ACCESS }
        - { path: ^/contact, role: PUBLIC_ACCESS }
        - { path: ^/login$, role: PUBLIC_ACCESS }
        - { path: ^/register, role: PUBLIC_ACCESS }
        - { path: ^/resetting, role: PUBLIC_ACCESS }

        - { path: ^/admin/login, role: PUBLIC_ACCESS }
        - { path: ^/admin/logout, role: PUBLIC_ACCESS }
        - { path: ^/admin/login_check, role: PUBLIC_ACCESS }

        - { path: /admin, role: ROLE_OPERATOR }
        - { path: ^/admin/translations, role: ROLE_SUPER_ADMIN }
        - { path: ^/admin/jobs, role: ROLE_SUPER_ADMIN }

        # Front vendor access control
        - { path: ^/front/vendor/connect/start, role: PUBLIC_ACCESS }
        - { path: ^/front/vendor/logout/info, role: PUBLIC_ACCESS }
        - { path: ^/front/vendor, role: ROLE_VENDOR }

        # API
        - { path: ^/middleware/docs, role: ROLE_BUYER_ADMIN }
        - { path: ^/middleware/api/login_check, role: ROLE_BUYER_ADMIN }
        - { path: ^/middleware/api, role: ROLE_API }
