fos_user:
    db_driver: orm # other valid values are 'mongodb' and 'couchdb'
    firewall_name: main
    user_class: AppBundle\Entity\User
    from_email:
        address: "%env(FOSUSER_FROM_EMAIL)%"
        sender_name: "%env(FOSUSER_FROM_NAME)%"
    registration:
        confirmation:
            enabled: false
            from_email:
                address: '%env(FOSUSER_FROM_EMAIL)%'
                sender_name: '%env(FOSUSER_FROM_NAME)%'
        form:
            type: AppBundle\Form\Type\RegistrationFormType
    resetting:
        retry_ttl: 0
        form:
            type: AppBundle\Form\Type\ResettingFormType
        email:
            from_email:
                address: '%env(FOSUSER_FROM_EMAIL)%'
                sender_name: '%env(FOSUSER_FROM_NAME)%'
    service:
        mailer: AppBundle\Services\MailService
