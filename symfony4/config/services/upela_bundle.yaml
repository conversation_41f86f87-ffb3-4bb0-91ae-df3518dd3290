services:
  _defaults:
    autowire: true
    autoconfigure: true
    public: false

  Upela\:
    resource: '../../src/Upela'
    exclude:
      - '../../src/Upela/DependencyInjection/'
      - '../../src/Upela/Entity/'
      - '../../src/Upela/Kernel.php'
      - '../../src/Upela/Tests/'

  Upela\Api\ProductApi:
    arguments:
      - '%env(UPELA_PRE_ORDER_API_URL)%'
      - '%env(UPELA_PRE_ORDER_USERNAME)%'
      - '%env(UPELA_PRE_ORDER_PASSWORD)%'

  Upela\Api\PreOrderApi:
    public: true
    arguments:
      - '%env(UPELA_PRE_ORDER_API_URL)%'
      - '%env(UPELA_PRE_ORDER_USERNAME)%'
      - '%env(UPELA_PRE_ORDER_PASSWORD)%'


