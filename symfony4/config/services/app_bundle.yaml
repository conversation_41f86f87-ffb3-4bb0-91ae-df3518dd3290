imports:
    - { resource: app_bundle-parameters.yaml }

services:

    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
        bind:
            int $comparisonSheetMaxItem: '%env(int:MAX_ITEM_COMPARISON)%'
            array $historizedEntities: '%historized_entities%'
            array $appSettings: '%app.settings%'
            array $izbergOrderAttributes: '%izberg_order_attributes%'
            array $izbergMerchantAttributes: '%izberg_merchant_attributes%'
            string $izbergDatePattern: '%env(IZBERG_DATE_PATTERN)%'
            int $paymentNbDaysBeforeReminder: '%env(PAYMENT_NB_DAYS_BEFORE_REMINDER)%'
            array $paymentNbDaysBeforeLateReminder: '%env(json:PAYMENT_NB_DAYS_BEFORE_LATE_REMINDER)%'
            string $fosuserFromEmail: '%env(FOSUSER_FROM_EMAIL)%'
            string $fosuserFromName: '%env(FOSUSER_FROM_NAME)%'
            string $privateTmpDir: '%private_tmp_dir%'

    AppBundle\:
        resource: '../../src/AppBundle'
        exclude:
            - '../../src/AppBundle/DependencyInjection/'
            - '../../src/AppBundle/Doctrine/'
            - '../../src/AppBundle/Entity/'
            - '../../src/AppBundle/Event/'
            - '../../src/AppBundle/EventListener/'
            - '../../src/AppBundle/Exception/'
            - '../../src/AppBundle/Form/'
            - '../../src/AppBundle/Menu/'
            - '../../src/AppBundle/Request/'
            - '../../src/AppBundle/Repository/'
            - '../../src/AppBundle/Serializer/'
            - '../../src/AppBundle/StreamFilter/'
            - '../../src/AppBundle/Validator/'

    # controllers are imported separately to make sure services can be injected
    # as action arguments even if you don't extend any base controller class
    AppBundle\Controller\:
        resource: '../../src/AppBundle/Controller/'
        tags: [ 'controller.service_arguments' ]


    AppBundle\Form\SiteForm:
        arguments:
            $companyService: '@AppBundle\Services\CompanyService'

    AppBundle\Repository\CompanyCatalogRepository:
        class: AppBundle\Repository\CompanyCatalogRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\CompanyCatalog' ]

    AppBundle\Repository\CompanyRepository:
        class: AppBundle\Repository\CompanyRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\Company' ]

    AppBundle\Repository\ThreadParentMessageRepository:
        class: AppBundle\Repository\ThreadParentMessageRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\ThreadParentMessage' ]

    AppBundle\Repository\ThreadMessageRepository:
        class: AppBundle\Repository\ThreadMessageRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\ThreadMessage' ]

    AppBundle\Repository\OrderRepository:
        class: AppBundle\Repository\OrderRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\Order' ]

    AppBundle\Repository\Payload\PayloadRepository: ~

    AppBundle\Repository\AddressRepository:
        class: AppBundle\Repository\AddressRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\Address' ]

    AppBundle\Repository\BafvRequestRepository:
        class: AppBundle\Repository\BafvRequestRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\BafvRequest' ]
        calls:
            -   method: "setPaginator"
                arguments: [ "@knp_paginator" ]

    AppBundle\Repository\ImageRepository:
        class: AppBundle\Repository\ImageRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\Image' ]

    AppBundle\Repository\UserBafvMerchantListRepository:
        class: AppBundle\Repository\UserBafvMerchantListRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\UserBafvMerchantList' ]

    AppBundle\Repository\ShippingPointRepository:
        class: AppBundle\Repository\ShippingPointRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\ShippingPoint' ]

    AppBundle\Repository\TvaGroupRepository:
        class: AppBundle\Repository\TvaGroupRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\TvaGroup' ]

    AppBundle\Repository\TvaRateRepository:
        class: AppBundle\Repository\TvaRateRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\TvaRate' ]

    AppBundle\Repository\UserRepository:
        class: AppBundle\Repository\UserRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\User' ]

    AppBundle\Repository\VendorRepository:
        class: AppBundle\Repository\VendorRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\Vendor' ]

    AppBundle\Repository\WishListRepository:
        class: AppBundle\Repository\WishListRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\WishList' ]

    AppBundle\Repository\WishListItemRepository:
        class: AppBundle\Repository\WishListItemRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\WishListItem' ]

    AppBundle\Repository\MerchantRepository:
        class: AppBundle\Repository\MerchantRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\Merchant' ]
        calls:
            -   method: "setPaginator"
                arguments: [ "@knp_paginator" ]

    AppBundle\Repository\InvoiceRepository:
        class: AppBundle\Repository\InvoiceRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\Invoice' ]

    AppBundle\Repository\CreditNoteRepository:
        class: AppBundle\Repository\CreditNoteRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\CreditNote' ]

    AppBundle\Repository\CartRepository:
        class: AppBundle\Repository\CartRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\Cart' ]

    AppBundle\Repository\BddFileRepository:
        class: AppBundle\Repository\BddFileRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\BddFile' ]

    AppBundle\Repository\CartShippingOptionRepository:
        class: AppBundle\Repository\CartShippingOptionRepository
        factory: [ '@doctrine.orm.default_entity_manager', getRepository ]
        arguments: [ 'AppBundle\Entity\CartShippingOption' ]

    AppBundle\Repository\StorageRepository:
        class: AppBundle\Repository\StorageRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\Storage' ]

    AppBundle\Repository\CountryRepository:
        class: AppBundle\Repository\CountryRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\Country' ]

    AppBundle\Repository\PurchaseRequestRepository:
        class: AppBundle\Repository\PurchaseRequestRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\PurchaseRequest' ]

    AppBundle\Repository\PurchaseRequestItemRepository:
        class: AppBundle\Repository\PurchaseRequestItemRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\PurchaseRequestItem' ]

    # Provide repository to fetch node entities
    AppBundle\Repository\SettingRepository:
        class: AppBundle\Repository\SettingRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\Setting' ]

    AppBundle\Util\SettingsProvider:
        class: AppBundle\Util\SettingsProvider
        arguments:
            - "%app.settings%"

    # Provide a mapper for the company identification validation
    AppBundle\Form\Rule\Mapping\CompanyIdentificationRule:
        class: AppBundle\Form\Rule\Mapping\CompanyIdentificationRule
        tags:
            - { name: form_rule_constraint_mapper }

    # Provide repository to fetch redirect entities
    AppBundle\Repository\RedirectRepository:
        class: AppBundle\Repository\RedirectRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\Redirect' ]

    # Provide repository to fetch node entities
    AppBundle\Repository\NodeRepository:
        class: AppBundle\Repository\NodeRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\Node' ]

    AppBundle\EventSubscriber\BuyerRegistrationSubscriber:
        class: AppBundle\EventSubscriber\BuyerRegistrationSubscriber
        arguments:
            - "@router"
            - "@security.token_storage"
        tags:
            - { name: kernel.event_subscriber }

    AppBundle\EventSubscriber\KernelEventSubscriber:
        class: AppBundle\EventSubscriber\KernelEventSubscriber
        arguments:
            - '@request_stack'
            - '@security.token_storage'
            - '@Open\TicketBundle\Services\TicketService'
            - '@AppBundle\Repository\CartRepository'
            - '@AppBundle\Services\OfferService'
        tags:
            - { name: kernel.event_subscriber }

    AppBundle\EventListener\SessionExpiredListener:
        class: AppBundle\EventListener\SessionExpiredListener
        arguments:
            - "@request_stack"
            - "@security.authorization_checker"
            - "@security.token_storage"
            - "@router"
            - "@translator"
            - '%env(int:APP_SESSION_MAX_IDLE_TIME)%'
        tags:
            - {
                name: kernel.event_listener,
                event: kernel.request,
                method: onKernelRequest
            }

    AppBundle\Services\AlstomCustomAttributes:
        arguments:
            - '%izberg_custom_attributes%'

    AppBundle\Services\MailService:
        calls:
            -   method: "setBcc"
                arguments: [ "%env(json:MAIL_BCC)%" ]
        arguments:
            - { fosuser_from_email: "%env(FOSUSER_FROM_EMAIL)%", fosuser_from_name: "%env(FOSUSER_FROM_NAME)%", force_locale: false }
            - '%env(DOMAIN)%'
            - '%env(PROTOCOL)%'
            - '%env(ENVIRONMENT)%'
            - '@AppBundle\Services\SecurityService'
            - '@Symfony\Component\Mailer\MailerInterface'
            - '@doctrine.orm.entity_manager'
            - '@twig'
            - '@router'
            - '@AppBundle\Repository\CompanyRepository'
            - '@AppBundle\Repository\OrderRepository'
            - '@messenger.default_bus'

    AppBundle\Services\MerchantOrderService:
        arguments:
            - '%env(ORDER_MAX_DAY_LIMIT_BEFORE_AUTOCANCEL)%'
            - '%private_tmp_dir%'

    AppBundle\Services\EmailTemplateService:
        public: true
        arguments:
            - "%kernel.project_dir%/config/emails.yml"

    AppBundle\Services\MerchantService:
        arguments:
            - '%env(IZBERG_MERCHANT_BASE_URL)%'
            - { izberg_merchant_carrier: '%env(IZBERG_MERCHANT_CARRIER)%', izberg_merchant_shipping_provider: '%env(IZBERG_MERCHANT_SHIPPING_PROVIDER)%', izberg_merchant_zone: '%env(IZBERG_MERCHANT_ZONE)%'}
            - '%env(CRYPT_KEY)%'

    AppBundle\Services\OfferService:
        arguments:
            - { en: '@Open\IzbergBundle\Algolia\AlgoliaServiceEn', fr: '@Open\IzbergBundle\Algolia\AlgoliaServiceFr', es: '@Open\IzbergBundle\Algolia\AlgoliaServiceEs', de: '@Open\IzbergBundle\Algolia\AlgoliaServiceDe', it: '@Open\IzbergBundle\Algolia\AlgoliaServiceIt', nl: '@Open\IzbergBundle\Algolia\AlgoliaServiceNl' }
            - '%izberg_ignored_attributes%'
            - '%env(STOCK_CLEARANCE_CATEGORY)%'

    AppBundle\Services\OrderPDFGenerator:
        arguments:
            - '%private_tmp_dir%'
        calls:
            - [ 'withFontDir', [ '%kernel.project_dir%/../web/fonts/Open_Sans/' ] ]

    AppBundle\Services\OfferPDFGenerator:
        arguments:
            - '%private_tmp_dir%'
        calls:
            - [ 'withFontDir', [ '%kernel.project_dir%/../web/fonts/Open_Sans/' ] ]

    AppBundle\Services\PaymentService:
        arguments:
            - '%env(CACHE_PAYMENT_TERMS_TTL)%'
            - '%env(PAYMENT_NB_HOURS_BEFORE_REFUND)%'
        calls:
            -   method: "setWebHelpService"
                arguments: [ '@AppBundle\Services\WPSService' ]
            -   method: "setCartService"
                arguments: [ '@AppBundle\Services\CartService' ]

    AppBundle\Services\SearchService:
        arguments:
            - { en: '@Open\IzbergBundle\Algolia\QuerySuggestionServiceEn', fr: '@Open\IzbergBundle\Algolia\QuerySuggestionServiceFr', es: '@Open\IzbergBundle\Algolia\QuerySuggestionServiceEs', de: '@Open\IzbergBundle\Algolia\QuerySuggestionServiceDe', it: '@Open\IzbergBundle\Algolia\QuerySuggestionServiceIt', nl: '@Open\IzbergBundle\Algolia\QuerySuggestionServiceNl' }
            - '%env(json:SUPPORTED_LOCALES)%'

    AppBundle\Services\InvoiceService:
        public: true
        arguments:
            - '%env(IZBERG_DATE_PATTERN)%'
            - '%env(FOSUSER_FROM_EMAIL)%'
            - '%env(FOSUSER_FROM_NAME)%'

    AppBundle\Services\LanguageService:
        public: true
        arguments:
            - '%env(json:SUPPORTED_LOCALES)%'

    Upela\Api\ProductApi:
        arguments:
            - '%env(UPELA_PRE_ORDER_API_URL)%'
            - '%env(UPELA_PRE_ORDER_USERNAME)%'
            - '%env(UPELA_PRE_ORDER_PASSWORD)%'

    AppBundle\Services\UpelaApiService:
        arguments:
            - { url: '%env(UPELA_API_URL)%', support_emails_address: '%env(json:UPELA_SUPPORT_EMAIL_ADDRESS)%' }

    AppBundle\Services\WPSService:
        arguments:
            - '%env(PAYMENT_NB_HOURS_BEFORE_REFUND)%'

    AppBundle\Services\WebHelpIbanService:
        arguments:
            - '%env(WEBHELP_IBAN_ACCOUNT_NAME_USD)%'
            - '%env(WEBHELP_IBAN_USD)%'
            - '%env(WEBHELP_IBAN_ACCOUNT_NAME_EUR)%'
            - '%env(WEBHELP_IBAN_EUR)%'

    AppBundle\Services\ParameterService:
        arguments:
            - '%env(json:SUPPORTED_LOCALES)%'
            - '%env(DISPLAY_FIRST_LEVEL_CATEGORY_ONLY)%'

    AppBundle\Form\Type\RegistrationFormType:
        arguments:
            - '%fos_user.model.user.class%'
            - '@Symfony\Contracts\Translation\TranslatorInterface'
            - '@Symfony\Component\HttpFoundation\RequestStack'
            - '@AppBundle\Services\CompanyService'
            - '@ddeboer_vatin.vatin_validator'
            - '@AppBundle\Services\CountryService'
        tags:
            - { name: form.type, alias: front_user_registration }

    AppBundle\Validator\Constraints\SlugExistValidator:
        arguments:
            - '@Symfony\Component\Routing\RouterInterface'
            - '@Doctrine\ORM\EntityManagerInterface'
            - '@AppBundle\Repository\NodeRepository'
            - '@AppBundle\Repository\RedirectRepository'
            - '@Symfony\Component\HttpFoundation\RequestStack'

    AppBundle\Form\AddressWoCountryEditableForm:
        arguments:
            - '@Symfony\Contracts\Translation\TranslatorInterface'

    AppBundle\Form\AddressForm:
        arguments:
            - '@Symfony\Contracts\Translation\TranslatorInterface'

    AppBundle\Services\FeesOperatorShippingService:
        public: true
        calls:
            - [ setEuroCommisionId, [ '%env(IZBERG_OPERATOR_FEES_EURO_COMMISSION_ID)%' ] ]
            - [ setUsdCommissionId, [ '%env(IZBERG_OPERATOR_FEES_USD_COMMISSION_ID)%' ] ]

    AppBundle\Services\ShippingService:
        calls:
            - [ setShippingProductId, ['%env(int:IZBERG_SHIPPING_PRODUCT_ID)%']]
            - [ setCarrierEndpoint, ['%env(IZBERG_CARRIER_ENDPOINT)%']]
