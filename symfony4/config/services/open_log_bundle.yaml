services:
  _defaults:
    autowire: true
    autoconfigure: true
    public: false

  Open\LogBundle\:
    resource: '../../src/Open/LogBundle'
    exclude:
      - '../../src/Open/LogBundle/DependencyInjection/'
      - '../../src/Open/LogBundle/Entity/'
      - '../../src/Open/LogBundle/Kernel.php'
      - '../../src/Open/LogBundle/Tests/'


  monolog.formatter.user_data:
    class: Monolog\Formatter\LineFormatter
    arguments:
      - "[%%datetime%%] %%channel%%.%%level_name%%: %%message%% %%context%% %%extra%%\n"

  Open\LogBundle\Processor\UserDataProcessor:
    tags:
      - { name: monolog.processor }

  Open\LogBundle\Processor\ProcessIdProcessor:
    tags:
      - { name: monolog.processor }
