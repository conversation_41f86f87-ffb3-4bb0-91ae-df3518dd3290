services:

  _defaults:
    autowire: true
    autoconfigure: true
    public: false

  Open\BackBundle\:
    resource: '../../src/Open/BackBundle'
    exclude:
      - '../../src/Open/BackBundle/DependencyInjection/'
      - '../../src/Open/BackBundle/Entity/'
      - '../../src/Open/BackBundle/Kernel.php'
      - '../../src/Open/BackBundle/Tests/'

  # Provide a mapper for the slug validation
  Open\BackBundle\Form\Rule\Mapping\SlugHttpRule:
    class: Open\BackBundle\Form\Rule\Mapping\SlugHttpRule
    tags:
      - { name: form_rule_constraint_mapper }

  # Provide a mapper for the slug validation
  Open\BackBundle\Form\Rule\Mapping\SlugFormatRule:
    class: Open\BackBundle\Form\Rule\Mapping\SlugFormatRule
    tags:
      - { name: form_rule_constraint_mapper }


  # Provide a mapper for the slug validation
  Open\BackBundle\Form\Rule\Mapping\RedirectDestinationFormatRule:
    class: Open\BackBundle\Form\Rule\Mapping\RedirectDestinationFormatRule
    tags:
      - { name: form_rule_constraint_mapper }

  # Provide a mapper for the slug validation
  # We want to do the Existence verification last
  # So this rule must be defined after the other slug rules
  #admin.form.rule.slug_exist_mapper:
  #  class: Open\BackBundle\Form\Rule\Mapping\SlugExistRule
  #  tags:
  #    - { name: form_rule_constraint_mapper }

  Open\BackBundle\Service\HealthMonitorService:
    class: Open\BackBundle\Service\HealthMonitorService
    arguments:
      - '@doctrine.orm.entity_manager'
      - '@Open\IzbergBundle\Api\AuthenticationApi'
      - '@Open\IzbergBundle\Api\ApiClientManager'
      - '@Open\IzbergBundle\Api\ApiConfigurator'
      - '@Open\IzbergBundle\Service\RedisService'
      - '@AppBundle\Services\JobService'

