services:
  _defaults:
    autowire: true
    autoconfigure: true
    public: false

  Open\TicketBundle\:
    resource: '../../src/Open/TicketBundle'
    exclude:
      - '../../src/Open/TicketBundle/DependencyInjection/'
      - '../../src/Open/TicketBundle/Entity/'
      - '../../src/Open/TicketBundle/Kernel.php'
      - '../../src/Open/TicketBundle/Tests/'

  Open\TicketBundle\Controller\:
    resource: '../../src/Open/TicketBundle/Controller/'
    tags: [ 'controller.service_arguments' ]

  #    open_ticket.example:
  #        class: Open\TicketBundle\Example
  #        arguments: ["@service_id", "plain_value", "%parameter%"]

  Open\TicketBundle\FilterQueryBuilder\TicketQueryBuilder:
    class: Open\TicketBundle\FilterQueryBuilder\TicketQueryBuilder

  Open\TicketBundle\Services\TicketService:
    class: Open\TicketBundle\Services\TicketService
    arguments:
      - "@doctrine.orm.entity_manager"
      - "@knp_paginator"
      - '@AppBundle\Services\SecurityService'
      - '@Open\TicketBundle\FilterQueryBuilder\TicketQueryBuilder'
      - '@Open\IzbergBundle\Api\MessageApi'

  ticket.type:
    class: Open\TicketBundle\Form\TicketType
    arguments: ['@AppBundle\Services\SecurityService']
    tags:
      - { name: form.type }

  ticket.waylf.type:
    class: Open\TicketBundle\Form\TicketWaylfType
    arguments: ['@AppBundle\Services\SecurityService']
    tags:
      - { name: form.type }

  ticket.message.type:
    class: Open\TicketBundle\Form\TicketMessageType
    arguments: ['@AppBundle\Services\SecurityService']
    tags:
      - { name: form.type }
