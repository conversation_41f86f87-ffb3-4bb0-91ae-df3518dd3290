#index:
#    path: /
#    controller: Open\FrontBundle\Controller\DefaultController::indexAction

redirect_homepage:
    path: '/'
    controller: Symfony\Bundle\FrameworkBundle\Controller\RedirectController
    defaults:
        path: '/en'
        permanent: true

home_locale:
    path: '/{_locale}/'
    defaults:
        _controller: Open\FrontBundle\Controller\DefaultController::indexAction
    requirements:
        _locale: 'en|fr|es|de|it|nl'
    options:
        i18n: true

routers_api_platform:
    resource: 'routes/api_platform.yaml'
fos_js_routing:
    resource: 'routes/fos_js_routing.yaml'
fos_user:
    resource: 'routes/fos_user.yaml'
framework:
    resource: 'routes/framework.yaml'

when@dev:
    dev_routes:
        type: directory
        resource: 'routes/dev/'

controllers_appbundle:
    resource: '../src/AppBundle/Controller'
    type: attribute
    prefix: '/{_locale}'
    requirements:
        _locale: 'en|fr|es|de|it|nl'
    defaults:
        _locale: 'en'

controllers_open_BackBundle:
    resource: '../src/Open/BackBundle/Controller/*Controller.php'
    type: attribute
    prefix: '/{_locale}'
    requirements:
        _locale: 'en|fr|es|de|it|nl'
    defaults:
        _locale: 'en'
controllers_open_FrontBundle:
    resource: '../src/Open/FrontBundle/Controller/*Controller.php'
    type: attribute
    prefix: '/{_locale}'
    requirements:
        _locale: 'en|fr|es|de|it|nl'
    defaults:
        _locale: 'en'
controllers_open_FrontVendorBundle:
    resource: '../src/Open/FrontVendorBundle/Controller/*Controller.php'
    type: attribute
#    prefix: '/{_locale}'
    requirements:
        _locale: 'en|fr|es|de|it|nl'
    defaults:
        _locale: 'en'
controllers_open_IzbergBundle:
    resource: '../src/Open/IzbergBundle/Controller/*Controller.php'
    type: attribute
controllers_open_TicketBundle:
    resource: '../src/Open/TicketBundle/Controller/*Controller.php'
    type: attribute
    prefix: '/{_locale}'
    requirements:
        _locale: 'en|fr|es|de|it|nl'
    defaults:
        _locale: 'en'
controllers_open_WebhelpBundle:
    resource: '../src/Open/WebhelpBundle/Controller/*Controller.php'
    type: attribute
    prefix: '/{_locale}'
    requirements:
        _locale: 'en|fr|es|de|it|nl'
    defaults:
        _locale: 'en'
controllers_health_monitor:
    resource: '../src/Open/BackBundle/Controller/HealthMonitor.php'
    type: attribute
    prefix: '/{_locale}'
    requirements:
        _locale: 'en|fr|es|de|it|nl'
    defaults:
        _locale: 'en'
controllers_redis_key_list:
    resource: '../src/Open/BackBundle/Controller/RedisKeyList.php'
    type: attribute
    prefix: '/{_locale}'
    requirements:
        _locale: 'en|fr|es|de|it|nl'
    defaults:
        _locale: 'en'


login_check:
    path: /login_check

api_login_check:
    path: /middleware/api/login_check
    options:
        i18n: false

#swagger_ui:
#    path: /middleware/docs
#    controller: api_platform.swagger.action.ui
#    options:
#        i18n: false
api_platform:
    resource: .
    type: api_platform
    prefix: /middleware/api

api_doc:
    path: /middleware/docs
    controller: api_platform.action.documentation

download_doc:
    path: /download/api/doc
    controller: Api\Core\Controller\DownloadJsonDocController
    options:
        i18n: false

login_failure_route:
    path: /login/failure
    defaults: { _controller: Open\FrontBundle\Controller\LoginController::loginFailureAction }
    options:
        i18n: false

login_success_route:
    path: /login/success
    defaults: { _controller: Open\FrontBundle\Controller\LoginController::loginSuccessAction }
    options:
        i18n: false

admin_logout:
    path:  /admin/logout
    defaults: { _controller: FOSUserBundle:Security:logout }
    options:
        i18n: false

admin_login:
    path: /admin/login
    controller: Symfony\Bundle\FrameworkBundle\Controller\RedirectController::redirectAction
    defaults:
        route: login
        permanent: true
    options:
        i18n: false

admin_check:
    path:  /admin/login_check
    defaults:
        _controller: FrameworkBundle:Redirect:redirect
        route: login_check
        permanent: true
    options:
        i18n: false

admin_tickets_list:
    path: /admin/tickets
    defaults: { _controller: OpenTicketBundle:Ticket:listAdminTickets}
    options:
        i18n: false

admin_ticket_edit:
    path: /admin/ticket/edit
    defaults: { _controller: OpenTicketBundle:Ticket:editAdminTicket}
    options:
        i18n: false

admin_ticket_create:
    path: /admin/ticket/create
    defaults: { _controller: OpenTicketBundle:Ticket:createAdminTicket}
    options:
        i18n: false

login:
    path: /login
    defaults: { _controller: Open\FrontBundle\Controller\LoginController::loginAction }
    options:
        i18n: false

catch_all:
    path:     /{_locale}/{slug}
    defaults: { _controller: Open\FrontBundle\Controller\DefaultController::catchAllAction }
    options:
        i18n: true
    requirements:
        _locale: 'en|fr|es|de|it|nl'
        slug: ".*" #probably not needed since the '/' route is already used for the homepage


