# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices/configuration.html#application-related-configuration
parameters:
    # This parameter defines the codes of the locales (languages) enabled in the application
    app_locales: en|fr|de|es|it|nl

    default_redirect_type: 302

    ########################## IZBERG #################################

    #########################
    # IZBERG CATEGORY ICONS #
    #########################
    izberg_category_icons_path: images/categories
    izberg_category_icons:
        1508: battery.jpg
        1506: cables.jpg

    ################################################
    # LIST OF CATEGORIES, that must not be displayed
    ################################################
    env(HS_SCRIPT): ~

    ############################
    # STOCK CLEARANCE CATEGORY
    ############################

    env(STOCK_CLEARANCE_CATEGORY): ~

    captcha_enabled: false
    captcha_secret: ~

    ############################################
    izberg_algolia_facets: # facet indispensable
        - 'product.application_categories'

    env(LOGS_DIR): '%kernel.logs_dir%'

    ###################################################
    # temporary directory used for generating files
    ###################################################
    private_tmp_dir: '%kernel.project_dir%/temp'

    ####################################################
    # From parameters in old config.yml
    ####################################################
    slider_background_directory: '%kernel.project_dir%/public/images/sliders_backgrounds/'
    logo_directory: '%kernel.project_dir%/public/images/logos/'
    locale: en
    civs:
        profile.civ.mr: mr
        profile.civ.ms: ms

    historized_entities:
        - AppBundle\Entity\ZipCode
        - AppBundle\Entity\Region
        - AppBundle\Entity\OpeningTime
        - AppBundle\Entity\Document
        - AppBundle\Entity\Country
        - AppBundle\Entity\Contact
        - AppBundle\Entity\Address
        - AppBundle\Entity\NodeContent
        - AppBundle\Entity\Node
        - AppBundle\Entity\Company
        - AppBundle\Entity\Site
        - AppBundle\Entity\User
        - AppBundle\Entity\Setting
        - AppBundle\Entity\Redirect
        - Open\TicketBundle\Entity\Ticket
        - Open\TicketBundle\Entity\TicketMessage

    e_catalog_url: '%env(default::E_CATALOG_URL)%'

imports:
    - { resource: services/app_bundle.yaml }
    - { resource: services/ews_recaptcha_bundle.yaml }
    - { resource: services/twig_extension.yaml }
    - { resource: services/open_back_bundle.yaml }
    - { resource: services/open_ticket_bundle.yaml }
    - { resource: services/open_front_vendor_bundle.yaml }
    - { resource: services/upela_bundle.yaml }
    - { resource: services/open_log_bundle.yaml }

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
#        bind:
#            $locales: '%app_locales%'
#            $defaultLocale: '%locale%'
    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/App'
        exclude:
            - '../src/App/DependencyInjection/'
            - '../src/App/Entity/'
            - '../src/App/Kernel.php'
            - '../src/App/Tests/'

    # controllers are imported separately to make sure services can be injected
    # as action arguments even if you don't extend any base controller class
    App\Controller\:
        resource: '../src/App/Controller/'
        tags: ['controller.service_arguments']

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones

    Ddeboer\Vatin\Validator: '@ddeboer_vatin.vatin_validator'
    FOS\UserBundle\Form\Factory\FactoryInterface $registrationFormFactory: '@fos_user.registration.form.factory'

    Open\BackBundle\Menu\Builder:
        arguments:
            $translator: '@translator.default'
            $factory: '@knp_menu.factory'
    Open\BackBundle\Menu\MainMenuProvider:
        arguments:
            $builder: '@Open\BackBundle\Menu\Builder'
        tags: [ 'knp_menu.provider' ]
