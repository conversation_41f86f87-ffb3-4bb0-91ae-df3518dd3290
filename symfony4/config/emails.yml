
BUYER_ACCOUNT_UNCOMPLETED_AFTER_X_DAYS_TO_BUYER:
  body: "Hello {{firstName}} {{lastName}}. Your company {{companyName}} has been created for {{Xdays}} days. Please complete complete your profile to enjoy the benefits of using StationOne."
  variables:
    - name : "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "companyName"
      default: "my buyer company"
    - name: "Xdays"
      default: 5
    - name: "host"
      default: "https://stationone.com/"


SPECIFIC_PRICE_UPLOAD_TO_VENDOR:
  body: "To {{firstName}} {lastName}} {{merchantName}} SPECIFIC PRICES UPLOAD REPORT <br>{{message}}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "merchantName"
      default: "merchantName"
    - name: "message"
      default: "your specific price file has been uploaded"
    - name: "host"
      default: "https://stationone.com/"

PAYMENT_TERMPAYMENT_BANK_TRANSFER_INSCTRUCTIONS_TO_BUYER:
  body: "Bonjour {{firstName}} {{lastName}} <br> Votre commande {{orderNumber}} en date du {{orderDate}} est en attente de traitement depuis plus de {{nbDays}} jours. Merci de réaliser un transfert avec les informations suivantes: iban: {{iban}}, clé de réconciliation: {{reconciliationKey}} Montant restant à payer: {{remainingAmount}} {{currency}}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "orderNumber"
      default: "orderNumber"
    - name: "orderDate"
      default: "2018-11-25"
    - name: "nbDays"
      default: 5
    - name: "ibanAccountName"
      default: "iban account name"
    - name: "iban"
      default: "FR14 2004 1010 0505 0001 3M02 606"
    - name: "reconciliationKey"
      default: "0500013M026"
    - name: "remainingAmount"
      default: 375.5
    - name: "currency"
      default: "euro"
    - name: "host"
      default: "https://stationone.com/"
    - name: "invoiceDate"
      default: "2018-11-25"
    - name: "invoiceAmount"
      default: 500
    - name: "invoiceNumber"
      default: "1564"
    - name: "dueDate"
      default: "2018-12-03"


PAYMENT_TERMPAYMENT_BANK_TRANSFER_REMINDER_TO_BUYER:
  body: "Bonjour {{firstName}} {{lastName}} <br> Votre commande {{orderNumber}} en date du {{invoiceDate}} est en attente de traitement depuis plus de {{nbDays}} jours. Merci de réaliser un transfert avec les informations suivantes: iban: {{iban}}, clé de réconciliation: {{reconciliationKey}} Montant restant à payer: {{remainingAmount}} {{currency}}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "orderNumber"
      default: "orderNumber"
    - name: "orderDate"
      default: "03/12/2018"
    - name: "nbDays"
      default: 5
    - name: "ibanAccountName"
      default: "iban account name"
    - name: "iban"
      default: "FR14 2004 1010 0505 0001 3M02 606"
    - name: "reconciliationKey"
      default: "0500013M026"
    - name: "remainingAmount"
      default: 375.5
    - name: "currency"
      default: "euro"
    - name: "host"
      default: "https://stationone.com/"
    - name: "invoiceDate"
      default: "03/12/2018"
    - name: "invoiceAmount"
      default: 500
    - name: "invoiceNumber"
      default: "1564"
    - name: "dueDate"
      default: "03/12/2018"

BUYER_REFUND_DONE:
  body: "Hello {{firstName}} {{lastName}}. The refund of amount {{amount}} {{currency}} for your order {{orderNumber}} (invoice {{invoiceNumber}}) has been performed.
          <p>List of products:{%for item in products%} <p>{{item.sellerReference}} {{item.offerName}}</p> {%endfor%}<p>
          Send by : {{vendorName}}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "amount"
      default: 37.5
    - name: "orderNumber"
      default: "orderNumber"
    - name: "currency"
      default: "euro"
    - name: "host"
      default: "https://stationone.com/"
    - name: "memo"
      default: "refund reason"
    - name: "vendorName"
      default: "vendorName"
    - name: "invoiceNumber"
      default: "invoiceNumber"
    - name: "products"
      children:
        - name: "sellerReference"
          default: "seller reference"
        - name: "offerName"
          default: "offer name"
SEND_INVOICE_TO_BUYER:
  body: "Hello {{firstName}} {{lastName}}. A new invoice is available here: {{url}}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "url"
      default: "http://linktoinvoice.com/invoice/idinvoice"
    - name: "host"
      default: "https://stationone.com/"
    - name: "orderNumber"
      default: "orderNumber"
    - name: "orderDate"
      default: "23/12/2018"
    - name: "customerInvoiceNumber"
      default: "ABC123"
    - name: "currency"
      default: "EUR"
    - name: "amount"
      default: "160.00"

SEND_CREDIT_NOTE_TO_BUYER:
  body: "Hello {{firstName}} {{lastName}}. A new credit note is available here: {{url}}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "url"
      default: "http://linktoinvoice.com/invoice/idinvoice"
    - name: "host"
      default: "https://stationone.com/"
    - name: "orderNumber"
      default: "orderNumber"
    - name: "orderDate"
      default: "23/12/2018"
    - name: "customerInvoiceNumber"
      default: "ABC123"
    - name: "currency"
      default: "EUR"
    - name: "amount"
      default: "160.00"



CART_ASSIGN_ACCEPTED_TO_BUYER:
  body: "Hello  {{firstName}} {{lastName}}. {{ recipientAssignation }} has accept cart assignation."
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "recipientAssignation"
      default: "Jean Dupont"
    - name: "host"
      default: "https://stationone.com/"
    - name: "cartId"
      default: "Cart ID"


CART_ASSIGN_TO_BUYER:
  body: "Hello  {{firstName}} {{lastName}}. The cart {{ url }} has been assigned to you by {{authorAssignation}}. Comment from the author of the assignation {{ comment }}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "authorAssignation"
      default: "Jean Dupont"
    - name: "url"
      default: "https//stationone.com/cart/123"
    - name: "comment"
      default: "this is the assignation comment"
    - name: "host"
      default: "https://stationone.com/"


CART_ASSIGN_REJECTED_TO_BUYER:
  body: "Hello  {{firstName}} {{lastName}}. {{ recipientAssignation }} has rejected the cart {{ url }}. Comment: {{ comment }}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "recipientAssignation"
      default: "Jean Dupont"
    - name: "url"
      default: "https//stationone.com/cart/123"
    - name: "comment"
      default: "this is the assignation comment"
    - name: "host"
      default: "https://stationone.com/"
    - name: "cartId"
      default: "Cart ID"


TERM_PAYMENT_REQUEST_ACCEPTED_TO_BUYER:
  body: "Hello  {{firstName}} {{lastName}}.  OPERATOR accepts payment term request for your company {{companyName}}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "companyName"
      default: "companyName"
    - name: "host"
      default: "https://stationone.com/"



TERM_PAYMENT_REQUEST_TO_OPERATOR:
  body: "Hello  {{firstName}} {{lastName}}. {{companyName}} Owner requests to add payment terms. Link to edit company: {{ url }}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "companyName"
      default: "companyName"
    - name: "url"
      default: "https://stationone.com/admin/company/12"
    - name: "host"
      default: "https://stationone.com/"



TERM_PAYMENT_REQUEST_REJECTED_TO_BUYER:
  body: "Hello  {{firstName}} {{lastName}}. Station One operators has rejected your payment term request : {{comment}}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "comment"
      default: "You are not authorized to use this. Please contact us for more information"
    - name: "host"
      default: "https://stationone.com/"



BUYER_ACCOUNT_CREATION_TO_BUYER:
  body: "Hello  {{firstName}} {{lastName}}. Congratulation, the registration of your company {{companyName}} has succeed"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "companyName"
      default: "companyName"
    - name: "host"
      default: "https://stationone.com/"


RESET_PASSWORD_TO_USER:
  body: "Hello  {{firstName}} {{lastName}}.  Follow link to change password : {{url}}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "url"
      default: "https://stationone.com/reset/"
    - name: "host"
      default: "https://stationone.com/"



BUYER_ACCOUNT_NEW_USER_TOUSER:
  body: "Hello  {{firstName}} {{lastName}}. You can finish your account creation by setting your personal password by following this link : {{url}}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "url"
      default: "https://stationone.com/reset/"
    - name: "host"
      default: "https://stationone.com/"



VENDOR_ACCOUNT_VALIDATED_TO_VENDOR:
  body: "Hello {{firstName}} {{lastName}} Your vendor account for the company {{companyName}} has been accepted by StationOne operators"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "companyName"
      default: "my StationOne online shop"
    - name: "host"
      default: "https://stationone.com/"
    - name: "email"
      default: "email"


VENDOR_ACCOUNT_CREATION_TO_VENDOR:
  body: "Hello {{firstName}} {{lastName}} Your vendor account for the company {{companyName}} has been successfully registered. Tour registration is now being reviewed by the StationOne team"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "companyName"
      default: "my StationOne online shop"
    - name: "host"
      default: "https://stationone.com/"


VENDOR_ACCOUNT_CREATION_TO_OPERATOR:
  body: "Hello {{firstName}} {{lastName}}. A new vendor account has been created: {{merchantFirstName}} {{merchantLastName}} {{merchantEmail}} {{companyName}}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "companyName"
      default: "my StationOne online shop"
    - name: "merchantFirstName"
      default: "Jean"
    - name : "merchantLastName"
      default: "Dupont"
    - name : "merchantEmail"
      default: "<EMAIL>"
    - name: "host"
      default: "https://stationone.com/"
    - name: "url"
      default: "http://stationone.com/admin/vendor/12"


VENDOR_ACCOUNT_REJECTED_TO_VENDOR:
  body: "Hello {{firstName}} {{lastName}}. your registration request for your company {{companyName}} has been rejected by the StationOne team for the following reason: {{comment}}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "companyName"
      default: "my StationOne online shop"
    - name: "comment"
      default: "Some information about your company are missing. Please contact us for more information"
    - name: "host"
      default: "https://stationone.com/"



ORDER_NEW_TOCONFIRM_TO_VENDOR:
  body: "Hello {{firstName}} {{lastName}}. A new command is available {{ orderNumber}}. You have to process it or have {{paymentNbHoursBeforeRefund}} hours to refund order if you can't deliver."
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "orderNumber"
      default: "DBC123456"
    - name: "paymentNbHoursBeforeRefund"
      default: 5
    - name: "host"
      default: "https://stationone.com/"
    - name: "buyer"
      default: "name of the buyer"

BUYER_ACCOUNT_IN_PENDING_VALIDATION_TO_OPERATOR:
  body: 'A new buyer account has finalized his registration: {{companyName}}<br><br>

           Please find below information associated to the requestor:<br>
           Firstname: {{firstName}}<br>
           Name: {{lastName}}<br>
           Email address: {{email}}<br><br>

           Please login to the administration interface to accept/reject this account.<br><br>
           <a href="{{host}}">{{host}}</a>'
  variables:
    - name: 'companyName'
      default: 'companyName'
    - name: 'firstName'
      default: 'firstName'
    - name: 'lastName'
      default: 'lastName'
    - name: 'email'
      default: 'email'
    - name: 'companyId'
      default: "https://stationone.com/"
    - name: 'link'
      default: 'link'

BUYER_ACCOUNT_VALIDATED_TO_BUYER:
  body: "Hello {{firstName}} {{lastName}}. Your company {{companyName}} has been validated by StationOne team"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "companyName"
      default: "my buyer company"
    - name: "host"
      default: "https://stationone.com/"
    - name: "email"
      default: "email"


BUYER_ACCOUNT_DEACTIVATED_TO_BUYER:
  body: "Hello {{firstName}} {{lastName}}. Your company {{companyName}} has been de-activated by the StationOne team. reason : {{comment}}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "companyName"
      default: "my buyer company"
    - name: "comment"
      default: "You have not respected the conditions of the service. Please contact the support for more information"
    - name: "host"
      default: "https://stationone.com/"


BUYER_ACCOUNT_REJECTED_TO_BUYER:
  body: "Hello {{firstName}} {{lastName}}. The registration of your company {{companyName}} has been refused by the StationOne team. reason : {{comment}}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "companyName"
      default: "my buyer company"
    - name: "comment"
      default: "Some mandatory information are missing about your company. Please contact the support for more information"
    - name: "host"
      default: "https://stationone.com/"


BUYER_ACCOUNT_INFO_UPDATED_TO_BUYER:
  body: "Hello {{firstName}} {{lastName}}. Your company {{companyName}} has been modified by a operator/admin. Please check the modifications by following this link: {{url}} "
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "companyName"
      default: "my buyer company"
    - name: "url"
      default: "https://stationone.com/company/1234"
    - name: "host"
      default: "https://stationone.com/"



ILLEGAL_CONTENT_TO_OPERATOR:
  body: "Hello {{firstName}} {{lastName}}. An illegal content has been reported by {{reporterFirstName}} {{reporterLastName}} from company {{reporterCompanyName}} on the following {{url}} <br> {{content}}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "reporterFirstName"
      default: "reporterFirstName"
    - name: "reporterLastName"
      default: "reporterLastName"
    - name: "reporterCompanyName"
      default: "my buyer company"
    - name: "url"
      default: "https://stationone.com/illegalcontent/1234"
    - name: "content"
      default: "this is illegal content. Please moderate me"
    - name: "host"
      default: "https://stationone.com/"


PAYMENT_PREPAYMENT_BANK_TRANSFER_INSCTRUCTIONS_TO_BUYER:
  body: "Hello {{firstName}} {{lastName}}. <br> Your order is {{orderNumber}} is waiting for payment. Please, make a transfer with the following information: iban: {{iban}}, reconciliationKey: {{reconciliationKey}}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "orderNumber"
      default: "DB123X45"
    - name: "iban"
      default: "DE89 3704 0044 0532 0130 00"
    - name: "ibanAccountName"
      default: "iban account name"
    - name: "reconciliationKey"
      default: "********"
    - name: "host"
      default: "https://stationone.com/"
    - name: "currency"
      default: "EUR"
    - name : "amount"
      default: "160.00"



BUYER_ACCOUNT_CREATION_TO_OPERATOR:
  body: "Hello {{firstName}} {{lastName}}. A new buyer account has been created and need to be moderate. Information about the created account: {{accountCreatedFirstName}} {{accountCreatedLastName}} {{accountCreatedEmail}} {{accountCreatedCompanyName}}. You can edit this new account by following this link: {{url}}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "accountCreatedFirstName"
      default: "accountCreatedFirstName"
    - name: "accountCreatedLastName"
      default: "accountCreatedLastName"
    - name: "accountCreatedEmail"
      default: "accountCreatedEmail"
    - name: "accountCreatedCompanyName"
      default: "accountCreatedCompanyName"
    - name: "url"
      default: "https://stationone.com/admin/user/1234"
    - name: "host"
      default: "https://stationone.com/"



TICKET_THREAD_UPDATED_BY_VENDOR_TO_BUYER:
  body: "Hello {{firstName}} {{lastName}}. Vendor {{vendor}} has replied to your message: {{content}}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "vendor"
      default: "Vendor Name"
    - name: "content"
      default: "Hello, I confirm that we can deliver this item."
    - name: "host"
      default: "https://stationone.com/"
    - name: "ticketNumber"
      default: "123ABC"
    - name: "subject"
      default: "this is the subject"
    - name: "url"
      default : "https://stationone.com/ticket/123"



ORDER_REFUSED_BY_VENDOR_PRE_CREDIT_CARD_TO_BUYER:
  body: "Hello {{firstName}} {{lastName}}.  The merchant {{vendor}} has rejected your command {{orderNumber}}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "vendor"
      default: "Vendor Name"
    - name: "orderNumber"
      default: "DB123X45"
    - name: "host"
      default: "https://stationone.com/"
    - name: "cancel_msg"
      default: "Merchant Order Cancel Message"

ORDER_REFUSED_BY_VENDOR_PRE_BANK_TRANSFER_TO_BUYER:
  body: "Hello {{firstName}} {{lastName}}.  The merchant {{vendor}} has rejected your command {{orderNumber}}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "vendor"
      default: "Vendor Name"
    - name: "orderNumber"
      default: "DB123X45"
    - name: "host"
      default: "https://stationone.com/"
    - name: "cancel_msg"
      default: "Merchant Order Cancel Message"


ORDER_REFUSED_BY_VENDOR_TERM_BANK_TRANSFER_TO_BUYER:
  body: "Hello {{firstName}} {{lastName}}.  The merchant {{vendor}} has rejected your command {{orderNumber}}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "vendor"
      default: "Vendor Name"
    - name: "orderNumber"
      default: "DB123X45"
    - name: "host"
      default: "https://stationone.com/"
    - name: "cancel_msg"
      default: "Merchant Order Cancel Message"


ORDER_CANCEL_AUTO_NO_VENDOR_CONFIRM_PRE_CREDIT_CARD_TO_BUYER:
  body: "Hello {{firstName}} {{lastName}}. Your order {{orderNumber}} has been cancelled. Please contact the StationOne support for more information"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "orderNumber"
      default: "DB123X45"
    - name: "host"
      default: "https://stationone.com/"
    - name: "vendor"
      default: "name of the vendor"


ORDER_CANCEL_AUTO_NO_VENDOR_CONFIRM_TERM_BANK_TRANSFER_TO_BUYER:
  body: "Hello {{firstName}} {{lastName}}. Your order {{orderNumber}} has been cancelled. Please contact the StationOne support for more information"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "orderNumber"
      default: "DB123X45"
    - name: "host"
      default: "https://stationone.com/"
    - name: "vendor"
      default: "name of the vendor"


ORDER_CANCEL_AUTO_NO_VENDOR_CONFIRM_PRE_BANK_TRANSFER_TO_BUYER:
  body: "Hello {{firstName}} {{lastName}}. Your order {{orderNumber}} has been cancelled. Please contact the StationOne support for more information"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "orderNumber"
      default: "DB123X45"
    - name: "host"
      default: "https://stationone.com/"
    - name: "vendor"
      default: "name of the vendor"



ORDER_CANCEL_AUTO_NO_PAYMENT_RECEIVED_TO_BUYER:
  body: "Hello {{firstName}} {{lastName}}. Your order {{orderNumber}} has been cancelled because the payment has not been received within the specified period. "
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "orderNumber"
      default: "DB123X45"
    - name: "host"
      default: "https://stationone.com/"


#TODO: Ajouter l'url vers la page détail de la commande

ORDER_CONFIRMED_BY_VENDOR_TO_BUYER:
  body: "<p>Hello {{firstName}} {{lastName}}.</p> <p>Merchant {{vendor}} has validated your order {{orderNumber}}.</p> <p>List of validated items:{%for item in validatedItems%} <p>{{item.name}} {{item.quantity}} {{item.itemImageUrl}} {{item.amountVatIncluded}} {{item.amount}} {{item.currency}} </p> {%endfor%}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "orderNumber"
      default: "DB123X45"
    - name: "vendor"
      default: "Vendor Name"
    - name: "amount"
      default: 130
    - name: "amountVatIncluded"
      default: 153
    - name: "host"
      default: "https://stationone.com/"
    - name: "validatedItems"
      children:
        - name: "name"
          default: "nom du produit"
        - name: "quantity"
          default: 12
        - name: "itemImageUrl"
          default: "https://photo.123bearing.com/6201-C3-FAG/A-8-6201-C3-FAG-FR.jpg"
        - name: "amountVatIncluded"
          default: 153
        - name: "amount"
          default: 130
        - name: "currency"
          default: "eur"
        - name: "manufacturerRef"
          default: "Manufacturer ref"
        - name: "sellerRef"
          default: "Seller ref"
    - name: "cancelledItems"
      children:
        - name: "name"
          default: "nom du produit"
        - name: "quantity"
          default: 12
        - name: "itemImageUrl"
          default: "https://photo.123bearing.com/6201-C3-FAG/A-8-6201-C3-FAG-FR.jpg"
        - name: "amountVatIncluded"
          default: 153
        - name: "amount"
          default: 130
        - name: "currency"
          default: "eur"
        - name: "manufacturerRef"
          default: "Manufacturer ref"
        - name: "sellerRef"
          default: "Seller ref"


#TODO: Ajouter l'url vers la page détail de la commande
ORDER_PROCESSED_BY_VENDOR_TO_BUYER:
  body: "<p>Hello {{firstName}} {{lastName}}.</p> <p>Merchant {{vendor}} has processed your order {{orderNumber}}.</p> </p> <p>List of validated items:{%for item in validatedItems%} <p>{{item.name}} {{item.quantity}} {{item.itemImageUrl}} {{item.amountVatIncluded}} {{item.amount}} {{item.currency}} </p> {%endfor%}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "orderNumber"
      default: "DB123X45"
    - name: "vendor"
      default: "Vendor Name"
    - name: "amount"
      default: 130
    - name: "amountVatIncluded"
      default: 153
    - name: "host"
      default: "https://stationone.com/"
    - name: "validatedItems"
      children:
        - name: "name"
          default: "nom du produit"
        - name: "quantity"
          default: 12
        - name: "itemImageUrl"
          default: "https://photo.123bearing.com/6201-C3-FAG/A-8-6201-C3-FAG-FR.jpg"
        - name: "amountVatIncluded"
          default: 153
        - name: "amount"
          default: 130
        - name: "currency"
          default: "eur"
        - name: "manufacturerRef"
          default: "Manufacturer ref"
        - name: "sellerRef"
          default: "Seller ref"
    - name: "cancelledItems"
      children:
        - name: "name"
          default: "nom du produit"
        - name: "quantity"
          default: 12
        - name: "itemImageUrl"
          default: "https://photo.123bearing.com/6201-C3-FAG/A-8-6201-C3-FAG-FR.jpg"
        - name: "amountVatIncluded"
          default: 153
        - name: "amount"
          default: 130
        - name: "currency"
          default: "eur"
        - name: "manufacturerRef"
          default: "Manufacturer ref"
        - name: "sellerRef"
          default: "Seller ref"

# Inform operators that the ticket he has created has been taken into account
TICKET_THREAD_NEW_FROM_OPERATOR_TO_OPERATOR:
  body: "Hello {{firstName}} {{lastName}}. Your ticket {{subject}} has been taken into account. Your can consult it by following this link {{url}}  <br>Content: {{content}}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "subject"
      default: "this is the subject of the message"
    - name: "url"
      default: "https://stationone.com/ticket/123"
    - name: "content"
      default: "this is the content of the message"
    - name: "host"
      default: "https://stationone.com/"
    - name: "ticketNumber"
      default: "ABC123"


# Inform the user that the ticket he has created has been taken into account (discussion created)
TICKET_THREAD_NEW_FROM_BUYER_TO_BUYER:
  body: "Hello {{firstName}} {{lastName}}. Your ticket {{subject}} has been taken into account.  Your can consult it by following this link {{url}}  <br>Content: {{content}}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "subject"
      default: "this is the subject of the message"
    - name: "url"
      default: "https://stationone.com/ticket/123"
    - name: "content"
      default: "this is the content of the message"
    - name: "host"
      default: "https://stationone.com/"
    - name: "ticketNumber"
      default: "123ABC"


# Inform the user that a new ticket has been posted form him (discussion created)
TICKET_THREAD_NEW_FROM_OPERATOR_TO_BUYER:
  body: "Hello {{firstName}} {{lastName}}. A new ticket has been created. Here the information: {{subject}} {{content}} {{url}}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "subject"
      default: "this is the subject of the message"
    - name: "url"
      default: "https://stationone.com/ticket/123"
    - name: "content"
      default: "this is the content of the message"
    - name: "host"
      default: "https://stationone.com/"
    - name: "ticketNumber"
      default: "123ABC"


# Inform the operators that a new ticket has been posted (discussion created)
TICKET_THREAD_NEW_FROM_BUYER_TO_OPERATOR:
  body: "Hello {{firstName}} {{lastName}}. A new ticket has been created. Here the information: {{subject}} {{content}} {{url}}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "subject"
      default: "this is the subject of the message"
    - name: "url"
      default: "https://stationone.com/ticket/123"
    - name: "content"
      default: "this is the content of the message"
    - name: "host"
      default: "https://stationone.com/"
    - name: "ticketNumber"
      default: "123ABC"


# Inform the user that a new message has been posted in a existing discussion
TICKET_THREAD_UPDATED_TO_BUYER:
  body: "Hello {{firstName}} {{lastName}}. A new message has been posted in a discussion. Here the information: {{subject}} {{content}} {{url}}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "subject"
      default: "this is the subject of the message"
    - name: "url"
      default: "https://stationone.com/ticket/123"
    - name: "content"
      default: "this is the content of the message"
    - name: "host"
      default: "https://stationone.com/"
    - name: "ticketNumber"
      default: "123ABC"


# Inform the operators that a new message has been posted in a existing discussion
TICKET_THREAD_UPDATED_TO_OPERATOR:
  body: "Hello {{firstName}} {{lastName}}. A new message has been posted in a discussion. Here the information: {{subject}} {{content}} {{url}}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "subject"
      default: "this is the subject of the message"
    - name: "url"
      default: "https://stationone.com/ticket/123"
    - name: "content"
      default: "this is the content of the message"
    - name: "host"
      default: "https://stationone.com/"
    - name: "ticketNumber"
      default: "123ABC"



# Inform the operators that a technical error occurred
SUPPORT_TECHNICAL_ERROR_TO_ADMIN:
  body: "Hello {{firstName}} {{lastName}}. A technical error occurred in the following user step: {{step}}. Error message: {{traceAsString}}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "step"
      default: "term payment process"
    - name: "traceAsString"
      default: "complete PHP stack trace"
    - name: "host"
      default: "https://stationone.com/"



# Inform the user that too much money has been received and that he will be refund
REFUND_AUTO_EXCEEDED_PAYMENT_TO_BUYER:
  body: "Hello {{firstName}} {{lastName}}. You have transfer too much money. Your transaction will be refused and you will be refund. Please contact the StationOne support for more information"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "host"
      default: "https://stationone.com/"
    - name: "orderNumber"
      default: "order number"
    - name: "amountReceived"
      default: "amount received"
    - name: "amountExpected"
      default: "amount expected"
    - name: "amountRefund"
      default: "amount refunded"


# Inform the user that not enough money has been received and that he will be refund
REFUND_AUTO_NOT_ENOUGH_PAYMENT_TO_BUYER:
  body: "Hello {{firstName}} {{lastName}}. Your bank transfer is below the price of the order. Your transaction will be refused and you will be refund. Please contact the StationOne support for more information"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "host"
      default: "https://stationone.com/"
    - name: "orderNumber"
      default: "order number"
    - name: "amountReceived"
      default: "amount received"
    - name: "amountExpected"
      default: "amount expected"

# Inform the user that his payment has been received and no amount remains
PAYMENT_FULL_RECEIVED_TO_BUYER:
  body: "Hello {{firstName}} {{lastName}}. All money has been received for the order {{orderNumber}}. Amount of the payment {{amount}}{{currency}}. "
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "orderNumber"
      default: "DB123X45"
    - name: "amount"
      default: 1555.55
    - name: "currency"
      default: "euro"
    - name: "host"
      default: "https://stationone.com/"
    - name: "vendor"
      default: "name of the vendor"


# Inform the seller that the complete amount of money has been received for the specified order
PAYMENT_FULL_RECEIVED_TO_VENDOR:
  body: "Hello {{firstName}} {{lastName}}. All money has been received for the order {{orderNumber}}. Amount of the payment {{amount}}{{currency}}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "orderNumber"
      default: "DB123X45"
    - name: "amount"
      default: 1555.55
    - name: "currency"
      default: "euro"
    - name: "host"
      default: "https://stationone.com/"
    - name: "buyer"
      default: "name of the buyer"


# Inform the user that his payment has been received, but it remains amount to pay
PAYMENT_PARTIAL_RECEIVED_TO_BUYER:
  body: "Hello {{firstName}} {{lastName}}. Your payment for the order {{orderNumber}} has been received. Amount of the payment {{amount}}{{currency}}. "
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "orderNumber"
      default: "DB123X45"
    - name: "amount"
      default: 1555.55
    - name: "currency"
      default: "euro"
    - name: "host"
      default: "https://stationone.com/"
    - name: "vendor"
      default: "name of the vendor"


# Inform the seller that his payment has been received, but it remains amount to pay
PAYMENT_PARTIAL_RECEIVED_TO_VENDOR:
  body: "Hello {{firstName}} {{lastName}}. A payment has been received for the order {{orderNumber}}. Amount of the payment {{amount}}{{currency}}. "
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "orderNumber"
      default: "DB123X45"
    - name: "amount"
      default: 1555.55
    - name: "currency"
      default: "euro"
    - name: "host"
      default: "https://stationone.com/"
    - name: "buyer"
      default: "name of the buyer"


# Inform the buyer that his order has been confirmed
ORDER_CONFIRMATION_TO_BUYER:
  body: "Hello {{firstName}} {{lastName}}. Your order {{orderNumber}} is confirmed. each merchant has {{ paymentNbHoursBeforeRefund}} hours to validate item before refunding  "
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "orderNumber"
      default: "DB123X45"
    - name: "paymentNbHoursBeforeRefund"
      default: 5
    - name: "host"
      default: "https://stationone.com/"

BAFV_REQUEST_APPROVED:
  body: "Hello,<br> Your request to access our catalog prices has been approved. <br> Thank you <br> {{ vendorName }}"
  variables:
    - name: "vendorName"
      default: "vendorName"

BAFV_REQUEST_REJECTED:
  body: "Hello,<br> Your request to access our catalog prices has been rejected.<br> Reason of the rejection:{{ rejectionReason }} <br> Thank you <br> {{ vendorName }}"
  variables:
    - name: "rejectionReason"
      default: "rejectionReason"
    - name: "vendorName"
      default: "vendorName"

OPERATOR_NEW_BAFV_REQUEST:
  body: "Hello, <br> new buyer account for vendor request. <br>
          Created at: {{ createdAt }} <br>
          Buyer company name: {{ buyerCompanyName }}
          Vendor company name: {{ vendorCompanyName }}
          "
  variables:
    - name: "createdAt"
      default: "d/m/Y H:i:s"
    - name: "buyerCompanyName"
      default: "buyer company name"
    - name: "vendorCompanyName"
      default: "vendor company name"

UPELA_ADDRESS_ISSUE:
  body: "Hello, there is an issue on an address sent to Upela for quotation <br>
         Time : {{ date }} <br>
         Vendor Company name : {{ vendorCompanyName }} <br>
         Address send from : {{ addressSendFrom }} <br>
         Buyer Company name : {{ buyerCompanyName }} <br>
         Address send to : {{ addressSendTo }} <br>

         Debug infos : <br>
         Upela request : <br>
         <pre>
             {{ upelaRequest }}
         </pre>
         Upela response : <br>
         <pre>
             {{ upelaResponse }}
         </pre>"
  variables:
    - name: "date"
      default: "date"
    - name: "buyerCompanyName"
      default: "buyerCompanyName"
    - name: "addressSendTo"
      default: "addressSendTo"
    - name: "vendorCompanyName"
      default: "vendorCompanyName"
    - name: "addressSendFrom"
      default: "addressSendFrom"
    - name: "upelaResponse"
      default: "upelaResponse"
    - name: "upelaRequest"
      default: "upelaRequest"

UPELA_QUOTATION_REQUEST_ISSUE:
  body: "Hello, there is an issue on quotation response from Upela <br>
         Time : {{ date }} <br>
         Vendor Company name : {{ vendorCompanyName }} <br>
         Buyer Company name : {{ buyerCompanyName }} <br>

         Debug infos : <br>
         Upela request : <br>
         <pre>
             {{ upelaRequest }}
         </pre>
         Upela response : <br>
         <pre>
             {{ upelaResponse }}
         </pre>"
  variables:
    - name: "date"
      default: "date"
    - name: "buyerCompanyName"
      default: "buyerCompanyName"
    - name: "vendorCompanyName"
      default: "vendorCompanyName"
    - name: "upelaResponse"
      default: "upelaResponse"
    - name: "upelaRequest"
      default: "upelaRequest"

PURCHASE_REQUEST_SEND_NOT_FOUND:
  body: 'Hello,<br>
           This email informs you that a new import has been made in the Purchase Request Page.<br><br>

           Company name : {{company}}<br>
           Import date : {{date}}<br>
           Total no. of references : {{totalRef}}<br>
           Total no. of missing references : {{totalNotFoundRef}}<br><br>

           Click on below links to download file
           <a href="{{link_import_file}}">label import file</a>
           <a href="{{link_not_found_file}}">label not found file</a>'
  variables:
    - name: "company"
      default: "company"
    - name: "totalRef"
      default: "totalRef"
    - name: "totalNotFoundRef"
      default: "totalNotFoundRef"
    - name: "date"
      default: "Now"
    - name: "link_import_file"
      default: "LINK IMPORT FILE"
    - name: "link_not_found_file"
      default: "LINK NOT FOUND FILE"

EMITTED_INVOICE_TO_ACCOUNTING_DEPARTMENT:
  body: "Dear accounting department,<br/>
    Please find enclosed the invoice coming from StationOne with the invoice number « {{invoiceNumber}} » corresponding to your order number « {{orderNumber}} ».<br/>
    For any question regarding this invoice, please contact: {{cartValidatorEmail}}<br/><br/>
    Best Regards,<br/>
    StationOne Team<br/>
  "
  variables:
    - name: "invoiceNumber"
      default: "invoice number"
    - name: "orderNumber"
      default: "order number"
    - name: "cartValidatorEmail"
      default: "Cart Validator E-mail"
