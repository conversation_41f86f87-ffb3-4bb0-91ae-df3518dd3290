var Encore = require('@symfony/webpack-encore');

Encore
    // directory where compiled assets will be stored
    .setOutputPath('public/dist/')
    // public path used by the web server to access the output path
    .setPublicPath('/dist')
    // only needed for CDN's or sub-directory deploy
    //.setManifestKeyPrefix('build/')

    /*
     * ENTRY CONFIG
     *
     * Add 1 entry for each "page" of your app
     * (including one that's included on every page - e.g. "app")
     *
     * Each entry will result in one JavaScript file (e.g. app.js)
     * and one CSS file (e.g. app.css) if your JavaScript imports CSS.
     */

    .addEntry('app', './src/Open/FrontBundle/Resources/private/js/app.js')
    .addEntry('back-app', './src/Open/BackBundle/Resources/private/js/app.js')
    .addEntry('purchase-request', './src/Open/FrontBundle/Resources/private/js/app/purchase-request/purchase-request-launch.js')

    // will require an extra script tag for runtime.js
    // but, you probably want this, unless you're building a single-page app
    .enableSingleRuntimeChunk()

    .cleanupOutputBeforeBuild()
    .enableSourceMaps(!Encore.isProduction())
    // enables hashed filenames (e.g. app.abc123.css)
    .enableVersioning(Encore.isProduction())

// uncomment if you use TypeScript
//.enableTypeScriptLoader()

    // uncomment if you use Sass/SCSS files
    .enableSassLoader()

    // uncomment if you're having problems with a jQuery plugin
    .autoProvidejQuery()

    .addLoader({ test: /\.(cur)$/, loader: 'file-loader' })
;

Encore.enableVueLoader();

var config = Encore.getWebpackConfig();

config.resolve = {
    extensions: [ '.js', '.vue' ],
    alias: {
        'jquery-ui/ui/widget': 'blueimp-file-upload/js/vendor/jquery.ui.widget.js',
    }
};

module.exports = config;
