<?php
use <PERSON>\Config\RectorConfig;
use <PERSON>\Php80\ValueObject\AnnotationToAttribute;
use <PERSON>\Symfony\Set\SymfonySetList;
use <PERSON>\CodeQuality\Rector\If_\SimplifyIfReturnBoolRector;
use <PERSON>\Doctrine\Set\DoctrineSetList;
use <PERSON>\Php80\Rector\Class_\AnnotationToAttributeRector;
use App\Rector\Rules\ReplaceEntityStringWithClassRector;

return static function (RectorConfig $rectorConfig): void {
    $rectorConfig->paths([
        __DIR__ . '/src',
    ]);
	$rectorConfig->symfonyContainerXml('/var/cache/symfony/dev/AppApp_KernelDevDebugContainer.xml');
	// $rectorConfig->sets([
    // 	SymfonySetList::SYMFONY_72,
	// ]);

    // $rectorConfig->sets([
    //     DoctrineSetList::ANNOTATIONS_TO_ATTRIBUTES,
    // ]);
	// $rectorConfig->rule(SimplifyIfReturnBoolRector::class);
    $rectorConfig->ruleWithConfiguration(AnnotationToAttributeRector::class, [new AnnotationToAttribute('JMS\\Serializer\\Annotation\\Accessor'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\AccessorOrder'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\AccessType'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\Discriminator'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\Exclude'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\ExclusionPolicy'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\Expose'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\Groups'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\Inline'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\MaxDepth'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\PostDeserialize'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\PostSerialize'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\PreSerialize'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\ReadOnly'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\ReadOnlyProperty'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\SerializedName'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\Since'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\SkipWhenEmpty'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\Type'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\Until'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\VirtualProperty'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\XmlAttributeMap'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\XmlAttribute'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\XmlDiscriminator'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\XmlElement'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\XmlKeyValuePairs'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\XmlList'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\XmlMap'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\XmlNamespace'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\XmlRoot'), new AnnotationToAttribute('JMS\\Serializer\\Annotation\\XmlValue')]);
    $rectorConfig->rule(ReplaceEntityStringWithClassRector::class);
};
