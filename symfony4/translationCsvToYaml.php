<?php

include 'vendor/autoload.php';

$fileFr = __DIR__.'/src/AppBundle/Resources/translations/AppBundle.fr.yml';
$fileEn = __DIR__.'/src/AppBundle/Resources/translations/AppBundle.en.yml';
$fileEs = __DIR__.'/src/AppBundle/Resources/translations/AppBundle.es.yml';
$fileIt = __DIR__.'/src/AppBundle/Resources/translations/AppBundle.it.yml';
$fileDe = __DIR__.'/src/AppBundle/Resources/translations/AppBundle.de.yml';

$dataFr = [];
$dataEn = [];
$dataEs = [];
$dataIt = [];
$dataDe = [];

if (($handle = fopen("traduction.csv", "r")) !== false) {
    $first = true;
    while (([$key, $frValue, $enValue, $esValue, $itValue, $deValue] = fgetcsv($handle, 10000, ";")) !== false) {

        $dataFr = array_merge_recursive($dataFr, setData($key, $frValue));
        $dataEn = array_merge_recursive($dataEn, setData($key, $enValue));
        $dataEs = array_merge_recursive($dataEs, setData($key, $esValue));
        $dataIt = array_merge_recursive($dataIt, setData($key, $itValue));
        $dataDe = array_merge_recursive($dataDe, setData($key, $deValue));
        $first = false;
    }
    fclose($handle);
}

file_put_contents($fileFr, \Symfony\Component\Yaml\Yaml::dump($dataFr,10));
file_put_contents($fileEn, \Symfony\Component\Yaml\Yaml::dump($dataEn, 10));
file_put_contents($fileEs, \Symfony\Component\Yaml\Yaml::dump($dataEs, 10));
file_put_contents($fileIt, \Symfony\Component\Yaml\Yaml::dump($dataIt, 10));
file_put_contents($fileDe, \Symfony\Component\Yaml\Yaml::dump($dataDe, 10));

function setData($key, $value) {
    $data = [];
    $fullPath = explode('.', $key);

    $first = true;
    foreach(array_reverse($fullPath) as $path) {
        $tmpData = [];
        $tmpData[$path] = ($first) ? $value : $data;
        $data = $tmpData;
        $first = false;
    }

    return $data;
}

echo "DONE"."\n";