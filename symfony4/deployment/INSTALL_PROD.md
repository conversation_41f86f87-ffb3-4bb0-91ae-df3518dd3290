## PRODUCTION INSTALLATION

## Messenger: Queued Message Handling

Install supervisor on the production server
```
sudo apt-get install supervisor
```

Configure supervisor by creating /etc/supervisor/conf.d/messenger-worker.conf

```
;/etc/supervisor/conf.d/messenger-worker.conf
[program:messenger-consume]
command=php /path/to/your/app/bin/console messenger:consume async_priority_high async_priority_low --limit=10
user=www-data
numprocs=2
startsecs=0
autostart=true
autorestart=true
process_name=%(program_name)s_%(process_num)02d
```

Next, tell <PERSON>visor to read your config and start your workers:

```
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start messenger-consume:*
```

