<?xml version="1.0"?>
<psalm
        errorLevel="4"
        phpVersion="8.1"
        resolveFromConfigFile="true"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="https://getpsalm.org/schema/config"
        xsi:schemaLocation="https://getpsalm.org/schema/config vendor/vimeo/psalm/config.xsd"
>
    <plugins>
        <pluginClass class="Psalm\SymfonyPsalmPlugin\Plugin" />
        <pluginClass class="Psalm\PhpUnitPlugin\Plugin" />
    </plugins>
    <projectFiles>
<!--
    Ultimately we should run psalm on src
    Before that we go directory by directory
-->
<!--        <directory name="src" />-->
        <directory name="src/AppBundle" />
        <directory name="src/Open" />
        <directory name="src/Upela" />
        <directory name="src/Boekkooi" />
        <directory name="tests/AppBundle/Services" />
        <ignoreFiles>
            <directory name="vendor" />
        </ignoreFiles>
    </projectFiles>
    </psalm>
