<?php

namespace Open\FrontBundle\Controller;

use A<PERSON><PERSON><PERSON><PERSON>\Controller\MkoController;
use AppB<PERSON>le\Entity\Company;
use AppBundle\Services\SecurityService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\Session;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\Translation\TranslatorInterface;

class LoginController extends AbstractController
{
    /**
     * Redirect user to the localized login page
     * @param SecurityService $securityService
     * @return RedirectResponse|Response
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/authentication', name: 'front.login')]
    public function loginAction(
        SecurityService $securityService
    )
    {
        if (!$securityService->isAnonymous()) {
            return $this->redirect($this->generateUrl('homepage'));
        }

        return $this->render("@OpenFront/security/login.html.twig", []);
    }

    /**
     * Redirect user to the localized login page after a login failure
     * @param Request $request
     * @param TranslatorInterface $translator
     * @return mixed
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/login/failure', name: 'front.login.failure')]
    public function loginFailureAction(
        Request $request,
        TranslatorInterface $translator
    )
    {
        /** @var Session $session */
        $session = $request->getSession();

        $authErrorKey = 'security.last_error';
        // get the error if any (works with forward and redirect -- see below)
        if ($request->attributes->has($authErrorKey)) {
            $error = $request->attributes->get($authErrorKey);
        } elseif (null !== $session && $session->has($authErrorKey)) {
            $error = $session->get($authErrorKey);
            $session->remove($authErrorKey);
        } else {
            $error = null;
        }

        if ($error) {
            $this->addFlash('error', $translator->trans($error->getMessageKey(), [], 'security'));
        } else {
            $this->addFlash('error', $translator->trans('security.login.fail', [], 'AppBundle'));
        }

        return $this->redirectToRoute("front.login");
    }

    /**
     * @param Request $request
     * @param AuthorizationCheckerInterface $authorizationChecker
     * @return mixed
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/login/success', name: 'front.login.success')]
    public function loginSuccessAction(
        Request $request,
        AuthorizationCheckerInterface $authorizationChecker
    )
    {
        $user = $this->getUser();

        $company = null;
        if ($user) {
            /** @var Company $company */
            $company = $user->getCompany();
        }

        $response = $this->redirectToRoute('homepage');

        // If Operator or Admin => redirect to admin
        if ($authorizationChecker->isGranted('ROLE_OPERATOR')) {
            $response = $this->redirectToRoute('admin.dashboard');

        } elseif ($user === null || is_string($user)) {

            $response = $this->redirectToRoute('front.login');

        } elseif ($company && in_array($company->getStep(), [0, 1, 2])) {
            $request->getSession()->set(MkoController::SESSION_ACCOUNT_CREATION, true);
            $response = $this->redirectToRoute('front.company.info');

        } elseif ($company && $company->getStep() == 3) {

            $previousUrl = $request->headers->get('referer');
            $response = $this->redirect($previousUrl ?? "https://www.station-one.com");

            $loginUrl = $this->generateUrl('front.login');
            $failureUrl = $this->generateUrl('front.login.failure');
            $failureUrl = preg_replace('/\/(en|fr|es|de|it|nl)\//', '', $failureUrl);

            foreach ([$failureUrl, $loginUrl] as $goToHomepageUrl) {
                if (strpos($previousUrl, $goToHomepageUrl) !== false) {
                    $response = $this->redirectToRoute('homepage');
                    break;
                }
            }
        }

        return $response;
    }
}
