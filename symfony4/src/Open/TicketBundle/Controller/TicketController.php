<?php

namespace Open\TicketBundle\Controller;

use AppBundle\Controller\MkoController;
use AppBundle\Entity\Company;
use AppBundle\Entity\Document;
use AppBundle\Entity\ThreadMessage;
use AppBundle\Entity\ThreadParentMessage;
use AppBundle\Entity\User;
use AppBundle\Model\AttachmentLink;
use AppBundle\Repository\UserRepository;
use AppBundle\Services\CompanyService;
use AppBundle\Services\MailService;
use AppBundle\Services\MerchantService;
use AppBundle\Services\SecurityService;
use AppBundle\Services\UserBddService;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\ORMException;
use Knp\Component\Pager\PaginatorInterface;
use Open\BackBundle\Form\FilterTicketType;
use Open\IzbergBundle\Api\MessageApi;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Open\TicketBundle\Entity\Ticket;
use Open\TicketBundle\Entity\TicketMessage;
use Open\TicketBundle\Entity\TicketMessageAttachment;
use Open\TicketBundle\Form\TicketIzbergType;
use Open\TicketBundle\Form\TicketMessageType;
use Open\TicketBundle\Form\TicketType;
use Open\TicketBundle\Form\TicketWaylfType;
use Open\TicketBundle\Model\MessageActor;
use Open\TicketBundle\Model\TicketConstant;
use Open\TicketBundle\Services\TicketService;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;

use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\Finder\Exception\AccessDeniedException;
use Symfony\Component\HttpFoundation\File\UploadedFile;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\Router;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Contracts\Translation\TranslatorInterface;
use function _HumbugBox49e3974037c0\Amp\Iterator\toArray;

class TicketController extends MkoController
{
    private const SERVICE_TICKET = TicketService::class;
    private const ROUTE_ADMIN_TICKET_EDIT = "admin.ticket.edit";
    private const VIEW_PARAM_IS_ADMIN = "isAdmin";


    /**
     * get the attachment of the ticket
     *
     * @param                     $token
     * @param int                 $attachmentId the identifier of the message attachment id
     *
     * @param TicketService       $ticketService
     *
     * @param TranslatorInterface $translator
     *
     * @return mixed
     * @throws NonUniqueResultException
     * @throws \Doctrine\ORM\NoResultException
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/attachment/{token}/{attachmentId}', name: 'attachment.view')]
    public function actionGetAttachment($token, $attachmentId, TicketService $ticketService, TranslatorInterface $translator, SecurityService $securityService)
    {

        /**
         * @var TicketMessageAttachment $attachment
         */
        $attachment = $ticketService->findAttachmentById($attachmentId);

        if (!$attachment) {
            // try to download izberg attachment
            return $this->redirect($ticketService->fetchAttachmentUrl(strval($attachmentId)));
        }

        /** @var Ticket $ticket */
        $ticket = $ticketService->findTicketByNumber($token);

        // 404 if ticket doesn't exist
        if (!$ticket) {
            throw new NotFoundHttpException($translator->trans(self::NOT_FOUND_EXCEPTION));
        }

        // For anonymous users mainly
        if ($ticket->getId() !== $attachment->getMessage()->getTicket()->getId()) {
            throw new AccessDeniedException($translator->trans(self::ACCESS_DENIED_EXCEPTION));
        }

        //security checks
        $this->isUserAuthorizedToAccessTicket($ticket, $securityService, $translator);

        $file = stream_get_contents($attachment->getBinary());

        return new Response(
            $file,
            200,
            array(
                'Content-Type' => $attachment->getMimeType(),
                'Content-Disposition' => 'inline; filename="' . $attachment->getFilename() . '"'
            )
        );
    }

    /**
     * list the tickets for the authenticated user
     * Can only be called by a authenticated user
     *
     * @param Request            $request the request
     *
     * @param MessageApi         $messageApi
     * @param TicketService      $ticketService
     * @param PaginatorInterface $paginator
     *
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws \Exception
     */
    #[IsGranted(new Expression('is_granted("ROLE_OPERATOR") or is_granted("ROLE_SUPER_ADMIN")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/admin/tickets/', name: 'admin.ticket.list')]
    public function listAdminTicketsAction(Request $request, MessageApi $messageApi, TicketService $ticketService, PaginatorInterface $paginator)
    {
        $form = $this->createForm(FilterTicketType::class);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $filter = $form->getData();
        } else {
            $filter = [];
            $filter['family'] = 'all';
            $filter['productCategory'] = 'all';
            $filter['type'] = 'all';
        }
        return $this->listTickets($request, TicketConstant::TICKET_ADMIN, $filter, $form, $messageApi, $ticketService, $paginator);
    }

    /**
     * list the tickets for the authenticated user
     * Can only be called by a authenticated user
     *
     * @param Request         $request the request
     *
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws \Exception
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER") or is_granted("ROLE_BUYER_ADMIN")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/tickets/', name: 'buyer.ticket.list')]
    public function actionListBuyerTickets(Request $request,MessageApi $messageApi, TicketService $ticketService, PaginatorInterface $paginator)
    {
        return $this->listTickets($request, TicketConstant::TICKET_BUYER, null, null, $messageApi, $ticketService,$paginator);
    }

    /**
     * Route to create a ticket for a anonymous user
     *
     * @param Request $request
     *
     * @param SecurityService $securityService
     *
     * @param TranslatorInterface $translator
     * @param TicketService $ticketService
     *
     * @param UserBddService $userBddService
     * @param MailService $mailService
     * @param MerchantService $merchantService
     * @param MessageApi $messageApi
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws \AppBundle\Exception\MailException
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/contact', name: 'anonymous.ticket.create')]
    public function createCreateAnonymousTicket(
        Request $request,
        SecurityService $securityService,
        TranslatorInterface $translator,
        TicketService $ticketService,
        UserBddService $userBddService,
        MailService $mailService,
        MerchantService $merchantService,
        MessageApi $messageApi
    )
    {
        /*
         * if the user is not anonymous, we want to redirect to the correct route
         */
        if ($this->getUser()) {
            if ($securityService->isAdmin($this->getUser())) {
                $result = $this->redirectToRoute("admin.ticket.create");
            } else if ($securityService->isBuyer($this->getUser())) {
                $result = $this->redirectToRoute("buyer.ticket.create");
            } else {
                throw $this->createAccessDeniedException("unknown user type");
            }
            return $result;

        }

        return $this->createTicket(
            $request,
            TicketConstant::TICKET_ANONYMOUS,
            null,
            $securityService,
            $translator,
            $ticketService,
            $userBddService,
            $mailService,
            $merchantService,
            $messageApi
        );
    }

    /**
     * Route to create a ticket for a anonymous user
     *
     * @param Request             $request
     *
     * @param TranslatorInterface $translator
     *
     * @param TicketService       $ticketService
     *
     * @param SecurityService     $securityService
     *
     * @param UserBddService      $userBddService
     * @param MailService         $mailService
     *
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws \AppBundle\Exception\MailException
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/what-are-you-looking-for', name: 'front.what_are_you_looking_for')]
    public function createWhatAreYouLookingForTicket(Request $request, TranslatorInterface $translator, TicketService $ticketService, SecurityService $securityService,
                                                     UserBddService $userBddService, MailService $mailService)
    {
        $ticketMessage = new TicketMessage();
        $createBycustomer = !$this->isOperator() && !$this->isAdmin();
        $ticket = new Ticket($createBycustomer);
        $ticket->addMessage($ticketMessage);
        $ticket->setSubject('What are you looking for');

        $ticketType = TicketConstant::TICKET_WHAT_ARE_YOU_LOOKING_FOR;
        $isAnonymous = (is_null($this->getUser()));

        $form_options = [];
        if ($isAnonymous) {
            $form_options['validation_groups'] = ["Default", "TicketAnonymous"];

            $form_options['captcha_enabled'] = true;
        }

        $form = $this->createForm(
            TicketWaylfType::class,
            $ticket,
            $form_options
        );

        $viewParams = [
            "base" => TicketConstant::BASES[$ticketType],
            "isAnonymous" => $isAnonymous,
            "customShowMenu" => false
        ];

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            /**
             * @var Ticket $ticket
             */
            $ticket = $form->getData();

            $ticket->setTicketNumber($this->guidv4(openssl_random_pseudo_bytes(16)));
            $ticket->setFromEmail($this->getUserEmail());

            //complete data
            $ticket->getMessages()[0]->setTicket($ticket);

            if (!is_string($this->getUser()) && !$isAnonymous) {
                $ticket->getMessages()[0]->setUserCreated($this->getUser());
            }
            $ticket->setIsCreatedByAdmin(false);
            $ticket->setLastMessage($ticket->getMessages()[0]);
            $ticket->setStatus(TicketConstant::STATUS_NEW);

            //set author if non anonymous
            if (!$isAnonymous) {
                $ticket->setAuthor($this->getUser()->getCompany());
                /**
                 * @var User $companyUser
                 */
                $companyUser = $this->getUser();
                $companyUser->getCompany()->addTicket($ticket);
            } else {
                $ticket->setAnonymousLocale($request->getLocale());
            }

            /** @var TicketMessage $message */
            $message = $ticket->getLastMessage();

            $files = $message->getAttachments();
            $attachments = array();

            $errors = [];
            /** @var UploadedFile $file */
            foreach ($files as $file) {

                if (!in_array($file->getMimeType(), Document::MIME_CONSTRAINT)) {
                    $errors[] = $file->getClientOriginalName() . ' : ' . $translator->trans('document.upload.mime', array('%contrainte%' => implode(", ", Document::MIME_CONSTRAINT)), self::TRANSLATION_DOMAIN);
                }
                if (strlen(strval(file_get_contents($file->getRealPath()))) > Document::SIZE_CONSTRAINT) {
                    $errors[] = $file->getClientOriginalName() . ' : ' . $translator->trans('document.upload.size', array('%contrainte%' => Document::SIZE_CONSTRAINT / (1024 * 1024)), self::TRANSLATION_DOMAIN);
                }

                $attachment = new TicketMessageAttachment();

                $attachment->setFilename($file->getClientOriginalName());
                $attachment->setMessage($message);
                $attachment->setMimeType($file->getMimeType());
                $attachment->setSize($file->getSize());
                $attachment->setBinary(file_get_contents($file->getRealPath()));
                $attachments[] = $attachment;
            }

            $ticket->getLastMessage()->setAttachments($attachments);

            //create the ticket in the db
            try {
                if (count($errors) > 0) {
                    // create a flash back
                    foreach ($errors as $error) {
                        $this->addFlash('error', $error);
                    }
                } else {
                    $ticketService->createTicket($ticket);

                    //send emails notification
                    $this->sendTicketCreationEmails($ticket,$userBddService, $mailService, $securityService);

                    //add success flash
                    $this->addFlash('success', $translator->trans('ticket.success.create', [], self::TRANSLATION_DOMAIN));

                    //redirect to homepage
                    return $this->redirectToRoute('homepage');
                }
            } catch (ORMException $e) {
                $this->logger->error(
                    "unable to save the ticket",
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::TECHNICAL_ERROR,
                        LogUtil::USER_NAME => $this->getUsername(),
                        "exception" => $e->getTraceAsString(),
                    ])
                );
                $this->addFlash('error', $translator->trans('ticket.error.create', [], self::TRANSLATION_DOMAIN));
            }

        }

        $viewParams['form'] = $form->createView();
        $viewParams['front'] = true;
        $viewParams['isAdmin'] = false;

        return $this->render(
            '@OpenTicket/ticket_what_are_you_looking_for.html.twig',
            $viewParams
        );
    }

    /**
     * create a ticket
     *
     * @param Request $request
     *
     * @param SecurityService $securityService
     * @param TranslatorInterface $translator
     * @param TicketService $ticketService
     *
     * @param UserBddService $userBddService
     * @param MailService $mailService
     * @param MerchantService $merchantService
     * @param MessageApi $messageApi
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws \AppBundle\Exception\MailException
     */
    #[IsGranted(new Expression('is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/admin/ticket/create/', name: 'admin.ticket.create')]
    public function createAdminTicketAction(
        Request $request,
        SecurityService $securityService,
        TranslatorInterface $translator,
        TicketService $ticketService,
        UserBddService $userBddService,
        MailService $mailService,
        MerchantService $merchantService,
        MessageApi $messageApi
    )
    {
        return $this->createTicket(
            $request,
            TicketConstant::TICKET_ADMIN,
            null,
            $securityService,
            $translator,
            $ticketService,
            $userBddService,
            $mailService,
            $merchantService,
            $messageApi
        );
    }


    /**
     * create a ticket
     *
     * @param Request $request
     *
     * @param SecurityService $securityService
     * @param TranslatorInterface $translator
     * @param TicketService $ticketService
     *
     * @param UserBddService $userBddService
     * @param MailService $mailService
     * @param MerchantService $merchantService
     * @param MessageApi $messageApi
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws \AppBundle\Exception\MailException
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_BUYER") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_ADMIN")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/ticket/create/', name: 'buyer.ticket.create')]
    public function actionCreateBuyerTicket(
        Request $request,
        SecurityService $securityService,
        TranslatorInterface $translator,
        TicketService $ticketService,
        UserBddService $userBddService,
        MailService $mailService,
        MerchantService $merchantService,
        MessageApi $messageApi
    )
    {
        return $this->createTicket(
            $request,
            TicketConstant::TICKET_BUYER,
            null,
            $securityService,
            $translator,
            $ticketService,
            $userBddService,
            $mailService,
            $merchantService,
            $messageApi
        );
    }


    /**
     * edit a ticket
     *
     * @param Request             $request
     * @param                     $id
     *
     * @param TicketService       $ticketService
     * @param TranslatorInterface $translator
     * @param SecurityService     $securityService
     *
     * @param MailService         $mailService
     * @param UserBddService      $userBddService
     *
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws \Doctrine\ORM\NoResultException
     */
    #[IsGranted(new Expression('is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/admin/ticket/edit/{id}', name: 'admin.ticket.edit')]
    public function editAdminTicketAction(Request $request, $id, TicketService $ticketService, TranslatorInterface $translator, SecurityService $securityService, MailService $mailService, UserBddService $userBddService)
    {
        return $this->editTicket($id, $request, TicketConstant::TICKET_ADMIN, $ticketService, $translator, $securityService,  $mailService,  $userBddService);
    }

    /**
     * edit a ticket
     *
     * @param Request             $request
     * @param integer             $id the identifier of the ticket
     *
     * @param TicketService       $ticketService
     * @param TranslatorInterface $translator
     * @param SecurityService     $securityService
     *
     * @param MailService         $mailService
     * @param UserBddService      $userBddService
     *
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws \Doctrine\ORM\NoResultException
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_BUYER") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_ADMIN")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/ticket/edit/{id}', name: 'buyer.ticket.edit')]
    public function actionEditBuyerTicket(Request $request, $id, TicketService $ticketService, TranslatorInterface $translator, SecurityService $securityService, MailService $mailService, UserBddService $userBddService)
    {
        return $this->editTicket($id, $request, TicketConstant::TICKET_BUYER, $ticketService, $translator, $securityService,  $mailService,  $userBddService);
    }

    /**
     * @param Request       $request
     * @param integer       $id the identifier of the ticket
     *
     * @param TicketService $ticketService
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_OPERATOR") or is_granted("ROLE_SUPER_ADMIN")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/admin/ticket/edit/reopen/{id}', name: 'admin.ticket.reopen')]
    public function actionReopenTicket(Request $request, $id, TicketService $ticketService)
    {
        return $this->reopenTicket($id, $ticketService);
    }


    /**
     * edit a ticket
     *
     * @param Request             $request
     * @param integer             $id the identifier of the ticket
     * @param CompanyService      $companyService
     *
     * @param MessageApi          $messageApi
     *
     * @param TranslatorInterface $translator
     * @return \Symfony\Component\HttpFoundation\Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_BUYER") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_ADMIN")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/ticket/external/edit/{id}', name: 'izberg.ticket.edit')]
    public function actionIzbergEditBuyerTicket(Request $request, $id, CompanyService $companyService, MessageApi $messageApi, TranslatorInterface $translator)
    {

        $message = $messageApi->getMessageById($id);

        $sender = MessageActor::createFromIzbergResourceUrl($message->sender->resource_uri, $message->sender->id);
        $receiver = MessageActor::createFromIzbergResourceUrl($message->receiver->resource_uri, $message->receiver->id);

        if (
            ($sender->isApplicationType() || $sender->isMerchantType())
            && $message->status == 'unread'
        ) {
            $messageApi->markAsRead($id);
            $this->doctrine->getRepository(ThreadMessage::class)->updateHasUnreadMessagesByIzbergId($id, 0);
        }

        /**@var Company $company * */
        $company = $companyService->getCompanyByIzbergUserId($message->sender->id);
        $ticket = new Ticket(true);
        if ($company) {
            $ticket->setTicketCreator($company->getMainContact());
        }

        $ticket->setAuthor($this->getUser()->getCompany());
        $ticket->setCompany($this->getUser()->getCompany());
        $ticket->setSubject($message->subject);
        $ticket->setTicketNumber($message->id);
        $ticket->setId(intval($message->id));
        $ticket->setIzbDate($message->sent_at);
        $ticket->setFromIzb(true);

        $seller = $message->receiver;

        $ticketMessage = new TicketMessage();
        $ticketMessage->setContent($message->body);
        $ticketMessage->setIzbDate($message->sent_at);

        // set attachments
        $attachments = [];
        if ($message->attachment_count > 0) {
            $messageAttachments = $messageApi->getMessageAttachments(intval($message->id));

            /** @var \stdClass $messageAttachment */
            foreach($messageAttachments as $izbergAttachment) {
                $messageAttachment = (new TicketMessageAttachment())
                    ->setId($izbergAttachment->id)
                    ->setFileName($izbergAttachment->file_name);
                $attachments[] = $messageAttachment;
            }
        }

        $ticketMessage->setAttachments($attachments);

        if ($company) {
            $ticketMessage->setIzbName($company->getName());
            $ticket->setAnonymousFirstName($company->getName());
        } else {
            $ticketMessage->setIzbName($message->from_display_name);
            $ticket->setAnonymousFirstName($message->from_display_name);
        }
        $ticket->addMessage($ticketMessage);
        $ticket->setLastMessage($ticketMessage);

        // Récupérer les messages enfant du message initial
        $this->addNextMessage($ticket, $message, $messageApi);

        $ticketMessageList = $ticket->getMessages();
        usort($ticketMessageList, function (TicketMessage $a, TicketMessage $b) {
            return intval($a->getIzbDate() < $b->getIzbDate());
        });

        $ticket->setMessages($ticketMessageList);

        $form = $this->createForm(
            TicketIzbergType::class
        );

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {

            $attachedFiles = $form->get('attachments')->getData();
            // if $receiver is un user (undefined) and $sender is merchant or application (!= undefined)
            // save sender as receiver
            if ($receiver->getType() === 'undefined' && $sender->getType() !== 'undefined') {
                $receiver = $sender;
            }

            $userName = ($this->getUser())? $this->getUser()->getFirstName() .' '. $this->getUser()->getLastName() : 'anonymous';
            $msg = $form->getData()['message'];
            $msg .= "\n\n".$userName;

            $status = $messageApi->answerMerchant(
                $message->subject,
                $msg,
                $this->getUser()->getCompany()->getIzbergUserId(),
                $receiver,
                $message->root_msg,
                ...$attachedFiles
            );

            if ($status == 201) {
                // Add success message
                $this
                    ->addFlash(
                        'success',
                        $translator->trans('contactMerchant.form.success', array(), self::TRANSLATION_DOMAIN)
                    );
            } else {
                // Add success message
                $this
                    ->addFlash(
                        'error',
                        $translator->trans('contactMerchant.form.error', array(), self::TRANSLATION_DOMAIN)
                    );
            }

            return $this->redirectToRoute('izberg.ticket.edit', array('id' => $id));
        }

        $viewParams = array(
            "base" => TicketConstant::BASES[TicketConstant::TICKET_BUYER],
            "ticket" => $ticket,
            'seller' => $seller,
            "locale" => $request->getLocale(),
            self::VIEW_PARAM_IS_ADMIN => false,
            "fromIZB" => true,
            'front' => true,
            'form' => $form->createView(),
            "customShowMenu" => false
        );


        return $this->render(
            '@OpenTicket/ticket_edit.html.twig',
            $viewParams
        );
    }

    /**
     * @param               $id
     *
     * @param TicketService $ticketService
     *
     * @return \Symfony\Component\HttpFoundation\RedirectResponse
     */
    private function reopenTicket($id, TicketService $ticketService)
    {
        $ticketService->reopenTicket($id);
        return $this->redirectToRoute('admin.ticket.edit', array('id' => $id));
    }

    /**
     * @param Ticket     $tab
     * @param            $message
     *
     * @param MessageApi $messageApi
     *
     * @return Ticket
     */
    private function addNextMessage(Ticket $tab, $message, MessageApi $messageApi)
    {
        $rootMessage = $messageApi->getMessagesByRootId($message->id);
        foreach ($rootMessage->objects as $object) {
            if ($object->id != $message->id) {
                $ticketMessage = new TicketMessage();
                $ticketMessage->setContent($object->body);
                $ticketMessage->setIzbDate($object->sent_at);
                if (strpos($object->from_resource_uri, '/user/') !== false) {
                    $ticketMessage->setIzbName($this->getUser()->getCompany()->getName());
                } else {
                    $ticketMessage->setIzbName($object->from_display_name);
                }

                // set attachments
                $attachments = [];
                if ($object->attachment_count > 0) {
                    $messageAttachments = $messageApi->getMessageAttachments($object->id);

                    /** @var \stdClass $messageAttachment */
                    foreach($messageAttachments as $izbergAttachment) {
                        $messageAttachment = (new TicketMessageAttachment())
                            ->setId($izbergAttachment->id)
                            ->setFileName($izbergAttachment->file_name);
                        $attachments[] = $messageAttachment;
                    }
                }

                $ticketMessage->setAttachments($attachments);

                $tab->addMessage($ticketMessage);
                $tab->setLastMessage($ticketMessage);

                $sender = MessageActor::createFromIzbergResourceUrl($object->sender->resource_uri, $object->id);
                // Si la société acheteur n'est pas l'envoyeur du message et qu'il est pas encore lu => on le passe à l'état lu
                if (
                    ($sender->isMerchantType() || $sender->isApplicationType())
                    && $object->status == 'unread'
                ) {
                    $parts = explode('/', trim($object->root_msg, '/'));
                    $messageApi->markAsRead($object->id);
                    $this->doctrine->getRepository(ThreadMessage::class)->updateHasUnreadMessagesByIzbergId(end($parts), 0);
                }
            }
        }
        return $tab;
    }

    /**
     * edit a ticket for anonymous user
     *
     * @param Request             $request
     * @param integer             $id the identifier of the ticket
     *
     * @param SecurityService     $securityService
     *
     * @param TicketService       $ticketService
     * @param TranslatorInterface $translator
     *
     * @param MailService         $mailService
     * @param UserBddService      $userBddService
     *
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws \Doctrine\ORM\NoResultException
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/contact/{id}', name: 'anonymous.ticket.edit')]
    public function actionEditAnonymousTicket(Request $request, $id, SecurityService $securityService, TicketService $ticketService, TranslatorInterface $translator, MailService $mailService, UserBddService $userBddService)
    {
        /*
         * if the user is not anonymous, we want to redirect to the correct route
         */
        if (!$this->getUser()) {
            return $this->editTicket($id, $request, TicketConstant::TICKET_ANONYMOUS, $ticketService, $translator, $securityService,  $mailService,  $userBddService);
        }

        if ($securityService->isAdmin($this->getUser())) {
            return $this->redirectToRoute(self::ROUTE_ADMIN_TICKET_EDIT, ["id" => $id]);
        }

        if ($securityService->isBuyer($this->getUser())) {
            return $this->redirectToRoute("buyer.ticket.edit", ["id" => $id]);
        }

        throw $this->createAccessDeniedException("unknown user type");
    }


    /**
     * Edit a ticket
     *
     * @param                     $id
     * @param Request             $request
     * @param                     $ticketType
     **
     * @param TicketService       $ticketService
     * @param TranslatorInterface $translator
     *
     * @param SecurityService     $securityService
     *
     * @param MailService         $mailService
     * @param UserBddService      $userBddService
     *
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws \Doctrine\ORM\NoResultException
     */
    private function editTicket($id, $request, $ticketType, TicketService $ticketService, TranslatorInterface $translator, SecurityService $securityService, MailService $mailService, UserBddService $userBddService)
    {

        /**
         * @var Ticket $ticket
         */
        $ticket = $ticketService->getTicketByNumber($id);

        //security check
        $this->isUserAuthorizedToAccessTicket($ticket, $securityService, $translator);

        // update the status to say it is not new anymore
        $ticket = $this->updateNewStatus($ticket, $ticketService);

        $message = new TicketMessage();

        $options = array(
            'is_response' => true
        );

        if (!empty($request->request->get('ticket_message')['resolved']) && intval($request->request->get('ticket_message')['resolved']) === 1) {
            $options['validation_groups'] = array("ClosedTicket");
        }

        $form = $this->createForm(
            TicketMessageType::class,
            $message,
            $options
        );

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {

            /**
             * @var TicketMessage $message
             */
            $message = $form->getData();

            try {
                $message->setTicket($ticket);

                if ($this->getUSer()) {
                    $message->setUserCreated($this->getUSer());
                }

                $files = $message->getAttachments();
                $attachments = array();

                $errors = [];
                /** @var UploadedFile $file */
                foreach ($files as $file) {

                    if (!in_array($file->getMimeType(), Document::MIME_CONSTRAINT)) {
                        $errors[] = $file->getClientOriginalName() . ' : ' . $translator->trans('document.upload.mime', array('%contrainte%' => implode(", ", Document::MIME_CONSTRAINT)), self::TRANSLATION_DOMAIN);
                    }
                    if (strlen(strval(file_get_contents($file->getRealPath()))) > Document::SIZE_CONSTRAINT) {
                        $errors[] = $file->getClientOriginalName() . ' : ' . $translator->trans('document.upload.size', array('%contrainte%' => Document::SIZE_CONSTRAINT / (1024 * 1024)), self::TRANSLATION_DOMAIN);
                    }

                    $attachment = new TicketMessageAttachment();

                    $attachment->setFilename($file->getClientOriginalName());
                    $attachment->setMessage($message);
                    $attachment->setMimeType($file->getMimeType());
                    $attachment->setSize($file->getSize());
                    $attachment->setBinary(file_get_contents($file->getRealPath()));
                    $attachments[] = $attachment;
                }

                if (count($errors) > 0) {
                    // create a flash back
                    foreach ($errors as $error) {
                        $this->addFlash('error', $error);
                    }
                } else {
                    $message->setAttachments($attachments);

                    //we only want to create the message if the content is not empty
                    if (!empty($message->getContent()) || count($message->getAttachments()) > 0) {
                        $ticketService->createMessage($message);
                        $ticket->addMessage($message);
                        $ticket->setLastMessage($message);
                    }

                    if ($message->isResolved()) {
                        $ticket->setStatus(TicketConstant::STATUS_RESOLVED);
                    } else if ($ticketType === TicketConstant::TICKET_ADMIN) {
                        $ticket->setStatus(TicketConstant::STATUS_OPERATOR_RESPONDED);
                    } else {
                        $ticket->setStatus(TicketConstant::STATUS_USER_RESPONDED);
                    }

                    $ticket = $this->updateRepliedStatus($ticket);

                    //notify users
                    $this->sendTicketUpdateEmails($ticket, $securityService,  $mailService,  $userBddService);

                    //to avoid serialization error
                    $message->setAttachments(null);
                    $ticketService->updateTicket($ticket);


                    // create a flash back
                    $this->addFlash('success', $translator->trans('ticket.success.update', [], self::TRANSLATION_DOMAIN));

                    //create a new empty form
                    $form = $this->createForm(
                        TicketMessageType::class,
                        new TicketMessage()
                    );

                    //force reload (for ordering)
                    $ticketService->clearTicket($ticket);
                    $ticket = $ticketService->getTicketByNumber($id);
                }

            } catch (\Exception $e) {

                $this->logger->error(
                    "unable to save the ticket",
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::TECHNICAL_ERROR,
                        LogUtil::USER_NAME => $this->getUsername(),
                        "exception" => $e->getTraceAsString()
                    ])
                );

                $this->addFlash('error', $translator->trans('ticket.error.update', [], self::TRANSLATION_DOMAIN));
            }
        }

        $ticket->setTicketCreator($ticket->getLastMessage()->getUserCreated());

        $viewParams = array(
            "base" => TicketConstant::BASES[$ticketType],
            "ticket" => $ticket,
            "locale" => $request->getLocale(),
            "fromIZB" => false,
            "customShowMenu" => false,
            "ticketType" => $ticketType,
            self::VIEW_PARAM_IS_ADMIN => $securityService->isAdmin($this->getUser())
        );

        if ($ticket->getStatus() !== TicketConstant::STATUS_RESOLVED) {
            $viewParams["form"] = $form->createView();
        }

        $viewParams['front'] = $ticketType === TicketConstant::TICKET_ANONYMOUS ||
            $ticketType === TicketConstant::TICKET_BUYER;

        return $this->render(
            '@OpenTicket/ticket_edit.html.twig',
            $viewParams
        );
    }

    /**
     *
     * @return bool
     */
    private function isAdmin()
    {
        return $this->isGranted('ROLE_SUPER_ADMIN');
    }

    /**
     *
     * @return bool
     */
    private function isOperator()
    {
        return $this->isGranted('ROLE_OPERATOR');
    }

    /**
     * @param Ticket          $ticket
     *
     * @return mixed
     */
    private function updateRepliedStatus($ticket)
    {
        if ($this->isOperator()) {
            $ticket->setOperatorReplied(true);
            $ticket->setCustomerRead(true);
        } else {
            $ticket->setCustomerReplied(true);
            $ticket->setOperatorRead(true);
        }
        return $ticket;
    }

    /**
     * @param Ticket        $ticket
     *
     * @param TicketService $ticketService
     *
     * @return mixed
     */
    private function updateNewStatus($ticket, TicketService $ticketService)
    {
        if ($this->isOperator() or $this->isAdmin()) {
            $ticket->setOperatorNew(false);
            $ticket->setOperatorRead(false);
        } else {
            $ticket->setCustomerNew(false);
            $ticket->setCustomerRead(false);
        }
        $ticketService->updateTicket($ticket);

        return $ticket;
    }

    /**
     * create a ticket
     *
     * @param Request $request
     * @param                     $ticketType
     * @param                     $userEmail
     *
     **
     * @param SecurityService $securityService
     * @param TranslatorInterface $translator
     *
     * @param TicketService $ticketService
     * @param UserBddService $userBddService
     * @param MailService $mailService
     * @param MerchantService $merchantService
     * @param MessageApi $messageApi
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws \AppBundle\Exception\MailException
     */
    private function createTicket(
        Request $request,
        $ticketType,
        $userEmail,
        SecurityService $securityService,
        TranslatorInterface $translator,
        TicketService $ticketService,
        UserBddService $userBddService,
        MailService $mailService,
        MerchantService $merchantService,
        MessageApi $messageApi
    )
    {

        $ticketMessage = new TicketMessage();
        $createBycustomer = !$this->isOperator() && !$this->isAdmin();
        $ticket = new Ticket($createBycustomer);
        $ticket->addMessage($ticketMessage);

        $isAnonymous = $ticketType === TicketConstant::TICKET_ANONYMOUS;

        $form_options = [];
        if ($isAnonymous) {
            $form_options['validation_groups'] = ["Default", "TicketAnonymous"];

            $form_options['captcha_enabled'] = true;
        }

        $merchants = [];
        if (!is_string($this->getUser()) && !$isAnonymous) {
            $merchants = $merchantService->getAcceptedMerchants();
        }

        if ($userEmail) {
            $form_options['userEmail'] = $userEmail;
        }

        $form = $this->createForm(
            TicketType::class,
            $ticket,
            $form_options
        );

        $viewParams = array(
            "base" => TicketConstant::BASES[$ticketType],
            self::VIEW_PARAM_IS_ADMIN => $securityService->isAdmin($this->getUser()),
            "isAnonymous" => $isAnonymous,
            "customShowMenu" => false,
            "merchants" => $merchants
        );

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {

            /**
             * @var Ticket $ticket
             */
            $ticket = $form->getData();

            // check if merchant ID is in the request
            // if it is the case send send a message to the vendor
            // otherwise continue
            $isAdmin = (int)$request->get('isadmin'); // this propriety is set in the HTML
            $merchant = $request->get('merchants');
            $files = $ticketMessage->getAttachments();

            if ($merchant !== null && $isAdmin === 0) {
                $merchantId = (int)$merchant;
                $response = $messageApi->contactMerchant(
                    $ticket->getSubject(),
                    $ticketMessage->getContent(),
                    $this->getUser()->getCompany()->getIzbergUserId(),
                    $merchantId,
                    ...$files
                );

                $status = $response->getStatusCode();

                if( $status == 201 ){
                    $content = json_decode($response->getBody()->getContents(), true);
                    $izberg_id = $content['id']??'';
                    $this->logger->info("thread_parent_message izberg_id: $izberg_id");

                    if(!empty($izberg_id)) {
                        $threadParentMessageRepository = $this->doctrine->getRepository(ThreadParentMessage::class);
                        $threadParentMessage = new ThreadParentMessage();
                        $threadParentMessage->setIzbergId($izberg_id);
                        $threadParentMessage->setFromEmail($this->getUser()->getId());
                        $threadParentMessageRepository->add($threadParentMessage, true);
                    }

                    // Add success message
                    $this
                        ->addFlash(
                            'success',
                            $translator->trans('contactMerchant.form.success', array(), self::TRANSLATION_DOMAIN)
                        );

                    $ticket = new Ticket();
                    $ticketMessage = new TicketMessage();
                    $ticket->addMessage($ticketMessage);

                    $form = $this->createForm(
                        TicketType::class,
                        $ticket,
                        $form_options
                    );

                } else {
                    // add an error message
                    $this
                        ->addFlash(
                            'error',
                            $translator->trans('contactMerchant.form.error', array(), self::TRANSLATION_DOMAIN)
                        );
                }

            } else {
                $ticket->setTicketNumber($this->guidv4(openssl_random_pseudo_bytes(16)));

                //complete data
                $ticket->getMessages()[0]->setTicket($ticket);

                if (!is_string($this->getUser()) && !$isAnonymous) {
                    $ticket->getMessages()[0]->setUserCreated($this->getUser());
                }
                $ticket->setIsCreatedByAdmin($securityService->isAdmin($this->getUser()));
                $ticket->setLastMessage($ticket->getMessages()[0]);
                $ticket->setStatus(TicketConstant::STATUS_NEW);

                /** @var User $user */
                if ($userEmail) {
                    $em = $this->doctrine->getManager();

                    /**
                     * @var UserRepository $userRepository
                     */
                    $userRepository = $em->getRepository(User::class);
                    $user = $userRepository->findByEmail($userEmail);
                    $ticket->setRecipient($user);
                }

                //set author if non anonymous
                if (!$isAnonymous && $ticketType != TicketConstant::TICKET_ADMIN) {
                    $this->logger->info(
                        "Send ticket by front contact form",
                        LogUtil::buildContext([
                            LogUtil::EVENT_NAME => EventNameEnum::CONTACT_SEND_BY_USER,
                            LogUtil::USER_NAME => $this->getUser()->getUsername(),
                            'subject' => $ticket->getSubject(),
                            'message' => $ticket->getLastMessage()
                        ])
                    );
                    $ticket->setAuthor($this->getUser()->getCompany());
                    /**
                     * @var User $companyUser
                     */
                    $companyUser = $this->getUser();
                    $companyUser->getCompany()->addTicket($ticket);

                } else if ($ticketType === TicketConstant::TICKET_ADMIN) {
                    $ticket->getRecipient()->addTicket($ticket);
                } else {
                    $ticket->setAnonymousLocale($request->getLocale());
                    $this->logger->info(
                        "Send ticket by front contact form",
                        LogUtil::buildContext([
                            LogUtil::EVENT_NAME => EventNameEnum::CONTACT_SEND_ANONYMOUS,
                            'CAPTCHA' => $request->request->get('g-recaptcha-response'),
                            'email' => $ticket->getAnonymousEmail(),
                            'subject' => $ticket->getSubject(),
                            'message' => $ticket->getLastMessage(),
                        ])
                    );
                }

                /** @var TicketMessage $message */
                $message = $ticket->getLastMessage();

                $files = $message->getAttachments();
                $attachments = array();


                $errors = [];
                /** @var UploadedFile $file */
                foreach ($files as $file) {

                    if (!in_array($file->getMimeType(), Document::MIME_CONSTRAINT)) {
                        $errors[] = $file->getClientOriginalName() . ' : ' . $translator->trans('document.upload.mime', array('%contrainte%' => implode(", ", Document::MIME_CONSTRAINT)), self::TRANSLATION_DOMAIN);
                    }
                    if (strlen(strval(file_get_contents($file->getRealPath()))) > Document::SIZE_CONSTRAINT) {
                        $errors[] = $file->getClientOriginalName() . ' : ' . $translator->trans('document.upload.size', array('%contrainte%' => Document::SIZE_CONSTRAINT / (1024 * 1024)), self::TRANSLATION_DOMAIN);
                    }

                    $attachment = new TicketMessageAttachment();

                    $attachment->setFilename($file->getClientOriginalName());
                    $attachment->setMessage($message);
                    $attachment->setMimeType($file->getMimeType());
                    $attachment->setSize($file->getSize());
                    $attachment->setBinary(file_get_contents($file->getRealPath()));
                    $attachments[] = $attachment;
                }

                $ticket->getLastMessage()->setAttachments($attachments);

                //create the ticket in the db
                try {
                    if (count($errors) > 0) {
                        // create a flash back
                        foreach ($errors as $error) {
                            $this->addFlash('error', $error);
                        }
                    } else {
                        $ticketService->createTicket($ticket);

                        //send emails notification
                        $this->sendTicketCreationEmails($ticket, $userBddService,  $mailService, $securityService);

                        //add success flash
                        $this->addFlash('success', $translator->trans('ticket.success.create', [], self::TRANSLATION_DOMAIN));

                        //redirect to edit route
                        return $this->redirectToRoute(TicketConstant::EDIT_ROUTES[$ticketType], array('id' => $ticket->getTicketNumber()));
                    }
                } catch (ORMException $e) {
                    $this->logger->error(
                        "unable to save the ticket",
                        LogUtil::buildContext([
                            LogUtil::EVENT_NAME => EventNameEnum::TECHNICAL_ERROR,
                            LogUtil::USER_NAME => $this->getUsername(),
                            "exception" => $e->getTraceAsString(),
                        ])
                    );

                    $this->addFlash('error', $translator->trans('ticket.error.create', [], self::TRANSLATION_DOMAIN));
                }
            }
        }

        $viewParams['form'] = $form->createView();

        $viewParams['front'] = $ticketType === TicketConstant::TICKET_ANONYMOUS ||
            $ticketType === TicketConstant::TICKET_BUYER;

        return $this->render(
            '@OpenTicket/ticket_create.html.twig',
            $viewParams
        );
    }

    private function guidv4($data)
    {
        assert(strlen($data) == 16);

        $data[6] = chr(ord($data[6]) & 0x0f | 0x40); // set version to 0100
        $data[8] = chr(ord($data[8]) & 0x3f | 0x80); // set bits 6-7 to 10

        return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
    }

    /**
     * @param Request            $request
     * @param int                $ticketType
     * @param                    $filter
     * @param                    $form
     *
     * @param MessageApi         $messageApi
     * @param TicketService      $ticketService
     *
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws \Exception
     */
    private function listTickets($request, $ticketType, $filter, $form, MessageApi $messageApi, TicketService $ticketService, PaginatorInterface $paginator)
    {
        /**
         * @var User|null $user
         */
        $user = $this->getUser();
        if ($request->get("status") !== "closed" && (!$this->isAdmin() && !$this->isOperator())) {
            // $izbTicket = $this->getMessagesIZBFromApi($messageApi, $ticketService);
            $izbTicket = $this->getMessagesIZBFromDB();
            // Récupération des messages internes
            $internTicket = $ticketService->getInternTicket($user, $request);
            // Concaténation avec les messages internes
            $tickets = array_merge($internTicket, $izbTicket);

            // Variable de trie de la request
            $sort = $request->query->get('sort') ?? '';
            $direction = $request->query->get('direction') ?? '';

            // Trie par date de modification ou de création par ordre ascendant ou descendant
            usort($tickets, function (Ticket $a, Ticket $b) use ($sort, $direction) {
                // par défaut par date de modification
                $aDate = null;
                if ($a->isFromIzb() && $sort === 'createdAt') {
                    $aDate = (new \DateTime($a->getIzbDate()))->getTimestamp();
                } else if ($a->isFromIzb()) {
                    $aDate = (new \DateTime($a->getLastMessage()->getIzbDate()))->getTimestamp();
                } else {
                    $aDate = $a->getCreatedAt()->getTimestamp();
                }

                $bDate = null;
                if ($b->isFromIzb() && $sort === 'createdAt') {
                    $bDate = (new \DateTime($b->getIzbDate()))->getTimestamp();
                } else if ($b->isFromIzb()) {
                    $bDate = (new \DateTime($b->getLastMessage()->getIzbDate()))->getTimestamp();
                } else {
                    $bDate = $b->getCreatedAt()->getTimestamp();
                }

                // Par défaut par ordre descendant
                if ($direction === 'asc') {
                    return ($aDate < $bDate) ? -1 : 1;
                }
                return ($aDate > $bDate) ? -1 : 1;
            });

            // Juste une petite boucle pour "remplir" l'objet ticket aec/company/contact

            /** @var Ticket $ticket */
            foreach ($tickets as $ticket) {
                $ticket->setTicketCreator($ticket->getLastMessage()->getUserCreated());
            }

            $viewParams = array(
                "base" => TicketConstant::BASES[$ticketType],
                self::VIEW_PARAM_IS_ADMIN => $ticketType === TicketConstant::TICKET_ADMIN,
                "isOperator" => $this->isOperator(),
                "addRoute" => TicketConstant::ADD_ROUTES[$ticketType],
                "locale" => $request->getLocale(),
                "editRoute" => TicketConstant::EDIT_ROUTES[$ticketType],
                "editRouteFromIZB" => 'izberg.ticket.edit',
                "listRoute" => TicketConstant::LIST_ROUTES[$ticketType],
                "pagination" => $paginator->paginate($tickets, $request->query->get('page', 1), 10),
                "customShowMenu" => false
            );

            return $this->render(
                '@OpenTicket/ticket_list.html.twig',
                $viewParams
            );
        } else {

            $numberPerPage = $ticketType === TicketConstant::TICKET_ADMIN ? 25 : 10;

            $tickets = $ticketService->getCustomPaginator($user, $request, $filter, $numberPerPage);

            // Juste une petite boucle pour "remplir" l'objet ticket aec/company/contact

            /** @var Ticket $ticket */
            foreach ($tickets as $ticket) {
                $ticket->setTicketCreator($ticket->getLastMessage()->getUserCreated());
            }

            $viewParams = array(
                "base" => TicketConstant::BASES[$ticketType],
                self::VIEW_PARAM_IS_ADMIN => $ticketType === TicketConstant::TICKET_ADMIN,
                "isOperator" => $this->isOperator(),
                "addRoute" => TicketConstant::ADD_ROUTES[$ticketType],
                "locale" => $request->getLocale(),
                "editRoute" => TicketConstant::EDIT_ROUTES[$ticketType],
                "listRoute" => TicketConstant::LIST_ROUTES[$ticketType],
                "pagination" => $tickets,
                "customShowMenu" => false
            );

            if ($form) {
                $viewParams["form"] = $form->createView();
            }

            return $this->render(
                '@OpenTicket/ticket_list.html.twig',
                $viewParams
            );
        }
    }

    private function getMessagesIZBFromApi(MessageApi $messageApi, TicketService $ticketService)
    {
        // Récupérer les messages d'IZB
        $messagesOutbox = $messageApi->getBuyerOutboxMessages($this->getUser()->getCompany()->getIzbergUserId());
        $messagesInbox = $messageApi->getBuyerInboxMessages($this->getUser()->getCompany()->getIzbergUserId());
        $izbMessages = array_merge($messagesOutbox, $messagesInbox);

        $izbTicket = [];
        $izbergUnreadMessageIds = $ticketService->getUnreadMessageIds($this->getUser()->getCompany()->getIzbergUserId());

        // Hiérarchiser les messages comme le système interne
        foreach ($izbMessages as $message) {
            // Création du ticket virtuel pour matcher avec le système interne
            $ticket = new Ticket(true);
            if (strpos($message->sender->resource_uri, '/user/') != 0) {
                $ticket->setAuthor($this->getUser()->getCompany());
            } else {
                $from = $message->from_display_name;
                if (strrpos($from, ']') != 0) {
                    $from = substr($from, strrpos($from, ']') + 2);
                }
                $company = new Company();
                $company->setName($from);
                $ticket->setAuthor($company);
            }

            $ticket->setCompany($this->getUser()->getCompany());
            $ticket->setSubject($message->subject);
            $ticket->setTicketNumber($message->id);
            $ticket->setId(intval($message->id));
            $ticket->setIzbDate($message->sent_at);
            $ticket->setFromIzb(true);
            $ticket->setVendor($message->to_display_name);

            // Création du TicketMessage virtuel pour la date de mise à jour
            $ticketMessage = new TicketMessage();
            // S'il n'y a pas de message suivant => date de mise à jour = date du message
            if (sizeof($message->next_messages) == 0) {
                $ticketMessage->setIzbDate($message->sent_at);
                $ticket->setLastMessage($ticketMessage);
                // Sinon date de mise à jour = date du premier message suivant ( trier par date décroissante par IZB )
            } else {
                $nextUrl = substr($message->next_messages[0], 0, -1);
                $nextId = substr($nextUrl, strrpos($nextUrl, '/') + 1);
                $next = $messageApi->getMessageById($nextId);

                $ticketMessage = new TicketMessage();
                $ticketMessage->setIzbDate($next->sent_at);
                $ticket->setLastMessage($ticketMessage);
            }

            $ticket->setNotReadIzberg(in_array($message->id, $izbergUnreadMessageIds));

            $izbTicket[] = $ticket;
        }
        return $izbTicket;
    }

    private function getMessagesIZBFromDB()
    {
        $izbMessages = $this->doctrine->getRepository(ThreadMessage::class)->findAllByIzbergUserId($this->getUser()->getCompany()->getIzbergUserId());
        $izbTicket = [];
        foreach ($izbMessages as $message) {
            // Création du ticket virtuel pour matcher avec le système interne
            $ticket = new Ticket(true);
            if (strpos($message->getSenderResourceUri(), '/user/') != 0) {
                $ticket->setAuthor($this->getUser()->getCompany());
            } else {
                $from = $message->getFromDisplayName();
                if (strrpos($from, ']') != 0) {
                    $from = substr($from, strrpos($from, ']') + 2);
                }
                $company = new Company();
                $company->setName($from);
                $ticket->setAuthor($company);
            }

            $ticket->setCompany($this->getUser()->getCompany());
            $ticket->setSubject($message->getSubject());
            $ticket->setTicketNumber($message->getIzbergId());
            $ticket->setId(intval($message->getIzbergId()));
            $ticket->setIzbDate($message->getOriginalCreatedDate());
            $ticket->setFromIzb(true);
            $ticket->setVendor($message->getToDisplayName());

            $ticketMessage = new TicketMessage();
            $ticketMessage->setIzbDate($message->getLastModifiedDate());
            $ticket->setLastMessage($ticketMessage);

            $ticket->setNotReadIzberg($message->getHasUnreadMessages() == 1);

            $izbTicket[] = $ticket;
        }
        return $izbTicket;
    }

    /**
     * @param Ticket              $ticket
     * @param SecurityService     $securityService
     * @param TranslatorInterface $translator
     */
    private function isUserAuthorizedToAccessTicket($ticket, SecurityService $securityService, TranslatorInterface $translator)
    {
        if ($ticket) {
            /**
             * @var User|null $user
             */
            $user = $this->getUser();
            if (!$securityService->isAdmin($user) && $ticket->getCompany() != null && $ticket->getCompany()->getId() !== $this->getUser()->getCompany()->getId()) {
                throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_EXCEPTION));
            }
        } else {
            throw $this->createNotFoundException('The ticket does not exist');
        }
    }

    /**
     * @param Ticket                $ticket
     *
     * @return array
     */
    private function getFilesForEmail($ticket)
    {
        $attachFiles = [];
        $files = $ticket->getLastMessage()->getAttachments();
        if ($files->count() > 0) {
            /** @var TicketMessageAttachment $file */
            foreach ($files as $file) {
                $attachment = new AttachmentLink();

                $attachment->setFilename($file->getFilename());
                $attachment->setLink($this->generateUrl("attachment.view", [
                    "token" => $ticket->getTicketNumber(),
                    "attachmentId" => $file->getId()
                ], Router::ABSOLUTE_URL));

                $attachFiles[] = $attachment;
            }
        }
        return $attachFiles;
    }

    /**
     * send emails to author and recipient to inform them that a new ticket has been created
     *
     * @param Ticket          $ticket
     *
     *
     * @param UserBddService  $userBddService
     * @param MailService     $mailService
     *
     * @param SecurityService $securityService
     *
     * @throws \AppBundle\Exception\MailException
     */
    private function sendTicketCreationEmails(Ticket $ticket, UserBddService $userBddService, MailService $mailService, SecurityService $securityService)
    {

        /////////////////////////////////////////////////////////////////////////////////////
        /// SEND EMAIL FOR TICKET CREATION CONFIRMATION
        /////////////////////////////////////////////////////////////////////////////////////

        $attachFiles = $this->getFilesForEmail($ticket);

        $recipients = [];
        if ($ticket->isCreatedByAdmin()) {
            $recipients = $userBddService->getOperatorsUsers();

            /**
             * @var User $recipient
             */
            foreach ($recipients as $recipient) {

                if ($recipient->isEnabled()) {
                    $ticketUrl = $this->getUrlViewTicketForUser($recipient, $ticket, $securityService);

                    $mailService->sendEmailMessage(MailService::TICKET_NEW_ADMINISTRATOR_SENDER, "en", $recipient->getEmail(), [
                        MailService::FIRST_NAME_VAR => $recipient->getFirstname(),
                        MailService::LAST_NAME_VAR => $recipient->getLastname(),
                        "url" => $ticketUrl,
                        "ticketNumber" => $ticket->getTicketNumber(),
                        "content" => $ticket->getLastMessage()->getContent(),
                        "subject" => $ticket->getSubject()
                    ],
                        null,
                        null,
                        $attachFiles);
                }
            }
        } else {

            /** @var User $user */
            $user = $this->getUser();
            if ($user !== null) {
                $recipients[] = $user;
            } else {
                //create anonymous user
                $recipients[] = $this->createAnonymousUser($ticket);
            }

            /**
             * @var User $recipient
             */
            foreach ($recipients as $recipient) {

                if ($recipient->isEnabled()) {
                    $ticketUrl = $this->getUrlViewTicketForUser($recipient, $ticket, $securityService);

                    $mailService->sendEmailMessage(MailService::TICKET_NEW_USER_SENDER,
                        $recipient->getLocale(),
                        $recipient->getEmail(),
                        [
                            MailService::FIRST_NAME_VAR => $recipient->getFirstname(),
                            MailService::LAST_NAME_VAR => $recipient->getLastname(),
                            "url" => $ticketUrl,
                            "subject" => $ticket->getSubject(),
                            "content" => $ticket->getLastMessage()->getContent(),
                            "ticketNumber" => $ticket->getTicketNumber()
                        ],
                        null,
                        null,
                        $attachFiles);
                }
            }
        }


        /////////////////////////////////////////////////////////////////////////////////////
        /// SEND EMAIL FOR RECIPIENTS
        /////////////////////////////////////////////////////////////////////////////////////

        //if ticket has been created by admin, the recipient is the recipient field
        //note that an admin can't send a ticket to an anonymous user. So for a ticket by an admin, the recipient field of the ticket can't be null
        if ($ticket->isCreatedByAdmin()) {

            $recipient = $ticket->getRecipient();
            /**
             * @var User $user
             */
            foreach ($recipient->getUsers() as $user) {
                if ($user->isEnabled()) {
                    $recipientEmail = $user->getEmail();
                    $ticketUrl = $this->getUrlViewTicketForUser($user, $ticket, $securityService);
                    $lang = $user->getLocale();
                    $mailService->sendEmailMessage(MailService::TICKET_NEW_USER_RECIPIENT, $lang, $recipientEmail, [
                        "url" => $ticketUrl,
                        "content" => $ticket->getLastMessage()->getContent(),
                        "subject" => $ticket->getSubject(),
                        "ticketNumber" => $ticket->getTicketNumber(),
                        MailService::FIRST_NAME_VAR => $user->getFirstname(),
                        MailService::LAST_NAME_VAR => $user->getLastname()
                    ],
                        null,
                        null,
                        $attachFiles);
                }
            }
        } //if the ticket was not created by an admin, the recipient is all the administrators
        else {
            $recipients = $userBddService->getOperatorsUsers();
            /**
             * @var User $recipient
             */
            foreach ($recipients as $recipient) {
                if ($recipient->isEnabled()) {
                    $ticketUrl = $this->generateUrl(self::ROUTE_ADMIN_TICKET_EDIT, ["id" => $ticket->getTicketNumber()], UrlGeneratorInterface::ABSOLUTE_URL);
                    $mailService->sendEmailMessage(MailService::TICKET_NEW_ADMINISTRATOR_RECIPIENT,
                        $recipient->getLocale(),
                        $recipient->getEmail(),
                        [
                            "url" => $ticketUrl,
                            "content" => $ticket->getLastMessage()->getContent(),
                            "subject" => $ticket->getSubject(),
                            "ticketNumber" => $ticket->getTicketNumber(),
                            MailService::FIRST_NAME_VAR => $recipient->getFirstname(),
                            MailService::LAST_NAME_VAR => $recipient->getLastname()
                        ],
                        null,
                        null,
                        $attachFiles);
                }
            }
        }
    }


    /**
     * send emails to all contributors of a ticket to inform them that he has been updated
     *
     * @param Ticket          $ticket the ticket
     *
     * @throws \AppBundle\Exception\MailException
     */
    private function sendTicketUpdateEmails(Ticket $ticket, SecurityService $securityService, MailService $mailService, UserBddService $userBddService)
    {

        //prepare the attachment if needed
        $attachFiles = $this->getFilesForEmail($ticket);

        //GET THE LIST OF ALL NON ADMINISTRATOR USER TO NOTIFY
        $users = [];

        if ($ticket->getAuthor() != null) {
            foreach ($ticket->getAuthor()->getUsers() as $author) {
                $users[$author->getEmail()] = $author;
            }
        }
        if ($ticket->getRecipient() != null) {
            foreach ($ticket->getRecipient()->getUsers() as $recipient) {
                $users[$recipient->getEmail()] = $recipient;
            }
        }
        if ($ticket->getAnonymousEmail()) {
            $users [$ticket->getAnonymousEmail()] = $this->createAnonymousUser($ticket);
        }

        //iterate on messages to add all non admin contributors

        /**
         * @var TicketMessage $message
         */
        foreach ($ticket->getMessages() as $message) {
            if ($message->getUserCreated() && !$securityService->isAdmin($message->getUserCreated())) {
                $users[$message->getUserCreated()->getEmail()] = $message->getUserCreated();
            }
        }

        //we don't want to notify many time the same user
        $users = array_unique($users);

        //now notify
        /**
         * @var User $user
         */
        foreach ($users as $email => $user) {
            if ($user->isEnabled()) {
                $ticketUrl = $this->getUrlViewTicketForUser($user, $ticket, $securityService);
                $mailService->sendEmailMessage(MailService::TICKET_UPDATE_USER,
                    $user->getLocale(),
                    $email,
                    [
                        "url" => $ticketUrl,
                        "content" => $ticket->getLastMessage()->getContent(),
                        "subject" => $ticket->getSubject(),
                        "ticketNumber" => $ticket->getTicketNumber(),
                        MailService::FIRST_NAME_VAR => $user->getFirstname(),
                        MailService::LAST_NAME_VAR => $user->getLastname()
                    ],
                    null,
                    null,
                    $attachFiles);
            }
        }

        //also send a notification to all administrators
        $ticketUrl = $this->generateUrl(self::ROUTE_ADMIN_TICKET_EDIT, ["id" => $ticket->getTicketNumber()], UrlGeneratorInterface::ABSOLUTE_URL);
        $operators = $userBddService->getOperatorsUsers();
        /**
         * @var User $operator
         */
        foreach ($operators as $operator) {
            $mailService->sendEmailMessage(MailService::TICKET_UPDATE_ADMINISTRATOR,
                $operator->getLocale(),
                $operator->getEmail(),
                [
                    "url" => $ticketUrl,
                    "content" => $ticket->getLastMessage()->getContent(),
                    "subject" => $ticket->getSubject(),
                    "ticketNumber" => $ticket->getTicketNumber(),
                    MailService::FIRST_NAME_VAR => $operator->getFirstname(),
                    MailService::LAST_NAME_VAR => $operator->getLastname()
                ],
                null,
                null,
                $attachFiles);
        }
    }

    /**
     * @param User            $user
     * @param Ticket          $ticket
     *
     * @param SecurityService $securityService
     *
     * @return string url to view the ticket, depending on the user type
     */
    private function getUrlViewTicketForUser(User $user, Ticket $ticket, SecurityService $securityService)
    {

        /*
         * if the user is not anonymous, we want to redirect to the correct route
         */
        if ($securityService->isAdmin($user)) {
            $route = self::ROUTE_ADMIN_TICKET_EDIT;
        } else if ($securityService->isBuyer($user)) {
            $route = 'buyer.ticket.edit';
        } else {
            $route = 'anonymous.ticket.edit';
        }

        return $this->generateUrl($route, ["id" => $ticket->getTicketNumber()], UrlGeneratorInterface::ABSOLUTE_URL);
    }

    /**
     * create a temporary anonymous user (for email sending)
     * @param Ticket $ticket
     * @return User
     */
    private function createAnonymousUser(Ticket $ticket)
    {
        $recipient = new User();
        $recipient->setEmail($ticket->getAnonymousEmail());
        $recipient->setFirstname($ticket->getAnonymousFirstName());
        $recipient->setLastname($ticket->getAnonymousLastName());
        $recipient->setLocale($ticket->getAnonymousLocale());
        $recipient->setRoles([]);
        $recipient->setEnabled(true);
        return $recipient;
    }
}
