<?php

namespace Open\IzbergBundle\Controller;

use Api\Domain\Invoice\Message\InvoicePayloadMessage;
use Api\Domain\Order\Message\OrderPayloadMessage;
use AppBundle\Controller\MkoController;
use AppBundle\Entity\Company;
use AppBundle\Entity\Country;
use AppBundle\Entity\Site;
use AppBundle\Entity\User;
use AppBundle\Factory\OrderFactory;
use AppBundle\Model\Offer;
use AppBundle\Repository\CompanyRepository;
use AppBundle\Repository\ThreadParentMessageRepository;
use AppBundle\Repository\UserRepository;
use AppBundle\Services\AlstomCustomAttributes;
use AppBundle\Services\CartService;
use AppBundle\Services\CompanyService;
use AppBundle\Services\CountryService;
use AppBundle\Services\CustomsService;
use AppBundle\Services\MailService;
use AppBundle\Services\MerchantOrderService;
use AppBundle\Services\OfferService;
use AppBundle\Services\OrderService;
use AppBundle\Services\ProcessService;
use AppBundle\Services\Shipping\PreOrderService;
use AppBundle\Services\WPSService;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Persistence\ManagerRegistry;
use Exception;
use JMS\Serializer\SerializerInterface;
use Open\IzbergBundle\Api\ApiClientManager;
use Open\IzbergBundle\Api\ApiConfigurator;
use Open\IzbergBundle\Api\CartApi;
use Open\IzbergBundle\Api\GatewayApi;
use Open\IzbergBundle\Api\InvoiceApi;
use Open\IzbergBundle\Api\MerchantApi;
use Open\IzbergBundle\Api\OrderApi;
use Open\IzbergBundle\Api\ProductOfferApi;
use Open\IzbergBundle\Model\InvoiceWebHookRequest;
use Open\IzbergBundle\Model\Item;
use Open\IzbergBundle\Model\MerchantOrder;
use Open\IzbergBundle\Model\Order;
use Open\IzbergBundle\Model\WebHookMerchantOrderRefundRequest;
use Open\IzbergBundle\Model\WebHookMerchantOrderRequest;
use Open\IzbergBundle\Model\WebHookMessageRequest;
use Open\IzbergBundle\Model\WebHookOrderRequest;
use Open\IzbergBundle\WebHook\WebHookException;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Open\WebhelpBundle\Api\TransactionApi;
use RuntimeException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;

class WebHookController extends MkoController
{
    private const LOG_IZBERG_EVENT = "izberg_event";
    private const LOG_WH_CONTENT = "hook_content";

    private const JSON_TYPE = 'json';

    private OrderService $orderService;

    private MessageBusInterface $messageBus;

    public function __construct(OrderService $orderService, MessageBusInterface $messageBus, ManagerRegistry $doctrine)
    {
        parent::__construct($doctrine);
        $this->orderService = $orderService;
        $this->messageBus = $messageBus;
    }

    private function getOrderSiteFromIzbergOrderId(int $izbergOrderId): ?Site
    {
        return $this->orderService->getOrderSiteFromIzbergOrderId($izbergOrderId);
    }


    /**
     * @param Request $request
     * @param ApiClientManager $apiClientManager
     * @param ApiConfigurator $apiConfigurator
     *
     * @param ProcessService $processService
     * @param OrderService $orderService
     *
     * @param SerializerInterface $serializer
     * @param OfferService $offerService
     * @param ProductOfferApi $productOfferApi
     * @param CartService $cartService
     * @return JsonResponse
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/hooks/order/confirmed', name: 'hooks.order.status.confirmed', methods: ['POST'])]
    public function orderConfirmed(
        Request $request,
        ApiClientManager $apiClientManager,
        ApiConfigurator $apiConfigurator,
        ProcessService $processService,
        OrderService $orderService,
        SerializerInterface $serializer,
        OfferService $offerService,
        ProductOfferApi $productOfferApi,
        CartService $cartService
    )
    {
        $this->configureApiConnection('webhook', $apiClientManager, $apiConfigurator);

        $event = 'order_confirmed';
        try {
            /** @var WebHookOrderRequest $hook */
            $hook = $serializer->deserialize($request->getContent(), WebHookOrderRequest::class, self::JSON_TYPE);

            /** @var Order $order */
            $order = $hook->getData();
            $orderService->syncSingleOrder($order);

            $processService->processBeginFor($order);

            // As the order is confirmed, we want to update the real stock of the bought products
            $this->updateRealStock($order, $event, $offerService, $productOfferApi, $cartService);

            $processService->processEndFor();
        } catch (Exception $e) {
            $this->logger->error(
                "unknown error while processing izberg webHook: " . $event,
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                    self::LOG_IZBERG_EVENT => $event,
                    self::LOG_WH_CONTENT => $request->getContent(),
                    "message" => $e->getMessage()
                ])
            );
            return new JsonResponse(new \ArrayObject(), 500);
        }

        return $this->json(new \ArrayObject());
    }

    /**
     * @param Request             $request
     * @param ApiClientManager    $apiClientManager
     * @param ApiConfigurator     $apiConfigurator
     *
     * @param ProcessService      $processService
     * @param OrderService        $orderService
     *
     * @param SerializerInterface $serializer
     * @return JsonResponse
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/hooks/order/processed', name: 'hooks.order.status.processed', methods: ['POST'])]
    public function orderProcessed(Request        $request, ApiClientManager $apiClientManager, ApiConfigurator $apiConfigurator,
                                   ProcessService $processService, OrderService $orderService, SerializerInterface $serializer)
    {
        $this->configureApiConnection('webhook', $apiClientManager, $apiConfigurator);

        $event = 'order_processed';
        try {
            /** @var WebHookOrderRequest $hook */
            $hook = $serializer->deserialize($request->getContent(), WebHookOrderRequest::class, self::JSON_TYPE);

            /** @var Order $order */
            $order = $hook->getData();
            $orderService->syncSingleOrder($order);

            $processService->processBeginFor($order);
            $processService->processEndFor();

            $this->messageBus->dispatch(new OrderPayloadMessage($order));
        } catch (Exception $e) {
            $this->logger->error(
                "unknown error while processing izberg webHook: " . $event,
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                    self::LOG_IZBERG_EVENT => $event,
                    self::LOG_WH_CONTENT => $request->getContent(),
                    "message" => $e->getMessage()
                ])
            );
            return new JsonResponse(new \ArrayObject(), 500);
        }

        return $this->json(new \ArrayObject());
    }

    /**
     * @param Request                $request
     * @param ApiClientManager       $apiClientManager
     * @param ApiConfigurator        $apiConfigurator
     *
     * @param CustomsService         $customsService
     * @param CompanyService         $companyService
     * @param CountryService         $countryService
     * @param MerchantApi            $merchantApi
     * @param InvoiceApi             $invoiceApi
     * @param AlstomCustomAttributes $alstomCustomAttributesService
     * @param SerializerInterface    $serializer
     * @return JsonResponse
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/hooks/invoice/updated', name: 'hooks.invoice.status.updated', methods: ['POST'])]
    public function invoiceStatusChanged(Request                $request, ApiClientManager $apiClientManager, ApiConfigurator $apiConfigurator,
                                         CustomsService         $customsService, CompanyService $companyService, CountryService $countryService, MerchantApi $merchantApi, InvoiceApi $invoiceApi,
                                         AlstomCustomAttributes $alstomCustomAttributesService, SerializerInterface $serializer)
    {
        $this->configureApiConnection('webhook', $apiClientManager, $apiConfigurator);

        $event = 'hooks.invoice.status.updated';
        try {
            //loading services

            /** @var InvoiceWebHookRequest $hook */
            $hook = $serializer->deserialize($request->getContent(), InvoiceWebHookRequest::class, self::JSON_TYPE);

            /** @var array $invoice */
            $invoice = $hook->getData();

            $this->logger->info(
                "IZBERG HOOK: " . $event,
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                    "invoiceId" => $invoice["id"],
                    self::LOG_IZBERG_EVENT => $event,
                ])
            );

            if ($invoice['status'] === 'draft') {

                //preparing variable to store the mentions to set
                $mentionsToSet = "";

                //load merchant
                $merchant = $merchantApi->getMerchant($invoice["issuer"]["id"]);
                //loading buyer address
                $this->logger->info(
                    " + loading company from receiver id",
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                        "receiverId" => $invoice['receiver']['id']
                    ])
                );

                /** @var Company $company */
                $company = $companyService->findByIzbergUserId($invoice['receiver']['id']);
                if ($company !== null && $company->getMainAddress() !== null && $company->getMainAddress()->getCountry() !== null) {
                    $buyerCountry = $company->getMainAddress()->getCountry();

                    //now try to get the country of the seller
                    $this->logger->info(
                        " + loading merchant from issuer id",
                        LogUtil::buildContext([
                            LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                            "issuerId" => $invoice['issuer']['id']
                        ])
                    );

                    /** @var Country $sellerCountry */
                    $sellerCountry = $countryService->getCountryByIzbergId($merchant->addresses[0]->country->id);

                    if ($sellerCountry !== null) {

                        //at this point, we can patch the invoice
                        $mentionsToSet = $customsService->getAdditionalLegalNotices($sellerCountry, $buyerCountry);

                        $this->logger->info(
                            "   => getting legal notice from country",
                            LogUtil::buildContext([
                                LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                                "invoiceId" => $invoice["id"],
                                "sellerCountryCode" => $sellerCountry->getCode(),
                                "buyerCountryCode" => $buyerCountry->getCode(),
                                "mention" => $mentionsToSet
                            ])
                        );
                    } else {
                        $this->logger->error(
                            " * no country was found for the merchant izberg country id: ",
                            LogUtil::buildContext([
                                LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                                self::LOG_IZBERG_EVENT => $event,
                                "izbergCountryId" => $merchant->addresses[0]->country->id,
                                "merchantId" => $merchant->id
                            ])
                        );
                    }
                } else {
                    $this->logger->error(
                        " * no company was found for this izbergUserId: ",
                        LogUtil::buildContext([
                            EventNameEnum::IZBERG_WEB_HOOK,
                            self::LOG_IZBERG_EVENT => $event,
                            "izbergUserId" => $invoice['receiver']['id']
                        ])
                    );
                }

                //check if merchant has customs mentions
                $this->logger->info(
                    ' * checking if merchant has legal notice',
                    LogUtil::buildContext([
                        EventNameEnum::IZBERG_WEB_HOOK,
                        "merchantId" => $merchant->id
                    ])
                );

                $merchantLegalNoticeAttributeName = $alstomCustomAttributesService->getMerchantLegalNotice();
                $merchantLegalNoticeAttribute = $merchantApi->getMerchantCustomAttribute($merchant->id, $merchantLegalNoticeAttributeName);
                if ($merchantLegalNoticeAttribute !== null) {
                    if (!empty($mentionsToSet)) {
                        $mentionsToSet .= "\n";
                    }
                    $mentionsToSet .= $merchantLegalNoticeAttribute;
                }

                //check if mention to set is empty
                if (empty(trim($mentionsToSet))) {
                    $mentionsToSet = "-";
                }

                $this->logger->info(
                    "   => setting legals mentions for invoice",
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                        "invoiceId" => $invoice["id"],
                        "mention" => $mentionsToSet,
                        self::LOG_IZBERG_EVENT => $event
                    ])
                );

                //now we cant set mentions
                $invoiceApi->setLegalNotice($invoice["id"], $mentionsToSet);

            }

            //returning success
            return $this->json(new \ArrayObject());

        } catch (Exception $e) {
            $this->logger->error(
                " * unknown error while processing izberg webHook: " . $event,
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                    self::LOG_IZBERG_EVENT => $event,
                    self::LOG_WH_CONTENT => $request->getContent(),
                    "message" => $e->getMessage()
                ])
            );

            return new JsonResponse(new \ArrayObject(), 500);
        }
    }

    /**
     * update stock from the quantity in the merchant order
     *
     * @param MerchantOrder   $merchantOrder
     * @param string          $event for logging
     * @param OfferService    $offerService
     * @param ProductOfferApi $productOfferApi
     */
    private function reIncrementStock(MerchantOrder $merchantOrder, string $event,
                                      OfferService  $offerService, ProductOfferApi $productOfferApi)
    {

        //we don't want this code produces error, so catching everything
        try {
            /** @var Item $item */
            foreach ($merchantOrder->getItems() as $item) {

                //need to get current stock...
                /**@var OfferService $offerService */
                $offer = $offerService->findOfferById($item->getOfferId());

                // when no offer found do not increment stock
                if (!$offer instanceof Offer) {
                    continue;
                }

                if ($offer->isStockManagement()) {
                    continue;
                }

                $this->logger->info(
                    " + re increment stock for confirmed order",
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                        self::LOG_IZBERG_EVENT => $event,
                        "offerId" => $item->getOfferId(),
                        "stockToAdd" => $item->getQuantity()
                    ])
                );

                //adding stock
                $productOfferApi->patchProductOffer($item->getOfferId(), ["stock" => $offer->getQuantity() + $item->getQuantity()]);
            }

        } catch (Exception $e) {
            $this->logger->error(
                " * Enable to re increment stock: " . $e->getMessage(),
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                    self::LOG_IZBERG_EVENT => $event,
                    "message" => $e->getMessage()
                ])
            );
        }
    }

    /**
     * update real stock from the quantity in the order
     *
     * @param Order $order
     * @param string $event for logging
     * @param OfferService $offerService
     * @param ProductOfferApi $productOfferApi
     * @param CartService $cartService
     */
    private function updateRealStock(
        Order           $order,
        string          $event,
        OfferService    $offerService,
        ProductOfferApi $productOfferApi,
        CartService     $cartService
    )
    {
        try {
            /** @var User $user */
            $user = $this->getUser();
            $cart = $cartService->findCart($user, $order->getCart()->getId());

            foreach ($cart->getItems() as $item) {
                $offer = $offerService->findOfferById($item->getOfferId());

                // when no offer found do not increment stock
                if (!$offer instanceof Offer) {
                    continue;
                }

                if (empty($offer->getRealStock()) || $offer->isStockManagement()) {
                    continue;
                }

                $this->logger->info(
                    " - re update real stock for confirmed order",
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                        self::LOG_IZBERG_EVENT => $event,
                        "offerId" => $item->getOfferId(),
                        "stockToDecrease" => $item->getQuantity()
                    ])
                );

                // update real stock
                $productOfferApi->patchProductOffer($item->getOfferId(), ["attributes" => ["BAC06_Real_Stock" => $offer->getRealStock() - $item->getQuantity()]]);
            }

        } catch (Exception $e) {
            $this->logger->error(
                " * An error when updating real stock: " . $e->getMessage(),
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                    self::LOG_IZBERG_EVENT => $event,
                    "message" => $e->getMessage()
                ])
            );
        }
    }

    /**
     *
     * @param Request              $request
     * @param ApiClientManager     $apiClientManager
     * @param ApiConfigurator      $apiConfigurator
     * @param ProcessService       $processService
     * @param SerializerInterface  $serializer
     * @param CartApi              $cartApi
     * @param OrderApi             $orderApi
     * @param GatewayApi           $gatewayApi
     * @param TransactionApi       $transactionApi
     * @param WPSService           $WPSService
     * @param PreOrderService      $preOrderService
     * @param OrderService         $orderService
     * @param MerchantOrderService $merchantOrderService
     * @return JsonResponse
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/hooks/order/merchant/confirmed', name: 'hooks.merchant.order.confirmed', methods: ['POST'])]
    public function merchantOrderConfirmed(Request         $request, ApiClientManager $apiClientManager, ApiConfigurator $apiConfigurator,
                                           ProcessService  $processService, SerializerInterface $serializer, CartApi $cartApi, OrderApi $orderApi,
                                           GatewayApi      $gatewayApi, TransactionApi $transactionApi, WPSService $WPSService,
                                           PreOrderService $preOrderService, OrderService $orderService, MerchantOrderService $merchantOrderService,
                                           CompanyService  $companyService, MailService $mailService, OfferService $offerService, ProductOfferApi $productOfferApi)
    {
        $this->configureApiConnection('webhook', $apiClientManager, $apiConfigurator);

        $event = 'merchant_order_confirmed';
        try {
            /** @var WebHookMerchantOrderRequest $hook */
            $hook = $serializer->deserialize($request->getContent(), WebHookMerchantOrderRequest::class, self::JSON_TYPE);
            /** @var MerchantOrder $merchantOrder */
            $merchantOrder = $hook->getData();

            $processService->processBeginFor($merchantOrder);

            //first, as the order is confirmed, we want to update the stock of the bought products
            $this->reIncrementStock($merchantOrder, $event, $offerService, $productOfferApi);

            //validate the content of this order before continuing
            $this->validateMerchantOrder($merchantOrder, $event);

            $this->logger->info(
                "IZBERG HOOK: " . $event,
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                    self::LOG_IZBERG_EVENT => $event,
                    "orderId" => $merchantOrder->getId()
                ])
            );

            //fetch the IZB cart
            $cart = $cartApi->getCart($merchantOrder->getOrder()->getCartId(), true);

            //validate the cart
            $this->validateCart($cart, $merchantOrder->getOrder()->getId(), "merchant_order_cancelled");

            /** get merchant order logs */
            $merchantOrderLogs = $orderApi->getMerchantOrderLogs($merchantOrder->getId());

            $lastMerchantOrderStatus = $merchantOrderLogs[0]->from_state;
            $lastMerchantOrderStatusUser = $merchantOrderLogs[0]->user_username;

            if ($cart->selected_payment_type === "prepayment") {

                $this->logger->info(
                    " + payment for this merchant order is prepayment mode",
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                        self::LOG_IZBERG_EVENT => $event,
                        "merchantOrderId" => $merchantOrder->getId(),
                        "cartId" => $cart->id,
                        "last_status" => $lastMerchantOrderStatus,
                        "last_status_user" => $lastMerchantOrderStatusUser
                    ])
                );

                $gateway = $gatewayApi->getGatewayByMerchantOrderId($merchantOrder->getId());
                $subTransactionId = $gateway->external_id;

                //compute amount to refund (all items from order
                $amountToRefund = $this->computeRefundAmount($merchantOrder);

                $prepaymentConditionPassed = false;

                // Credit cart payment
                if ($cart->selected_payment_method->code == "cb" && $lastMerchantOrderStatus === "60" && !empty($lastMerchantOrderStatusUser)) {
                    // Some items are refused but order are confirmed => cancel items refused
                    if ($amountToRefund > 0) {
                        $transactionApi->cancelSubTransaction($amountToRefund, $subTransactionId);
                        $this->logger->info(
                            "Cancelling sub transaction",
                            LogUtil::buildContext([
                                LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                                self::LOG_IZBERG_EVENT => $event,
                                "merchantOrderId" => $merchantOrder->getId(),
                                "subTransactionId" => $subTransactionId,
                                "amount" => $amountToRefund
                            ])
                        );
                    }

                    // Charge
                    $WPSService->charge($merchantOrder->getAmountVatIncluded(), $subTransactionId);

                    // Pay Gateway
                    $gatewayApi->payGateway($subTransactionId);
                    $this->logger->info(
                        "IZBERG HOOK received: " . $event . " => Pay gateway",
                        LogUtil::buildContext([
                            LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                            self::LOG_IZBERG_EVENT => $event,
                            "merchantOrderId" => $merchantOrder->getId(),
                            "subTransactionId" => $subTransactionId
                        ])
                    );

                    $prepaymentConditionPassed = true;
                }

                // Virement pre_payment
                if ($cart->selected_payment_method->code === "bankwire" && $lastMerchantOrderStatus === "60" && !empty($lastMerchantOrderStatusUser)) {

                    $this->logger->info(
                        "      + payment for this merchant order is prepayment/bankwire mode",
                        LogUtil::buildContext([
                            LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                            self::LOG_IZBERG_EVENT => $event,
                            "merchantOrderId" => $merchantOrder->getId(),
                            "cartId" => $cart->id,
                            "amountToRefund" => $amountToRefund
                        ])
                    );

                    // Some items are refused but order are confirmed => refund items refused

                    if ($amountToRefund > 0) {
                        $transactionApi->refund($amountToRefund, $subTransactionId);
                        $this->logger->info(
                            "      + Refunding cancelled orders",
                            LogUtil::buildContext([
                                LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                                self::LOG_IZBERG_EVENT => $event,
                                "merchantOrderId" => $merchantOrder->getId(),
                                "subTransactionId" => $subTransactionId,
                                "amountRefunded" => $amountToRefund
                            ])
                        );
                    }

                    // Pay Gateway
                    $gatewayApi->payGateway($subTransactionId);
                    $this->logger->info(
                        "          + Setting izberg gateway as paid Pay gateway",
                        LogUtil::buildContext([
                            EventNameEnum::IZBERG_WEB_HOOK,
                            self::LOG_IZBERG_EVENT => $event,
                            "merchantOrderId" => $merchantOrder->getId(),
                            "subTransactionId" => $subTransactionId
                        ])
                    );

                    $prepaymentConditionPassed = true;
                }

                if (!$prepaymentConditionPassed) {
                    $this->logger->alert(
                        "Webhook merchant order confirmed - merchant Order status is not status confirmed: 60",
                        LogUtil::buildContext([
                            EventNameEnum::IZBERG_WEB_HOOK,
                            self::LOG_IZBERG_EVENT => $event,
                            "merchantOrderId" => $merchantOrder->getId(),
                            "cartId" => $cart->id,
                            "last_status" => $lastMerchantOrderStatus,
                            "last_status_user" => $lastMerchantOrderStatusUser
                        ])
                    );
                }
            }

            try {
                $preOrderService->createPreorder($merchantOrder);
            } catch (\Throwable $e) {
                $this->logger->error("IZBERG HOOK: " . $event . " - " . $e->getMessage());
            }

            $param = [
                "orderNumber" => $merchantOrder->getOrder()->getIdNumber(),
                "vendor" => $merchantOrder->getMerchant()->getName(),
                "amount" => $merchantOrder->getAmount(),
                "amountVatIncluded" => $merchantOrder->getAmountVatIncluded(),
                "validatedItems" => $orderService->getItemsByStatus($merchantOrder, Item::ITEM_VALID_STATUS),
                "cancelledItems" => $orderService->getItemsByStatus($merchantOrder, Item::ITEM_CANCELLED_STATUS)
            ];

            $this->sendEmail($merchantOrder, MailService::BUYER_MERCHANT_ORDER_CONFIRMED, $param, $companyService, $mailService);

            $order = $orderService->fetchOrderById($merchantOrder->getOrder()->getId());

            $merchantOrderService->uploadMerchantOrderPdfToMerchant($merchantOrder->getId());

            $orderService->syncSingleOrder($order);

            $processService->processEndFor();

            $this->dispatchPayloadOrder($merchantOrder, $order);
        } catch (Exception $e) {
            $this->logger->error(
                "unknown error while processing izberg webHook: " . $event,
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                    self::LOG_IZBERG_EVENT => $event,
                    self::LOG_WH_CONTENT => $request->getContent(),
                    "message" => $e->getMessage()
                ])
            );
            return new JsonResponse(new \ArrayObject(), 500);
        }

        return $this->json(new \ArrayObject());
    }

    /**
     * @param MerchantOrder $merchantOrder
     *
     * @return integer the amount to refund
     */
    private function computeRefundAmount(MerchantOrder $merchantOrder)
    {
        $amount = 0;
        //we iterate on each item
        /** @var Item $item */
        foreach ($merchantOrder->getItems() as $item) {
            if ($item->getStatus() === "2000") {
                $amount += $item->getAmountVatIncluded() * $item->getQuantity();
            }
        }
        return $amount;
    }

    /**
     *
     * @param Request             $request
     * @param ApiClientManager    $apiClientManager
     * @param ApiConfigurator     $apiConfigurator
     * @param ProcessService      $processService
     * @param SerializerInterface $serializer
     * @return JsonResponse
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/hooks/order/merchant/processed', name: 'hooks.merchant.order.processed', methods: ['POST'])]
    public function merchantOrderProcessed(Request        $request, ApiClientManager $apiClientManager, ApiConfigurator $apiConfigurator,
                                           ProcessService $processService, SerializerInterface $serializer, MerchantOrderService $merchantOrderService,
                                           OrderService   $orderService, CompanyService $companyService, MailService $mailService)
    {
        $this->configureApiConnection('webhook', $apiClientManager, $apiConfigurator);

        try {
            /** @var WebHookMerchantOrderRequest $hook */
            $hook = $serializer->deserialize($request->getContent(), WebHookMerchantOrderRequest::class, self::JSON_TYPE);
            /** @var MerchantOrder $merchantOrder */
            $merchantOrder = $hook->getData();

            $processService->processBeginFor($merchantOrder);

            $event = "merchant_order_processed";

            $this->logger->info(
                "IZBERG HOOK received: " . $event,
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                    self::LOG_IZBERG_EVENT => $event,
                    "merchantOrderId" => $merchantOrder->getId(),
                ])
            );

            $merchantOrderService->uploadMerchantOrderPdfToMerchant($merchantOrder->getId());

            $param = [
                "orderNumber" => $merchantOrder->getOrder()->getIdNumber(),
                "vendor" => $merchantOrder->getMerchant()->getName(),
                "amount" => $merchantOrder->getAmount(),
                "amountVatIncluded" => $merchantOrder->getAmountVatIncluded(),
                "validatedItems" => $orderService->getItemsByStatus($merchantOrder, Item::ITEM_PROCESSED_STATUS),
                "cancelledItems" => $orderService->getItemsByStatus($merchantOrder, Item::ITEM_CANCELLED_STATUS)
            ];

            $this->sendEmail($merchantOrder, MailService::BUYER_MERCHANT_ORDER_PROCESSED, $param, $companyService, $mailService);

            $order = $orderService->fetchOrderById($merchantOrder->getOrder()->getId());

            $this->dispatchPayloadOrder($merchantOrder, $order);

            $processService->processEndFor();
        } catch (Exception $e) {
            $event = 'merchant_order_processed';
            $this->logger->error(
                "unknown error while processing izberg webHook: " . $event,
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                    self::LOG_IZBERG_EVENT => $event,
                    self::LOG_WH_CONTENT => $request->getContent(),
                    "message" => $e->getMessage()
                ])
            );
            return new JsonResponse(new \ArrayObject(), 500);
        }

        return $this->json(new \ArrayObject());
    }

    /**
     * @param Request             $request
     * @param ApiClientManager    $apiClientManager
     * @param ApiConfigurator     $apiConfigurator
     * @param ProcessService      $processService
     * @param OrderService        $orderService
     * @param SerializerInterface $serializer
     * @return JsonResponse
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/hooks/order/cancelled', name: 'hooks.order.cancelled', methods: ['POST'])]
    public function orderCancelled(Request        $request, ApiClientManager $apiClientManager, ApiConfigurator $apiConfigurator,
                                   ProcessService $processService, OrderService $orderService, SerializerInterface $serializer)
    {
        $this->configureApiConnection('webhook', $apiClientManager, $apiConfigurator);

        try {
            $hook = $serializer->deserialize($request->getContent(), WebHookOrderRequest::class, self::JSON_TYPE);

            /** @var Order $order */
            $order = $hook->getData();

            $processService->processBeginFor($order);

            $orderService->syncSingleOrder($order);

            $processService->processEndFor();

            $this->messageBus->dispatch(new OrderPayloadMessage($order));
        } catch (Exception $e) {
            $this->logger->error(
                "unknown error while processing izberg webHook: order_cancelled",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                    self::LOG_IZBERG_EVENT => "order_cancelled",
                    self::LOG_WH_CONTENT => $request->getContent(),
                    "message" => $e->getMessage()
                ])
            );
            return new JsonResponse(new \ArrayObject(), 500);
        }
        return $this->json(new \ArrayObject());
    }

    /**
     *
     * @param Request          $request
     * @param ApiClientManager $apiClientManager
     * @param ApiConfigurator  $apiConfigurator
     * @return JsonResponse
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/hooks/order/merchant/cancelled', name: 'hooks.merchant.order.cancelled', methods: ['POST'])]
    public function merchantOrderCancelled(
        Request              $request,
        ApiClientManager     $apiClientManager,
        ApiConfigurator      $apiConfigurator,
        ProcessService       $processService,
        SerializerInterface  $serializer,
        GatewayApi           $gatewayApi,
        CartApi              $cartApi,
        OrderApi             $orderApi,
        TransactionApi       $transactionApi,
        MerchantOrderService $merchantOrderService,
        CompanyService       $companyService,
        MailService          $mailService,
        OrderService         $orderService
    )
    {
        $this->configureApiConnection('webhook', $apiClientManager, $apiConfigurator);

        try {
            /** @var WebHookMerchantOrderRequest $hook */
            $hook = $serializer->deserialize($request->getContent(), WebHookMerchantOrderRequest::class, self::JSON_TYPE);
            /** @var MerchantOrder $merchantOrder */
            $merchantOrder = $hook->getData();

            $processService->processBeginFor($merchantOrder);

            $event = "merchant_order_cancelled";

            $this->validateMerchantOrder($merchantOrder, $event);

            $this->logger->info(
                "IZBERG HOOK received: " . $event,
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                    self::LOG_IZBERG_EVENT => $event,
                    "merchantOrderId" => $merchantOrder->getId()
                ])
            );

            $subTransactionId = null;
            $gateway = $gatewayApi->getGatewayByMerchantOrderId($merchantOrder->getId());
            if ($gateway != null) {
                $subTransactionId = $gateway->external_id;
            }

            //compute amount to refund
            $amount_to_refund = $this->computeRefundAmount($merchantOrder);

            $cart = $cartApi->getCart($merchantOrder->getOrder()->getCartId(), true, $merchantOrder->getCurrency()->getCode());

            //validate the cart
            $this->validateCart($cart, $merchantOrder->getOrder()->getId(), $event);

            // Get the last generated logs (the first element of the array)
            $cancelledLogs = $orderApi->getMerchantOrderLogs($merchantOrder->getId())[0];

            $param = [
                "orderNumber" => $merchantOrder->getOrder()->getIdNumber(),
                "vendor" => $merchantOrder->getMerchant()->getName(),
                "cancel_msg" => $merchantOrder->getCancelMsg()
            ];

            $selectedPaymentType = $cart->selected_payment_type;
            $selectedPaymentMethod = $cart->selected_payment_method->code ?? '';

            //////////////////////////////////////////////
            /// Manual cancellation
            /////////////////////////////////////////////

            if ($cancelledLogs->user_id != 0 && $cancelledLogs->from_state == '60') {

                $this->logger->info(
                    " + manual cancellation detected",
                    LogUtil::buildContext([
                        EventNameEnum::IZBERG_WEB_HOOK,
                        self::LOG_IZBERG_EVENT => $event,
                        "orderId" => $merchantOrder->getId(),
                        "merchantId" => $merchantOrder->getMerchant()->getId(),
                        "paymentType" => $selectedPaymentType,
                        "paymentMethod" => $selectedPaymentMethod
                    ])
                );

                // Credit cart payment => cancelSubTransaction
                if ($selectedPaymentType == "prepayment" && $selectedPaymentMethod == "cb") {
                    if ($subTransactionId != null) {

                        $this->logger->info(
                            "  + cancelling sub transaction",
                            LogUtil::buildContext([
                                LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                                self::LOG_IZBERG_EVENT => $event,
                                "merchantOrderId" => $merchantOrder->getId(),
                                "subTransactionId" => $subTransactionId,
                                "amount" => $amount_to_refund
                            ])
                        );

                        $transactionApi->cancelSubTransaction($amount_to_refund, $subTransactionId);
                    }
                    $this->sendEmail($merchantOrder, MailService::ORDER_REFUSED_BY_VENDOR_PRE_CREDIT_CARD_TO_BUYER, $param, $companyService, $mailService);
                }

                // bank wire prepaid
                if ($selectedPaymentType == "prepayment" && $selectedPaymentMethod == "bankwire") {
                    if ($subTransactionId != null) {

                        //refunding
                        $this->logger->info(
                            " + bank wire prepaid case: Performing refound ",
                            LogUtil::buildContext([
                                LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                                self::LOG_IZBERG_EVENT => $event,
                                "merchantOrderId" => $merchantOrder->getId(),
                                "subTransactionId" => $subTransactionId,
                                "amount" => $amount_to_refund
                            ])
                        );

                        $transactionApi->refund($amount_to_refund, $subTransactionId);


                    }
                    $this->sendEmail($merchantOrder, MailService::ORDER_REFUSED_BY_VENDOR_PRE_BANK_TRANSFER_TO_BUYER, $param, $companyService, $mailService);
                }

                // term payment prepaid
                if ($selectedPaymentType == "term_payment") {
                    $this->sendEmail($merchantOrder, MailService::ORDER_REFUSED_BY_VENDOR_TERM_BANK_TRANSFER_TO_BUYER, $param, $companyService, $mailService);
                }
            }


            /////////////////////////////////////////////////
            /// Automatic cancelled
            ////////////////////////////////////////////////

            // Old status = authorize
            if ($cancelledLogs->from_state == '60' && $cancelledLogs->user_id === 0) {

                $this->logger->info(
                    " + automatic cancellation detected",
                    LogUtil::buildContext([
                        EventNameEnum::IZBERG_WEB_HOOK,
                        self::LOG_IZBERG_EVENT => $event,
                        "orderId" => $merchantOrder->getId(),
                        "merchantId" => $merchantOrder->getMerchant()->getId(),
                        "paymentType" => $selectedPaymentType,
                        "paymentMethod" => $selectedPaymentMethod
                    ])
                );

                // Credit cart payment => cancelSubTransaction
                if ($selectedPaymentType == "prepayment" && $selectedPaymentMethod == "cb" && $cancelledLogs->user_id == 0) {
                    if ($subTransactionId != null) {
                        $transactionApi->cancelSubTransaction($amount_to_refund, $subTransactionId);
                        $this->logger->info(
                            "IZBERG HOOK received: " . $event . " => Sub Transaction cancel",
                            LogUtil::buildContext([
                                EventNameEnum::IZBERG_WEB_HOOK,
                                self::LOG_IZBERG_EVENT => $event,
                                "merchantOrderId" => $merchantOrder->getId(),
                                "subTransactionId" => $subTransactionId
                            ])
                        );
                    }
                    $this->sendEmail($merchantOrder, MailService::AUTOMATIC_ORDER_REFUSED_PREPAYMENT_CB, $param, $companyService, $mailService);
                }

                // Virement pre_payment
                if ($selectedPaymentType == "prepayment" && $selectedPaymentMethod == "bankwire" && $cancelledLogs->user_id == 0) {
                    if ($subTransactionId != null) {
                        $transactionApi->refund($amount_to_refund, $subTransactionId);
                        $this->logger->info(
                            "IZBERG HOOK received: " . $event . " => Sub Transaction refund",
                            LogUtil::buildContext([
                                LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                                self::LOG_IZBERG_EVENT => $event,
                                "merchantOrderId" => $merchantOrder->getId(),
                                "subTransactionId" => $subTransactionId
                            ])
                        );
                    }
                    $this->sendEmail($merchantOrder, MailService::AUTOMATIC_ORDER_REFUSED_PREPAYMENT_BANK_TRANSFER, $param, $companyService, $mailService);
                }

                if ($selectedPaymentType == "term_payment") {
                    $this->sendEmail($merchantOrder, MailService::AUTOMATIC_ORDER_REFUSED_TERM_PAYMENT, $param, $companyService, $mailService);
                }

            } // Old status = initial
            elseif ($cancelledLogs->from_state == '0') {
                // If pre payment bank transfer ans user = none
                if ($selectedPaymentType == "prepayment" && $selectedPaymentMethod == "bankwire" && $cancelledLogs->user_id == 0) {
                    $this->sendEmail($merchantOrder, MailService::AUTOMATIC_ORDER_REFUSED_NO_PAYMENT_RECEIVED, $param, $companyService, $mailService);
                }
            }
            $merchantOrderService->uploadMerchantOrderPdfToMerchant($merchantOrder->getId());

            $order = $orderService->fetchOrderById($merchantOrder->getOrder()->getId());

            $this->dispatchPayloadOrder($merchantOrder, $order);

            $processService->processEndFor();

        } catch (Exception $e) {
            $this->logger->error(
                "unknown error while processing izberg webHook: merchant_order_cancelled",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                    self::LOG_IZBERG_EVENT => "merchant_order_cancelled",
                    self::LOG_WH_CONTENT => $request->getContent(),
                    "message" => $e->getMessage()
                ])
            );
            return new JsonResponse(new \ArrayObject(), 500);
        }
        return $this->json(new \ArrayObject());
    }

    private function dispatchPayloadOrder(MerchantOrder $merchantOrder, Order $order): void
    {
        $order->setMerchantOrders(new ArrayCollection([$merchantOrder]));
        $this->messageBus->dispatch(new OrderPayloadMessage($order));
    }


    /**
     * @param $cart
     * @param $orderId
     * @param $event
     *
     * @throws WebHookException
     */
    private function validateCart($cart, $orderId, $event)
    {
        if ($cart == null) {
            $this->logger->error(
                "    * unable to load cart with order id " . $orderId . " from database because it doesn't exist",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                    self::LOG_IZBERG_EVENT => $event,
                    "orderId" => $orderId
                ])
            );

            throw new WebHookException("unable to load cart with order id " . $orderId . " from database because it doesn't exist");
        }
    }

    /**
     * @param            $merchantOrder
     * @param            $event
     */
    private function validateMerchantOrder($merchantOrder, $event)
    {
        if ($merchantOrder == null) {
            $this->logger->error(
                "    * unable to process received merchant order because it is null",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                    self::LOG_IZBERG_EVENT => $event,
                ])
            );

            throw new WebHookException("unable to process received merchant order because it doesn't exist (null)");
        }
        if ($merchantOrder->getOrder() === null) {
            $this->logger->error(
                "    * unable to process  merchant order " . $merchantOrder->getId() . " because it has no parent order",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                    self::LOG_IZBERG_EVENT => $event,
                    "merchantOrderId" => $merchantOrder->getId()
                ])
            );
            throw new WebHookException("unable to process  merchant order " . $merchantOrder->getId() . " because it has no parent order");
        }

        if ($merchantOrder->getOrder()->getId() === null) {
            $this->logger->error(
                "    * unable to process  merchant order " . $merchantOrder->getId() . " because parent order has no id",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                    self::LOG_IZBERG_EVENT => $event,
                    "merchantOrderId" => $merchantOrder->getId()
                ])
            );

            throw new WebHookException("unable to process  merchant order " . $merchantOrder->getId() . " because parent order has no id");
        }

        if ($merchantOrder->getItems() === null || count($merchantOrder->getItems()) === 0) {
            $this->logger->error(
                "    * unable to process  merchant order " . $merchantOrder->getId() . " because parent order has no id",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                    self::LOG_IZBERG_EVENT => $event,
                    "merchantOrderId" => $merchantOrder->getId()
                ])
            );
            throw new WebHookException("unable to process  merchant order " . $merchantOrder->getId() . " because it hasn't item");
        }
    }


    /**
     * @param MerchantOrder  $merchantOrder
     * @param string         $template
     * @param array          $param
     * @param CompanyService $companyService
     * @param MailService    $mailService
     *
     * @throws \AppBundle\Exception\MailException
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    private function sendEmail(
        MerchantOrder  $merchantOrder,
        string         $template,
                       $param,
        CompanyService $companyService,
        MailService    $mailService
    )
    {

        $this->logger->info("WebHookController_sendEmail - Template: " . $template);

        if(in_array($template, [MailService::BUYER_ORDER_CONFIRMATION, MailService::BUYER_MERCHANT_ORDER_PROCESSED, MailService::BUYER_MERCHANT_ORDER_CONFIRMED,
            MailService::ORDER_REFUSED_BY_VENDOR_PRE_BANK_TRANSFER_TO_BUYER, MailService::ORDER_REFUSED_BY_VENDOR_PRE_CREDIT_CARD_TO_BUYER, MailService::ORDER_REFUSED_BY_VENDOR_TERM_BANK_TRANSFER_TO_BUYER,
            MailService::AUTOMATIC_ORDER_REFUSED_PREPAYMENT_CB, MailService::AUTOMATIC_ORDER_REFUSED_TERM_PAYMENT, MailService::AUTOMATIC_ORDER_REFUSED_PREPAYMENT_BANK_TRANSFER, MailService::AUTOMATIC_ORDER_REFUSED_NO_PAYMENT_RECEIVED])) {
            return $this->sendEmailOneToOne($merchantOrder, $template, $param, $companyService, $mailService);
        }

        $notifiedUsers = [];
        /** @var Company $company */
        $company = $companyService->findByIzbergUserId($merchantOrder->getUser()->getId());
        $merchantOrderSite = $this->getOrderSiteFromIzbergOrderId($merchantOrder->getOrder()->getId());
        if (!$merchantOrderSite) {
            $msg = sprintf("unable to notify user from this company: no site found for this merchant order %s", $merchantOrder->getId());
            $this->logger->error($msg);
            throw new RuntimeException($msg);
        }

        if ($company !== null) {
            /** @var User $user */
            foreach ($company->getUsers() as $user) {
                if ($user !== null && $user->isEnabled() && !in_array($user->getEmail(), $notifiedUsers)) {
                    $userSitesIds = array_map(function (Site $site) {
                        return $site->getId();
                    }, $user->getSites()->getValues());


                    $this->logger->info(
                        sprintf('ORDER EMAIL NOTIFICATION FOR ORDER SITE #%s | user %s -> sites list %s', $merchantOrderSite->getId(), $user->getId(), implode(' | ', $userSitesIds))
                    );


                    if ($user->isBuyerAdmin() || in_array($merchantOrderSite->getId(), $userSitesIds)) {
                        $notifiedUsers [] = $user->getEmail();
                        $param[MailService::FIRST_NAME_VAR] = $user->getFirstname();
                        $param[MailService::LAST_NAME_VAR] = $user->getLastname();
                        $mailService->sendEmailMessage($template,
                            $user->getLocale(),
                            $user->getEmail(),
                            $param,
                            $_ENV["FOSUSER_FROM_EMAIL"],
                            $_ENV["FOSUSER_FROM_NAME"]
                        );
                    }
                }
            }
        } else {
            $this->logger->error(
                "unable to notify user from this company: no company found for this izbergUserId",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_GENERAL_ERROR,
                    WPSService::LOG_STATUS_CODE => "NOTIFICATION_ERROR",
                    "izbergUserId" => $merchantOrder->getUser()->getId()
                ])
            );
        }
    }

    /**
     *
     * @param Request             $request
     * @param ApiClientManager    $apiClientManager
     * @param ApiConfigurator     $apiConfigurator
     *
     * @param ProcessService      $processService
     * @param SerializerInterface $serializer
     * @param CompanyService      $companyService
     *
     * @param MailService         $mailService
     * @param ThreadParentMessageRepository $threadParentMessageRepository
     * @return JsonResponse
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/hooks/order/message/response', name: 'hooks.order.message.response', methods: ['POST'])]
    public function newMessageResponse(Request        $request, ApiClientManager $apiClientManager, ApiConfigurator $apiConfigurator,
                                       ProcessService $processService, SerializerInterface $serializer, CompanyService $companyService, MailService $mailService, ThreadParentMessageRepository $threadParentMessageRepository)
    {
        $this->configureApiConnection('webhook', $apiClientManager, $apiConfigurator);

        try {
            /** @var WebHookMessageRequest $hook */
            $hook = $serializer->deserialize($request->getContent(), WebHookMessageRequest::class, self::JSON_TYPE);
            $message = $hook->getData();

            $processService->processBeginFor($message);

            $this->logger->info(
                "IZBERG HOOK received: new_response",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                    self::LOG_IZBERG_EVENT => 'new_response'
                ])
            );

            // Si message venant du vendeur
            if (strpos($message->getFromResourceUri(), '/merchant/') !== false) {
                $this->logger->info("IZBERG HOOK received: new_response from merchant");

                if ($message->getToResourceUri() !== null) {
                    // Récupération de la société destinataire du message
                    $companyId = $message->getToResourceUri();
                    $companyId = substr($companyId, 0, -1);
                    $companyId = substr($companyId, strrpos($companyId, '/') + 1);
                    $company = $companyService->findByIzbergUserId($companyId);

                    $rootMessageId = $message->getRootMsg();
                    $this->logger->info("IZBERG HOOK received: new_response rootMessageId = $rootMessageId");
                    $rootMessageId = substr($rootMessageId, 0, -1);
                    $rootMessageId = substr($rootMessageId, strrpos($rootMessageId, '/') + 1);

                    if ($company !== null) {
                        $usersToNotified = $company->getUsers();

                        if(!empty($rootMessageId)) {
                            $threadParentMessage = $threadParentMessageRepository->findOneByIzbergId($rootMessageId);
                            if(!empty($threadParentMessage)) {
                                $em = $this->doctrine->getManager();
                                /**
                                 * @var UserRepository $userRepository
                                 */
                                $userRepository = $em->getRepository(User::class);
                                $userEntity = $userRepository->find($threadParentMessage->getFromEmail());
                                $usersToNotified = [$userEntity];
                            }
                        }

                        $notifiedUsers = [];
                        /** @var User $user */
                        foreach ($usersToNotified as $user) {
                            if ($user !== null && !in_array($user->getEmail(), $notifiedUsers)) {
                                $notifiedUsers [] = $user->getEmail();
                                if ($user->isEnabled()) {
                                    $url = $this->generateUrl('izberg.ticket.edit', ["id" => $message->getId()], UrlGeneratorInterface::ABSOLUTE_URL);
                                    $mailService->sendEmailMessage(MailService::NEW_MERCHANT_RESPONSE,
                                        $user->getLocale(),
                                        $user->getEmail(),
                                        array(
                                            MailService::FIRST_NAME_VAR => $user->getFirstname(),
                                            MailService::LAST_NAME_VAR => $user->getLastname(),
                                            'vendor' => $message->getFromDisplayName(),
                                            "subject" => $message->getSubject(),
                                            'ticketNumber' => strval($message->getId()),
                                            'content' => $message->getBodyRaw(),
                                            'url' => $url),
                                        $_ENV["FOSUSER_FROM_EMAIL"],
                                        $_ENV["FOSUSER_FROM_NAME"]
                                    );
                                }
                            }
                        }
                    } else {
                        $this->logger->error(
                            "unable to notify user from this company: no company found for this izbergUserId",
                            LogUtil::buildContext([
                                LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_GENERAL_ERROR,
                                WPSService::LOG_STATUS_CODE => "NOTIFICATION_ERROR",
                                "izbergUserId" => $companyId
                            ])
                        );
                    }
                }
            }

            $processService->processEndFor();
        } catch (Exception $e) {
            $this->logger->error(
                "unknown error while processing izberg webHook: new_message",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                    self::LOG_IZBERG_EVENT => "merchant_order_cancelled",
                    self::LOG_WH_CONTENT => $request->getContent(),
                    "message" => $e->getMessage()
                ])
            );
            return new JsonResponse(new \ArrayObject(), 500);
        }

        return $this->json(new \ArrayObject());
    }

    /**
     *
     * @param Request              $request
     * @param ApiClientManager     $apiClientManager
     * @param ApiConfigurator      $apiConfigurator
     *
     * @param ProcessService       $processService
     * @param OrderFactory         $orderFactory
     * @param OrderService         $orderService
     * @param SerializerInterface  $serializer
     * @param MerchantOrderService $merchantOrderService
     * @param CompanyRepository    $companyRepository
     * @return JsonResponse
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/hooks/order/merchant/authorized', name: 'hooks.merchant.order.authorized', methods: ['POST'])]
    public function merchantOrderAuthorized(
        Request              $request,
        ApiClientManager     $apiClientManager,
        ApiConfigurator      $apiConfigurator,
        ProcessService       $processService,
        OrderFactory         $orderFactory,
        OrderService         $orderService,
        SerializerInterface  $serializer,
        MerchantOrderService $merchantOrderService,
        CompanyRepository    $companyRepository
    )
    {
        $this->logger->info(
            "IZBERG HOOK: Starting webhook merchantOrderAuthorized",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK
            ])
        );
        $this->configureApiConnection('webhook', $apiClientManager, $apiConfigurator);

        $event = 'merchant_order_authorized';
        try {

            /** @var WebHookMerchantOrderRequest $hook */
            $hook = $serializer->deserialize(
                $request->getContent(),
                WebHookMerchantOrderRequest::class,
                self::JSON_TYPE
            );

            // for every order_items set delivery dates
            /** @var MerchantOrder $izbergMerchantOrder */
            $izbergMerchantOrder = $hook->getData();
            $company = $companyRepository->findCompanyByIzbergUserId($izbergMerchantOrder->getUser()->getId());

            $merchantOrder = $orderFactory->buildMerchantOrderFromIzbergMerchantOrder($izbergMerchantOrder, $company, true);
            $processService->processBeginFor($merchantOrder);

            $orderService->updateOrderItemsDeliveryDates(array_values($merchantOrder->getItems()));

            $this->logger->info(
                "IZBERG HOOK: " . $event,
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                    self::LOG_IZBERG_EVENT => $event,
                    "orderId" => $merchantOrder->getId()
                ])
            );

            $merchantOrderService->uploadMerchantOrderPdfToMerchant($merchantOrder->getId());

            $processService->processEndFor();

        } catch (Exception $e) {
            $this->logger->error(
                "unknown error while processing izberg webHook: " . $event,
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                    self::LOG_IZBERG_EVENT => $event,
                    self::LOG_WH_CONTENT => $request->getContent(),
                    "message" => $e->getMessage()
                ])
            );
            return new JsonResponse(new \ArrayObject(), 500);
        }

        return $this->json(new \ArrayObject());
    }

    /**
     * @param Request              $request
     * @param ApiClientManager     $apiClientManager
     * @param ApiConfigurator      $apiConfigurator
     *
     * @param PreOrderService      $preOrderService
     * @param MerchantOrderService $merchantOrderService
     * @param SerializerInterface  $serializer
     * @return JsonResponse
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/hooks/merchant-order/refund', name: 'hooks.refund', methods: ['POST'])]
    public function merchantOrderRefundAction(Request         $request, ApiClientManager $apiClientManager, ApiConfigurator $apiConfigurator,
                                              PreOrderService $preOrderService, MerchantOrderService $merchantOrderService, SerializerInterface $serializer)
    {
        $this->configureApiConnection('webhook', $apiClientManager, $apiConfigurator);
        $event = 'merchant_order_refund';

        try {
            /** @var WebHookMerchantOrderRefundRequest $hook */
            $hook = $serializer->deserialize(
                $request->getContent(),
                WebHookMerchantOrderRefundRequest::class,
                self::JSON_TYPE
            );

            $merchantOrderRefund = $hook->getData();

            $merchantOrderService->uploadMerchantOrderPdfToMerchant($merchantOrderRefund->getMerchantOrder()->getId());
            $preOrderService->cancelPreOrderProducts($merchantOrderRefund->getMerchantOrder()->getId(), $merchantOrderRefund->getOrderItems());

        } catch (Exception $e) {
            $this->logger->error(
                "unknown error while processing izberg webHook: " . $event,
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                    self::LOG_IZBERG_EVENT => $event,
                    self::LOG_WH_CONTENT => $request->getContent(),
                    "message" => $e->getMessage()
                ])
            );
            return new JsonResponse(new \ArrayObject(), 500);
        }

        return $this->json(new \ArrayObject());
    }

    /**
     *
     * @param Request             $request
     * @param MailService         $mailService
     * @param ProcessService      $processService
     *
     * @param SerializerInterface $serializer
     * @return JsonResponse
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/hooks/order/authorized', name: 'hooks.order.authorized', methods: ['POST'])]
    public function orderAuthorized(Request $request, MailService $mailService, ProcessService $processService, SerializerInterface $serializer)
    {
        $payment_nb_hours_before_refund = $_ENV['PAYMENT_NB_HOURS_BEFORE_REFUND'];

        $event = 'order_authorized';
        try {

            /** @var WebHookOrderRequest $hook */
            $hook = $serializer->deserialize($request->getContent(), WebHookOrderRequest::class, self::JSON_TYPE);
            $order = $hook->getData();
            $processService->processBeginFor($order);

            //notify buyer
            $this->sendEmailToBuyers($order, MailService::BUYER_ORDER_CONFIRMATION, [
                'orderNumber' => $order->getIdNumber(),
                'paymentNbHoursBeforeRefund' => $payment_nb_hours_before_refund
            ], $event, $mailService);

            $processService->processEndFor();

            $this->messageBus->dispatch(new OrderPayloadMessage($order));
        } catch (Exception $e) {
            $this->logger->error(
                "unknown error while processing izberg webHook: " . $event,
                LogUtil::buildContext([
                    EventNameEnum::IZBERG_WEB_HOOK,
                    self::LOG_IZBERG_EVENT => $event,
                    self::LOG_WH_CONTENT => $request->getContent(),
                    "message" => $e->getMessage()
                ])
            );
            return new JsonResponse(new \ArrayObject(), 500);
        }

        return $this->json(new \ArrayObject());
    }

    /**
     * Use to notify a buyer
     *
     * @param Order       $order
     * @param string      $template the name of the email templates
     * @param array       $param    the list of the params to be used to populate the template
     * @param             $logEvent
     * @param MailService $mailService
     */
    private function sendEmailToBuyers(Order $order, string $template, $param, $logEvent, MailService $mailService)
    {
        $mailService->sendEmailToBuyersOrder($order, $template, $param, $logEvent);
    }

    private function sendEmailOneToOne(
        MerchantOrder  $merchantOrder,
        string         $template,
                       $param,
        CompanyService $companyService,
        MailService    $mailService
    )
    {
        $this->logger->info(sprintf("notify_one_to_one_2 start sendEmailOneToOne , template: %s", $template));
        $order = $merchantOrder->getOrder();
        if (!$order) {
            $msg = sprintf("unable to identify order");
            $this->logger->error($msg);
            throw new RuntimeException($msg);
        }

        $this->logger->info(sprintf("notify_one_to_one_2 sendEmailOneToOne for order %s", $order->getId()));

        $cartEntity = $mailService->getCartByOrder($order);
        if (!$cartEntity) {
            $msg = sprintf("notify_one_to_one_2 unable to identify cart from the order: %s", $order->getId());
            $this->logger->error($msg);
            throw new RuntimeException($msg);
        }

        $createdUser = $cartEntity->getCreatedUser();
        if (!$createdUser) {
            $msg = sprintf("notify_one_to_one_2 unable to identify createdUser from the cart: %s", $cartEntity->getId());
            $this->logger->error($msg);
            throw new RuntimeException($msg);
        }

        $this->logger->info(sprintf('notify_one_to_one_2 ORDER EMAIL NOTIFICATION FOR user %s ', $createdUser->getId()));
        $this->logger->info(sprintf('notify_one_to_one_2 sendEmailMessage to %s', $createdUser->getEmail()));

        $param[MailService::FIRST_NAME_VAR] = $createdUser->getFirstname();
        $param[MailService::LAST_NAME_VAR] = $createdUser->getLastname();
        $mailService->sendEmailMessage($template,
            $createdUser->getLocale(),
            $createdUser->getEmail(),
            $param,
            $_ENV["FOSUSER_FROM_EMAIL"],
            $_ENV["FOSUSER_FROM_NAME"]
        );

        return true;
    }

    #[\Symfony\Component\Routing\Attribute\Route(path: '/hooks/shipment/new_parcel_created', name: 'hooks.shipment.new_parcel_created', methods: ['POST'])]
    public function newParcelCreatedAction(Request $request, SerializerInterface $serializer)
    {
        $event = 'new_parcel_created';
        try {
            /** @var WebHookShipmentRequest $hook */
            $hook = $serializer->deserialize($request->getContent(), WebHookShipmentRequest::class, self::JSON_TYPE);
            $shipment = $hook->getData();


        } catch (\Throwable $e) {
            $this->logger->error(
                "unknown error while processing izberg webHook: " . $event,
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::IZBERG_WEB_HOOK,
                    self::LOG_IZBERG_EVENT => $event,
                    self::LOG_WH_CONTENT => $request->getContent(),
                    "message" => $e->getMessage()
                ])
            );
            return new JsonResponse(new \ArrayObject(), 500);
        }

        return $this->json(new \ArrayObject());
    }
}
