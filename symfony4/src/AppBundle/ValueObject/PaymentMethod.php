<?php

declare(strict_types=1);

namespace AppBundle\ValueObject;

use Api\Core\Exception\InvalidArgumentException;
use Open\FrontBundle\Form\PaymentModeSelectForm;

final class PaymentMethod implements \Stringable
{
    private const SUPPORTED_METHODS = [
        PaymentModeSelectForm::PAYMENT_PRE_CARD,
        PaymentModeSelectForm::PAYMENT_PRE_WIRE,
        PaymentModeSelectForm::PAYMENT_TERM,
    ];

    private string $method;

    private function __construct(string $method)
    {
        if (!in_array($method, self::SUPPORTED_METHODS)) {
            throw new InvalidArgumentException(
                sprintf(
                    'The %s payment method is not supported : %s',
                    $method,
                    join(', ', self::SUPPORTED_METHODS)
                )
            );
        }

        $this->method = $method;
    }

    public static function createToLabel(string $label): self
    {
        return new self($label);
    }

    /**
     * @inheritDoc
     */
    public function __toString(): string
    {
        return $this->method;
    }
}
