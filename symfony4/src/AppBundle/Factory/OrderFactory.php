<?php

namespace AppBundle\Factory;

use AppBundle\Entity\Company;
use AppBundle\Model\Offer;
use AppBundle\Model\Order\Order;
use AppBundle\Model\Order\OrderItem;
use AppBundle\Services\OfferService;
use AppBundle\Services\SpecificPriceService;
use Open\IzbergBundle\Api\OrderApi;
use Open\IzbergBundle\Model\Item;
use Open\IzbergBundle\Model\MerchantOrder;
use Open\IzbergBundle\Model\Order as IzbergOrder;
use Open\IzbergBundle\Model\OrderMerchant;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use Psr\Log\LoggerInterface;

final class OrderFactory implements LoggerAwareInterface, OrderFactoryInterface
{
    use LoggerAwareTrait;

    public function __construct(
        private OrderApi $orderApi,
        private OfferService $offerService,
        private SpecificPriceService $specificPriceService
    ) {}

    public function buildOrderFromOrderId(int $orderId): Order
    {
        $izbergOrder = $this->orderApi->fetchOrder($orderId);
        return $this->buildOrder($izbergOrder);
    }

    public function buildOrder(IzbergOrder $izbergOrder): Order
    {
        return (new Order())
            ->setIzbergId($izbergOrder->getId())
            ->setCurrency($izbergOrder->getCurrency())
            ->setMerchantOrders(
                $merchantOrders = $this->buildOrderMerchantOrders($izbergOrder->getMerchantOrders()->toArray())
            )
            ->setItems($this->buildOrderItems($merchantOrders))
            ->setAmountVatIncluded($izbergOrder->getAmountVatIncluded())
            ->setIdNumber($izbergOrder->getIdNumber());
    }

    private function buildOrderMerchantOrders(array $merchantOrders)
    {
        return array_map(
            function (MerchantOrder $merchantOrder) {
                $merchantOrder = $this->orderApi->fetchMerchantsOrderByOrderId($merchantOrder->getId());
                return $this->buildMerchantOrder($merchantOrder);
            },
            $merchantOrders
        );
    }

    private function buildMerchantOrder(OrderMerchant $izbergMerchantOrder): \AppBundle\Model\Order\MerchantOrder
    {
        $offers = $this->offerService->findOffersByIds(
            array_map(
                function (\Open\IzbergBundle\Model\OrderItem $izbergOrderItem) {
                    return $izbergOrderItem->getOfferId();
                },
                $izbergMerchantOrder->getItems()->toArray()
            )
        );

        $izbergMerchant = $izbergMerchantOrder->getMerchant();

        return (new \AppBundle\Model\Order\MerchantOrder())
            ->setId($izbergMerchantOrder->getId())
            ->setAmountVatIncluded($izbergMerchantOrder->getAmountVatIncluded())
            ->setItems(array_map(
                function (\Open\IzbergBundle\Model\OrderItem $izbergOrderItem) use ($offers) {
                    return $this->buildOrderItem($izbergOrderItem, $offers);
                },
                $izbergMerchantOrder->getItems()->toArray()
            ))
            ->setMerchantId($izbergMerchant ? $izbergMerchant->getId() : null);
    }

    private function buildOrderItem(\Open\IzbergBundle\Model\OrderItem $izbergOrderItem, array $offers): OrderItem
    {
        $orderItem = new OrderItem();
        $orderItem->setId($izbergOrderItem->getId());
        $orderItem->setOfferId($izbergOrderItem->getOfferId());
        $orderItem->setOfferExternalId($izbergOrderItem->getOfferExternalId());
        $orderItem->setQuantity($izbergOrderItem->getQuantity());
        $orderItem->setPrice($izbergOrderItem->getPrice());
        $orderItem->setCurrency($izbergOrderItem->getCurrency());

        $this->buildOrderItemFromOffer($orderItem, $offers);

        return $orderItem;
    }

    private function buildOrderItemFromItem(Item $izbergOrderItem, array $offers): OrderItem
    {
        $orderItem = new OrderItem();
        $orderItem->setId($izbergOrderItem->getId());
        $orderItem->setOfferId($izbergOrderItem->getOfferId());
        $orderItem->setQuantity($izbergOrderItem->getQuantity());
        $orderItem->setCurrency($izbergOrderItem->getCurrency());

        $this->buildOrderItemFromOffer($orderItem, $offers);

        return $orderItem;

    }

    private function buildOrderItemFromOffer(OrderItem $orderItem, array $offers): OrderItem
    {
        $offer = $offers[$orderItem->getOfferId()] ?? null;

        if (!$offer instanceof Offer) {
            $this->logger->error(
                'order item offer not found when trying to build detailed order item',
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::OFFER_NOT_FOUND,
                    'offerId' => $orderItem->getOfferId(),
                    'offersFound' => array_map(
                        function (Offer $offer) {
                            return [
                                'id' => $offer->getIzbergReference(),
                                'trueDeliveryTime' => $offer->getTrueDeliveryTime(),
                                'deliveryTime' => $offer->getDeliveryTime()
                            ];
                        },
                        $offers
                    )
                ])
            );
            return $orderItem;
        }

        $orderItem->setDeliveryTime($offer->getDeliveryTime());
        $orderItem->setTrueDeliveryTime($offer->getTrueDeliveryTime());

        return $orderItem;
    }

    private function buildOrderItems(array $merchantOrders)
    {
        $merchantOrderItems = array_map(
            function (\AppBundle\Model\Order\MerchantOrder $merchantOrder) {
                return $merchantOrder->getItems();
            },
            $merchantOrders
        );

        return array_merge([], ...array_values($merchantOrderItems));
    }

    public function buildMerchantOrderFromIzbergMerchantOrder(
        MerchantOrder $izbergMerchantOrder,
        Company $company,
        bool $shippingDecorated = false
    ): \AppBundle\Model\Order\MerchantOrder
    {
        // SHIPPING-INVOICE do not include shipping upela offer
        $orderItems = array_filter(
            $izbergMerchantOrder->getItems()->toArray(),
            function (Item $item) {
                return !(preg_match('/^shipment-[0-9]+$/', $item->getOfferExternalId()));
            }
        );

        $offers = $this->offerService->findOffersByIds(
            array_map(
                function (Item $izbergOrderItem) {
                    return $izbergOrderItem->getOfferId();
                },
                $orderItems
            )
        );

        $offers = $this->specificPriceService->bulkUpdateOfferSpecificPrices($company, $offers);

        $izbergMerchant = $izbergMerchantOrder->getMerchant();

        $merchantOrder = (new \AppBundle\Model\Order\MerchantOrder())
            ->setId($izbergMerchantOrder->getId())
            ->setAmountVatIncluded($izbergMerchantOrder->getAmountVatIncluded())
            ->setItems(array_map(
                function (Item $izbergOrderItem) use ($offers) {
                    return $this->buildOrderItemFromItem($izbergOrderItem, $offers);
                },
                $orderItems
            ))
            ->setMerchantId($izbergMerchant ? $izbergMerchant->getId() : null);

        if ($shippingDecorated) {
            $this->decorateWithShippingData($merchantOrder);
        }

        return $merchantOrder;
    }

    private function decorateWithShippingData(\AppBundle\Model\Order\MerchantOrder $merchantOrder)
    {
        $orderItemsDeliveryDates = [];

        $shippingOptions = $this->orderApi->fetchMerchantOrderShippingOptions($merchantOrder->getId());

        foreach ($shippingOptions as $shippingOption) {
            $izbergShippingOffer = $shippingOption->options->offer ?? null;

            if (!$izbergShippingOffer) {
                continue;
            }

            $deliveryDate = $izbergShippingOffer->deliveryDate ?? null;
            $shipmentDate = $izbergShippingOffer->shipmentDate ?? null;

            $orderItemsDeliveryDates = array_reduce(
                $shippingOption->order_items,
                function (array $orderItems, \stdClass $orderItem) use ($deliveryDate, $shipmentDate) {
                    $orderItems[$orderItem->id] = [
                        $deliveryDate ? new \DateTimeImmutable($deliveryDate) : null,
                        $shipmentDate ? new \DateTimeImmutable($shipmentDate) : null,
                    ];
                    return $orderItems;
                },
                $orderItemsDeliveryDates
            );
        }

        $merchantOrderItems = array_map(
            function (OrderItem $merchantOrderItem) use ($orderItemsDeliveryDates) {
                [$expectedDeliveryDate, $expectedShippingDate] = $orderItemsDeliveryDates[$merchantOrderItem->getId()] ?? [null, null];
                $merchantOrderItem->setExpectedDeliveryDate($expectedDeliveryDate);
                $merchantOrderItem->setExpectedShippingDate($expectedShippingDate);
                return $merchantOrderItem;
            },
            $merchantOrder->getItems()
        );

        // update the merchant order items delivery dates
        $merchantOrder->setItems($merchantOrderItems);
    }
}
