<?php

namespace AppBundle\Factory;

use AppBundle\Model\Transaction;
use Open\WebhelpBundle\Model\TransactionResponse;

class TransactionFactory
{
    public function buildFromTransactionResponse(
        TransactionResponse $transactionResponse,
        int $izbergOrderId,
        int $izbergPaymentId
    )
    {
        $transaction = (new Transaction())
            ->setIzbergOrderId($izbergOrderId)
            ->setIzbergPaymentId($izbergPaymentId)
            ->setWpsTransactionCode($transactionResponse->getCodeTransactionWps())
            ->setReason($transactionResponse->getReason())
            ->setReconciliationkey($transactionResponse->getReconciliationKey())
            ->setRedirectionCardUrl($transactionResponse->getRedirectionCardUrl())
            ->setStatus($transactionResponse->getStatus());

        return $transaction;
    }
}
