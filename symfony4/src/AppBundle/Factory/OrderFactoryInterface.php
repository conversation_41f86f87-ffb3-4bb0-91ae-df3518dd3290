<?php

declare(strict_types=1);

namespace AppBundle\Factory;

use AppBundle\Entity\Company;
use AppBundle\Model\Order\MerchantOrder as ModelMerchantOrder;
use AppBundle\Model\Order\Order;
use Open\IzbergBundle\Model\MerchantOrder;
use Open\IzbergBundle\Model\Order as ModelOrder;

interface OrderFactoryInterface
{
    public function buildOrderFromOrderId(int $orderId): Order;

    public function buildOrder(ModelOrder $izbergOrder): Order;

    public function buildMerchantOrderFromIzbergMerchantOrder(
        MerchantOrder $izbergMerchantOrder,
        Company $company,
        bool $shippingDecorated = false
    ): ModelMerchantOrder;
}
