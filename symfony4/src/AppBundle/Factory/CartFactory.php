<?php

namespace AppBundle\Factory;

use AppBundle\Entity\Company;
use AppBundle\Entity\User;
use AppBundle\Entity\WishListItem;
use AppBundle\Model\Cart\Cart;
use AppBundle\Model\Cart\CartItem;
use AppBundle\Model\Cart\CartMerchant;
use AppBundle\Model\Offer;
use AppBundle\Model\Order\OrderItem;
use AppBundle\Repository\CartRepository;
use AppBundle\Repository\CartShippingOptionRepository;
use AppBundle\Repository\MerchantRepository;
use AppBundle\Services\AlstomCustomAttributes;
use AppBundle\Services\CompanyCatalogService;
use AppBundle\Services\CustomsService;
use AppBundle\Services\MerchantService;
use AppBundle\Services\OfferService;
use AppBundle\Services\SecurityService;
use AppBundle\Services\SerializerService;
use AppBundle\Services\ShippingService;
use AppBundle\Services\SpecificPriceService;
use AppBundle\Util\DateUtil;
use DateInterval;
use DateTimeImmutable;
use Open\IzbergBundle\Api\CartApi;
use Open\IzbergBundle\Api\MerchantApi;
use Open\IzbergBundle\Model\CartItem as IzbergCartItem;
use stdClass;

class CartFactory
{
    private CartRepository $cartRepository;
    private CartApi $cartApi;
    private MerchantApi $merchantApi;
    private MerchantService $merchantService;
    private OfferService $offerService;
    private SpecificPriceService $specificPriceService;
    private CustomsService $customsService;
    private SecurityService $securityService;
    private CompanyCatalogService $companyCatalogService;
    private OrderFactory $orderFactory;
    private AlstomCustomAttributes $alstomCustomAttributesService;
    private ShippingService $shippingCartService;
    private CartShippingOptionRepository $cartShippingOptionRepository;
    private MerchantRepository $merchantRepository;
    private SerializerService $serializerService;

    public function __construct(
        CartRepository $cartRepository,
        CartApi $cartApi,
        MerchantApi $merchantApi,
        MerchantService $merchantService,
        OfferService $offerService,
        SpecificPriceService $specificPriceService,
        CustomsService $customsService,
        SecurityService $securityService,
        CompanyCatalogService $companyCatalogService,
        OrderFactory $orderFactory,
        CartShippingOptionRepository $cartShippingOptionRepository,
        MerchantRepository $merchantRepository,
        ShippingService $shippingCartService,
        SerializerService $serializerService,
        AlstomCustomAttributes $alstomCustomAttributesService
    )
    {
        $this->cartRepository = $cartRepository;
        $this->cartShippingOptionRepository = $cartShippingOptionRepository;
        $this->merchantRepository = $merchantRepository;
        $this->cartApi = $cartApi;
        $this->merchantApi = $merchantApi;
        $this->merchantService = $merchantService;
        $this->offerService = $offerService;
        $this->specificPriceService = $specificPriceService;
        $this->customsService = $customsService;
        $this->securityService = $securityService;
        $this->companyCatalogService = $companyCatalogService;
        $this->orderFactory = $orderFactory;
        $this->alstomCustomAttributesService = $alstomCustomAttributesService;
        $this->shippingCartService = $shippingCartService;
        $this->serializerService = $serializerService;
    }

    public function buildCart(int $cartId, Company $company, ?User $user = null): Cart
    {
        $izbergCart = $this->cartApi->fetchIzbergCart($cartId);
        /** @var \AppBundle\Entity\Cart $cartEntity */
        $cartEntity = $this->cartRepository->find($cartId);

        $cart = new Cart();
        $cart->setStatus(Cart::STATUS_CREATE);
        $cart->setId($izbergCart->getId());
        $cart->setCurrency($izbergCart->getCurrency());
        $cart->setSubTotalWithoutVat(0);
        $cart->setSubTotalVat([]);
        $cart->setTotal(0);

        $offers = $this->retrieveOffers(
            array_map(
                fn (IzbergCartItem $izbergCartItem): int => $izbergCartItem->getOffer()->getId(),
                $izbergCart->getItems()->toArray()
            ),
            $company
        );
        $cartItems = array_map(
            fn(IzbergCartItem $izbergCartIem) => $this->buildCartItem($izbergCartIem, $offers),
            $izbergCart->getItems()->toArray()
        );
        $this->addItemsToCart($cart, $cartItems, $company);

        if (!$cartEntity) {
            $cartEntity = new \AppBundle\Entity\Cart();
            $cartEntity->setCartId($cartId);
            $cartEntity->setStatus(\AppBundle\Entity\Cart::STATUS_CREATE);
            $cartEntity->setCreatedUser($user);
            $cartEntity->setCurrentUser($user);
            $this->cartRepository->save($cartEntity);
        }

        if ($cartEntity->getOrderId()) {
            $cart->setOrder($this->orderFactory->buildOrderFromOrderId($cartEntity->getOrderId()));
        }

        if ($cartEntity->getAddress()) {
            $cart->setaddress($cartEntity->getAddress());
            $cart->setShippingPoint($cartEntity->getAddress()->getShippingPoints()->get(0));
        }

        $carShippingOptions = [];
        $shippingOptions = $this->cartShippingOptionRepository->findBy(['cart' => $cartId]);
        foreach ($shippingOptions as $shippingOption) {
            $carShippingOptions[$shippingOption->getMerchantId()][] = $shippingOption;
        }
        $cart->setShippingOption($carShippingOptions);

        if ($cartEntity->getBillingAddress()) {
            $cart->setBillingAddress($cartEntity->getBillingAddress());
        }

        if ($cartEntity->getPaymentMode()) {
            $cart->setPaymentMode($cartEntity->getPaymentMode());
        }

        if ($cartEntity->getPaymentTerm()) {
            $cart->setPaymentTerm($cartEntity->getPaymentTerm());
        }

        if ($cartEntity->getPaymentMethod()) {
            $cart->setPaymentMethod($cartEntity->getPaymentMethod());
        }

        if ($cartEntity->getPaymentType()) {
            $cart->setPaymentType($cartEntity->getPaymentType());
        }

        if ($cartEntity->getPaymentTermIzbergId()) {
            $cart->setPaymentTermIzbergId($cartEntity->getPaymentTermIzbergId());
        }

        $cart->setStatus($cartEntity->getStatus());
        $cart->setAssignedSite($cartEntity->getSite());
        $cart->setValidationNumber($cartEntity->getValidationNumber());
        $cart->setBuyerOrderId($cartEntity->getBuyerOrderId());
        $cart->setCreator($cartEntity->getCreatedUser());
        $cart->setAssignedUser($cartEntity->getCurrentUser());
        $cart->setDocumentsRequests($cartEntity->getDocumentsRequest());
        $cart->setAccountingEmail($cartEntity->getAccountingEmail());

        return $cart;
    }

    public function decorateWithShipping(Cart $cart): Cart
    {
        $merchants = array_map(
            function (CartMerchant $cartMerchant) use ($cart) : CartMerchant {
                $cartMerchantItemsDeliveryDates = [];
                $cartMerchantShippingTotal = null;
                $cartMerchantShippingTotalVatIncluded = null;
                $cartMerchantShippingName = null;
                $shippingSubTotalVat = [];

                $cartMerchantItemsIds = array_map(
                    fn (CartItem $cartItem): int =>$cartItem->getId(),
                    $cartMerchant->getItems()
                );

                $shippingOptions = $this->cartApi->fetchCartShippingOptions($cart->getId());

                foreach ($shippingOptions as $shippingOption) {
                    // check if shipping option is part of the given merchant ?
                    $shippingOptionCartItemsIds = array_map(
                        fn (object $cartItem) => $cartItem->id,
                        $shippingOption->cart_items
                    );

                    $canProcessShippingOption = (count(array_intersect($cartMerchantItemsIds, $shippingOptionCartItemsIds)) > 0);

                    if (!$canProcessShippingOption) {
                        continue;
                    }

                    $shippingChoice = $shippingOption->shipping_choices[0];
                    $izbergShippingOffer = $shippingChoice->options->offer ?? null;

                    if ($izbergShippingOffer) {
                        $vatRate = (int)$izbergShippingOffer->vatRate;
                        $vatPrice = $izbergShippingOffer->priceTi - $izbergShippingOffer->priceTe;

                        if (!array_key_exists($vatRate, $shippingSubTotalVat)) {
                            $shippingSubTotalVat[$vatRate] = 0;
                        }
                        $shippingSubTotalVat[$vatRate] += $vatPrice;

                        $cartMerchantItemsDeliveryDates = array_reduce(
                            $shippingOption->cart_items,
                            function (array $cartItems, stdClass $cartItem) use ($izbergShippingOffer): array {
                                $cartItems[$cartItem->id] = new DateTimeImmutable($izbergShippingOffer->deliveryDate);
                                return $cartItems;
                            },
                            $cartMerchantItemsDeliveryDates
                        );
                    }

                    $cartMerchantShippingTotal = $cartMerchantShippingTotal + $izbergShippingOffer->priceTe;
                    $cartMerchantShippingTotalVatIncluded = $cartMerchantShippingTotalVatIncluded + $izbergShippingOffer->priceTi;
                    $cartMerchantShippingName = $shippingChoice->name;
                }

                $cartItems = array_map(
                    function (CartItem $cartItem) use ($cartMerchantItemsDeliveryDates): CartItem {
                        $deliveryDate = $cartMerchantItemsDeliveryDates[$cartItem->getId()] ?? $cartItem->getDeliveryDate();
                        $cartItem->setDeliveryDate($deliveryDate);
                        $toBeShipped = array_key_exists($cartItem->getId(), $cartMerchantItemsDeliveryDates);
                        $cartItem->setToBeShipped($toBeShipped);

                        return $cartItem;
                    },
                    $cartMerchant->getItems()
                );

                // update the merchant cart items delivery dates
                $cartMerchant->setItems($cartItems);

                // update the merchant cart shipping total
                $cartMerchant->setShippingTotal($cartMerchantShippingTotal);
                $cartMerchant->setShippingTotalVatIncluded($cartMerchantShippingTotalVatIncluded);

                // update the merchant cart shipping name
                // translatable shipping name
                if (in_array($cartMerchantShippingName, ['cheapest', 'fastest'])) {
                    $cartMerchantShippingName = sprintf('shipping.option.%s', $cartMerchantShippingName);
                }
                $cartMerchant->setShippingName($cartMerchantShippingName);

                // update the merchant order subtotalVat with shipping VAT
                $merchantSubTotalVat = $cartMerchant->getSubTotalVat();

                foreach ($shippingSubTotalVat as $vatRate => $vatPrice) {
                    if (!array_key_exists($vatRate, $merchantSubTotalVat)) {
                        $merchantSubTotalVat[$vatRate] = 0;
                    }

                    $merchantSubTotalVat[$vatRate] += $vatPrice;
                }
                krsort($merchantSubTotalVat);

                $cartMerchant->setSubTotalVat($merchantSubTotalVat);

                return $cartMerchant;
            },
            $cart->getMerchants()
        );

        $cart->setMerchants($merchants);

        // set $cart->subTotalVat
        $subTotalVat = array_reduce(
            $cart->getMerchants(),
            function (array $subTotalVat, CartMerchant $cartMerchant) use ($cart) {
                $cart->addTotalShipping($cartMerchant->getShippingTotalVatIncluded());
                $cart->addTotal($cartMerchant->getShippingTotalVatIncluded());
                $cartMerchant->addTotal($cartMerchant->getShippingTotalVatIncluded());

                $cartMerchantSubTotalVat = $cartMerchant->getSubTotalVat();
                foreach ($cartMerchantSubTotalVat as $percent => $totalVat) {
                    if (!isset($subTotalVat[$percent])) {
                        $subTotalVat[$percent] = 0.0;
                    }

                    $subTotalVat[$percent] = $subTotalVat[$percent] + $totalVat;
                }

                return $subTotalVat;
            },
            []
        );
        krsort($subTotalVat);
        $cart->setSubTotalVat($subTotalVat);

        return $cart;
    }

    private function retrieveOffers(array $offerIds, Company $company): array
    {
        $result = [];
        $offers = $this->offerService->findOffersByIds($offerIds);

        foreach ($offers as $offer) {
            $offer->setBuyerRef($this->companyCatalogService->searchCompanyBuyerReference($company, $offer));
        }

        foreach($offers as $offer) {
            $izbergReference = $offer->getIzbergReference();
            $offerWithSpecificPrice = $this->specificPriceService->updateOfferSpecificPrices($company, $offer);
            if ($izbergReference !== null) {
                $result[$izbergReference] = $offerWithSpecificPrice;
            }
        }

        return $result;
    }

    private function buildCartItem(IzbergCartItem $izbergCartItem, array $offers): CartItem
    {
        $cartItem = new CartItem();
        $cartItem->setId($izbergCartItem->getId());
        $cartItem->setName($izbergCartItem->getName());
        $cartItem->setUnitPrice($izbergCartItem->getUnitPrice());
        $cartItem->setUnitVat($izbergCartItem->getUnitVat());
        $cartItem->setUnitPriceVatIncluded($izbergCartItem->getUnitPriceVatIncluded());
        $cartItem->setQuantity($izbergCartItem->getQuantity());
        $cartItem->setMoq(0);
        $cartItem->setBatchSize(1);
        $cartItem->setStock(0);
        $cartItem->setOfferId($izbergCartItem->getOffer()->getId());
        $cartItem->setImageUrl($izbergCartItem->getImageUrl());

        $taxRate = '';
        if($izbergCartItem->getUnitPrice() > 0) {
            $taxRate = '' . ($izbergCartItem->getUnitVat() * 100) / $izbergCartItem->getUnitPrice();
        }
        $cartItem->setTaxRate($taxRate);
        $cartItem->setTotalItem($izbergCartItem->getQuantity() * $izbergCartItem->getUnitPrice());
        $cartItem->setMerchantId($izbergCartItem->getMerchant()->getId());

        $offer = $offers[$cartItem->getOfferId()] ?? null;
        if (!$offer instanceof Offer) {
            $cartItem->lost();
            $cartItem->setValid(false);
            return $cartItem;
        } else {
            $cartItem->setOffer($offer);
            $cartItem->setCurrency($offer->getCurrency());
        }

        // check validity of cartItem compare prices
        $offerPrice = $offer->getPriceForQuantity($cartItem->getQuantity());
        if ($cartItem->getUnitPrice() != $offerPrice) {
            $cartItem->setValid(false);
        }

        $this->buildCartItemFromOffer($cartItem, $offer);
        $this->buildCartItemExtraInfo($cartItem);

        return $cartItem;
    }

    private function buildCartItemExtraInfo(CartItem $cartItem)
    {
        // get extra info from api
        $cartItemExtraInfo = $this->cartApi->getCartItemExtraInfo($cartItem->getId());
        $cartItemExtraInfo['Buyer-internal-ref'] = $cartItemExtraInfo['Buyer-internal-ref'] ?? $cartItem->getBuyerRef();
        $cartItemExtraInfo['Cart-item-comment'] = $cartItemExtraInfo['Cart-item-comment'] ?? $cartItem->getCartItemComment();
        $cartItemSplitDelivery = $cartItemExtraInfo['Cart-item-split-delivery'] ?? null;
        if (!empty($cartItemSplitDelivery) && !is_array($cartItemSplitDelivery)) {
            $cartItemSplitDelivery = json_decode($cartItemSplitDelivery, true);
        }
        $cartItemExtraInfo['Cart-item-split-delivery'] = $cartItemSplitDelivery ?? $cartItem->getCartItemSplitDelivery();

        // update the cart item
        $cartItem->setBuyerInternalRef($cartItemExtraInfo['Buyer-internal-ref'] ?? $cartItem->getBuyerRef());
        $cartItem->setCartItemComment($cartItemExtraInfo['Cart-item-comment'] ?? $cartItem->getCartItemComment());
        $cartItem->setCartItemSplitDelivery($cartItemExtraInfo['Cart-item-split-delivery']);
        $cartItem->setPurchaseRequestId($cartItemExtraInfo['Purchase-request-id'] ?? null);
        $cartItem->setExtraInfo($cartItemExtraInfo);

    }

    public function buildCartItemFromOrderItem(OrderItem $orderItem, $price, Offer $offer)
    {
        $cartItem = new CartItem();
        $cartItem->setQuantity($orderItem->getQuantity());
        $cartItem->setOfferId($orderItem->getOfferId());
        $cartItem->setTotalItem($orderItem->getQuantity() * $price);
        $cartItem->setCurrency($orderItem->getCurrency());
        $cartItem->setImageUrl("");
        $cartItem->setUnitPrice($price);
        $cartItem->setUnitVat(($price * $this->customsService->taxRateToApplyFromOffer($offer)) / 100);
        $cartItem->setUnitPriceVatIncluded($cartItem->getUnitPrice() + $cartItem->getUnitVat());
        $cartItem->setTaxRate($this->customsService->taxRateToApplyFromOffer($offer));
        $cartItem->setOffer($offer);
        $cartItem->setMerchantId($offer->getMerchant()->getId());

        $this->buildCartItemFromOffer($cartItem, $offer);

        return $cartItem;
    }

    public function buildCartItemFromWishListItem(WishListItem $wishListItem, $price, Offer $offer, $currency)
    {
        $cartItem = new CartItem();
        $cartItem->setQuantity($wishListItem->getQuantity());
        $cartItem->setOfferId(intval($wishListItem->getOfferId()));
        $cartItem->setTotalItem($wishListItem->getQuantity() * $price);
        $cartItem->setCurrency($currency);
        $cartItem->setImageUrl("");
        $cartItem->setUnitPrice($price);
        $cartItem->setUnitVat(($price * $this->customsService->taxRateToApplyFromOffer($offer)) / 100);
        $cartItem->setUnitPriceVatIncluded($cartItem->getUnitPrice() + $cartItem->getUnitVat());
        $cartItem->setTaxRate($this->customsService->taxRateToApplyFromOffer($offer));
        $cartItem->setOffer($offer);
        $cartItem->setMerchantId($offer->getMerchant()->getId());

        $this->buildCartItemFromOffer($cartItem, $offer);

        return $cartItem;
    }

    private function buildCartItemFromOffer(CartItem $cartItem, Offer $offer)
    {

        $cartItem->setMoq($offer->getMoq());
        $cartItem->setBatchSize((!is_null($offer->getBatchSize())) ? $offer->getBatchSize() : 1);
        $cartItem->setStock($offer->getQuantity());
        $cartItem->setName($offer->getOfferTitle());
        $cartItem->setIncoterm($offer->getIncoterm());
        $cartItem->setCountryOfDelivery($offer->getIncotermCountry());
        $cartItem->setSellerRef($offer->getSellerRef());
        $cartItem->setBuyerRef($offer->getBuyerRef());
        $cartItem->setFcaAddress($offer->getFcaAddress() . ' ' . $offer->getFcaZipCode() . ' ' . $offer->getFcaZipTown());
        $cartItem->setDeliveryTime($offer->getDeliveryTime());
        $cartItem->setDeliveryTimeDelayBeforeShipping($offer->getDeliveryTimeDelayBeforeShipping());

        $deliveryDate = (new DateTimeImmutable())
            ->add(
                (new DateInterval(sprintf('P%dD', $offer->getDeliveryTime())))
            );
        $deliveryDateDelayBeforeShipping = (new DateTimeImmutable())
            ->add(
                (new DateInterval(sprintf('P%dD', $offer->getDeliveryTimeDelayBeforeShipping())))
            );

        if ($offer->getIncoterm() === 'FCA') {
            $deliveryDate = DateUtil::moveToNextWorkingDay($deliveryDate);
            $deliveryDateDelayBeforeShipping = DateUtil::moveToNextWorkingDay($deliveryDateDelayBeforeShipping);
        }

        $cartItem->setDeliveryDate($deliveryDate);
        $cartItem->setDeliveryDateDelayBeforeShipping($deliveryDateDelayBeforeShipping);
        $cartItem->setTrueDeliveryTime($offer->getTrueDeliveryTime());
        $cartItem->setIsDangerousProduct($offer->isDangerousProduct());
    }

    private function buildCartMerchants(Cart $cart, ?Company $company = null)
    {
        $merchantItems = [];
        $merchantIds = array_unique(
            array_map(
                function (CartItem $cartItem) use (&$merchantItems) {
                    $merchantId = $cartItem->getMerchantId();

                    if (!isset($merchantItems[$merchantId])) {
                        $merchantItems[$merchantId] = [];
                    }

                    $merchantItems[$merchantId][] = $cartItem;

                    return $merchantId;
                },
                $cart->getItems()
            )
        );

        $merchants = array_map(
            fn (int $merchantId): CartMerchant => $this->buildCartMerchant($merchantId, $merchantItems[$merchantId], $company),
            $merchantIds
        );

        usort($merchants, function (CartMerchant $merchantA, CartMerchant $merchantB) {
            return intval($merchantA->getName() < $merchantB->getName());
        });

        $cart->setMerchants($merchants);
    }

    private function buildCartMerchant(int $izbergMerchantId, array $cartItems, ?Company $company = null): CartMerchant
    {
        $izbergMerchant = $this->merchantApi->getMerchant($izbergMerchantId);

        $merchantMinimumOrderAmountName = $this->alstomCustomAttributesService->getMinimumOrderAmount();
        $merchantMinimumOrderAmount = $this->merchantApi->getMerchantCustomAttribute($izbergMerchant->id, $merchantMinimumOrderAmountName);

        $upelaActive = $this->merchantService->merchantIsUpelaActive($izbergMerchant->id);

        $cartMerchant = new CartMerchant();
        $cartMerchant->setId($izbergMerchant->id);
        $cartMerchant->setName($izbergMerchant->name);
        $cartMerchant->setDescription($izbergMerchant->description);
        $cartMerchant->setLongDescription($izbergMerchant->long_description);
        $cartMerchant->setLogoImage($izbergMerchant->logo_image);
        $cartMerchant->setCountry($this->merchantService->getCountryFromIzbergResponse($izbergMerchant));
        $cartMerchant->setConditions('');
        $cartMerchant->setSubTotalVat([]);
        $cartMerchant->setSubTotalWithoutVat(0);
        $cartMerchant->setVat(0);
        $cartMerchant->setTotal(0);
        $cartMerchant->setMinimumOrderAmount($merchantMinimumOrderAmount);
        $cartMerchant->setUpelaActive($upelaActive ?? false);

        foreach ($cartItems as $cartItem) {
            $cartMerchant->addSubTotalVat($cartItem->getTaxRate(), $cartItem->getQuantity() * $cartItem->getUnitVat());
            $cartMerchant->addVat($cartItem->getQuantity() * $cartItem->getUnitVat());
            $cartMerchant->addSubTotalWithoutVat($cartItem->getTotalItem());
            $cartMerchant->addTotal($cartItem->getQuantity() * $cartItem->getUnitPriceVatIncluded());
        }

        usort($cartItems, function (CartItem $cartItemA, CartItem $cartItemB) {
            return intval($cartItemA->getName() > $cartItemB->getName());
        });

        $cartMerchant->setItems($cartItems);

        // parcourir all merchant offers et si une offre est shippable alors le marchant est shippable
        $isShippable = false;
        /** @var CartItem $cartItem */
        foreach ($cartMerchant->getItems() as $cartItem) {
            $offer = $cartItem->getOffer();
            if ($offer instanceof Offer) {
                if ($offer->isShippable()) {
                    $isShippable = true;
                    break;
                }
            }
        }

        $cartMerchant->setShippable($isShippable);

        $cartMerchant->setVatInformation(
            $this->customsService->getInfo(
                $cartMerchant->getCountry(),
                ($company) ? $company->getFiscalCountry() : $this->securityService->getFiscalCountry()
            )
        );

        return $cartMerchant;
    }

    public function addItemsToCart(Cart $cart, array $items, ?Company $company = null)
    {
        if (count($items) == 0) {
            $cart->setItems([]);
        }

        /** @var CartItem $item */
        foreach ($items as $item) {
            if (!$item->isValid()) {
                $cart->setValid(false);
            }

            $cart->addItem($item);
            $cart->addSubTotalVat($item->getTaxRate(), $item->getQuantity() * $item->getUnitVat());
            $cart->addVat($item->getQuantity() * $item->getUnitVat());
            $cart->addSubTotalWithoutVat($item->getTotalItem());
            $cart->addTotal($item->getQuantity() * $item->getUnitPriceVatIncluded());
        }
        $cart->setItemsCount(count($cart->getItems()));
        $this->buildCartMerchants($cart, $company);
    }
}
