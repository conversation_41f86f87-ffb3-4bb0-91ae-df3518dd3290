<?php

namespace AppBundle\Factory;

use AppBundle\Model\ProcessId;
use AppBundle\Repository\CompanyRepository;
use Open\IzbergBundle\Api\IzbergUtils;
use Open\IzbergBundle\Model\MerchantOrder;
use Open\IzbergBundle\Model\Order;
use Open\IzbergBundle\Model\Refund;
use Open\IzbergBundle\Model\Resource;
use Open\WebhelpBundle\Model\AutomaticResponse;
use Open\WebhelpBundle\Model\RefundTransaction;
use Open\WebhelpBundle\Model\SubTransaction;

class ProcessIdFactory
{
    private CompanyRepository $companyRepository;

    public function __construct(CompanyRepository $companyRepository)
    {
        $this->companyRepository = $companyRepository;
    }

    public function build($object): ProcessId
    {
        if ($object instanceof Order) {
            $companyEmail = null;
            $company = $this->companyRepository->findCompanyByIzbergUserId($object->getUser()->getId());
            if ($company) {
                $companyEmail = $company->getMainContact()->getEmail();
            }
            return (new ProcessId())
                ->setCompanyEmail($companyEmail)
                ->setOrderId($object->getId())
                ->setOrderNumber($object->getIdNumber());
        }

        if ($object instanceof MerchantOrder) {
            $companyEmail = null;
            $company = $this->companyRepository->findCompanyByIzbergUserId($object->getUser()->getId());
            if ($company) {
                $companyEmail = $company->getMainContact()->getEmail();
            }
            return (new ProcessId())
                ->setCompanyEmail($companyEmail)
                ->setMerchantOrderId($object->getId());
        }

        if ($object instanceof Refund) {
            $merchantOrderId = null;
            /** @var Resource $merchantOrderResource */
            if ($merchantOrderResource = $object->getMerchantOrder()) {
                $merchantOrderId = IzbergUtils::parseIzbergResourceAndGetId($merchantOrderResource->getResourceUri());
            }

            return (new ProcessId())
                ->setRefundId($object->getId())
                ->setMerchantOrderId(strval($merchantOrderId));
        }

        if ($object instanceof AutomaticResponse) {
            return (new ProcessId())
                ->setTransactionId($object->getCodeTransactionWps());
        }

        if ($object instanceof SubTransaction) {
            return (new ProcessId())
                ->setTransactionId($object->getCodeSubTransactionWps());
        }

        if ($object instanceof RefundTransaction) {
            return (new ProcessId())
                ->setTransactionId($object->getTransactionId());
        }

        return (new ProcessId());
    }
}
