<?php

namespace AppBundle\Controller;

use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class DebugController
{
    #[\Symfony\Component\Routing\Attribute\Route(path: '/debug_test.php', name: 'debug_test_php', options: ['i18n' => false])]
    public function debugTest(): Response
    {
        ob_start();
        echo '<!doctype html><html><head><meta charset="utf-8"><title>Xdebug Test</title></head><body>';

        echo '<h2>Basic breakpoint test</h2>';
        if (function_exists('xdebug_break')) {
            echo '<p>Calling xdebug_break()... If your IDE is listening and mapping is correct, it should stop here.</p>';
            // Manual breakpoint
            xdebug_break();
        } else {
            echo '<p><strong>xdebug_break()</strong> not available (Xdebug not loaded).</p>';
        }

        echo '<h2>Xdebug status</h2>';
        if (function_exists('xdebug_info')) {
            echo '<p>xdebug_info() output:</p>';
            xdebug_info();
        } else {
            echo '<p><strong>xdebug_info()</strong> not available.</p>';
        }

        echo '<h2>phpinfo</h2>';
        phpinfo();

        echo '</body></html>';
        $html = ob_get_clean();

        return new Response($html);
    }
}
