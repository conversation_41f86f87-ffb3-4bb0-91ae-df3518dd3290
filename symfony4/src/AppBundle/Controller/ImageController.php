<?php

namespace AppBundle\Controller;

use AppBundle\Repository\ImageRepository;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;


class ImageController extends MkoController
{
    /**
     * @param string $id
     * @param ImageRepository $imageRepository
     * @return Response
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/image/{id}', name: 'front.get.image')]
    public function getImageById(string $id, ImageRepository $imageRepository): Response
    {
        $img = $imageRepository->getImgById($id);

        $file = stream_get_contents($img->getBlob());

        return new Response(
            $file,
            200,
            array(
                'Content-Type' => $img->getMime(),
                'Content-Disposition' => 'inline; filename="' . $img->getName() .'"'
            )
        );
    }
}
