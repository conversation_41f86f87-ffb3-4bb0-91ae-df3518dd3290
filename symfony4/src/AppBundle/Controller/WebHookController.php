<?php

namespace AppBundle\Controller;

use AppBundle\Request\InvoiceCreationRequestBuilder;
use AppBundle\Services\Shipping\InvoiceService;
use Open\IzbergBundle\Api\ApiClientManager;
use Open\IzbergBundle\Api\ApiConfigurator;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class WebHookController extends MkoController
{
    /**
     * webhook for automatic invoicing
     * upela payload will consist on a shipment object to be updated
     *
     * @param Request                       $request
     * @param InvoiceService                $invoiceService
     * @param InvoiceCreationRequestBuilder $invoiceCreationRequestBuilder
     *
     *
     * @param ApiClientManager              $apiClientManager
     * @param ApiConfigurator               $apiConfigurator
     *
     * @return Response
     * @throws \Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface
     * @throws \Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface
     * @throws \Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     * @throws \Upela\Api\Exception
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/hooks/shipment/delivered', name: 'hooks.shipment.delivered', methods: ['POST'], options: ['i18n' => false])]
    public function shipmentDeliveredAction(Request $request, InvoiceService $invoiceService, InvoiceCreationRequestBuilder $invoiceCreationRequestBuilder, ApiClientManager $apiClientManager, ApiConfigurator $apiConfigurator):Response
    {
        $this->logger->info(
            "Upela shipment sent webHook called ",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME=>EventNameEnum::UPELA_SHIPMENT_SENT,
                'payload' => $request->getContent(),
            ])
        );

        $this->configureApiConnection('webhook', $apiClientManager, $apiConfigurator);
        $shipmentObject = json_decode($request->getContent());

        $shipmentId = $shipmentObject->shipment_id ?? null;

        if (!$shipmentId) {
            return new Response('Invalid argument', Response::HTTP_BAD_REQUEST);
        }

        $invoiceCreationRequests = $invoiceCreationRequestBuilder->build($shipmentId);
        $invoiceService->invoice($invoiceCreationRequests);

        return new Response();
    }
}
