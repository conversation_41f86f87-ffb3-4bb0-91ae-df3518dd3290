<?php

namespace AppBundle\Controller;

use AppBundle\Entity\Document;
use AppBundle\Entity\Site;
use AppBundle\Entity\User;
use AppBundle\Repository\CompanyRepository;
use AppBundle\Repository\DocumentRepository;
use AppBundle\Repository\SiteRepository;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use AppBundle\Entity\Company;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * Class DocumentController
 * @package Open\FrontBundle\Controller
 */
class DocumentController extends MkoController
{
    const ACL_PROVIDER = 'security.acl.provider';
    const FILES = "files";
    const TRANSLATION_DOMAIN = 'AppBundle';
    const GLOBALE = "global";

    /**
     * @param string $id
     * @param TranslatorInterface $translator
     *
     * @param DocumentRepository $documentRepository
     * @return mixed
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/document/{id}/view', name: 'front.document.view', options: ['expose' => true])]
    public function viewAction(string $id, TranslatorInterface $translator, DocumentRepository $documentRepository)
    {
        /** @var Document $document */
        $document = $documentRepository->find($id);

        $this->securityCheck($document, $translator);

        $file = stream_get_contents($document->getBinary());

        return new Response(
            $file,
            200,
            array(
                'Content-Type' => $document->getMimeType(),
                'Content-Disposition' => 'inline; filename="' . $document->getFilename() . '"'
            )
        );
    }

    /**
     * @param Document            $document
     * @param TranslatorInterface $translator
     */
    private function securityCheck (Document $document, TranslatorInterface $translator):void{

        /** @var DocumentRepository $documentRepository */
        $documentRepository = $this->doctrine->getRepository(Document::class);

        /** security checks
         */
        $siteOrCompany = $documentRepository->getSiteOrCompanyByTypeAndId($document->getId());
        if (empty($siteOrCompany)){
            throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_EXCEPTION));
        }
        else{
            $user = $this->getUser();
            if(!$user instanceof User){
                throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_EXCEPTION));
            }
            $company = $user->getCompany();
            if(!$company instanceof Company){
                throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_EXCEPTION));
            }
            if ($siteOrCompany[0] instanceof Site){
                if (!$this->isGranted(self::ROLE_OPERATOR) && !$this->isGranted(self::ROLE_SUPER_ADMIN)) {
                    if ($company->getId() != $siteOrCompany[0]->getCompany()->getId() || (!$this->isGranted(self::ROLE_BUYER_ADMIN))) {
                        throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_EXCEPTION));
                    }
                }
            }
            else{
                if (!$this->isGranted(self::ROLE_OPERATOR) && !$this->isGranted(self::ROLE_SUPER_ADMIN)) {
                    if ($company->getId() != $siteOrCompany[0]->getId() || (!$this->isGranted(self::ROLE_BUYER_ADMIN))) {
                        throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_EXCEPTION));
                    }
                }
            }
        }
    }

    /**
     * @param Request $request
     * @param DocumentRepository $documentRepository
     * @param CompanyRepository $companyRepository
     * @param SiteRepository $siteRepository
     * @param TranslatorInterface $translator
     * @return mixed
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/document/remove', name: 'front.document.remove', methods: 'post', options: ['expose' => true])]
    public function removeAction(
        Request $request,
        DocumentRepository $documentRepository,
        CompanyRepository $companyRepository,
        SiteRepository $siteRepository,
        TranslatorInterface $translator
    )
    {
        $companyId = $request->get('companyId', 0);
        $siteId = $request->get('siteId', 0);
        $docId = $request->get('docId', 0);

        /** @var Document $document */
        $document = $documentRepository->find($docId);

        //security check
        $this->securityCheck($document, $translator);

        $company = null;
        $site = null;

        if($companyId != null){
            /** @var Company $company */
            $company = $companyRepository->find($companyId);
        }

        if($siteId != null){
            /** @var Site $site */
            $site = $siteRepository->find($siteId);
        }


        if($company != null){
          $company->removeDocument($document);
        }


        // Get manager
        $em = $this->doctrine->getManager();

        // Persist
        if($company != null){
            $em->persist($company);
        }

        if($site != null){
            $em->persist($site);
        }

        // Delete document
        $em->remove($document);

        // flush
        $em->flush();

        $json = array(
            'success' => true
        );

        return new JsonResponse(json_encode($json), 200, array(), true);

    }

    /**
     * Upload Document
     *
     * @param Request             $request
     * @param int                 $id
     * @param TranslatorInterface $translator
     * @return JsonResponse
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/uploadBusinessRegistration/{id}', name: 'company.upload.businessRegistration', defaults: ['id' => 0])]
    public function uploadCompanyBusinessRegistrationAction(Request $request, TranslatorInterface $translator, int $id = 0):JsonResponse
    {
        /* Retrieve th FileBag */
        $files = $request->files->get(self::FILES);

        /* Let storeDocument manage this */
        $ret = $this->storeDocument($files, $id,  Document::BUSINESS_REGISTRATION, $translator);

        return new JsonResponse($ret);
    }

    /***
     * @param array               $files
     * @param int                 $id
     * @param string              $type
     * @param TranslatorInterface $translator
     *
     * @return array
     */
    private function storeDocument(array $files, int $id, string $type, TranslatorInterface $translator):array
    {
        $allErrors = [];

        $em = $this->doctrine->getManager();

        $company = $em->getRepository(Company::class)->find($id);

        if (!$company instanceof Company) {
            throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_EXCEPTION));
        }

        if (!$this->isGranted(self::ROLE_BUYER_ADMIN)) {
            throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_EXCEPTION));
        }
        $user = $this->getUser();
        if(!$user instanceof User){
            throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_EXCEPTION));
        }
        if ($company->getId() != $user->getCompany()->getId()) {
            throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_EXCEPTION));
        }

        $docUploadeds = [];

        $docs = $this->handleDocs($files, $type);

        if(empty($docs)){
            $allErrors[] = $translator->trans('document.upload.type', [], self::TRANSLATION_DOMAIN);
        }

        foreach ($docs as $doc) {
            /* Contraintes applicatives sur les documents */
            $errors = $this->checkDocConstraints($doc, $translator);

            $doc->setCompany($company);

            /* On empile les erreurs */
            $allErrors += $errors;

            if(count($errors) == 0){
                switch ($type) {
                    case Document::BUSINESS_REGISTRATION:
                        $company->addDocument($doc);
                        break;
                    default:
                        break;
                }

                $docUploadeds[] = $doc;

                $em->flush();
            }

        }

        // build the response here
        $ret = [];
        if (count($allErrors) == 0) {
            $ret[self::GLOBALE] = $translator->trans('document.upload.ok', array(), self::TRANSLATION_DOMAIN);
            $ret['status'] = "OK";
            foreach ($docUploadeds as $doc) {
                $ret[self::FILES][] = array('name' => $doc->getFileName(),
                    'id' => $doc->getId());
            }
        } else {
            $ret[self::GLOBALE] = $translator->trans('document.upload.ko', array(), self::TRANSLATION_DOMAIN);
            $ret['status'] = "KO";
            $ret['errors'] = $allErrors;
        }
        return $ret;
    }

    /***
     * Check the contraints on files
     * @param Document $doc
     * @param TranslatorInterface $translator
     * @return array of errors (string)
     */
    private function checkDocConstraints(Document $doc, TranslatorInterface $translator):array
    {
        $errors = [];

        if (!in_array($doc->getMimeType(), Document::MIME_CONSTRAINT)) {
            $errors[] = $doc->getFilename() . ' : ' . $translator->trans('document.upload.mime', array('%contrainte%' => implode(", ", Document::MIME_CONSTRAINT)), self::TRANSLATION_DOMAIN);
        }
        if (strlen($doc->getBinary()) > Document::SIZE_CONSTRAINT) {
            $errors[] = $doc->getFilename() . ' : ' . $translator->trans('document.upload.size', array('%contrainte%' => Document::SIZE_CONSTRAINT / (1024 * 1024)), self::TRANSLATION_DOMAIN);
        }

        return $errors;
    }

    /**
     * @param array $files
     * @param string $type
     * @return array
     */
    private function handleDocs(array $files, string $type): array
    {
        if (empty($files)) {
            return [];
        }
        $docArray = [];
        foreach ($files as $file) {
            if (!empty($file->getFilename())) {
                $document = new Document();
                $document->setBinary(file_get_contents($file));
                $document->setFilename($file->getClientOriginalName());
                $document->setType($type);
                $document->setMimeType($file->getMimeType());
                $docArray[] = $document;
            }
        }

        return $docArray;
    }
}
