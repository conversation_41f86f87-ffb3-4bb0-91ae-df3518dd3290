<?php

namespace AppBundle\Controller;

use AppBundle\Entity\Company;
use AppBundle\Entity\User;
use Open\IzbergBundle\Api\ApiClientManager;
use Open\IzbergBundle\Api\ApiConfigurator;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Doctrine\Persistence\ManagerRegistry;

abstract class MkoController extends AbstractController implements LoggerAwareInterface
{
    const ROLE_BUYER_ADMIN = "ROLE_BUYER_ADMIN";
    const ROLE_BUYER_BUYER = "ROLE_BUYER_BUYER";
    const ROLE_BUYER_PAYER = "ROLE_BUYER_PAYER";

    const ROLE_OPERATOR = 'ROLE_OPERATOR';
    const ROLE_SUPER_ADMIN = 'ROLE_SUPER_ADMIN';

    const TRANSLATOR = 'translator';
    const TRANSLATION_DOMAIN = 'AppBundle';

    const NOT_FOUND_EXCEPTION = 'exception.controller.not_found';

    const ACCESS_DENIED_EXCEPTION = 'exception.controller.access_denied';
    const ACCESS_DENIED_NOT_DRAFT_EXCEPTION = 'exception.controller.access_denied_draft';
    const ACTION_DENIED_EXCEPTION = 'exception.controller.action_denied';
    const ACCESS_DENIED_COMPANY_NOT_VALID = "exception.controller.access_denied_company_not_valid";

    const VALIDATION_GROUPS = 'validation_groups';

    protected const SESSION = 'session';

    const SESSION_DEPARTMENT_SEARCH = 'front.search.department_type';
    const SESSION_ACTIVE_SEARCH_TYPE = 'front.search.active_search_type';
    const SESSION_TEXT_SEARCH = 'front.search.text';
    const SESSION_FILTER_SEARCH = 'front.search.filter';
    const SESSION_ACCOUNT_CREATION = 'front.account.creation';

    protected LoggerInterface $logger;

    protected ManagerRegistry $doctrine;

    public function __construct(ManagerRegistry $doctrine)
    {
        $this->doctrine = $doctrine;
    }

    /**
     * get the identifier of the current user
     */
    protected function getUsername(): string
    {
        $user = $this->getUser();
        if (!$user instanceof User) {
            return 'anonymous';
        }
        return $user->getUsername();
    }

    /**
     * get the email of the current user
     */
    protected function getUserEmail(): string
    {
        $user = $this->getUser();
        if (!$user instanceof User) {
            return '';
        }
        return $user->getEmail();
    }

    /**
     * get the company of the authenticated user or null if no user is authenticated
     */
    protected function getCompany(): ?Company
    {
        $user = $this->getUser();
        if (!$user instanceof User) {
            return null;
        }
        return $user->getCompany();
    }

    /**
     * @param string $connection
     * @param ApiClientManager $apiClientManager
     * @param ApiConfigurator $apiConfigurator
     */
    protected function configureApiConnection(string $connection, ApiClientManager $apiClientManager, ApiConfigurator $apiConfigurator): void
    {
        $apiClientManager->useConnection($connection);
        $apiConfigurator->configure($apiClientManager);
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
