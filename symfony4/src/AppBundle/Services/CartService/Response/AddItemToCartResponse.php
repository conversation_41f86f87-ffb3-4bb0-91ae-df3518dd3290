<?php

namespace AppBundle\Services\CartService\Response;

use AppBundle\Model\Offer;
use AppBundle\Services\CartService\Response\Error\OfferError;
use AppBundle\Services\CartService\Response\Error\OfferNotFound;
use AppBundle\Services\Response\Response;
use Exception;

class AddItemToCartResponse extends Response
{

    private int $total = 0;

    public function addErrorOffer(int $offerId)
    {
         $this->addError(new OfferNotFound($offerId));
    }

    /**
     * @param Offer $offer
     * @throws Exception
     */
    public function addErrorStatus(Offer $offer)
    {
        $this->addError(new OfferError($offer, OfferError::ERROR_STATUS));
    }

    /**
     * @param Offer $offer
     * @throws Exception
     */
    public function addErrorStock(Offer $offer)
    {
        $this->addError(new OfferError($offer, OfferError::ERROR_STOCK));
    }

    /**
     * @param Offer $offer
     * @throws Exception
     */
    public function addErrorMoq(Offer $offer)
    {
        $this->addError(new OfferError($offer, OfferError::ERROR_MOQ));
    }

    /**
     * @param Offer $offer
     * @throws Exception
     */
    public function addErrorPrice(Offer $offer)
    {
        $this->addError(new OfferError($offer, OfferError::ERROR_PRICE));
    }

    /**
     * @param Offer $offer
     * @throws Exception
     */
    public function addErrorNoPrice(Offer $offer)
    {
        $this->addError(new OfferError($offer, OfferError::ERROR_NO_PRICE));
    }

    public function addCartItem()
    {
        $this->total += 1;
    }

    public function getTotal(): int
    {
        return $this->total;
    }
}
