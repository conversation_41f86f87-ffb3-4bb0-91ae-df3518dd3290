<?php

namespace AppBundle\Services\CartService\Response\Error;

use AppBundle\Model\Offer;
use AppBundle\Services\Response\Error;
use Exception;

class OfferError extends Error
{
    public const ERROR_STATUS = 'error_status';
    public const ERROR_STOCK = 'error_stock';
    public const ERROR_MOQ = 'error_mock';
    public const ERROR_PRICE = 'error_price';
    public const ERROR_NO_PRICE = 'error_no_price';

    private Offer $offer;

    /**
     * @param Offer $offer
     * @param string $status
     * @throws Exception
     */
    public function __construct(Offer $offer, string $status)
    {
        $validStatuses = [
            self::ERROR_STATUS,
            self::ERROR_STOCK,
            self::ERROR_MOQ,
            self::ERROR_PRICE,
            self::ERROR_NO_PRICE,
        ];

        if (!in_array($status, $validStatuses)) {
            throw new Exception(sprintf('status should be one of those values [%s], %s given', implode(', ', $validStatuses), $status));
        }

        $this->offer = $offer;
        $this->id = $status;
    }

    public function getOffer(): Offer
    {
        return $this->offer;
    }

    public function isErrorStatus(): bool
    {
        return ($this->id === self::ERROR_STATUS);
    }

    public function isErrorStock(): bool
    {
        return ($this->id === self::ERROR_STOCK);
    }

    public function isErrorMoq(): bool
    {
        return ($this->id === self::ERROR_MOQ);
    }

    public function isErrorPrice(): bool
    {
        return ($this->id === self::ERROR_PRICE);
    }

    public function isErrorNoPrice(): bool
    {
        return ($this->id === self::ERROR_NO_PRICE);
    }
}
