<?php

namespace AppBundle\Services;

use AppBundle\Entity\Country;
use Doctrine\ORM\EntityManagerInterface;
use Knp\Component\Pager\PaginatorInterface;

class CountryService extends AbstractPaginatedService
{
    public function __construct(EntityManagerInterface $em, PaginatorInterface $paginator)
	{
		parent::__construct($em, Country::class, $paginator);
	}

    public function getCountryById($id):? Country
    {
        return $this->repository->find($id);
    }

    public function getCountryByIzbergId($id): ?Country
    {
        $res = $this->repository->findBy(['izbergId' => $id]);
        if (count($res)>0){
            return $res[0];
        }

        return null;
    }

    public function getCountryByIzbergCode($code)
    {
        $res =$this->repository->findBy(['izbergCode' => $code]);
        if (count($res)>0){
            return $res[0];
        }

        return null;
    }
}
