<?php

namespace AppBundle\Services;

use AppBundle\Entity\Company;
use AppBundle\Entity\User;
use AppBundle\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use AppBundle\Entity\Country;

class SecurityService
{
    public const ROLE_BUYER_ADMIN = "ROLE_BUYER_ADMIN";
    public const ROLE_BUYER_BUYER = "ROLE_BUYER_BUYER";
    public const ROLE_BUYER_PAYER = "ROLE_BUYER_PAYER";
    public const ROLE_OPERATOR = 'ROLE_OPERATOR';
    public const ROLE_SUPER_ADMIN = 'ROLE_SUPER_ADMIN';
    public const ANONYMOUS_USER = 'anonymous';

    /**
     * @var EntityManagerInterface $em
     */
    private $em;

    /**
     * @var AuthorizationCheckerInterface
     */
    private $authChecker;

    /**
     * @var TokenStorageInterface
     */
    private $tokenStorage;

    public function __construct(EntityManagerInterface $em,
                                AuthorizationCheckerInterface $authChecker,
                                TokenStorageInterface $tokenStorage)
    {
        $this->em = $em;
        $this->authChecker = $authChecker;
        $this->tokenStorage = $tokenStorage;
    }


    public function getCurrentUserName(): string
    {
        $token = $this->tokenStorage->getToken();
        if (!$token) {
            return self::ANONYMOUS_USER;
        }

        $user = $token->getUser();
        if (!is_object($user)) {
            return self::ANONYMOUS_USER;
        }

        return $user->getUsername();
    }

    public function isAnonymous(): bool
    {
        return $this->getCurrentUserName() === self::ANONYMOUS_USER;
    }

    /**
     * @param User $user
     * @return bool
     */
    public function isAdminCompany($user)
    {
        return $this->isGranted("ROLE_BUYER_ADMIN", $user);
    }

    /**
     * @param User $user
     * @return bool
     */
    public function isAdmin($user)
    {
        return $this->isGranted("ROLE_OPERATOR", $user) || $this->isGranted("ROLE_SUPER_ADMIN", $user);
    }


    public function isBuyer($user)
    {

        //as role are inherited only test test basic one
        return $this->isGranted(self::ROLE_BUYER_BUYER, $user);

    }

    public function isAdminPiggyBack()
    {
        try {
            return $this->authChecker->isGranted('ROLE_PREVIOUS_ADMIN');
        } catch(\Exception $e) {
            return false;
        }
    }

    public function isGranted($role, $subject = null): bool
    {
        return $this->authChecker->isGranted($role, $subject);
    }

    /**
     * @param $role
     * @param User $user
     * @return bool
     */
    public function isGrantedNotReachable($role, $user)
    {
        // deal with anonymous user
        if ($user === null || is_string($user)) {
            return false;
        }
        return in_array($role, $user->getRoles(), true);
    }

    /**
     *  get the company admin users
     * @param Company $company
     * @return array users
     */
    public function getCompanyAdminUsers($company)
    {
        $adminUsers = [];
        foreach ($company->getUsers() as $user) {
            if ($this->isAdminCompany($user)) {
                $adminUsers[] = $user;
            }
        }
        return $adminUsers;
    }


    /***
     * @return mixed
     */
    public function getOperators()
    {
        /** @var UserRepository $userRepository */
        $userRepository = $this->em->getRepository(User::class);
        return $userRepository->findOperator();
    }

    /***
     * @return array
     */
    public function getOperatorMails()
    {
        $operators = $this->getOperators();
        $mails = [];
        /**
         * @var User $operator
         */
        foreach ($operators as $operator) {
            if ($operator->isEnabled()) {
                $mails[] = $operator->getEmail();
            }
        }
        return $mails;
    }


    /***
     * @return mixed
     */
    public function getUser()
    {
        $token = $this->tokenStorage->getToken();
        if(!$token instanceof TokenInterface){
            return null;
        }

        return $token->getUser();
    }

    /**
     * Build the country for Import purpose (we need to know in which country the company is located)
     * @param User|null $user
     * @return Country|null
     */
    public function getFiscalCountry(?User $user = null)
    {
        if (!$user) {
            /** @var user $user  */
            $user = $this->getUser();
        }

        if (!$user || is_string($user)) {
            return null;
        }
        /** @var Company $company */
        $company = $user->getCompany();
        if (!$company) {
            return null;
        }

        return $company->getFiscalCountry();
    }
}
