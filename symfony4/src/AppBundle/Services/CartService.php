<?php

namespace AppBundle\Services;

use AppBundle\Entity\Address;
use AppBundle\Entity\CartHistoric;
use AppBundle\Entity\Company;
use AppBundle\Entity\ShippingPoint;
use AppBundle\Entity\Site;
use AppBundle\Entity\User;
use AppBundle\Entity\WishList;
use AppBundle\Entity\WishListItem;
use AppBundle\Exception\CartException;
use AppBundle\Factory\CartFactory;
use AppBundle\Model\Cart\CartMerchant;
use AppBundle\Model\Offer;
use AppBundle\Model\Order\MerchantOrder;
use AppBundle\Model\Order\Order;
use AppBundle\Model\Order\OrderItem;
use AppBundle\Repository\CartHistoricRepository;
use AppBundle\Repository\CartRepository;
use AppBundle\Repository\ShippingPointRepository;
use AppBundle\Services\CartService\Response\AddItemToCartResponse;
use DateInterval;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Open\FrontBundle\Form\PaymentModeSelectForm;
use Open\IzbergBundle\Api;
use Open\IzbergBundle\Api\ApiException;
use Open\IzbergBundle\Api\AuthenticationApi;
use Open\IzbergBundle\Api\CartApi;
use Open\IzbergBundle\Api\MerchantApi;
use Open\IzbergBundle\ApiRequestParameter;
use Open\IzbergBundle\Model\Cart;
use Open\IzbergBundle\Model\CartItem;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\File\Exception\AccessDeniedException;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;

class CartService
{
    const SUB_TOTAL_VAT = 'subTotalVat';
    const SUB_TOTAL_WITHOUT_VAT = 'subTotalWithoutVat';
    const TOTAL = 'total';
    const TOTAL_ITEM = 'totalItem';
    const VAT = 'vat';
    const ITEMS = 'items';
    const ITEMS_COUNT = 'itemsCount';

    public const CURRENCY = 'currency';
    public const CURRENCY_EUR = 'EUR';
    public const CURRENCY_USD = 'USD';

    /**
     * @var EntityManagerInterface
     */
    private $em;

    /**
     * @var \Open\IzbergBundle\Api\CartApi
     */
    private $cartApi;

    /**
     * @var Api\OrderApi
     */
    private $orderApi;

    /**
     * @var \AppBundle\Services\OfferService
     */
    private $offerService;

    /**
     * @var \Open\IzbergBundle\Api\MerchantApi
     */
    private $merchantApi;

    /** @var MerchantService $merchantService */
    private $merchantService;

    /**
     * @var AuthorizationCheckerInterface $authorizationChecker
     */
    private $authorizationChecker;

    /**
     * @var SessionInterface $session
     */
    private $session;

    /**
     * @var AuthenticationApi $authenticationApi
     */
    private $authenticationApi;

    /**
     * @var RouterInterface $Router
     */
    private $router;

    /**
     * @var MailService $mailService
     */
    private $mailService;

    /**
     * @var UserBddService $userService
     */
    private $userService;

    /**
     * @var CustomsService $customsService
     */
    private $customsService;

    /** @var SecurityService $securityService */
    private $securityService;

    /** @var OrderService */
    private $orderService;

    /** @var CartRepository */
    private $cartRepository;

    /** @var SpecificPriceService */
    private $specificPriceService;

    /** @var CartFactory */
    private $cartFactory;

    /** @var PaymentService */
    private $paymentService;

    /** @var AddressService */
    private $addressService;

    /** @var ShippingPointRepository */
    private $shippingPointRepository;

    /** @var ShippingService */
    private $shippingService;

    /** @var MerchantOrderService */
    private $merchantOrderService;
    private EventDispatcherInterface $dispatcher;

    private $requestStack;

    /***
     * CartService constructor.
     */
    public function __construct(
        CartApi $cartApi,
        Api\OrderApi $orderApi,
        MerchantApi $merchantApi,
        AuthorizationCheckerInterface $authorizationChecker,
        RequestStack $requestStack,
        AuthenticationApi $authenticationApi,
        OfferService $offerService,
        RouterInterface $router,
        MailService $mailService,
        UserBddService $userService,
        EntityManagerInterface $em,
        CustomsService $customsService,
        MerchantService $merchantService,
        SecurityService $securityService,
        OrderService $orderService,
        SpecificPriceService $specificPriceService,
        CartFactory $cartFactory,
        PaymentService $paymentService,
        AddressService $addressService,
        ShippingPointRepository $shippingPointRepository,
        ShippingService $shippingService,
        MerchantOrderService $merchantOrderService,
        EventDispatcherInterface $dispatcher
    )
    {
        $this->cartApi = $cartApi;
        $this->orderApi = $orderApi;
        $this->merchantApi = $merchantApi;
        $this->offerService = $offerService;
        $this->requestStack = $requestStack;
        $this->authorizationChecker = $authorizationChecker;
        $this->authenticationApi = $authenticationApi;
        $this->router = $router;
        $this->mailService = $mailService;
        $this->userService = $userService;
        $this->em = $em;
        $this->customsService = $customsService;
        $this->merchantService = $merchantService;
        $this->securityService = $securityService;
        $this->orderService = $orderService;
        $this->cartRepository = $this->em->getRepository(\AppBundle\Entity\Cart::class);
        $this->specificPriceService = $specificPriceService;
        $this->cartFactory = $cartFactory;
        $this->paymentService = $paymentService;
        $this->addressService = $addressService;
        $this->shippingPointRepository = $shippingPointRepository;
        $this->shippingService = $shippingService;
        $this->merchantOrderService = $merchantOrderService;
        $this->dispatcher = $dispatcher;
    }

    /**
     * Récupération des informations du panier dont on connait l'identifiant
     * @throws ApiException
     */
    public function findCart(User $user, $cartId = null, $currency = null): ?\AppBundle\Model\Cart\Cart
    {
        if (is_null($cartId)) {
            return $this->getUserCart($user, $currency);
        }

        return $this->getCart($cartId, $user->getCompany(), $user);
    }

    protected function getUserCart(User $user, string $currency): ?\AppBundle\Model\Cart\Cart
    {
        $cartId = null;
        if ($currency === 'EUR') {
            $cartId = $user->getCartEURId();
        }

        if ($currency === 'USD') {
            $cartId = $user->getCartUSDId();
        }

        if (!$cartId) {
            return null;
        }

        return $this->getCart($cartId, $user->getCompany(), $user);
    }

    protected function getCart(int $cartId, Company $company, ?User $user = null): ?\AppBundle\Model\Cart\Cart
    {
        return $this->cartFactory->buildCart($cartId, $company, $user);
    }

    public function updateCartItemExtraInfo(int $cartItemId, string $field, ?string $value): void
    {
        $this->cartApi->updateItemExtraInfo($cartItemId, $field, $value ?? '');
    }

    public function resetQtySplitDelivery($cart, Offer $offer, $quantity)
    {
        $cartItems = array_filter(
            $cart->getItems(),
            function (\AppBundle\Model\Cart\CartItem $cartItem) use ($offer) {
                return ($cartItem->getOfferId() == $offer->getIzbergReference());
            }
        );
        $item = reset($cartItems);
        if ($quantity <= $item->getOffer()->getRealStock()) {
            $splitDeliverys = [];
            $value = json_encode($splitDeliverys, JSON_UNESCAPED_SLASHES);
            $this->updateCartItemExtraInfo($item->getId(), "cart-item-split-delivery", $value);
        }
    }

    /**
     * copy a cart and set the new id
     * @param \AppBundle\Entity\Cart $source
     * @param int $newId
     * @return \AppBundle\Entity\Cart
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function copyCartDb(\AppBundle\Entity\Cart $source, int $newId): \AppBundle\Entity\Cart
    {
        $source->setStatus(\AppBundle\Entity\Cart::STATUS_REMOVED);

        $target = new \AppBundle\Entity\Cart();
        $target->setId($newId);
        $target->setCreatedUser($source->getCreatedUser());
        $target->setAddress($source->getAddress());
        $target->setStatus($source->getStatus());
        $target->setSite($source->getSite());
        $target->setPaymentMode($source->getPaymentMode());
        $target->setCurrentUser($source->getCurrentUser());
        $target->setOrderId($source->getOrderId());
        $target->setWpsReconciliationKey($source->getWpsReconciliationKey());
        $target->forceCreatedAt($source->getCreatedAt());
        $target->forceUpdatedAt($source->getUpdatedAt());

        $this->em->merge($source);
        $this->em->persist($target);
        $this->em->flush();

        /** @var CartHistoric $cartHistory */
        foreach ($source->getCartHistoric() as $cartHistory) {
            $cartHistory->setCart($target);
            $this->em->merge($cartHistory);
        }

        $this->em->flush();

        return $target;
    }

    /**
     * Return cart information from databased (assignation)
     * @param $cartId
     * @return \AppBundle\Entity\Cart (or null if not exists)
     */
    public function getCartDb($cartId): ?\AppBundle\Entity\Cart
    {
        $cartRepository = $this->em->getRepository(\AppBundle\Entity\Cart::class);
        return $cartRepository->find($cartId);
    }

    /**
     * @param $cartId
     * @param $newStatus
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function updateCartDbStatus($cartId, $newStatus)
    {
        $cartRepository = $this->em->getRepository(\AppBundle\Entity\Cart::class);
        $cart = $cartRepository->find($cartId);
        $cart->setStatus($newStatus);
        $this->em->merge($cart);
        $this->em->flush();
    }

    /**
     * @param User $user
     * @param string $currency
     * @return \AppBundle\Model\Cart\Cart|null
     * @throws \Exception
     */
    public function createUserCart(User $user, string $currency): ?\AppBundle\Model\Cart\Cart
    {
        $validCurrencies = [
            self::CURRENCY_EUR,
            self::CURRENCY_USD,
        ];

        if (!in_array($currency, $validCurrencies)) {
            throw new \Exception(
                sprintf(
                    'Currency provided is not valid. Valid currencies are [%s], %s given',
                    implode(',', $validCurrencies),
                    $currency
                )
            );
        }

        $cart = null;
        if ($currency === self::CURRENCY_EUR) {
            $cart = $this->createUserEURCart($user);
        }

        if ($currency === self::CURRENCY_USD) {
            $cart = $this->createUserUSDCart($user);
        }

        return $cart;
    }

    /**
     * @param User $user
     * @return \AppBundle\Model\Cart\Cart|null
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function createUserEURCart(User $user)
    {
        $izbergCart = $this->cartApi->createCart(self::CURRENCY_EUR);
        $user->setCartEURId(strval($izbergCart->getId()));
        $this->em->persist($user);
        $this->em->flush();

        return $this->findCart($user, $izbergCart->getId());
    }

    /**
     * @param User $user
     * @return \AppBundle\Model\Cart\Cart|null
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function createUserUSDCart(User $user)
    {
        $izbergCart = $this->cartApi->createCart(self::CURRENCY_USD);
        $user->setCartUSDId(strval($izbergCart->getId()));
        $this->em->persist($user);
        $this->em->flush();

        return $this->findCart($user, $izbergCart->getId());
    }

    /**
     * @param User $user
     * @param string $currency
     * @return \AppBundle\Model\Cart\Cart|null
     * @throws \Exception
     */
    public function findOrCreateUserCart(User $user, string $currency): ?\AppBundle\Model\Cart\Cart
    {
        $currency = strtoupper($currency);

        $validCurrencies = [
            self::CURRENCY_EUR,
            self::CURRENCY_USD,
        ];

        if (!in_array($currency, $validCurrencies)) {
            throw new \Exception(
                sprintf(
                    'Currency provided is not valid. Valid currencies are [%s], %s given',
                    implode(',', $validCurrencies),
                    $currency
                )
            );
        }

        $cart = null;
        if ($currency === self::CURRENCY_EUR) {
            $cart = $this->findOrCreateUserEURCart($user);
        }

        if ($currency === self::CURRENCY_USD) {
            $cart = $this->findOrCreateUserUSDCart($user);
        }

        return $cart;
    }

    private function findOrCreateUserEURCart(User $user): ?\AppBundle\Model\Cart\Cart
    {
        $cartId = $user->getCartEURId();
        if (!$cartId) {
            return $this->createUserEURCart($user);
        }

        return $this->findCart($user, $cartId);
    }

    private function findOrCreateUserUSDCart(User $user): ?\AppBundle\Model\Cart\Cart
    {
        $cartId = $user->getCartUSDId();
        if (!$cartId) {
            return $this->createUserUSDCart($user);
        }

        return $this->findCart($user, $cartId);
    }

    /**
     * add an offer to a cart
     * @param Offer $offer
     * @param User $user the owner of the cart
     * @param int $quantity quantity to add in the cart
     * @param $price
     * @param int|null $targetCartId
     * @param bool $forceQuantity
     * @param array $extraInfo
     * @return int the identifier of the targeted cart
     * @throws CartException
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function addOfferToCart(
        Offer $offer,
        User $user,
        int $quantity,
        int $targetCartId = null,
        bool $forceQuantity = false,
        array $extraInfo = []
    ): int
    {

        if ($offer->isLimited()) {
            throw new CartException('Cannot add offer to cart - offer is limited');
        }

        $price = $this->specificPriceService->specificPrice($user->getCompany(), $offer, $quantity);

        if ($offer->hasNoPrice()) {
            throw new CartException('Cannot add offer to cart - offer has no price');
        }

        if (!$targetCartId) {
            $cart = $this->findOrCreateUserCart($user, $offer->getCurrency());
        } else {
            $cart = $this->findCart($user, $targetCartId, $offer->getCurrency());
        }

        if ($cart === null || $user->isBuyerApi()) {
            $cart = $this->createUserCart($user, $offer->getCurrency());
        }

        if (!$cart) {
            throw new CartException('Could not find or create cart');
        }

        $this->updateQuantity(
            $cart->getItems(),
            $quantity,
            $offer,
            $cart,
            $forceQuantity,
            $extraInfo
        );

        if (!($quantity >= $offer->getMoq())) {
            throw new CartException('Cannot add offer to cart - quantity must be >= ' . $offer->getMoq());
        }

        if ($offer->getBatchSize() != null && $quantity % $offer->getBatchSize() > 0) {
            throw new CartException('Cannot add offer to cart - quantity does not match the batchsize');
        }

        // CUSTOMS TAX CALCULATIONS
        $taxRate = $this->customsService->taxRateToApply($offer);

        //we can now add our product to the cart
        $this->cartApi->addProductOffer(
            $cart->getId(),
            (int)$offer->getIzbergReference(),
            $cart->getCurrency(),
            $quantity,
            $price,
            $taxRate,
            $extraInfo
        );

        $cart->addItem((new \AppBundle\Model\Cart\CartItem()));
        $this->syncTotalItemFromCart($cart, $user);

        return $cart->getId();
    }

    public function updateQuantity(
        array $cartItems,
        int $quantity,
        Offer $offer,
        \AppBundle\Model\Cart\Cart $cart,
        bool $forceQuantity = false,
        array &$extraInfo = []
    ): int {
        $cartItems = array_filter(
            $cartItems,
            function (\AppBundle\Model\Cart\CartItem $cartItem) use ($offer) {
                return ($cartItem->getOfferId() == $offer->getIzbergReference());
            }
        );

        return array_reduce(
            $cartItems,
            function ($quantity, \AppBundle\Model\Cart\CartItem $cartItem) use ($cart, &$extraInfo, $forceQuantity) {

                if (!$forceQuantity) {
                    $quantity += $cartItem->getQuantity();
                }

                $extraInfo = $cartItem->getExtraInfo();
                $this->cartApi->removeItem($cartItem->getId());
                $cart->removeItem($cartItem);

                return $quantity;
            },
            $quantity
        );
    }

    public function addOfferWithCart(
        \AppBundle\Model\Cart\Cart $cart,
        Offer $offer,
        User $user,
        int $quantity,
        array $extraInfo = []
    ): ?int {
        $price = $this->specificPriceService->specificPrice($user->getCompany(), $offer, $quantity);

        // CUSTOMS TAX CALCULATIONS
        $taxRate = $this->customsService->taxRateToApply($offer);

        //we can now add our product to the cart
        $this->cartApi->addProductOffer(
            $cart->getId(),
            (int)$offer->getIzbergReference(),
            $cart->getCurrency(),
            $quantity,
            $price,
            $taxRate,
            $extraInfo
        );

        $cart->addItem((new \AppBundle\Model\Cart\CartItem()));
        $this->syncTotalItemFromCart($cart, $user);

        return $cart->getId();
    }

    /**
     * @param int $offerId
     * @param int $cartId
     */
    public function removeOfferFromCart(User $user, int $offerId, int $cartId)
    {
        $cartItemId = null;
        $cartItems = $this->cartApi->getCartItems($cartId);
        /** @var CartItem $cartItem */
        foreach ($cartItems as $cartItem) {
            if ($cartItem->getOffer()->getId() === $offerId) {
                $cartItemId = $cartItem->getId();
                break;
            }
        }

        if ($cartItemId) {
            $this->removeItem($cartItemId);

            $computeTotalItems = function ($totalItems) {
                return $totalItems = ($totalItems > 0) ? $totalItems - 1 : 0;
            };

            if ($user->getCartUSDId() === $cartId) {
                $user->setItemInCartUSD($computeTotalItems($user->getItemInCartUSD()));
            }

            if ($user->getCartEURId() === $cartId) {
                $user->setItemInCartEUR($computeTotalItems($user->getItemInCartEUR()));
            }
        }
    }

    /**
     * Suppression d'un item dans le panier
     * @param integer $cartItemId ID de l'item
     */
    public function removeItem(int $cartItemId)
    {
        $this->cartApi->removeItem($cartItemId);
    }

    public function removeAllItems(\AppBundle\Model\Cart\Cart $cart)
    {
        /** @var CartItem $item */
        foreach ($cart->getItems() as $item) {
            $this->removeItem($item->getId());
        }
    }

    /**
     * Assign a cart to an user. Send email to this user for treat cart buy.
     * @param int $cartId Cart ID
     * @param int $currentUserId
     * @param int $assignUserId
     * @param int $siteId Cost center ID
     * @param int $addressId Specific Address for the cost center
     * @param string $comment
     * @return bool true if email is send with success, false in other cases
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws \AppBundle\Exception\MailException
     */
    public function assignCart(
        $cartId,
        $currentUserId,
        $assignUserId,
        $siteId,
        $addressId,
        $comment,
        $buyerReferenceId,
        ?string $accountingEmail = null,
        ?Address $billingAddress = null,
        ?string $paymentMethod = null
    ): bool
    {
        $res = false;
        $siteRepository = $this->em->getRepository(Site::class);
        $addressRepository = $this->em->getRepository(Address::class);
        $cartUrl = $this->router->generate('cart.pending.details', ['cartId' => $cartId], UrlGeneratorInterface::ABSOLUTE_URL);
        $assignUser = $this->userService->findById($assignUserId);
        $currentUser = $this->userService->findById($currentUserId);

        $site = $siteRepository->find($siteId);
        $address = $addressRepository->find($addressId);
        if (!is_null($assignUser) && !is_null($currentUser) && !is_null($site) && !empty($assignUser->getEmail())) {
            // Adding new cart on DB if not present
            $cartDb = $this->cartRepository->find($cartId);
            if (is_null($cartDb)) {
                $cartDb = new \AppBundle\Entity\Cart();
                $cartDb->setCartId($cartId);
                $cartDb->setCreatedUser($currentUser);
            }
            $cartDb->setStatus(\AppBundle\Entity\Cart::STATUS_ASSIGN);
            $cartDb->setSite($site);
            $cartDb->setCurrentUser($assignUser);
            $cartDb->setAddress($address);
            $cartDb->setValidationNumber($buyerReferenceId);
            $cartDb->setAccountingEmail($accountingEmail);
            $cartDb->setBillingAddress($billingAddress);
            $cartDb->setPaymentMode($paymentMethod);
            $this->em->persist($cartDb);

            // Adding new historic
            $cartHistoric = new CartHistoric();
            $cartHistoric->setCart($cartDb);
            $cartHistoric->setComment($comment);
            $cartHistoric->setDate(new \DateTime());
            $cartHistoric->setUser($currentUser);
            $cartHistoric->setAssignedUser($assignUser);
            $cartHistoric->setStatus(\AppBundle\Entity\Cart::STATUS_ASSIGN);
            $this->em->persist($cartHistoric);
            $this->em->flush();

            // Send mail to assign user
            $data = [
                'authorAssignation' => $this->getUsername($currentUser),
                'comment' => $comment,
                'url' => $cartUrl,
                MailService::FIRST_NAME_VAR => $assignUser->getFirstname(),
                MailService::LAST_NAME_VAR => $assignUser->getLastname()
            ];

            if ($assignUser->isEnabled()) {
                $this->mailService->sendEmailMessage(MailService::CART_ASSIGN_USER, $assignUser->getLocale(), $assignUser->getEmail(), $data);
            }

            $res = true;
        }

        return $res;
    }

    /**
     * update a cart.
     * @param int $cartId Cart ID
     * @param int $currentUserId
     * @param int $siteId Cost center ID
     * @param int $addressId Specific Address for the cost center
     * @param null $buyerReferenceId
     * @param null $selectPaymentMode
     * @param array $documentsRequests
     * @return bool true if success
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function updateCart(
        $cartId,
        $currentUserId,
        $siteId,
        $addressId,
        $buyerReferenceId = null,
        $selectPaymentMode = null,
        array $documentsRequests = [],
        ?string $accountingEmail = null,
        ?Address $billingAddress = null,
    ): bool
    {
        $res = false;
        $siteRepository = $this->em->getRepository(Site::class);
        $addressRepository = $this->em->getRepository(Address::class);
        $currentUser = $this->userService->findById($currentUserId);

        $site = $siteRepository->find($siteId);
        $address = $addressRepository->find($addressId);

        if (!is_null($currentUser) && !is_null($site)) {
            // Adding new cart on DB if not present
            $cartDb = $this->cartRepository->find($cartId);
            if (is_null($cartDb)) {
                $cartDb = new \AppBundle\Entity\Cart();
                $cartDb->setCartId($cartId);
                $cartDb->setCreatedUser($currentUser);
            }
            $cartDb->setSite($site);
            $cartDb->setAddress($address);
            $cartDb->setValidationNumber($buyerReferenceId);
            $cartDb->setPaymentMode($selectPaymentMode);
            $cartDb->setDocumentsRequest($documentsRequests);
            $cartDb->setAccountingEmail($accountingEmail);
            $cartDb->setBillingAddress($billingAddress);

            $this->em->persist($cartDb);
            $this->em->flush();

            $res = true;
        }

        return $res;
    }

    /**
     * Update the cart buyer order Id
     * This feature is used by the purchase request
     *
     * @param User $user
     * @param int $cartId
     * @param string $buyerOrderId
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function updateCartBuyerOrderId(User $user, int $cartId, string $buyerOrderId)
    {
        $cart = $this->cartRepository->findOneBy(['createdUser' => $user, 'id' => $cartId]);

        if (!$cart) {
            $cart = new \AppBundle\Entity\Cart();
            $cart->setCartId($cartId);
            $cart->setCreatedUser($user);
        }

        $cart->setBuyerOrderId($buyerOrderId);
        $this->em->merge($cart);
        $this->em->flush();
    }


    /**
     * Reject cart assignation by buyer payer.
     * @param int $cartId Cart ID
     * @param string $comment
     * @param User $author
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws \AppBundle\Exception\MailException
     */
    public function rejectAssignCart($cartId, $comment, User $author): bool
    {
        $res = false;
        $cartUrl = $this->router->generate('cart.details.before_buy', ['currencyOrCartId' => $cartId], UrlGeneratorInterface::ABSOLUTE_URL);
        $cartDb = $this->cartRepository->find($cartId);

        /** @var User $createdUser */
        $createdUser = $cartDb->getCreatedUser();

        if ($cartDb) {
            /** @var \AppBundle\Entity\Cart|null $cartDb */
            // Update status from cart (we consider that he is still assigned)
            $cartDb->setStatus(\AppBundle\Entity\Cart::STATUS_REJECTED);
            $cartDb->setCurrentUser($createdUser);

            // Adding new historic
            $cartHistoric = new CartHistoric();
            $cartHistoric->setCart($cartDb);
            $cartHistoric->setComment($comment);
            $cartHistoric->setDate(new \DateTime());
            $cartHistoric->setUser($author);
            $cartHistoric->setAssignedUser($cartDb->getCreatedUser());
            $cartHistoric->setStatus(\AppBundle\Entity\Cart::STATUS_REJECTED);

            $this->em->persist($cartDb);
            $this->em->persist($cartHistoric);
            $this->em->flush();

            // Send mail to user that create cart
            $data = [
                'firstName' => $createdUser->getFirstname(),
                'lastName' => $createdUser->getLastname(),
                'recipientAssignation' => $this->getUsername($author),
                'comment' => $comment,
                'url' => $cartUrl,
                'cartId' => $cartDb->getCartId()
            ];
            if ($cartDb->getCreatedUser()->isEnabled()) {
                $this->mailService->sendEmailMessage(MailService::CART_REJECT_ASSIGN, $cartDb->getCreatedUser()->getLocale(), $cartDb->getCreatedUser()->getEmail(), $data);
            }

            $res = true;
        }
        return $res;
    }

    /**
     * Accept cart assignation by buyer payer.
     * @param int $cartId Cart ID
     */
    public function acceptAssignCart($cartId): void
    {
        /** @var \AppBundle\Entity\Cart $cartDb */
        $cartDb = $this->cartRepository->find($cartId);

        if (!is_null($cartDb)) {
            $data = [
                MailService::FIRST_NAME_VAR => $cartDb->getCreatedUser()->getFirstname(),
                MailService::LAST_NAME_VAR => $cartDb->getCreatedUser()->getLastname(),
                'recipientAssignation' => $this->getUsername($cartDb->getCurrentUser()),
                'cartId' => $cartDb->getCartId()
            ];
            if ($cartDb->getCreatedUser()->getId() !== $cartDb->getCurrentUser()->getId() && $cartDb->getCreatedUser()->isEnabled()) {
                $this->mailService->sendEmailMessage(
                    MailService::CART_ACCEPT_ASSIGN,
                    $cartDb->getCreatedUser()->getLocale(),
                    $cartDb->getCreatedUser()->getEmail(),
                    $data
                );
            }
        }
    }

    public function getDatabaseCart($cartId)
    {
        $cartRepository = $this->em->getRepository(\AppBundle\Entity\Cart::class);
        return $cartRepository->find($cartId);
    }

    public function createDatabaseCart($cartId, $user, $site = null): \AppBundle\Entity\Cart
    {
        // Adding new cart on DB if not present
        $cartRepository = $this->em->getRepository(\AppBundle\Entity\Cart::class);
        $cartDb = $cartRepository->find($cartId);

        if (is_null($cartDb)) {
            $cartDb = new \AppBundle\Entity\Cart();
            $cartDb->setCartId($cartId);
        }

        //at this point the cart has not been ordered:
        $cartDb->setStatus(\AppBundle\Entity\Cart::STATUS_CREATE);
        $cartDb->setSite($site);
        $cartDb->setCreatedUser($user);
        $cartDb->setCurrentUser($user);
        $this->em->persist($cartDb);

        // Adding new historic
        $cartHistoric = new CartHistoric();
        $cartHistoric->setCart($cartDb);
        $cartHistoric->setComment('Order created');
        $cartHistoric->setDate(new \DateTime());
        $cartHistoric->setAssignedUser($cartDb->getCurrentUser());
        $cartHistoric->setUser($user);
        $cartHistoric->setStatus('ORDER');
        $this->em->persist($cartHistoric);
        $this->em->flush();
        return $cartDb;
    }

    public function addShippingAddress(int $izbergCartId, int $izbergAddressId)
    {
        $this->cartApi->setShippingAddress($izbergCartId, $izbergAddressId);
    }

    public function addBillingAddress(int $izbergCartId, int $izbergAddressId)
    {
        $this->cartApi->setBillingAddress($izbergCartId, $izbergAddressId);
    }

    public function addDeliveryDateOnCartItem(\AppBundle\Model\Cart\Cart $cart)
    {
        $cartItemDeliveryDates = array_map(
            function (\AppBundle\Model\Cart\CartItem $cartItem) {
                if (is_null($cartItem->getDeliveryTime()) || is_null($cartItem->getTrueDeliveryTime())) {
                    return null;
                }

                $today = new \DateTimeImmutable();
                $expectedDeliveryDate = $today->modify('+' . $cartItem->getDeliveryTime() . ' day');
                $expectedShippingDate = $today->modify('+' . $cartItem->getDeliveryTime() . ' day');
                $expectedShippingDate = $expectedShippingDate->modify('-' . $cartItem->getTrueDeliveryTime() . ' day');

                return [$cartItem->getId(), $expectedDeliveryDate, $expectedShippingDate];
            },
            $cart->getItems()
        );

        $this->cartApi->setDeliveryDates($cartItemDeliveryDates);
    }

    /**
     * @param $cartId
     * @param string $paymentMode
     * @param string $izbergPaymentModeId
     * @throws ApiException
     */
    public function setPaymentMode($cartId, $paymentMode, $izbergPaymentModeId)
    {
        if ($paymentMode === PaymentModeSelectForm::PAYMENT_PRE_CARD) {
            $this->cartApi->setPaymentMode($cartId, 'prepayment', 'cb');
        } else if ($paymentMode === PaymentModeSelectForm::PAYMENT_PRE_WIRE) {
            $this->cartApi->setPaymentMode($cartId, 'prepayment', 'bankwire');
        } else if ($izbergPaymentModeId !== null) {
            $this->cartApi->setPaymentMode($cartId, 'term_payment', null, $izbergPaymentModeId);
        }
    }


    /**
     * @param User $user
     * @param int $cartId
     * @return bool
     */
    public function isUserValidAssign(User $user, $cartId): bool
    {
        /** @var \AppBundle\Entity\Cart $cartDb */
        $cartDb = $this->cartRepository->find($cartId);
        if (!is_null($cartDb) && $cartDb->getCurrentUser() !== null) {
            return ($cartDb->getCurrentUser()->getId() === $user->getId());
        }
        return false;
    }

    public function isUserCartCreator(User $user, int $cartId): bool
    {
        /** @var \AppBundle\Entity\Cart $cartDb */
        $cartDb = $this->cartRepository->find($cartId);
        if (!is_null($cartDb) && $cartDb->getCreatedUser() !== null) {
            return $cartDb->getCreatedUser()->getId() === $user->getId();
        }
        return false;
    }

    /**
     * @param User $user
     * @param int $cartId
     * @return bool
     */
    public function isUserAuthorizeReject(User $user, $cartId): bool
    {
        $cartDb = $this->cartRepository->find($cartId);
        if (
            !is_null($cartDb)
            && $cartDb->getCurrentUser() !== null
            && $cartDb->getCreatedUser() !== null
        ) {
            return ($cartDb->getCurrentUser()->getId() === $user->getId()
                    && $cartDb->getCreatedUser()->getId() !== $user->getId());
        }

        return false;
    }

    /**
     * @param User $user
     * @return bool
     */
    public function isUserAuthorizedToBuy(User $user)
    {
        return strpos($user->getRole(), 'BUYER_PAYER') !== false ||
            strpos($user->getRole(), 'BUYER_ADMIN') !== false;
    }

    /**
     * Get historical information for a cart with his IS
     * @param int $cartId cart ID
     * @return array
     */
    public function getCartHistoric($cartId): array
    {
        /** @var CartHistoricRepository $cartRepo */
        $cartRepo = $this->em->getRepository(CartHistoric::class);
        return $cartRepo->getCartHistoric($cartId);
    }

    public function getCartAssignmentHistoric($cartId): array
    {
        /** @var CartHistoricRepository $cartRepo */
        $cartRepo = $this->em->getRepository(CartHistoric::class);
        return $cartRepo->getCartAssignmentHistoric($cartId);
    }

    public function isUserInAssignmentChains(User $user, ?int $cartId): bool
    {

        //this is the cart of the user, so we consider that the assignment process is not started
        if ($cartId === null) {
            return false;
        }

        $histories = $this->getCartAssignmentHistoric($cartId);
        if (empty($histories)) {
            return true; //no history, so current user is the creator
        }

        /** @var CartHistoric $history */
        foreach ($this->getCartAssignmentHistoric($cartId) as $history) {
            if ($history->getUser()->getId() === $user->getId()) {
                return true;
            }
        }

        return false;
    }

    /**
     * Return user name: firstname lastname
     * @param User $user
     * @return string user name
     */
    private function getUsername(User $user): string
    {
        return $user->getFirstname() . ' ' . $user->getLastname();
    }

    public function createCart($currency)
    {
        return $this->cartApi->createCart($currency);
    }

    public function addProductToCart($cartId, $offerId, $currency)
    {
        $this->cartApi->addProductOffer($cartId, $offerId, $currency);
    }

    public function getCartByWPSTransactionId($transactionId)
    {
        /** @var CartRepository $cartRepo */
        $cartRepo = $this->em->getRepository(\AppBundle\Entity\Cart::class);
        return $cartRepo->getCartByWPSTransactionId($transactionId);
    }

    public function getCartByOrderId($orderId)
    {
        /** @var CartRepository $cartRepo */
        $cartRepo = $this->em->getRepository(\AppBundle\Entity\Cart::class);
        return $cartRepo->getCartByOrderId($orderId);
    }


    /**
     * get the list of pending carts for the specified user
     *   rules:
     *             - if user is admin, returns all the pending carts of the company
     *             - otherwise get all the pending carts for which the user is the author or the recipient of the assignation
     *
     * @param User $user
     * @return array the list of pending carts for the specified user
     *
     */
    public function getPendingCartByUser(User $user)
    {
        /** @var CartHistoricRepository $cartHistoryRepository */
        $cartHistoricRepository = $this->em->getRepository(CartHistoric::class);

        if ($this->securityService->isAdminCompany($user)) {
            //return all the pending carts of the company
            $carts = $this->loadCarts($cartHistoricRepository->getAllPendingCartForCompany($user->getCompany()->getId()));
        } else {
            //return all the carts for which the user is in the assignation chains
            $carts = $this->loadCarts($cartHistoricRepository->getAllPendingCartForUser($user->getId()));
        }

        return array_filter(
            array_map(
                function (?\AppBundle\Entity\Cart $cart) use ($user) {
                    if (!$cart) {
                        return null;
                    }
                    $history = $this->getCartHistoric($cart->getId());
                    $cartModel = $this->getCart($cart->getId(), $user->getCompany(), $user);
                    $cartModel = $this->cartFactory->decorateWithShipping($cartModel);

                    return (object)[
                        'generalInfo' => $cartModel,
                        'pendingInfo' => $cart,
                        'history' => $history,
                        'userRole' => $cart->getCurrentUser()->getRole()
                    ];
                },
                $carts
            ),
            function ($object) {
                if (!$object) {
                    return false;
                }

                return (
                    $object->pendingInfo->getStatus() === \AppBundle\Entity\Cart::STATUS_ASSIGN
                    || $object->pendingInfo->getStatus() === \AppBundle\Entity\Cart::STATUS_REJECTED
                    || (
                        $object->pendingInfo->getStatus() === \AppBundle\Entity\Cart::STATUS_CREATE
                        && count($object->history) > 0
                    )
                );
            }
        );
    }

    private function loadCarts(array $cartIds): array
    {
        return array_map(
            function ($cartId) {
                return $this->getCartDb($cartId);
            },
            $cartIds
        );
    }

    public function totalItemsInCart(Cart $cart): int
    {
        return (!is_null($cart->getItems())) ? count($cart->getItems()) : 0;
    }

    public function findLostCartItems(int $izbergCartId)
    {
        $lostCartItems = [];
        $cart = $this->cartApi->fetchIzbergCart($izbergCartId);

        /** @var CartItem $cartItem */
        foreach ($cart->getItems() as $cartItem) {
            $offer = $this->offerService->findOfferById($cartItem->getOffer()->getId());

            if (!$offer) {
                $lostCartItems[] = $cartItem;
            }
        }

        return $lostCartItems;
    }

    public function findCartAddressId(int $cartId): ?int
    {
        $cartEntity = $this->cartRepository->find($cartId);
        if ($cartEntity === null) {
            return null;
        }

        $address = $cartEntity->getAddress();
        if ($address === null) {
            return null;
        }

        return $address->getId();
    }

    public function removeLostCartItemsFromCart(\AppBundle\Model\Cart\Cart $cart): array
    {
        return array_map(
            function (\AppBundle\Model\Cart\CartItem $lostCartItem) {
                $this->removeItem($lostCartItem->getId());

                return $lostCartItem;
            },
            $cart->fetchLostCartItems()
        );
    }

    /**
     * @param \AppBundle\Model\Cart\Cart $cart
     * @param User $user
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function syncTotalItemFromCart(\AppBundle\Model\Cart\Cart $cart, User $user)
    {
        $numberOfItemInCart = count($cart->getItems());

        if ($cart->getCurrency() == 'EUR') {
            $user->setItemInCartEUR($numberOfItemInCart);
        }

        if ($cart->getCurrency() == 'USD') {
            $user->setItemInCartUSD($numberOfItemInCart);
        }

        $this->em->merge($user);
        $this->em->flush();
    }

    public function getCountPendingCartByUserId($userId): int
    {
        return count($this->cartRepository
            ->getPendingCartByUserId($userId));
    }

    /**
     * @param Order $order
     * @param User $user
     * @return AddItemToCartResponse
     * @throws \Exception
     */
    public function addOrderItemsToNewUserCart(Order $order, User $user): AddItemToCartResponse
    {
        $serviceResponse = new AddItemToCartResponse();

        $cart = $this->createUserCart($user, $order->getCurrency());

        if (!$cart) {
            throw new \Exception('cannot create user cart');
        }

        $offersArray = $this->offerService->findOffersByOrder($order);
        $offers = [];

        /** @var Offer $offer */
        foreach($offersArray as $offer) {
            $izbergReference = $offer->getIzbergReference();
            if ($izbergReference) {
                $offers[$izbergReference] = $this->specificPriceService->updateOfferSpecificPrices(
                    $user->getCompany(), $offer
                );
            }
        }

        $orderItemsToAdd = [];

        /** @var OrderItem $orderItem */
        foreach ($order->getItems() as $orderItem) {
            /** @var Offer|null $offer */
            $offer = null;

            if (isset($offers[(string)$orderItem->getOfferId()])) {
                $offer = $offers[(string)$orderItem->getOfferId()];
            }

            if ($offer === null) {
                $serviceResponse->addErrorOffer($orderItem->getOfferId());
                continue;
            }

            if (!($offer->getStatus() == 'active' && $offer->getMerchant()->getStatus() == '10')) {
                $serviceResponse->addErrorStatus($offer);
                continue;
            }

            if ($offer->getQuantity() < $orderItem->getQuantity()) {
                $serviceResponse->addErrorStock($offer);
                continue;
            }

            if ($offer->getMoq() > $orderItem->getQuantity()) {
                $serviceResponse->addErrorMoq($offer);
                continue;
            }

            $price = $this->specificPriceService->specificPrice($user->getCompany(), $offer, $orderItem->getQuantity());

            if ($offer->hasNoPrice()) {
                $serviceResponse->addErrorNoPrice($offer);
                continue;
            }

            if ($price != $orderItem->getPrice()) {
                $serviceResponse->addErrorPrice($offer);
            }

            $orderItemsToAdd[] = $this->cartFactory->buildCartItemFromOrderItem($orderItem, $price, $offer);

            $serviceResponse->addCartItem();

        }

        $this->addItemsToCart(
            $cart,
            $orderItemsToAdd
        );

        return $serviceResponse;
    }

    public function addItemsToCart(\AppBundle\Model\Cart\Cart $cart, array $items)
    {
        $this->cartApi->addProductOffers($cart->getId(), $items);
        $this->cartFactory->addItemsToCart($cart, $items);
    }

    /**
     * @deprecated seems unused
     */
    public function addOrderItemToCart(\AppBundle\Model\Cart\Cart $cart, Offer $offer, int $quantity, float $price)
    {
        $this->cartApi->addProductOffer(
            $cart->getId(),
            intval($offer->getIzbergReference()),
            $offer->getCurrency(),
            $quantity,
            $price,
            $this->customsService->taxRateToApplyFromOffer($offer)
        );
    }

    /**
     * @param WishList $wishList
     * @param User $user
     * @return AddItemToCartResponse
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function addWishListItemsToUserCart(WishList $wishList, User $user)
    {
        $serviceResponse = new AddItemToCartResponse();

        $cart = $this->createUserCart($user, $wishList->getCurrency());

        if (!$cart) {
            throw new \Exception('cannot create user cart');
        }

        $offersArray = $this->offerService->findOffersByWishList($wishList);
        $offers = [];

        /** @var Offer $offer */
        foreach($offersArray as $offer) {
            $izbergReference = $offer->getIzbergReference();
            $offers[$izbergReference] = $this->specificPriceService->updateOfferSpecificPrices(
                $user->getCompany(), $offer
            );
        }

        $wishListItemToAdd = [];

        /** @var WishListItem $wishListItem */
        foreach ($wishList->getItems() as $wishListItem) {
            if (!isset($offers[$wishListItem->getOfferId()])) {
                $serviceResponse->addErrorOffer(intval($wishListItem->getOfferId()));
                continue;
            }

            /** @var Offer $offer */
            $offer = $offers[$wishListItem->getOfferId()];
            if (!($offer->getStatus() == 'active' && $offer->getMerchant()->getStatus() == '10')) {
                $serviceResponse->addErrorStatus($offer);
                continue;
            }

            if ($offer->getQuantity() < $wishListItem->getQuantity()) {
                $serviceResponse->addErrorStock($offer);
                continue;
            }

            if ($offer->getMoq() > $wishListItem->getQuantity()) {
                $serviceResponse->addErrorMoq($offer);
                continue;
            }

            $price = $this->specificPriceService->specificPrice($user->getCompany(), $offer, $wishListItem->getQuantity());


            $wishListItemToAdd[] = $this->cartFactory->buildCartItemFromWishListItem($wishListItem, $price, $offer, $wishList->getCurrency());

            $serviceResponse->addCartItem();
        }

        $this->addItemsToCart(
            $cart,
            $wishListItemToAdd
        );


        if ($serviceResponse->getTotal() > 0) {
            $this->syncTotalItemFromCart($cart, $user);
        }

        return $serviceResponse;
    }

    public function updateCartInfoBeforePayment(
        \AppBundle\Model\Cart\Cart $cart,
        User $user
    )
    {
        $company = $user->getCompany();

        // BUG FIX sanatize izbergAddressId
        $izbergAddressIdSanatizer = function ($izbergAddressId): ?int {
            if (preg_match('/^[0-9]+$/', $izbergAddressId)) {
                $izbergAddressId = intval($izbergAddressId);
            } else {
                $izbergAddressId = null;
            }

            return $izbergAddressId;
        };

        $shippingAddressId = call_user_func($izbergAddressIdSanatizer, $cart->getAddress()->getIzbergAddressId());
        if ($shippingAddressId === null) {
            $this->addressService->createIzbergShippingAddress(
                $cart->getAddress(),
                $company->getMainAddress()->getCountry()
            );
        }

        $billingAddressId = call_user_func($izbergAddressIdSanatizer, $cart->getBillingAddress()->getIzbergAddressId());
        if ($billingAddressId === null) {
            $this->addressService->createIzbergBillingAddress(
                $cart->getBillingAddress(),
                $company->getMainAddress()->getCountry()
            );
        }

        // update incoterm in cart item extra info
        // need cart item shipping info to update incoterm
        $cart = $this->cartFactory->decorateWithShipping($cart);
        /** @var CartMerchant $cartMerchant */
        foreach ($cart->getMerchants() as $cartMerchant) {
            /** @var \AppBundle\Model\Cart\CartItem $cartItem */
            foreach ($cartMerchant->getItems() as $cartItem) {
                $incoterm = strtoupper($cartItem->getIncoterm());
                $countryOfDelivery = $cartItem->getCountryOfDelivery();

                if (
                    $incoterm == 'FCA' &&
                    !$cartItem->isDangerousProduct() &&
                    $cartMerchant->isUpelaActive() &&
                    $cartMerchant->isShippable() &&
                    $cartItem->isToBeShipped()
                ) {
                    $incoterm = sprintf('FCA + shipment = DAP (%s)', $cart->getAddress()->getCountry()->getIzbFcaCountry());
                } else {
                    $incoterm = sprintf('%s (%s)', $incoterm, $countryOfDelivery);
                }

                $this->updateCartItemExtraInfo($cartItem->getId(), 'Incoterm', $incoterm);
            }
        }

        // SHIPPING-INVOICE create product offer shipping upela
        // SHIPPING-INVOICE add offer to cart and overide quantity and price for each shipment
        //          - name shipping-upela #1 - shipping option ID
        //          - quantity is 1
        //          - price is shipping option price

        // go over all shipping options
        /*
        $shippingOptions = $this->cartApi->fetchCartShippingOptions($cart->getId());
        foreach ($shippingOptions as $shippingOption) {

            $merchantId = $shippingOption->merchant->id;
            $shippingOfferOptions = $shippingOption->shipping_choices[0]->options;
            $priceTe = $shippingOfferOptions->offer->priceTe;
            $priceTi = $shippingOfferOptions->offer->priceTi;
            $vatRate = $shippingOfferOptions->offer->vatRate;
            $town = $shippingOfferOptions->offer->city ?? '';
            $shipmentDate = \DateTimeImmutable::createFromFormat('Y-m-d', $shippingOfferOptions->offer->shipmentDate);
            $shipmentId = $shippingOfferOptions->shipmentId;
            $parcelType = $shippingOfferOptions->offer->parcelType;

            // create product offer for given shipping option
            $shippingOfferId = $this->shippingService->createShippingOffer(
                $merchantId,
                $priceTe,
                $shipmentId,
                $town,
                $shipmentDate,
                $parcelType
            );

            // add product offer to cart
            $this->cartApi->addProductOffer(
                $cart->getId(),
                $shippingOfferId,
                $cart->getCurrency(),
                1,
                $priceTe,
                $vatRate
            );
        }
        */

        $data = [
            'selected_payment_type' => $cart->getPaymentType(),
            'selected_payment_method' => $cart->getPaymentMethod(),
            'shipping_address' => '/v' . $this->cartApi->getVersion() . '/address/' . $cart->getAddress()->getIzbergAddressId() . '/',
            'billing_address' => '/v' . $this->cartApi->getVersion() . '/address/' . $cart->getBillingAddress()->getIzbergAddressId() . '/',
        ];

        $this->cartApi->patch(
            $cart->getId(),
            $data
        );

        if ($cart->getPaymentMode() === PaymentModeSelectForm::PAYMENT_TERM) {

            $cartItems = $this->cartApi->getCartItems($cart->getId());

            $this->cartApi->sendConcurrentApiRequest(
                array_map(
                    function (CartItem $cartItem) use ($cart) {
                        return new ApiRequestParameter(
                            Api::HTTP_PATCH_OPERATION,
                            CartApi::CART_ITEM_PATH . intval($cartItem->getId()) . '/',
                            [
                                'selected_payment_term' => '/v1/payment_term/' . $cart->getPaymentTermIzbergId() . '/',
                            ]
                        );
                    },
                    $cartItems->toArray()
                ),
                true,
                5,
                [
                    CartApi::OPTION_NO_DATA_RETURN => true,
                ]
            );
        }
    }

    /**
     * @param User $user
     * @param int $cartId
     * @param string|null $currency
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function clearUserCurrentCarts(User $user, int $cartId, string $currency = null)
    {
        if ($cartId != null && ($cartId === $user->getCartUSDId() || $cartId === $user->getCartEURId())) {
            if ($currency && strlen($currency) > 0) {
                if ($currency == 'USD') {
                    $user->setCartUSDId(null);
                    $user->setItemInCartUSD(0);
                } else if ($currency == 'EUR') {
                    $user->setCartEURId(null);
                    $user->setItemInCartEUR(0);
                }
            } else {
                $user->setCartUSDId(null);
                $user->setItemInCartUSD(0);

                $user->setCartEURId(null);
                $user->setItemInCartEUR(0);
            }

            $this->em->persist($user);
            $this->em->flush();
        }
    }

    /**
     * @param User $user
     * @param \AppBundle\Model\Cart\Cart $cart
     * @return \AppBundle\Model\Cart\Cart
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function revertCart(User $user, \AppBundle\Model\Cart\Cart $cart): \AppBundle\Model\Cart\Cart
    {
        $company = $user->getCompany();

        if ($cart->getOrder() !== null) {
            $this->orderService->cancelOrder($cart->getOrder());
        }

        $newCart = $this->createCart($cart->getCurrency());
        $newCart = $this->findCart($user, $newCart->getId());

        /** @var \AppBundle\Model\Cart\CartItem $cartItem */
        foreach ($cart->getItems() as $cartItem) {
            $offer = $this->offerService->findDetailedOfferById($cartItem->getOfferId(), $company);
            $this->addOfferToCart(
                $offer->getOffer(),
                $user,
                intval($cartItem->getQuantity()),
                $newCart->getId()
            );
        }

        $this->copyCartDb($this->getCartDb($cart->getId()), $newCart->getId());

        //update the user current cart
        if ($cart->getCurrency() === 'EUR') {
            $user->setCartEURId(strval($newCart->getId()));
        } else {
            $user->setCartUSDId(strval($newCart->getId()));
        }

        $this->userService->updateUser($user);

        return $cart;
    }

    public function createOrderFromCart(
        \AppBundle\Model\Cart\Cart $cart,
        User $user,
        ?string $validationNumber = null,
        ?string $accountingEmail = null
    ): Order
    {
        $order = $this->orderService->createOrderFromCart($cart, $user, $validationNumber, $accountingEmail);
        $order->setCreatedByApi($cart->isApiSource());
        $order->setAccountingEmail($accountingEmail);
        $cart->setOrder($order);

        $merchantOrderIds = array_map(
            function (MerchantOrder $merchantOrder) {
                return $merchantOrder->getId();
            },
            $order->getMerchantOrders()
        );

        // create merchant order pdf
        foreach ($merchantOrderIds as $merchantOrderId) {
            $this->merchantOrderService->uploadMerchantOrderPdfToMerchant($merchantOrderId);
        }

        $this->cartRepository->saveCartOrder($cart);

        return $order;
    }

    public function checkoutCart(
        \AppBundle\Model\Cart\Cart $cart,
        User $user,
        $shippingAddressId,
        $paymentMode,
        $locale,
        ?Address $billingAddress = null
    ): void {
        $company = $user->getCompany();

        $validTerms = [
            PaymentModeSelectForm::PAYMENT_PRE_CARD,
            PaymentModeSelectForm::PAYMENT_PRE_WIRE,
            PaymentModeSelectForm::PAYMENT_TERM,
        ];

        if (!in_array($paymentMode, $validTerms)) {
            throw new \InvalidArgumentException(
                sprintf(
                    'Invalid term selected %s, only those values can be used: %s',
                    $paymentMode,
                    implode(', ', $validTerms)
                )
            );
        }

        if (
            $paymentMode === PaymentModeSelectForm::PAYMENT_TERM
            && ($user->getCompany() === null || !$user->getCompany()->getTermpaymentMoneyTransfertEnabled())
        ) {
            throw new AccessDeniedException("selected mode is " . $paymentMode . " but user with id " . $user->getId() . " is not authorized to use this mode");
        }

        $cart->setPaymentMode($paymentMode);
        $cart->setPaymentType('prepayment');
        $cart->setPaymentMethod('cb');

        $izbergTerm = null;

        if ($cart->getPaymentMode() === PaymentModeSelectForm::PAYMENT_PRE_WIRE) {
            $cart->setPaymentMethod('bankwire');
        }

        if ($cart->getPaymentMode() === PaymentModeSelectForm::PAYMENT_TERM) {
            $cart->setPaymentType('term_payment');
            $cart->setPaymentMethod(null);

            $izbergTerm = $this->paymentService->fetchPaymentTerm($locale);
            $cart->setPaymentTerm($izbergTerm->getLocalizedName());
            $cart->setPaymentTermIzbergId($izbergTerm->getId());
        }

        if ($shippingAddressId === null) {
            throw new BadRequestHttpException("addressId parameter is mandatory");
        }

        $shippingAddress = $this->addressService->get($shippingAddressId);

        //check if address exist
        if ($shippingAddress === null) {
            throw new BadRequestHttpException("Address with id " . $shippingAddressId . " doesn't exist");
        }

        //check if address is owned by the user
        /** @var Site $site */
        $site = $shippingAddress->getShippingPoints()->get(0)->getSite();

        /** @var ShippingPoint $shippingPoint */
        $shippingPoint = $this->shippingPointRepository->findOneBy(['site' => $site, 'address' => $shippingAddress]);

        if ($site->getCompany()->getId() !== $company->getId()) {
            throw new \Symfony\Component\Finder\Exception\AccessDeniedException(
                "the address with id " . $shippingAddressId . " is not owned by the user with id " . $user->getId()
            );
        }

        if ($billingAddress === null) {
            $billingAddress = $this->addressService->getCompanyBillingAddress($site->getCompany());
        }

        /**
         * @var \AppBundle\Entity\Cart $cartDB
         */
        $cartDB = $this->getDatabaseCart($cart->getId());

        if ($cartDB === null) {
            $cartDB = $this->createDatabaseCart(
                $cart->getId(),
                $user,
                $site
            );
        }

        $cartDB->setSite($site);
        $cartDB->setPaymentTermIzbergId($cart->getPaymentTermIzbergId());
        $cartDB->setPaymentTerm($cart->getPaymentTerm());
        $cartDB->setPaymentType($cart->getPaymentType());
        $cartDB->setPaymentMethod($cart->getPaymentMethod());
        $cartDB->setPaymentMode($cart->getPaymentMode());
        $cartDB->setAddress($shippingAddress);
        $cartDB->setBillingAddress($billingAddress);
        $cartDB->setStatus(\AppBundle\Entity\Cart::STATUS_ORDER);

        $cart->setaddress($shippingAddress);
        $cart->setBillingAddress($billingAddress);
        $cart->setShippingPoint($shippingPoint);

        $this->acceptAssignCart($cart->getId());

        // Adding new historic
        $cartHistoric = new CartHistoric();
        $cartHistoric->setCart($cartDB);
        $cartHistoric->setComment("checkout proceed by buyer or account creator");
        $cartHistoric->setDate(new \DateTime());
        $cartHistoric->setUser($user);
        $cartHistoric->setAssignedUser($user);
        $cartHistoric->setStatus(\AppBundle\Entity\Cart::STATUS_ORDER);

        $this->em->persist($cartHistoric);
        $this->em->flush();
    }

    public function saveDatabaseCart(\AppBundle\Entity\Cart $cart)
    {
        $this->cartRepository->save($cart);
    }

    public function splitDeliveryCart($merchantItem, $locale, $cartId, $user, $forceUpdate = false)
    {
        // Split delivery automatically
        if (empty($merchantItem->qty_greater_real_stock)) {
            return;
        }

        if (!$forceUpdate && !empty($merchantItem->getCartItemSplitDelivery())) {
            return;
        }

        $format = $locale == "fr" ? 'd/m/Y' : 'm/d/Y';

        $deliveryDate = (new DateTimeImmutable())->add(
            (new DateInterval(sprintf('P%dD', $merchantItem->getDeliveryTime())))
        );
        $deliveryDate = $deliveryDate->format($format);

        $deliveryDateDelayBeforeShipping = (new DateTimeImmutable())->add(
            (new DateInterval(sprintf('P%dD', $merchantItem->getDeliveryTimeDelayBeforeShipping())))
        );
        $deliveryDateDelayBeforeShipping = $deliveryDateDelayBeforeShipping->format($format);

        $value = [
            'on_stock_delivery' => [
                'date' => $deliveryDateDelayBeforeShipping,
                'quantity' => $merchantItem->quantity_on_stock,
            ],
            'demand_delivery' => [
                'date' => $deliveryDate,
                'quantity' => $merchantItem->quantity_demand,
            ],
        ];

        $this->updateCartItemExtraInfo(intval($merchantItem->getId()), "cart-item-split-delivery", json_encode($value, JSON_UNESCAPED_SLASHES));
        $merchantItem->setCartItemSplitDelivery($value);

        $this->addOfferToCart(
            $merchantItem->getOffer(),
            $user,
            intval($merchantItem->getQuantity()),
            $cartId,
            true
        );
    }
}
