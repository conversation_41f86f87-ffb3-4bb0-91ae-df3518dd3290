<?php

namespace AppBundle\Services;

use AppBundle\Model\Offer;
use AppBundle\StreamFilter\StreamFilterNewlines;
use Open\IzbergBundle\Algolia\AlgoliaField;
use Open\IzbergBundle\Algolia\AlgoliaQueryParams;
use Open\IzbergBundle\Algolia\AlgoliaServiceEn;
use Open\IzbergBundle\Api\AttributeApi;

/**
 * Class OfferCatalogService
 * @package AppBundle\Services
 */
class OfferCatalogService
{
    const ALLOWED_TYPE = ["active","inactive","draft"];
    /** @var AlgoliaServiceEn */
    private $algoliaServiceEn;

    /**
     * @var OfferService
     */
    private $offerService;

    /** @var array */
    private $customAttributes;

    /** @var AttributeApi */
    private $attributeApi;

    public function __construct(AlgoliaServiceEn $algoliaServiceEn, OfferService $offerService, AttributeApi $attributeApi, array $customAttributes = [])
    {
        $this->algoliaServiceEn = $algoliaServiceEn;
        $this->offerService = $offerService;
        $this->customAttributes = $customAttributes;
        $this->attributeApi = $attributeApi;
    }

    /**
     * Export the merchant offer catalog
     *
     * @param bool $isOperator
     * @param int|null $merchantId
     * @param string|null $locale
     * @param string|null $type
     */
    public function export(bool $isOperator = true,?int $merchantId = null, ?string $locale = null, ?string $type = null)
    {
        set_time_limit ( 0 );
        $type = $this->checkType($type);
        set_time_limit(0);
        $keys = $this->getKeyFromTemplate($isOperator);
        if($keys === null){
            $keys = $this->getDefaultKey($locale);
        }

        $fileHandle = fopen('php://output', 'w');

        stream_filter_register("newlines", StreamFilterNewlines::class);
        stream_filter_append($fileHandle, "newlines");

        fwrite($fileHandle, $bom = (chr(0xEF).chr(0xBB).chr(0xBF)));
        fputcsv( $fileHandle, $keys, ';');

        $queryParams = (new AlgoliaQueryParams())->addFacetFilters(
            AlgoliaField::STATUS . ':'.$type,
            AlgoliaField::MERCHANT_STATUS . ':10'
        );

        if ($this->algoliaServiceEn->getTransportCategory()) {
            $queryParams->addNotFilters(AlgoliaField::PRODUCT_CATEGORY . ':' . $this->algoliaServiceEn->getTransportCategory());
        }

        if ($merchantId) {
            $queryParams->addNumericFilters(sprintf('merchant.id=%d', $merchantId));
        }

        /**
         * !!!!!! WARNING !!!!!!
         *
         * To export the catalogue from algolia we use the method browse \Open\IzbergBundle\Algolia\AlgoliaService::browse
         * This method uses the primary index of algolia which it isn't configured automatically like the replica
         * If you use some new algolia filters they need to be configured manually on the primary indexes
         *
         */

        if($locale){
            $this->exportForLocale($locale, $queryParams, $fileHandle, $keys, count($keys));
        }else{
            foreach ($this->offerService->getAllLocale() as $locale) {
                $this->exportForLocale($locale, $queryParams, $fileHandle, $keys, count($keys));
          }
        }
        fclose($fileHandle);
    }

    /**
     * this function will get the key for the catalog export from a template.
     *
     * @return array|null
     */
    private function getKeyFromTemplate(bool $isOperator):?array{
        $template = $_ENV['EXPORT_CATALOGUE_TEMPLATE_PATH'];
        $key = null;
        $defaultKey = [];
        if($isOperator){
            $defaultKey = ['offer id','merchant id', 'merchant name'];
        }
        if(file_exists($template)){
            //Or we will get the last modification date
            $fp = fopen($template, "r");
            $key = fgetcsv($fp,0,';');
            fclose($fp);
        }

        return array_merge($defaultKey, $key);
    }

    /**
     * this function will get the default key if no template is found.
     *
     * @param string $locale
     * @return array
     */
    private function getDefaultKey(string $locale):array{
        $customAttributeKeys = $this->getOrderedCustomAttribute($locale);
        $keys = array_merge([
            'sku',
            'offer id',
            'merchant id',
            'merchant name',
            'price',
            'currency',
            'stock',
            'restock_date',
            'package_height',
            'package_width',
            'package_length',
            'package_weight',
            'offer title',
            'description',
            'manufacturer name',
            'application_categories',
            'images',
            'keywords',
            'GTIN'

        ],$customAttributeKeys );
        return $keys;
    }

    /**
     * @param string|null $type
     * @return string
     */
    private function checkType(?string $type): string{
        if($type ===null || (!in_array($type,self::ALLOWED_TYPE))){
            return "active";
        }
        return $type;
    }

    private function getPackage ($tab, $key){
        if(isset($tab) && key_exists($key, $tab)){
            $subTab = $tab[$key];
            if(isset($subTab) && count($subTab)>0){
                return array_pop($subTab);
            }
        }
        return '';
    }

    private function exportForLocale ($locale,$queryParams, $fileHandle, $keys, $lineSize ){

        $thereIsData = false;
        foreach ($this->offerService->getAlgoliaBrowseIterator($locale,$queryParams) as $hit) {
            $thereIsData = true;

            fputcsv(
                $fileHandle,
                $this->getAttributeValues($hit, $keys)
                ,
                ';'
            );

        }

        if($thereIsData){
            $this->addEmptyLines($fileHandle,$lineSize);
        }
    }

    /**
     * this function will build the content of the export.
     *
     * @param array $hit
     * @param array $keys
     * @return array
     */
    private function getAttributeValues(array $hit, array $keys):array{
        $product = $hit['product'] ?? [];
        $currency = $hit['currency'] ?? 'EUR';
        /** @var Offer $offer */
        $offer = $this->offerService->buildOfferFromAlgoliaResult($hit, false);
        $offerAttributes = $hit['attributes'] ?? [];
        $attributes = [];
        for($i = 0; $i<Count($keys); $i++){
            switch ($keys[$i]) {
                case 'sku':
                    $attributes[$i]=$hit['sku'] ?? '';
                    break;
                case 'offer id':
                    $attributes[$i]=$hit['id'] ?? '';
                    break;
                case 'manufacturer':
                    $attributes[$i]=$offer->getManufacturerName();
                    break;
                case 'merchant id':
                    $attributes[$i]=$offer->getMerchant()->getId();
                    break;
                case 'merchant name':
                    $attributes[$i]=$offer->getMerchant()->getName();
                    break;
                case 'price':
                    $attributes[$i]=$offer->getPrices()[$currency] ?? null;
                    break;
                case 'currency':
                    $attributes[$i]=$currency;
                    break;
                case 'stock':
                    $attributes[$i]=$offer->getQuantity();
                    break;
                case 'restock_date':
                    $attributes[$i]=$hit['restock_date'] ?? '';
                    break;
                case 'package_height':
                    $attributes[$i]=$this->getPackage($product,'package_height' );
                    break;
                case 'package_width':
                    $attributes[$i]=$this->getPackage($product,'package_width' );
                    break;
                case 'package_length':
                    $attributes[$i]=$this->getPackage($product,'package_length' );
                    break;
                case 'package_weight':
                    $attributes[$i]=$this->getPackage($product, 'package_weight' );
                    break;
                case 'offer title':
                    $attributes[$i]=$offer->getOfferTitle();
                    break;
                case 'description':
                    $attributes[$i]=$offer->getShortDescription();
                    break;
                case 'manufacturer name':
                    $attributes[$i]=$offer->getManufacturerName();
                    break;
                case 'application_categories':
                    $attributes[$i]=$this->getCategories($product);
                    break;
                case 'images':
                    $pics = $offer->getOfferPictures();
                    $picArrayFiltered = [];
                    foreach($pics as $pic){
                        if($pic!== "images/no_image_available.svg"){
                            $picArrayFiltered[]=$pic;
                        }
                    }

                    $attributes[$i]=join(',',$picArrayFiltered);
                    break;
                case 'keywords':
                    $attributes[$i]=join(',',$product['keywords']??[]);
                    break;
                case 'GTIN':
                    $attributes[$i]=$hit['gtin'] ?? '';
                    break;
                case 'name':
                    $attributes[$i]=$hit['name'] ?? '';
                break;
                default:
                    $attributes[$i]=$offerAttributes[$keys[$i]] ?? '';
                    break;

            }
        }
        return $attributes;
    }

    private function addEmptyLines ($fileHandle, $lineSize){
        $emptyLine = array_fill(0,$lineSize-1,'');
        fputcsv($fileHandle, $emptyLine,';');
        fputcsv($fileHandle, $emptyLine,';');
    }


    /**
     *  application_categories must be the leaf id of the catalog tree.
     *  see https://jira.open-groupe.com/browse/S1EVOL-256
     * @param $product
     * @return string
     */
    private function getCategories($product)
    {
        return end ($product['application_categories']);

    }

    private function getOrderedCustomAttribute ($locale = "en")
    {
        $attributeKeys = array_keys($this->attributeApi->getAttributes($locale));
        sort ($attributeKeys);
        return $attributeKeys;

    }
}
