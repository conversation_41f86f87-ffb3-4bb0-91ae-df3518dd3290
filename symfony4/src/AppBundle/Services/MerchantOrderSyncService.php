<?php

namespace AppBundle\Services;

use AppBundle\Repository\MerchantOrderRepository;
use Mpdf\MpdfException;
use Mpdf\Output\Destination;
use Open\IzbergBundle\Api\OrderApi;
use Open\IzbergBundle\Model\FetchMerchantOrdersResponse;
use Open\IzbergBundle\Model\MerchantOrder;
use Open\IzbergBundle\Model\OrderMerchant;
use Open\IzbergBundle\Service\AttributeService;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;

class MerchantOrderSyncService implements LoggerAwareInterface
{
    private MerchantOrderRepository $merchantOrderRepository;
    private OrderItemService $orderItemService;
    private LoggerInterface $logger;
    private OrderApi $orderApi;

    public function __construct(
        MerchantOrderRepository $merchantOrderRepository,
        OrderItemService $orderItemService,
        OrderApi $orderApi
    )
    {
        $this->orderApi = $orderApi;
        $this->merchantOrderRepository = $merchantOrderRepository;
        $this->orderItemService = $orderItemService;
    }

    /**
     * @param OrderMerchant $merchantOrder
     * @param \AppBundle\Entity\Order $orderEntity
     */
    public function syncMerchantOrder(OrderMerchant $merchantOrder, \AppBundle\Entity\Order $orderEntity):bool{
        $izbergId = $merchantOrder->getId();
        $merchantOrderEntity = $this->merchantOrderRepository->findOneBy(["izbergId"=>$izbergId, "order"=>$orderEntity]);
        if(!$merchantOrderEntity instanceof \AppBundle\Entity\MerchantOrder){
            $merchantOrderEntity = new \AppBundle\Entity\MerchantOrder();
        }
        $merchantName = null;
        $merchantId = null;
        if($merchantOrder->getMerchant() !== null){
            $merchantName = $merchantOrder->getMerchant()->getName();
            $merchantId = $merchantOrder->getMerchant()->getId();
        }

        $attributes = $merchantOrder->getAttributes();
        $buyerInternalOrderId = $attributes['ZZC-Internal-Buyer-Order-ID'] ?? null;
        $packagingRequest1 = $attributes['packaging_info_1'] ?? null;
        $packagingRequest2 = $attributes['packaging_info_2'] ?? null;
        $packagingRequest3 = $attributes['packaging_info_3'] ?? null;
        $documentationRequest1 = $attributes['delivery_info_1'] ?? null;
        $documentationRequest2 = $attributes['delivery_info_2'] ?? null;
        $documentationRequest3 = $attributes['delivery_info_3'] ?? null;
        $documentationRequest4 = $attributes['delivery_info_4'] ?? null;
        $documentationRequest5 = $attributes['delivery_info_5'] ?? null;
        $documentationRequest6 = $attributes['delivery_info_6'] ?? null;
        $documentationRequest7 = $attributes['delivery_info_7'] ?? null;
        $documentationRequest8 = $attributes['delivery_info_8'] ?? null;
        $documentationRequest9 = $attributes['delivery_info_9'] ?? null;
        $documentationRequest10 = $attributes['delivery_info_10'] ?? null;

        $merchantOrderEntity->setIzbergId($izbergId)
            ->setMerchantOrderStatus($merchantOrder->getStatus())
            ->setVendorName($merchantName)
            ->setVendorId($merchantId)
            ->setBuyerInternalOrderId($buyerInternalOrderId)
            ->setPackagingRequest1($packagingRequest1)
            ->setPackagingRequest2($packagingRequest2)
            ->setPackagingRequest3($packagingRequest3)
            ->setDocumentationRequest1($documentationRequest1)
            ->setDocumentationRequest2($documentationRequest2)
            ->setDocumentationRequest3($documentationRequest3)
            ->setDocumentationRequest4($documentationRequest4)
            ->setDocumentationRequest5($documentationRequest5)
            ->setDocumentationRequest6($documentationRequest6)
            ->setDocumentationRequest7($documentationRequest7)
            ->setDocumentationRequest8($documentationRequest8)
            ->setDocumentationRequest9($documentationRequest9)
            ->setDocumentationRequest10($documentationRequest10)
            ->setOrder($orderEntity);

        $items = $this->orderApi->fetchOrdersItemByMerchantOrder($izbergId);
        if($items !== null){

            foreach($items as $item){
                $this->orderItemService->syncOrderItem($item, $merchantOrderEntity);
            }
        }
        $this->merchantOrderRepository->save($merchantOrderEntity);
        return true;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
