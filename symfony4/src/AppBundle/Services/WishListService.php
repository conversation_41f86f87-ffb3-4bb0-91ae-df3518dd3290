<?php

namespace AppBundle\Services;

use AppBundle\Entity\User;
use AppBundle\Entity\WishList;
use AppBundle\Entity\WishListItem;
use AppBundle\Model\Cart\Cart;
use AppBundle\Model\Cart\CartItem;
use AppBundle\Repository\WishListItemRepository;
use AppBundle\Repository\WishListRepository;
use Knp\Component\Pager\Paginator;
use Knp\Component\Pager\PaginatorInterface;

class WishListService
{
    /**
     * @var WishListRepository
     */
    private $wishListRepository;

    /**
     * @var WishListItemRepository
     */
    private $wishListItemRepository;

    /**
     * @var PaginatorInterface
     */
    private $paginator;

    public function __construct(WishListRepository $wishListRepository, WishListItemRepository $wishListItemRepository, PaginatorInterface $paginator)
    {
        $this->wishListRepository = $wishListRepository;
        $this->wishListItemRepository = $wishListItemRepository;
        $this->paginator = $paginator;
    }

    public function createWishList(User $user, string $name, string $currency, array $offers): WishList
    {
        $wishList = new WishList();
        $wishList->setName($name);
        $wishList->setUser($user);
        $wishList->setCurrency($currency);

        foreach($offers as $offer) {
            $wishListItem = new WishListItem();
            $wishListItem->setOfferId($offer['offerId']);
            $wishListItem->setQuantity($offer['quantity']);
            $wishListItem->setWishList($wishList);

            $wishList->addItem($wishListItem);
        }

        $this->wishListRepository->save($wishList);

        return $wishList;
    }

    public function overwriteWishList(User $user, int $wishListId, array $offers): ?WishList
    {
        /** @var WishList $wishList */
        $wishList  = $this->wishListRepository->find($wishListId);

        if (!$wishList) {
            return null;
        }

        $newWishList = $this->createWishList($user, $wishList->getName(), $wishList->getCurrency(), $offers);
        $this->deleteWishList($wishListId);

        return $newWishList;
    }

    public function addOfferQuantityToWishList(
        User $user,
        int $offerId,
        int $quantity,
        ?int $wishListId = null,
        ?string $name = null,
        ?string $currency = null
    ): ?WishList
    {
        $wishList  = ($wishListId) ? $this->wishListRepository->find($wishListId) : null;

        if (!$wishList) {
            if (!$name || !$currency) {
                return null;
            }

            $wishList = $this->createWishList($user, $name, $currency, []);
        }

        $wishListItems = array_filter($wishList->getItems()->toArray(), function(WishListItem $wishListItem) use ($offerId){
            return ($wishListItem->getOfferId() == $offerId);
        });

        if (!count($wishListItems)) {
            $wishListItem = new WishListItem();
            $wishListItem->setOfferId(strval($offerId));
            $wishListItem->setQuantity($quantity);
            $wishListItem->setWishList($wishList);
            $wishList->addItem($wishListItem);

        } else {
            $wishListItem = current($wishListItems);
            $wishListItem->setQuantity($wishListItem->getQuantity() + $quantity);
        }

        $this->wishListRepository->save($wishList);

        return $wishList;
    }

    public function deleteWishList(int $wishListId): bool
    {
        $wishList = $this->wishListRepository->find($wishListId);
        if (!$wishList) {
            return false;
        }

        $this->wishListRepository->delete($wishList);

        return true;
    }

    public function deleteWishListItem(int $itemId): bool
    {
        $wishListItem = $this->wishListItemRepository->find($itemId);
        if (!$wishListItem) {
            return false;
        }

        $this->wishListItemRepository->delete($wishListItem);

        return true;
    }

    public function updateWishListItem(int $itemId, int $quantity){
        /** @var WishListItem $wishListItem */
        $wishListItem = $this->wishListItemRepository->find($itemId);
        $wishListItem->setQuantity($quantity);

        $this->wishListItemRepository->save($wishListItem);
    }

    public function getUserWishList($userId, ?string $currency = null)
    {
        return $this->wishListRepository->getUserWishList($userId, $currency);
    }

    public static function offersFromCart(Cart $cart): array
    {
        return array_map(
            function(CartItem $cartItem) {
                return [
                    'quantity' => $cartItem->getQuantity(),
                    'offerId' => $cartItem->getOfferId()
                ];
            },
            $cart->getItems()
        );
    }

    public function getWishList($id): ?WishList
    {
        return $this->wishListRepository->find($id);
    }

    public function userCanAccessWishListId(User $user, ?int $wishListId = null): bool
    {
        if (!$wishListId) {
            return true;
        }

        /** @var WishList $wishList */
        $wishList = $this->wishListRepository->find($wishListId);

        if (!$wishList) {
            return false;
        }
        return $wishList->getUser() == $user;
    }

    public function userCanAccessWishListItemId(User $user, int $wishListItemId): bool
    {
        /** @var WishListItem $wishListItem */
        $wishListItem = $this->wishListItemRepository->find($wishListItemId);

        if(!$wishListItem) {
            return false;
        }

        $wishList = $wishListItem->getWishList();
        return $wishList->getUser() == $user;
    }
}
