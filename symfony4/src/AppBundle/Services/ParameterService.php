<?php

namespace AppBundle\Services;

class ParameterService
{
    /** @var array  */
    private $locales;

    /** @var bool */
    private $displayFirstLevelCategoryOnly;

    public function __construct(
        array $locales,
        bool $displayfirstLevelCategoryOnly = false
    )
    {
        $this->locales = $locales;
        $this->displayFirstLevelCategoryOnly = $displayfirstLevelCategoryOnly;
    }

    public function supportedLocales()
    {
        return $this->locales;
    }

    public function displayFirstLevelCategoryOnly()
    {
        return $this->displayFirstLevelCategoryOnly;
    }
}
