<?php

namespace AppBundle\Services;

use AppBundle\Entity\User;
use AppBundle\Exception\PaymentException;
use AppBundle\Factory\PaymentFactory;
use AppBundle\Model\Cart\Cart;
use AppBundle\Model\Order\Order;
use AppBundle\Model\Transaction;
use Open\IzbergBundle\Api\GatewayApi;
use Open\IzbergBundle\Api\PaymentApi;
use Open\IzbergBundle\Model\OrderPayment;
use Open\IzbergBundle\Model\PaymentTerm;
use Open\IzbergBundle\Service\RedisService;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;

class PaymentService implements LoggerAwareInterface
{

    public const CACHE_PAYMENT_TERMS = "payment_terms_";

    private PaymentApi $paymentApi;
    private RedisService $cacheService;
    private int $cacheTTL;
    private OrderService $orderService;
    private WPSService $webHelpService;
    private LoggerInterface $logger;
    private CartService $cartService;
    private MailService $mailService;
    private WebHelpIbanService $webHelpIbanService;
    private PaymentFactory $paymentFactory;
    private MerchantService $merchantService;
    private int $nbHoursBeforeRefund;

    /**
     * PaymentService constructor.
     * @param PaymentApi $paymentApi
     * @param RedisService $cacheService
     * @param int $cacheTTL
     * @param OrderService $orderService
     * @param MailService $mailService
     * @param WebHelpIbanService $webHelpIbanService
     * @param PaymentFactory $paymentFactory
     * @param MerchantService $merchantService
     * @param int $nbHoursBeforeRefund
     */
    public function __construct(
        int $cacheTTL,
        int $nbHoursBeforeRefund,
        PaymentApi $paymentApi,
        RedisService $cacheService,
        OrderService $orderService,
        MailService $mailService,
        WebHelpIbanService $webHelpIbanService,
        PaymentFactory $paymentFactory,
        MerchantService $merchantService
    ){
        $this->paymentApi = $paymentApi;
        $this->cacheService = $cacheService;
        $this->cacheTTL = $cacheTTL;
        $this->orderService = $orderService;
        $this->mailService = $mailService;
        $this->webHelpIbanService = $webHelpIbanService;
        $this->paymentFactory = $paymentFactory;
        $this->merchantService = $merchantService;
        $this->nbHoursBeforeRefund = $nbHoursBeforeRefund;
    }

    public function setWebHelpService(WPSService $webHelpService)
    {
        $this->webHelpService = $webHelpService;
    }

    public function setCartService(CartService $cartService)
    {
        $this->cartService = $cartService;
    }

    /**
     * fetch the payment term for the specified locale
     * @param string $locale
     * @return mixed|PaymentTerm|null the payment term
     */
    public function fetchPaymentTerm(string $locale)
    {
        $terms = $this->cacheService->getItem(self::CACHE_PAYMENT_TERMS.$locale);
        if ($terms === null) {
            $terms = $this->paymentApi->getPaymentTerms($locale);
            $this->cacheService->saveItem(self::CACHE_PAYMENT_TERMS.$locale, $terms, $this->cacheTTL);
        }

        return $terms;
    }

    /**
     * find the izberg payment from the wps transaction code
     * @param string $wpsTransactionCode the wps transaction code
     * @return null|\stdClass null if no payment was found or if more than one payment was found
     */
    public function findIzbergPaymentFromWpsTransaction (string $wpsTransactionCode)
    {
        $payments = $this->paymentApi->findPayments(["external_id" => $wpsTransactionCode]);
        if ($payments === null || count($payments) !== 1){
            return null;
        }
        return $payments[0];
    }

    /**
     * @return PaymentApi
     */
    public function getPaymentApi(): PaymentApi
    {
        return $this->paymentApi;
    }

    /**
     * @param PaymentApi $paymentApi
     */
    public function setPaymentApi(PaymentApi $paymentApi): void
    {
        $this->paymentApi = $paymentApi;
    }

    /**
     * @return RedisService
     */
    public function getCacheService(): RedisService
    {
        return $this->cacheService;
    }

    /**
     * @param RedisService $cacheService
     */
    public function setCacheService(RedisService $cacheService): void
    {
        $this->cacheService = $cacheService;
    }

    /**
     * @return int
     */
    public function getCacheTTL(): int
    {
        return $this->cacheTTL;
    }

    /**
     * @param int $cacheTTL
     */
    public function setCacheTTL(int $cacheTTL): void
    {
        $this->cacheTTL = $cacheTTL;
    }

    public function changePaymentStatusToPaymentPendingAuthorization(OrderPayment $payment)
    {
        $this->paymentApi->setPaymentAsPending($payment->getId());
    }

    /**
     * @param Cart $cart
     * @param User $user
     * @param string|null $validationNumber
     * @return bool
     * @throws PaymentException
     * @throws \AppBundle\Exception\MailException
     */
    public function prePaymentWithWireTransfer(
        Cart $cart,
        User $user,
        ?string $validationNumber,
        ?string $accountingEmail
    ): bool {
        $company = $user->getCompany();
        if ($cart->hasOrder()) {
            $this->logger->error(
                sprintf(
                    'Trying to create new Order but Payment by bank transfer already created - izberg order id %s',
                    $cart->getOrder()->getIzbergId()
                ),
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::PAYMENT_COMMAND,
                    'Izberg cart ID' => $cart->getId(),
                    'Izberg order ID' => $cart->getOrder()->getIzbergId()
                ])
            );

            return false;
        }

        $this->cartService->updateCartInfoBeforePayment($cart, $user);
        $this->cartService->createOrderFromCart($cart, $user, $validationNumber, $accountingEmail);

        try {
            /////////////////////////////////////////////////////////////////
            // Create WPS transaction
            /////////////////////////////////////////////////////////////////
            $transaction = $this->webHelpService->createTransaction($cart->getOrder(), $company);

            /////////////////////////////////////////////////////////////////
            //create all sub transactions
            /////////////////////////////////////////////////////////////////
            $this->webHelpService->createSubTransactionsForOrder(
                $transaction,
                $cart->getOrder(),
                GatewayApi::TYPE_PREPAYMENT
            );

            $this->savePaymentFromTransactionAsPending($transaction);

            $this->cartService->updateCartDbStatus($cart->getId(), \AppBundle\Entity\Cart::STATUS_DONE);

        } catch(\Exception $exception) {
            // todo peut etre un mail pour dire que c'est en error
            // todo peut etre cancel l'order

            throw new PaymentException();
        }

        /////////////////////////////////////////////////////////////////
        //send confirmation email to the payer user
        /////////////////////////////////////////////////////////////////

        $this->mailService->sendEmailMessage(
            MailService::BUYER_PRE_PAYMENT_TRANSFER_INITIAL,
            $user->getLocale(),
            $user->getEmail(),
            [
                MailService::FIRST_NAME_VAR => $user->getFirstname(),
                MailService::LAST_NAME_VAR => $user->getLastname(),
                'orderNumber' => $cart->getOrder()->getIdNumber(),
                'iban' => $this->webHelpIbanService->getIbanFromCurrency($cart->getOrder()->getCurrency()),
                'ibanAccountName' => $this->webHelpIbanService->getIbanAccountNameFromCurrency(
                    $cart->getOrder()->getCurrency()
                ),
                'reconciliationKey' => $transaction->getReconciliationKey(),
                "currency" => $cart->getOrder()->getCurrency(),
                "amount" => $cart->getOrder()->getAmountVatIncluded()
            ]
        );

        return true;
    }

    /**
     * @param Cart $cart
     * @param User $user
     * @param string|null $validationNumber
     * @return bool
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     * @throws \Open\WebhelpBundle\Api\WPSException
     */
    public function termPayment(
        Cart $cart,
        User $user,
        ?string $validationNumber,
        ?string $accountingEmail
    ): bool
    {
        $company = $user->getCompany();

        if ($cart->hasOrder()) {
            $this->logger->error(
                sprintf(
                    'Trying to create new Order but Term payment already created - izberg order id %s',
                    $cart->getOrder()->getIzbergId()
                ),
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::PAYMENT_COMMAND,
                    'Izberg cart ID' => $cart->getId(),
                    'Izberg order ID' => $cart->getOrder()->getIzbergId(),
                    'Izberg accounting email' => $cart->getAccountingEmail()
                ])
            );

            return false;
        }

        $this->cartService->updateCartInfoBeforePayment($cart, $user);
        $this->cartService->createOrderFromCart($cart, $user, $validationNumber, $accountingEmail);

        $this->orderService->authorizeOrderByIzbergOrderId($cart->getOrder()->getIzbergId());
        $this->webHelpService->createCustomerWPS($company);

        $this->logger->info(
            "time payment order authorized",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME=>EventNameEnum::PAYMENT_PROCESS,
                LogUtil::USER_NAME=>$user->getUsername(),
                WPSService::LOG_PAYMENT_MODE => WPSService::LOG_TIME_MODE,
                WPSService::LOG_PAYMENT_STEP => "authorizeOrder",
                'numOrder' => $cart->getOrder()->getIdNumber(),
                'orderId' => $cart->getOrder()->getIzbergId(),
                'accountingEmail' => $cart->getAccountingEmail(),
            ])
        );

// DISABLE EMAIL NOTIFICATION - MANAGED BY IZBERG (https://jira.open-groupe.com/browse/S1TMA-112)
//        /** @var \AppBundle\Model\Order\MerchantOrder $merchantOrder */
//        foreach($cart->getOrder()->getMerchantOrders() as $merchantOrder) {
//            $merchant = $this->merchantService->findMerchantById($merchantOrder->getMerchantId());
//            $this->mailService->sendEmailMessage(
//                MailService::SELLER_NEW_COMMAND_PAID,
//                $merchant->getPreferedLanguage(),
//                $merchant->getMainContactEmail(),
//                [
//                    'firstName' => $merchant->getMainContactFirstName(),
//                    'lastName' => $merchant->getMainContactLastName(),
//                    'orderNumber' => $cart->getOrder()->getIdNumber(),
//                    'paymentNbHoursBeforeRefund' => $this->nbHoursBeforeRefund,
//                    'buyer' => $company->getName(),
//                ]
//            );
//        }

        //clear cart
        $this->cartService->clearUserCurrentCarts($user, $cart->getId(), $cart->getOrder()->getCurrency());
        $this->cartService->updateCartDbStatus($cart->getId(), \AppBundle\Entity\Cart::STATUS_DONE);

        return true;
    }

    /**
     * @param Order $order
     * @return \AppBundle\Model\Payment
     * @throws PaymentException
     */
    public function paymentForOrder(Order $order): \AppBundle\Model\Payment
    {
        return $this->paymentFactory->buildPaymentFromOrder($order);
    }

    public function saveTransactionToPayment(Transaction $transaction, \AppBundle\Model\Payment $payment)
    {
        //patching payment object with the wps transaction code
        $this->logger->info(
            "updating izberg payment object",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME=>EventNameEnum::PAYMENT_PROCESS,
                "paymentId" => $payment->getIzbergId(),
                "externalId" => $transaction->getWpsTransactionCode(),
                "orderId" => $transaction->getIzbergOrderId(),
            ])
        );

        $this->paymentApi->updatePayment(
            $payment->getIzbergId(),
            ['external_id' => $transaction->getWpsTransactionCode()]
        );
    }

    public function savePaymentFromTransactionAsPending(Transaction $transaction)
    {
        /////////////////////////////////////////////////////////////////
        //Prevent auto-cancel of the order from izberg
        /////////////////////////////////////////////////////////////////
        $this->logger->info(
            "setting payment as pending",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME=>EventNameEnum::PAYMENT_PROCESS,
                "paymentId" => $transaction->getIzbergPaymentId(),
                "orderId" => $transaction->getIzbergOrderId(),
            ])
        );

        $this->paymentApi->setPaymentAsPending($transaction->getIzbergPaymentId());
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
