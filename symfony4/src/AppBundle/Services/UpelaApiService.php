<?php

namespace AppBundle\Services;

use AppBundle\Entity\Contact;
use AppBundle\Model\CarrierOffer;
use AppBundle\Model\GroupOfPackages;
use Doctrine\Common\Collections\ArrayCollection;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;

class UpelaApiService implements LoggerAwareInterface
{
    public const ENVELOP_SHIPMENT_TYPE = 1;
    public const PARCEL_SHIPMENT_TYPE = 2;
    public const PALLET_SHIPMENT_TYPE = 3;

    private const NORMAL_ADDRESS = 0;
    private const PRO_ADDRESS = 1;

    private array $upelaConfig;

    private LoggerInterface $logger;
    private SerializerService $serializer;
    private MailService $mailService;

    public function __construct(array $upelaConfig, SerializerService $serializer,MailService $mailService)
    {
        $this->upelaConfig = $upelaConfig;
        $this->serializer = $serializer;
        $this->mailService = $mailService;
    }

    public function getQuotation(GroupOfPackages $groupOfPackages): ArrayCollection
    {
        $carrierOffers = new ArrayCollection();
        /** @var Contact $shippingContact */
        $shippingContact = $groupOfPackages->getShippingAddress()->getShippingPoints()->get(0)->getContact();
        $shipTo = [
            'company' => $groupOfPackages->getCompanyName(),
            'name' => $shippingContact->getFirstName() . '' . $shippingContact->getLastName(),
            'phone' => $shippingContact->getPhone1(),
            'email' => $shippingContact->getEmail(),
            'address1' => $groupOfPackages->getShippingAddress()->getAddress(),
            'address2' => $groupOfPackages->getShippingAddress()->getAddress2(),
            'country_code' => strtoupper($groupOfPackages->getShippingAddress()->getCountry()->getIzbergCode()),
            'postcode' => $groupOfPackages->getShippingAddress()->getZipCode(),
            'city' => $groupOfPackages->getShippingAddress()->getCity(),
            'pro' => self::PRO_ADDRESS,
        ];
        $dataBody = [
            'token' => $groupOfPackages->getMerchantId(),
            'mode' => 'api_key',
            'shipment_date' => $groupOfPackages->getShipmentDate(), //delivery date from cart
            'shipment_type' => $groupOfPackages->getShipmentType(),
            'parcel_unit' => 'fr',
            'ship_from' => [
                'company' => $groupOfPackages->getMerchantName(),
                'name' => '',
                'phone' => '',
                'email' => '',
                'address1' => $groupOfPackages->getStreet(),
                'country_code' => $groupOfPackages->getCountryCode(),
                'postcode' => $groupOfPackages->getZipCode(),
                'city' => $groupOfPackages->getTown(),
                'pro' => self::PRO_ADDRESS,
            ],
            'ship_to' => $shipTo,
            'parcels' => $groupOfPackages->getPackages(),
            'reason' => 'Commercial',
            'content' => 'Parts',
            'labelFormat' => 'PDF'
        ];

        try {
            $body = json_encode($dataBody);
            $this->logger->info('Upela get quotation request body',
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::UPELA_API_REQUEST_INFO,
                    "data" => $dataBody,
                    'url' => $this->upelaConfig['url']
                ])
            );

            $ch = curl_init($this->upelaConfig['url']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLINFO_HEADER_OUT, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                    'Content-Type: application/json',
                    'Content-Length: ' . strlen($body))
            );

            $result = curl_exec($ch);
            $response = json_decode(strval($result), true);

            $this->logger->info('Upela get quotation response body',
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::UPELA_API_RESPONSE_INFO,
                    "response" => $response
                ])
            );

            if (!$response['success']) {
                if(isset($response['errors']['from_address']) ||
                    isset($response['errors']['from_city']) ||
                    isset($response['errors']['from_postcode']) ||
                    isset($response['errors']['from_country']) ||
                    isset($response['errors']['from_address']) ||
                    isset($response['errors']['to_address'])){
                    $date = new \DateTime();
                    $dateFormat = "d/m/Y H:i:s";
                    $this->logger->info('Upela error with the address',
                        LogUtil::buildContext([
                            LogUtil::EVENT_NAME=>EventNameEnum::UPELA_API_ADDRESS_ERROR,
                            "response" => $response,
                            "date" => $date->format($dateFormat),
                            "company_name" => $groupOfPackages->getCompanyName(),
                            "address" => $shipTo,
                            'merchant' => $groupOfPackages->getMerchant(),
                            'buyer ' => $groupOfPackages->getUser()
                        ]));

                        $this->mailService->sendEmailMessage(
                            MailService::UPELA_ADDRESS_ISSUE,
                            'en',
                            $this->upelaConfig['support_emails_address'],
                            [
                                'date' => $date->format($dateFormat),
                                'buyerCompanyName' => $groupOfPackages->getCompanyName(),
                                'addressSendTo' => $shipTo['address1'] .','. $shipTo['city'].','.$shipTo['postcode'],
                                'vendorCompanyName' => $groupOfPackages->getMerchantName(),
                                'addressSendFrom' =>$groupOfPackages->getStreet() .','.$groupOfPackages->getTown().','.$groupOfPackages->getZipCode(),
                                'upelaResponse' => print_r($response, true),
                                'upelaRequest' => print_r($dataBody, true)
                            ]
                        );
                } else {
                    $date = new \DateTime();
                    $dateFormat = "d/m/Y H:i:s";
                    $this->logger->error('Upela error on quotation request',
                        LogUtil::buildContext([
                            LogUtil::EVENT_NAME=>EventNameEnum::UPELA_API_REQUEST_ERROR,
                            "data" => $body,
                            "date" => $date->format($dateFormat),
                            "company_name" => $groupOfPackages->getCompanyName(),
                            "address" => $shipTo,
                            'merchant' => $groupOfPackages->getMerchant(),
                            'buyer ' => $groupOfPackages->getUser()
                        ]));

                    $this->mailService->sendEmailMessage(
                        MailService::UPELA_QUOTATION_REQUEST_ISSUE,
                        'en',
                        $this->upelaConfig['support_emails_address'],
                        [
                            'date' => $date->format($dateFormat),
                            'buyerCompanyName' => $groupOfPackages->getCompanyName(),
                            'vendorCompanyName' => $groupOfPackages->getMerchantName(),
                            'upelaResponse' => print_r($response, true),
                            'upelaRequest' => print_r($dataBody, true)
                        ]
                    );
                }

                return $carrierOffers;
            }

            foreach ($response['offers'] as $offer) {
                $offer['carrier_identifier'] = md5($offer['service_code'] . $offer['service_name']);
                $offer['price'] = $offer['price_te'];
                /** @var CarrierOffer $carrierOffer */
                $carrierOffer = $this->serializer->deserialize(json_encode($offer), CarrierOffer::class);
                $carrierOffer->setCartItems($groupOfPackages->getCartItems());
                $carrierOffer->setGroupOfPackages($groupOfPackages);
                $carrierOffer->setShipmentId((int)$response['shipment_id']);
                /**
                 * @psalm-suppress InvalidArgument
                 */
                $carrierOffers->add($carrierOffer);
            }

        } catch (\Exception $e) {
            $this->logger->critical($e->getMessage(),
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::TECHNICAL_ERROR,
                    "trace" => $e->getTraceAsString()
                ])
            );
        }

        return $carrierOffers;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
