<?php

namespace AppBundle\Services;

use AppBundle\Entity\Address;
use AppBundle\Entity\Company;
use AppBundle\Entity\PaymentProcess\CreditNote;
use AppBundle\Entity\PaymentProcess\Invoice;
use AppBundle\Entity\PaymentProcess\Payment;
use AppBundle\Entity\PaymentProcess\Refund;
use AppBundle\Entity\PaymentProcessSummary;
use AppBundle\Entity\ShippingPoint;
use AppBundle\Entity\Site;
use AppBundle\Entity\User;
use AppBundle\Exception\UnexpectedValueException;
use AppBundle\Factory\OrderFactory;
use AppBundle\Model\Cart\Cart;
use AppBundle\Model\Offer;
use AppBundle\Model\Order\OrderItem;
use AppBundle\Repository\AddressRepository;
use AppBundle\Repository\CompanyRepository;
use AppBundle\Repository\OrderRepository;
use AppBundle\Repository\ShippingPointRepository;
use DateTimeImmutable;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Exception;
use Knp\Component\Pager\PaginatorInterface;
use Open\FrontBundle\Form\PaymentModeSelectForm;
use Open\IzbergBundle\Api\ApiException;
use Open\IzbergBundle\Api\CreditNoteApi;
use Open\IzbergBundle\Api\InvoiceApi;
use Open\IzbergBundle\Api\OrderApi;
use Open\IzbergBundle\Api\PaymentApi;
use Open\IzbergBundle\Api\RefundApi;
use Open\IzbergBundle\Model\FetchOrdersResponse;
use Open\IzbergBundle\Model\Item;
use Open\IzbergBundle\Model\MerchantOrder;
use Open\IzbergBundle\Model\Order;
use Open\IzbergBundle\Model\OrderMerchant;
use Open\IzbergBundle\Service\AttributeService;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Twig\Environment;
use ZipArchive;

class OrderService implements LoggerAwareInterface
{

    private OrderApi $orderApi;
    private PaginatorInterface $paginator;
    private SerializerService $serializerService;
    private JobService $jobService;
    private OrderRepository $orderRepository;
    private AddressRepository $addressRepository;
    private ShippingPointRepository $shippingPointRepository;
    private CompanyRepository $companyRepository;
    private CreditNoteApi $creditNoteApi;
    private PaymentApi $paymentApi;
    private InvoiceApi $invoiceApi;
    private RefundApi $refundApi;
    private AttributeService $izbergAttributesService;
    private OrderFactory $orderFactory;
    private OfferService $offerService;
    private AddressService $addressService;
    private SecurityService $securityService;
    private Environment $twig;
    private CompanyService $companyService;
    private ShippingService $shippingService;
    private LoggerInterface $logger;
    private MerchantOrderSyncService $merchantOrderSyncService;
    private OrderItemService $orderItemService;

    public function __construct(
        OrderApi $orderApi,
        PaginatorInterface $paginator,
        SerializerService $serializerService,
        JobService $jobService,
        OrderRepository $orderRepository,
        AddressRepository $addressRepository,
        ShippingPointRepository $shippingPointRepository,
        CompanyRepository $companyRepository,
        CreditNoteApi $creditNoteApi,
        PaymentApi $paymentApi,
        InvoiceApi $invoiceApi,
        RefundApi $refundApi,
        AttributeService $izbergAttributesService,
        OrderFactory $orderFactory,
        OfferService $offerService,
        AddressService $addressService,
        SecurityService $securityService,
        Environment  $twig,
        CompanyService $companyService,
        ShippingService $shippingService,
        MerchantOrderSyncService $merchantOrderSyncService,
        OrderItemService $orderItemService
    )
    {
        $this->orderApi = $orderApi;
        $this->paginator = $paginator;
        $this->serializerService = $serializerService;
        $this->jobService = $jobService;
        $this->orderRepository = $orderRepository;
        $this->addressRepository = $addressRepository;
        $this->shippingPointRepository = $shippingPointRepository;
        $this->companyRepository = $companyRepository;
        $this->creditNoteApi = $creditNoteApi;
        $this->paymentApi = $paymentApi;
        $this->invoiceApi = $invoiceApi;
        $this->refundApi = $refundApi;
        $this->izbergAttributesService = $izbergAttributesService;
        $this->orderFactory = $orderFactory;
        $this->offerService = $offerService;
        $this->addressService = $addressService;
        $this->securityService = $securityService;
        $this->twig = $twig;
        $this->companyService = $companyService;
        $this->shippingService = $shippingService;
        $this->merchantOrderSyncService = $merchantOrderSyncService;
        $this->orderItemService = $orderItemService;
    }

    /**
     * @param string $paymentMethod
     * @param null|string $validationNumber
     * @param string|null $paymentTerm
     * @return array
     */
    private function getOrderAdditionalCustomAttributes(
        string $paymentMethod,
        ?string $validationNumber,
        ?string $paymentTerm,
        ?string $accountingEmail,
    ): array {
        $customProperties = [];
        $customProperties['ZZB-Internal-Buyer-Validation-ID'] = $validationNumber;
        $customProperties['ZZE_Accountant_Email'] = $accountingEmail;

        if ($paymentMethod === PaymentModeSelectForm::PAYMENT_PRE_CARD) {
            $customProperties['ABA_payment_method'] = 'Credit card';
        }
        else{
            $customProperties['ABA_payment_method'] = 'Bank transfer';
        }
        if ($paymentTerm !== null){
            $customProperties['AABA_payment_term'] = $paymentTerm;
        }
        return $customProperties;
    }

    private function getOrderPackagingRequests(Cart $cart): array
    {
        return [
            'packaging_info_1' => $cart->getShippingPoint()->getPackagingRequest1() ? '✚ ' . $cart->getShippingPoint()->getPackagingRequest1() : '',
            'packaging_info_2' => $cart->getShippingPoint()->getPackagingRequest2() ? '✚ ' . $cart->getShippingPoint()->getPackagingRequest2() : '',
            'packaging_info_3' => $cart->getShippingPoint()->getPackagingRequest3() ? '✚ ' . $cart->getShippingPoint()->getPackagingRequest3() : '',
        ];
    }

    private function getOrderRequestedDocuments(Cart $cart): array
    {
        $requestedDocuments = [];
        $cpt = 1;

        foreach ($cart->getDocumentsRequests() as $document) {
            $izbergAttribute = sprintf('delivery_info_%s', $cpt);
            $requestedDocuments[$izbergAttribute] = '✚ ' . $document;

            $cpt++;
        }

        return $requestedDocuments;
    }

    /**
     * @param Cart $cart
     * @param User $user
     * @param string|null $validationNumber
     *
     * @return \AppBundle\Model\Order\Order
     *
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function createOrderFromCart(
        Cart $cart,
        User $user,
        ?string $validationNumber = null,
        ?string $accountingEmail = null,
    ): \AppBundle\Model\Order\Order {
        $this->logger->info(
            'OrderService::createOrderFromCart',
            LogUtil::buildContext([
                'cartID' => $cart->getId(),
            ])
        );

        $cartId = $cart->getId();
        $buyerOrderId = $cart->getBuyerOrderId();
        $izbergOrder = $this->orderApi->createOrderFromCart($cartId);

        $merchantOrderIds = array_map(
            function(MerchantOrder $merchantOrder) {
                return $merchantOrder->getId();
            },
            $izbergOrder->getMerchantOrders()->toArray()
        );

        $merchantOrders = array_map(
            function(MerchantOrder $merchantOrder): OrderMerchant {
                $orderMerchant = $this->fetchMerchantsOrderByOrderId($merchantOrder->getId());
                if(!$orderMerchant) {
                    throw new UnexpectedValueException(sprintf('Cannot find merchant order with id %s', $merchantOrder->getId()));
                }

                return $orderMerchant;
            },
            $izbergOrder->getMerchantOrders()->toArray()
        );

        $this->logger->info(
            "OrderService::createOrderFromCart - shippingOptions",
            LogUtil::buildContext([])
        );
        foreach($merchantOrders as $merchantOrder) {
            $shippingOptions = $this->orderApi->fetchMerchantOrderShippingOptions($merchantOrder->getId());
            if(empty($shippingOptions)) {
                continue;
            }
            foreach($shippingOptions as $shippingOption) {
                $this->logger->info(
                    "OrderService::createOrderFromCart - shippingOption",
                    LogUtil::buildContext([
                        'shippingOption' => $shippingOption
                    ])
                );
                //$this->orderApi->createParcel($merchantOrder->getId(), $shippingOption);
            }
        }

        $this->shippingService->removeShippingOfferFromMerchantOrders($merchantOrders);

        $orderPackagingRequests = $this->getOrderPackagingRequests($cart);

        $orderRequestedDocuments = $this->getOrderRequestedDocuments($cart);

        /**
         * @var ShippingPoint $shippingPoint
         */
        $shippingPoint = $cart->getShippingPoint() ?? null;
        $shippingAttributes = [];
        if ($shippingPoint) {
            $shippingAttributes = [
                'ZTA-Contact-First-Name' => $shippingPoint->getContact()->getFirstName(),
                'ZUA-Contact-Last-Name' => $shippingPoint->getContact()->getLastName(),
                'ZVA-Contact-Email' => $shippingPoint->getContact()->getEmail(),
                'ZWA-Contact-Main-Phone' => $shippingPoint->getContact()->getPhone1(),
                'ZXA-Contact-Phone' => $shippingPoint->getContact()->getPhone2(),
                'ZYA-Job-Title' => $shippingPoint->getContact()->getFunction(),
                'ZZA-Contact-Comment' => $shippingPoint->getComment(),
                'cost_center' => $shippingPoint->getSite()->getId(),
            ];
        }

        $orderCustomProperties = $this->getOrderAdditionalCustomAttributes(
            $cart->getPaymentMode(),
            $validationNumber,
            $cart->getPaymentTerm(),
            $accountingEmail,
        );

        $attributes = $orderPackagingRequests + $orderRequestedDocuments + $orderCustomProperties + $shippingAttributes + [
            'ZZC-Internal-Buyer-Order-ID' => $buyerOrderId,
            'ZSA-Contact-Info' => ' ',
            'ABC_customer_identification_number' => $user->getCompany()->getIdentification()
        ];

        $this->updateMerchantOrdersExtraInfos(
            $merchantOrderIds,
            $attributes,
            true
        );


        //we also want to sync this order
        $this->syncSingleOrder($izbergOrder, accountingEmail: $accountingEmail);

        return $this->orderFactory->buildOrder($izbergOrder);
    }

    /**
     * @param array $merchantOrdersIds
     * @param array $attributes
     * @param bool $runHasJob
     * @return void
     */
    public function updateMerchantOrdersExtraInfos(
        array $merchantOrdersIds,
        array $attributes,
        bool $runHasJob = false
    ): void
    {
        if ($runHasJob) {
            $this->jobService->setMerchantOrderExtraInfo($merchantOrdersIds, $attributes);
            return;
        }

        // Set order attributes
        $this->izbergAttributesService->setMerchantsOrdersExtraInfo($merchantOrdersIds, $attributes);
    }

    public function fetchIzbergOrders(int $offset = 0, int $limit = 10, ?DateTimeImmutable $lastSync = null): FetchOrdersResponse
    {
        return $this->orderApi->fetchAllOrders($offset, $limit, $lastSync);
    }

    public function paginateUserOrderBySearchAndStatus(User $user, ?string $search, array $orderStatuses, $page = 1, $limit = 10, $options = [])
    {
        $parameters = [];
        $target = $this->orderRepository->createQueryBuilder('o');

        if($search) {
            /** @psalm-suppress TooManyArguments */
            $target->andWhere(
                $target->expr()->orX(
                    $target->expr()->like('o.numberId', ':search'),
                    $target->expr()->like('o.createdOn', ':search'),
                    $target->expr()->like('o.validationNumber', ':search')
                )
            );

            $parameters['search'] = '%'. $search . '%';
        }

        $target->andWhere('o.status in (:orderStatuses)')
            ->andWhere('o.company = :company')
            ->orderBy('o.createdOn', 'DESC');

        $parameters['orderStatuses'] = $orderStatuses;
        $parameters['company'] = $user->getCompany();

        if (!$user->hasRole(User::ROLE_BUYER_ADMIN)) {
            $target->andWhere('o.site IN (:siteIds)');
            $parameters['siteIds'] = array_map(function(Site $site) {return $site->getId();}, $user->getSites()->toArray());
        }

        $target->setParameters($parameters);

        return $this->paginator->paginate($target, $page, $limit, $options);
    }

    public function fetchOrdersByUserId($userId, ?\DateTimeImmutable $fromDate = null, ?\DateTimeImmutable $toDate = null)
    {
        return $this->orderApi->fetchOrdersByUserId($userId, $fromDate, $toDate);
    }

    public function fetchOrdersByUserIdWithPagination($userId, &$size, $offset=0, $limit=10): FetchOrdersResponse
    {
        return $this->orderApi->fetchOrdersByUserIdWithPagination($userId, $size, $offset, $limit);
    }

    public function fetchMerchantOrders($userId){
        return $this->orderApi->fetchMerchantOrders($userId);
    }

    /**
     * @param $merchantOrderId
     * @return OrderMerchant|null
     */
    public function fetchMerchantsOrderByOrderId($merchantOrderId): ?OrderMerchant
    {
        try {
            $orderMerchant = $this->orderApi->fetchMerchantsOrderByOrderId($merchantOrderId);
        } catch (ApiException $apiException) {
            return null;
        }

        return $orderMerchant;
    }

    public function fetchOrderById($orderId): Order
    {
        return $this->orderApi->fetchOrder($orderId);
    }

    /**
     * @param int $merchantOrderId
     * @return Order|null
     */
    public function fetchOrderByMerchantOrderId(int $merchantOrderId): ?Order
    {
        $izbergMerchantOrder = $this->orderApi->fetchMerchantOrderById($merchantOrderId);
        $izbergOrder = $izbergMerchantOrder->getOrder();

        return $this->fetchOrderById($izbergOrder->getId());
    }

    /**
     * @param int $merchantOrderId
     * @return Company|null
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function retrieveCompanyFromMerchantOrderId(int $merchantOrderId): ?Company
    {
        $izbergMerchantOrder = $this->orderApi->fetchMerchantOrderById($merchantOrderId);
        $izbergUser = $izbergMerchantOrder->getUser();
        if (!$izbergUser) {
            return null;
        }

        return $this->companyService->findByIzbergUserId($izbergUser->getId());
    }

    public function authorizeOrderByIzbergOrderId(int $orderId): void
    {
        $order = $this->fetchOrderById($orderId);
        $payment = $order->getPayment();
        if ($payment === null) {
            $this->logger->info(
                "No payment return by izberg.",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_PROCESS,
                    LogUtil::USER_NAME => $order->getUser()->getUsername(),
                    WPSService::LOG_PAYMENT_MODE => WPSService::LOG_TIME_MODE,
                    WPSService::LOG_PAYMENT_STEP => "authorizeOrder",
                    'numOrder' => $order->getIdNumber(),
                    'orderId' => $orderId,
                ])
            );
            return;
        }

        $paymentId = $payment->getId();
        $this->paymentApi->authorizePayment($paymentId);
        $this->syncSingleOrder($order);
    }

    public function authorizeOrder(Order $order)
    {
        $this->authorizeOrderByIzbergOrderId($order->getId());
    }

    public function cancelOrder(\AppBundle\Model\Order\Order $order)
    {
        $this->cancelOrderById($order->getIzbergId());
    }

    /**
     * @param int $orderId
     */
    public function cancelOrderById(int $orderId)
    {
        $order = $this->orderApi->cancelOrder($orderId);
        $this->syncSingleOrder($order);
    }

    /**
     * @param $array
     * @param $numberPerPage
     * @param $request
     * @param $param
     * @return \Knp\Component\Pager\Pagination\PaginationInterface
     */
    public function paginateArray($array, $numberPerPage, $request, $param){
        if($request != null){
            $page = $request->query->getInt($param, 1);
        }else{
            $page = 1;
        }
        return $this->paginator->paginate(
            $array,
            $page,
            $numberPerPage,
            array(
                'pageParameterName' => $param,
                'active'=> $param,
            )
        );
    }

    /**
     * @param $merchantOrderId
     * @param bool $operator
     * @return mixed
     */
    public function getMerchantOrderInvoices($merchantOrderId, bool $operator = false)
    {
        return $this->orderApi->getMerchantOrderInvoices($merchantOrderId, $operator);
    }

    public function fetchOrdersByNumOrderId($numOrder)
    {
        return $this->orderApi->fetchOrdersByNumOrderId($numOrder);
    }

    public function getMerchantOrderRefunds($merchantOrderId)
    {
        return $this->refundApi->findRefundsByMerchantOrderId($merchantOrderId);
    }

    /**
     * fetch an order entity from the bdd
     * @param $orderId
     * @return mixed
     */
    public function fetchOrderEntity ($orderId)
    {
        return $this->orderRepository->findOneBy(['izbergId' => $orderId]);
    }

    /**
     * @param FetchOrdersResponse $orderResult
     * @param bool $jobQueue
     * @return null
     * @throws ORMException
     */
    public function sync(FetchOrdersResponse $orderResult)
    {
        /** @var Order $order */
        foreach($orderResult->getObjects() as $order) {
            $this->syncSingleOrder($order);
        }
    }


    /**
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Exception
     */
    public function syncSingleOrder(Order $order, ?string $accountingEmail = null): void
    {
        // Fetch first merchant order to get custom attributes
        $merchantOrder = null;
        $merchantOrders = $order->getMerchantOrders();
        if (!$merchantOrders->isEmpty()){
            $merchantOrder = $this->orderApi->fetchMerchantsOrderByOrderId($merchantOrders->first()->getId());
        }

        $izbergCartId = ($order->getCart()) ? $order->getCart()->getId() : null;

        if (!$izbergCartId) {
            // check archived_cart_id
            $izbergCartId = $order->getArchivedCartId();
        }

        // bug in production, it seems that some order has no cart Id linked to it
        if (!$izbergCartId) {
            $this->logger->error(
                sprintf('Order %d does not seems to have a cart ID', $order->getId()),
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::ORDER_SYNC
                ])
            );
        }

        $orderEntity = null;
        if ($order->getId()) {
            $orderEntity = $this->fetchOrderEntity($order->getId());
        }

        if (null === $orderEntity && $izbergCartId) {
            $orderEntity = $this->orderRepository->findOneBy(['cartId' => $izbergCartId]);
        }

        if (null === $orderEntity) {
            $orderEntity = new \AppBundle\Entity\Order();
        }

        $orderEntity->setIzbergId($order->getId());
        $orderEntity->setNumberId($order->getIdNumber());
        $orderEntity->setCartId($izbergCartId);
        $orderEntity->setCreatedOn(new DateTimeImmutable($order->getCreatedOn()));

        $address = null;
        if ($order->getShippingAddress()) {
            /** @var Address $address */
            $address = $this->addressRepository->findOneBy([
                'izbergAddressId' => $order->getShippingAddress()->getId()
            ]);
        }

        $orderEntity->setAddress($address);

        $shippingPoint = null;
        if ($address) {
            $shippingPoint = $this->shippingPointRepository->findOneBy([
                'address' => $address
            ]);
        }
        $orderEntity->setShippingPoint($shippingPoint);

        /** @var Company $company */
        $company = $this->companyRepository->findOneBy([
                'izbergUserId' => $order->getUser()->getId()]
        );
        $orderEntity->setCompany($company);

        /** @var ShippingPoint $shippingPoint */
        $shippingPoint = $this->shippingPointRepository->findOneBy(['address' => $address]);
        $site = ($shippingPoint) ? $shippingPoint->getSite() : null;
        $orderEntity->setSite($site);
        $orderEntity->setValidationNumber(
            $this->getMerchantOrderCustomAttribute($merchantOrder, 'ZZB-Internal-Buyer-Validation-ID')
        );
        $orderEntity->setAccountingEmail(
            $this->getMerchantOrderCustomAttribute($merchantOrder, 'ZZE_Accountant_Email') ?? $accountingEmail
        );
        $this->updateOrderStatus($orderEntity, $order);

        if ($order->getPayment() !== null && $order->getPayment()->getPaymentMethodCode() !== null) {
            $orderEntity->setPaymentCode($order->getPayment()->getPaymentMethodCode());
        }

        $orderEntity->setCurrency($order->getCurrency());
        $orderEntity->setAmount($order->getAmount());
        $orderEntity->setAmountVat($order->getVat());
        $orderEntity->setAmountVatIncluded($order->getAmountVatIncluded());
        $payment = $order->getPayment();
        $paymentMethodName = 'term payment';
        if($payment !== null){
            $paymentMethodName = $payment->getPaymentMethodName() ?? $paymentMethodName;
        }
        $orderEntity->setPaymentTerms($paymentMethodName);
        $merchants = $this->orderApi->fetchMerchantOrdersByOrder($order->getId());
        foreach($merchants as $merchantOrder){
            $this->merchantOrderSyncService->syncMerchantOrder($merchantOrder, $orderEntity);
        }
        $this->orderRepository->save($orderEntity);
    }

    /**
     * @param Cart $cart
     * @param User $user
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function createTemporaryOrderFromCart(
        Cart $cart,
        User $user
    )
    {
        $orderEntity = (new \AppBundle\Entity\Order())
            ->setCartId($cart->getId())
            ->setCreatedOn(new DateTimeImmutable())
            ->setAddress($cart->getAddress())
            ->setCompany($user->getCompany())
            ->setUserEmail($user->getEmail())
            ->setSite(($cart->getShippingPoint()) ? $cart->getShippingPoint()->getSite() : null)
            ->setStatus(\AppBundle\Entity\Order::STATUS_PENDING_CREATION)
            ->setIzbergStatus(Order::STATUS_PENDING_CREATION)
            ->setAmount($cart->getTotal())
            ->setAmountVatIncluded($cart->getTotalVatIncluded())
            ->setPaymentCode($cart->getPaymentMethod())
            ->setValidationNumber($cart->getValidationNumber())
            ->setAccountingEmail($cart->getAccountingEmail())
            ->setCurrency($cart->getCurrency())
            ->setShippingPoint($cart->getShippingPoint());

        $this->orderRepository->save($orderEntity);
    }


    public function getItemsByStatus (MerchantOrder $merchantOrder, $status){
        $result = [];

        $offerIndex = $this->offerService->findOffersByIds(
            array_map(
                function(Item $item) {
                    return $item->getOfferId();
                },
                $merchantOrder->getItems()->toArray()
            )
        );

        /** @var Item $item */
        foreach ($merchantOrder->getItems() as $item) {
            if ($item->getStatus() === $status) {
                $item->setManufacturerRef(null);
                $item->setSellerRef(null);

                /** @var Offer $offer */
                $offer = $offerIndex[$item->getOfferId()] ?? null;
                if($offer != null){
                    $item->setManufacturerRef($offer->getManufacturerRef());
                    $item->setSellerRef($offer->getSellerRef());
                }

                $result[] = $item;
            }
        }
        return $result;
    }
    public function updateOrderStatus(\AppBundle\Entity\Order $orderEntity, Order $orderModel)
    {
        $izbergStatus = (int)$orderModel->getStatus();
        $orderEntity->setIzbergStatus($izbergStatus);

        $pendingCreation = [Order::STATUS_PENDING_CREATION]; // app status
        $cancelled = [2000]; // izberg statuses
        $deleted = [3000]; // izberg statuses
        $initialized = [0]; // izberg statuses
        $other = [60, 80, 85, 110]; // izberg statuses

        // Pending creation
        if (in_array($izbergStatus, $pendingCreation)) {
            $orderEntity->setStatus(\AppBundle\Entity\Order::STATUS_PENDING_CREATION);
        }

        // cancelled
        if (in_array($izbergStatus, $cancelled)) {
            $orderEntity->setStatus(\AppBundle\Entity\Order::STATUS_CANCELLED);
        }

        // deleted
        if (in_array($izbergStatus, $deleted)) {
            $orderEntity->setStatus(\AppBundle\Entity\Order::STATUS_DELETED);
        }

        if (in_array($izbergStatus, $initialized)) {
            $orderEntity->setStatus(\AppBundle\Entity\Order::STATUS_RUNNING);
        }

        // other
        if (in_array($izbergStatus, $other)) {

            $totalInvoiced = 0;
            $totalRefunded = 0;
            /** @var MerchantOrder $merchantOrder */
            foreach ($orderModel->getMerchantOrders() as $merchantOrder) {
                $invoices = $this->getMerchantOrderInvoices($merchantOrder->getId());

                foreach ($invoices as $invoice) {
                    if ($invoice->status == "emitted") {
                        $totalInvoiced += round($invoice->total_amount_without_taxes, 2);
                    }
                }

                $refunds = $this->getMerchantOrderRefunds($merchantOrder->getId());
                foreach ($refunds as $refund) {
                    $totalRefunded += round($refund->amount_vat_excluded, 2);
                }
            }

            if (($orderModel->getAmount() - $totalInvoiced - $totalRefunded) < 0.01 && $orderModel->getStatus() == 85) { // past orders
                $orderEntity->setStatus(\AppBundle\Entity\Order::STATUS_PAST);
            } else { // running orders
                $orderEntity->setStatus(\AppBundle\Entity\Order::STATUS_RUNNING);
            }
        }
    }

    public function getStats($companyId, $currency, $year, $costCenterId = null){
        return $this->orderRepository->getStats($companyId, $currency, $year, $costCenterId);
    }

    public function getYears($companyId){
        return $this->orderRepository->getYears($companyId);
    }

    /**
     * @param int $merchantOrderId
     * @param bool $operator
     * @param array $options
     * @return PaymentProcessSummary
     * @throws Exception
     */
    public function paymentProcessSummary(int $merchantOrderId, bool $operator = false, array $options = []): PaymentProcessSummary
    {
        $defaultOptions = [
            'payment' => true,
            'refund' => true,
            'invoice' => true,
        ];

        ['payment' => $usePayment, 'refund' => $useRefund, 'invoice' => $useInvoice] = $options + $defaultOptions;

        $merchantOrder = $this->fetchMerchantsOrderByOrderId($merchantOrderId);
        if (!$merchantOrder) {
            throw new UnexpectedValueException(sprintf('Cannot find merchant order with id %s', $merchantOrderId));
        }

        $order = $this->fetchOrderById($merchantOrder->getOrder()->getId());

        $paymentSummary = new PaymentProcessSummary($order, $merchantOrder);

        if ($usePayment) {
            if($order->getPayment() != null){
                $payment = $this->paymentApi->getPayment($order->getPayment()->getId(), $operator);
                $paymentSummary->addPayment(
                    new Payment(
                        $payment->getId(),
                        new DateTimeImmutable($payment->getCreatedOn()),
                        $payment->getToCollectAmount()
                    )
                );
            }
        }

        if ($useRefund) {
            /** @var \Open\IzbergBundle\Model\Refund $refund */
            foreach($this->refundApi->findByFilters(['merchant_order' => $merchantOrderId], $operator) as $refund) {
                $paymentSummary->addRefund(
                    new Refund(
                        $refund->getId(),
                        new DateTimeImmutable($refund->getCreatedOn()),
                        $refund->getTotalRefundAmount()
                    )
                );
            }
        }

        if ($useInvoice) {
            foreach ($this->getMerchantOrderInvoices($merchantOrderId) as $invoice) {

                if ($invoice->status !== 'emitted') {
                    continue;
                }

                $invoicePaymentProcess = new Invoice(
                    $invoice->id_number,
                    ($invoice->created_on) ? new DateTimeImmutable($invoice->created_on) : null,
                    $invoice->total_amount_with_taxes,
                    ($invoice->due_on) ? new DateTimeImmutable($invoice->due_on) : null,
                    $invoice->pdf_file,
                    0.0,
                    $invoice->id
                );

                $creditNotes = $this->invoiceApi->fetchCreditNoteByInvoiceId($invoice->id);
                foreach($creditNotes as $creditNote) {
                    $invoicePaymentProcess->addCreditNote(
                        new CreditNote(
                            $creditNote->id_number,
                            new DateTimeImmutable($creditNote->created_on),
                            $creditNote->total_amount_with_taxes,
                            $creditNote->pdf_file,
                            $creditNote->id
                        )
                    );
                }

                $payments = $this->paymentApi->findByFilters(['customer_invoice' => $invoice->id], $operator);

                /** @var \Open\IzbergBundle\Model\Payment $payment */
                foreach($payments as $payment) {
                    $invoicePaymentProcess->addPayment(
                        new Payment(
                            $payment->getId(),
                            new DateTimeImmutable($payment->getCreatedOn()),
                            $payment->getToCollectAmount()
                        )
                    );
                }

                $refunds = $this->refundApi->findByFilters(['customer_invoice' => $invoice->id], $operator);
                foreach($refunds as $refund) {
                    $invoicePaymentProcess->addRefund(
                        new Refund(
                            $refund->getId(),
                            new DateTimeImmutable($refund->getCreatedOn()),
                            $refund->getTotalRefundAmount()
                        )
                    );
                }

                $paymentSummary->addInvoice($invoicePaymentProcess);
            }
        }

        return $paymentSummary;
    }

    private function getMerchantOrderCustomAttribute(?OrderMerchant $order, string $attributeName): ?string
    {
        if (
            $order === null
            || empty($order->getAttributes())
            || !array_key_exists($attributeName, $order->getAttributes())
        ){
            return null;
        }
        else{
            return $order->getAttributes()[$attributeName];
        }
    }

    /**
     * @return mixed
     */
    public function getIzbergAttributesService()
    {
        return $this->izbergAttributesService;
    }

    /**
     * @param mixed $izbergAttributesService
     */
    public function setIzbergAttributesService($izbergAttributesService): void
    {
        $this->izbergAttributesService = $izbergAttributesService;
    }

    public function findOrder(int $orderId): ?\AppBundle\Model\Order\Order
    {
        return $this->orderFactory->buildOrderFromOrderId($orderId);
    }

    /**
     * @param OrderItem[] $orderItems
     */
    public function updateOrderItemsDeliveryDates(array $orderItems)
    {
        $orderItemDeliveryDates = array_filter(
            array_map(
                function(OrderItem $orderItem) {
                    $expectedDeliveryDate = null;
                    $expectedShippingDate = null;

                    if ($orderItem->getDeliveryTime() !== null && $orderItem->getTrueDeliveryTime() !== null) {
                        $today = new DateTimeImmutable();
                        $expectedDeliveryDate = $today->modify('+' . $orderItem->getDeliveryTime() . ' day');
                        $expectedShippingDate = $today->modify('+' . $orderItem->getDeliveryTime() . ' day');
                        $expectedShippingDate = $expectedShippingDate->modify('-' . $orderItem->getTrueDeliveryTime() . ' day');
                    }

                    if ($orderItem->getExpectedDeliveryDate() && $orderItem->getExpectedShippingDate()) {
                        $expectedDeliveryDate = $orderItem->getExpectedDeliveryDate();
                        $expectedShippingDate = $orderItem->getExpectedShippingDate();
                    }

                    return [$orderItem->getId(), $expectedDeliveryDate, $expectedShippingDate, $orderItem->getQuantity()];
                },

                $orderItems
            )
        );

        $this->orderApi->setDeliveryDates($orderItemDeliveryDates);
    }

    public function updateBuyerOrderInternalId(int $merchantOrderId, string $value)
    {
        $this->izbergAttributesService->updateMerchantOrderAttributes($merchantOrderId, [
            'ZZC-Internal-Buyer-Order-ID' => $value
        ]);
    }

    public function updateMerchantOrderItemExtraInfo(int $orderItemId, string $field, ?string $value)
    {
        $this->orderApi->updateMerchantOrderItemExtraInfo($orderItemId, $field, $value);
        if ($field === 'order-line') {
            $this->orderItemService->syncOrderLine($orderItemId, $value);
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    public function getOrderSite(int $orderId): ?Site
    {
        /** @var \AppBundle\Entity\Order|null $order */
        $order = $this->orderRepository->find($orderId);

        return $order ? $order->getSite() : null;
    }

    public function getOrderSiteFromIzbergOrderId(int $izbergOrderId): ?Site
    {
        /** @var \AppBundle\Entity\Order|null $order */
        $order = $this->orderRepository->findOneBy([
            'izbergId' => $izbergOrderId
        ]);

        return $order ? $order->getSite() : null;
    }


    public function getReconciliationKeyFromOrder(Order $order): string
    {
        /** @var MerchantOrder $firstMerchantOrder */
        $firstMerchantOrder = $order->getMerchantOrders()[0];
        $izbMerchantOrder = $this->fetchMerchantsOrderByOrderId($firstMerchantOrder->getId());
        return $izbMerchantOrder->getAttributes()['reconciliation_key'] ?? '';
    }

    public function buildDocumentFileUploaded($attributes)
    {
        return array_filter($attributes, function($file) {
            $allowedExtensions = ['pdf', 'zip', 'jpg', 'jpeg', 'png'];
            $pattern = '/\.(' . implode('|', $allowedExtensions) . ')$/i';
            return preg_match($pattern, $file);
        });
    }

    public function getDocumentFileUploaded($files)
    {
        try {
            if (empty($files)) return false;
            if (count($files) === 1) return array_values($files)[0];

            $zip = new ZipArchive();
            $zipFilename = tempnam(sys_get_temp_dir(), 'zip');

            if ($zip->open($zipFilename, ZipArchive::CREATE) !== TRUE) {
                throw new Exception("Cannot create ZIP archive");
            }

            foreach ($files as $file) {
                $fileName = basename($file);
                $fileContent = file_get_contents($file);
                $zip->addFromString($fileName, $fileContent);
            }
            $zip->close();

            // set the zip title
            header('Content-Type: application/zip');
            header('Content-Disposition: attachment; filename=merchant_documents_uploaded.zip');
            header('Content-Length: ' . filesize($zipFilename));
            readfile($zipFilename);

            unlink($zipFilename);

            return true;

        } catch (Exception $e) {
            $this->logger->error($e->getMessage(), ['exception' => $e]);
            return false;
        }
    }
}
