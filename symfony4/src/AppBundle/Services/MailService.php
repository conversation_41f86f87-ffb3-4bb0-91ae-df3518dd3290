<?php

namespace AppBundle\Services;

use AppBundle\Entity\Cart;
use AppBundle\Entity\Company;
use AppBundle\Entity\Node;
use AppBundle\Entity\NodeContent;
use AppBundle\Entity\Site;
use AppBundle\Entity\TransactionalEmail;
use AppBundle\Entity\User;
use AppBundle\Exception\MailException;
use AppBundle\Exception\MailValidationException;
use AppBundle\Message\SendEmailMessage;
use AppBundle\Model\EmailTemplate;
use AppBundle\Repository\CartRepository;
use AppBundle\Repository\CompanyRepository;
use AppBundle\Repository\NodeRepository;
use AppBundle\Repository\OrderRepository;
use AppBundle\Repository\TransactionalEmailRepository;
use Doctrine\ORM\EntityManagerInterface;
use FOS\UserBundle\Mailer\MailerInterface as FOSMailerInterface;
use FOS\UserBundle\Model\UserInterface;
use Open\IzbergBundle\Model\MerchantOrder;
use Open\IzbergBundle\Model\Order;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use RuntimeException;
use Soundasleep\Html2Text;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Mailer\Mailer;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Mime\Address;
use Symfony\Component\Mime\Email;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Mailer\MailerInterface;
use Twig\Environment as Twig_Environment;

// todo replace Twig_Environment

class MailService implements FOSMailerInterface, LoggerAwareInterface
{
    public const DEFAULT_LOCALE = "en";

    //common template variables (to avoid misspelling errors and for more consistency)
    public const FIRST_NAME_VAR = "firstName";
    public const LAST_NAME_VAR = "lastName";

    //LIST OF TEMPLATES
    public const COLLABORATOR_REGISTRATION_CONFIRMED = 'BUYER_ACCOUNT_NEW_USER_TOUSER';
    public const USER_RESET_MDP = 'RESET_PASSWORD_TO_USER';
    public const BUYER_REGISTRATION_CONFIRMED = 'BUYER_ACCOUNT_CREATION_TO_BUYER';
    public const MERCHANT_REGISTRATION_CONFIRMED = 'VENDOR_ACCOUNT_CREATION_TO_VENDOR';
    public const OPERATOR_MERCHANT_REGISTRATION_CONFIRMED = 'VENDOR_ACCOUNT_CREATION_TO_OPERATOR';
    public const OPERATOR_ACCOUNT_CREATED = 'BUYER_ACCOUNT_CREATION_TO_OPERATOR';
    public const OWNERS_COMPANY_INFO_MODIFIED = 'BUYER_ACCOUNT_INFO_UPDATED_TO_BUYER';
    public const OWNERS_COMPANY_CONTACT_MODIFIED = 'BUYER_ACCOUNT_CONTACT_INFO_UPDATED_TO_BUYER';

    public const TICKET_NEW_ADMINISTRATOR_SENDER = 'TICKET_THREAD_NEW_FROM_OPERATOR_TO_OPERATOR';
    public const TICKET_NEW_USER_SENDER = 'TICKET_THREAD_NEW_FROM_BUYER_TO_BUYER';
    public const TICKET_NEW_ADMINISTRATOR_RECIPIENT = 'TICKET_THREAD_NEW_FROM_BUYER_TO_OPERATOR';
    public const TICKET_NEW_USER_RECIPIENT = 'TICKET_THREAD_NEW_FROM_OPERATOR_TO_BUYER';
    public const TICKET_UPDATE_USER = 'TICKET_THREAD_UPDATED_TO_BUYER';
    public const TICKET_UPDATE_ADMINISTRATOR = 'TICKET_THREAD_UPDATED_TO_OPERATOR';

    public const COMPANY_VALIDATED = 'BUYER_ACCOUNT_VALIDATED_TO_BUYER';
    public const COMPANY_DEACTIVATED = 'BUYER_ACCOUNT_DEACTIVATED_TO_BUYER';
    public const COMPANY_REJECTED = 'BUYER_ACCOUNT_REJECTED_TO_BUYER';
    public const COMPANY_PENDING_VALIDATION = 'BUYER_ACCOUNT_IN_PENDING_VALIDATION_TO_OPERATOR';

    public const MERCHANT_REJECTED = 'VENDOR_ACCOUNT_REJECTED_TO_VENDOR';
    public const MERCHANT_ACCEPTED = 'VENDOR_ACCOUNT_VALIDATED_TO_VENDOR';

    public const COMPANY_CREATED_SINCE_X_DAYS = 'BUYER_ACCOUNT_UNCOMPLETED_AFTER_X_DAYS_TO_BUYER';

    public const COMPANY_REQUEST_PAYMENT_TERM = 'TERM_PAYMENT_REQUEST_TO_OPERATOR';
    public const OPERATOR_REJECT_COMPANY_PAYMENT_TERM = 'TERM_PAYMENT_REQUEST_REJECTED_TO_BUYER';
    public const OPERATOR_ACCEPT_COMPANY_PAYMENT_TERM = 'TERM_PAYMENT_REQUEST_ACCEPTED_TO_BUYER';

    public const CART_ASSIGN_USER = 'CART_ASSIGN_TO_BUYER';
    public const CART_REJECT_ASSIGN = 'CART_ASSIGN_REJECTED_TO_BUYER';
    public const CART_ACCEPT_ASSIGN = 'CART_ASSIGN_ACCEPTED_TO_BUYER';

    //PAYMENT PROCESS TEMPLATE
    //Use following naming method: RECIPIENT_EVENT
    public const BUYER_MERCHANT_ORDER_CONFIRMED = "ORDER_CONFIRMED_BY_VENDOR_TO_BUYER";
    public const ORDER_REFUSED_BY_VENDOR_PRE_BANK_TRANSFER_TO_BUYER = "ORDER_REFUSED_BY_VENDOR_PRE_BANK_TRANSFER_TO_BUYER";
    public const ORDER_REFUSED_BY_VENDOR_PRE_CREDIT_CARD_TO_BUYER = "ORDER_REFUSED_BY_VENDOR_PRE_CREDIT_CARD_TO_BUYER";
    public const ORDER_REFUSED_BY_VENDOR_TERM_BANK_TRANSFER_TO_BUYER = "ORDER_REFUSED_BY_VENDOR_TERM_BANK_TRANSFER_TO_BUYER";
    public const BUYER_MERCHANT_ORDER_PROCESSED = "ORDER_PROCESSED_BY_VENDOR_TO_BUYER";
    public const BUYER_PRE_PAYMENT_TRANSFER_INITIAL = "PAYMENT_PREPAYMENT_BANK_TRANSFER_INSCTRUCTIONS_TO_BUYER";

    public const PAYMENT_TERMPAYMENT_BANK_TRANSFER_INSCTRUCTIONS_TO_BUYER = " PAYMENT_TERMPAYMENT_BANK_TRANSFER_INSCTRUCTIONS_TO_BUYER";
    public const PAYMENT_TERMPAYMENT_BANK_TRANSFER_REMINDER_TO_BUYER = "PAYMENT_TERMPAYMENT_BANK_TRANSFER_REMINDER_TO_BUYER";

    public const BUYER_SEND_INVOICE = "SEND_INVOICE_TO_BUYER";
    public const BUYER_SEND_CREDIT_NOTE = "SEND_CREDIT_NOTE_TO_BUYER";

    public const BUYER_REFUND_PAYMENT_NOT_ENOUGH = "REFUND_AUTO_NOT_ENOUGH_PAYMENT_TO_BUYER";
    public const BUYER_REFUND_PAYMENT_TOO_MUCH = "REFUND_AUTO_EXCEEDED_PAYMENT_TO_BUYER";

    //sent to the seller when a new command is available after a paid action
    public const SELLER_NEW_COMMAND_PAID = "ORDER_NEW_TOCONFIRM_TO_VENDOR";

    //send to buyer to inform him that this command is confirmed
    public const BUYER_ORDER_CONFIRMATION = "ORDER_CONFIRMATION_TO_BUYER";
    // in time payment process: Inform the buyer that the full payment for its order has been received
    public const BUYER_FULL_PAYMENT_RECEIVED = "PAYMENT_FULL_RECEIVED_TO_BUYER";
    // in time payment process: Inform the buyer that a partial payment has been received
    public const BUYER_PARTIAL_PAYMENT_RECEIVED = "PAYMENT_PARTIAL_RECEIVED_TO_BUYER";
    // in time payment process: Inform the seller that a partial payment has been received
    public const SELLER_PARTIAL_PAYMENT_RECEIVED = "PAYMENT_PARTIAL_RECEIVED_TO_VENDOR";
    // in time payment process: Inform the seller that the full payment for its order has been received
    public const SELLER_FULL_PAYMENT_RECEIVED = "PAYMENT_FULL_RECEIVED_TO_VENDOR";

    public const AUTOMATIC_ORDER_REFUSED_NO_PAYMENT_RECEIVED = "ORDER_CANCEL_AUTO_NO_PAYMENT_RECEIVED_TO_BUYER";
    public const AUTOMATIC_ORDER_REFUSED_TERM_PAYMENT = "ORDER_CANCEL_AUTO_NO_VENDOR_CONFIRM_TERM_BANK_TRANSFER_TO_BUYER";
    public const AUTOMATIC_ORDER_REFUSED_PREPAYMENT_CB = "ORDER_CANCEL_AUTO_NO_VENDOR_CONFIRM_PRE_CREDIT_CARD_TO_BUYER";
    public const AUTOMATIC_ORDER_REFUSED_PREPAYMENT_BANK_TRANSFER = "ORDER_CANCEL_AUTO_NO_VENDOR_CONFIRM_PRE_BANK_TRANSFER_TO_BUYER";

    // sent to buyer when a refund has been performed
    public const BUYER_REFUND_DONE = "BUYER_REFUND_DONE";

    // Send information about IZB to application specific price importation
    public const SPECIFIC_PRICE_UPLOADED = "SPECIFIC_PRICE_UPLOAD_TO_VENDOR";

    // sent to support when an error occurred
    public const SUPPORT_TECHNICAL_ERROR = "SUPPORT_TECHNICAL_ERROR_TO_ADMIN";

    public const ILLEGAL_CONTENT_CREATED = "ILLEGAL_CONTENT_TO_OPERATOR";
    public const PURCHASE_REQUEST_SEND_NOT_FOUND = "PURCHASE_REQUEST_SEND_NOT_FOUND";

    public const BAFV_REQUEST_APPROVED = "BAFV_REQUEST_APPROVED";
    public const BAFV_REQUEST_REJECTED = "BAFV_REQUEST_REJECTED";
    public const OPERATOR_NEW_BAFV_REQUEST = "OPERATOR_NEW_BAFV_REQUEST";

    public const UPELA_ADDRESS_ISSUE = "UPELA_ADDRESS_ISSUE";
    public const UPELA_QUOTATION_REQUEST_ISSUE = "UPELA_QUOTATION_REQUEST_ISSUE";

    public const EMITTED_INVOICE_TO_ACCOUNTING_DEPARTMENT = 'EMITTED_INVOICE_TO_ACCOUNTING_DEPARTMENT';

    private const EMAIL_ID = "EMAIL_ID";
    private const LANGUAGE = "LANGUAGE";
    private const USERS = "USERS";

    private const PARAM_FOUSER_FROM_EMAIL = "fosuser_from_email";
    private const PARAM_FOUSER_FROM_NAME = "fosuser_from_name";

    private const PARAM_FORCE_LOCALE = "force_locale";

    private const LOG_EXCEPTION_STR = 'exception';

    public const NEW_MERCHANT_RESPONSE = 'TICKET_THREAD_UPDATED_BY_VENDOR_TO_BUYER';

    private const LOG_WPS_EVENT = "wps_event";

    private const TOKEN = 'token';

    protected Mailer $mailer;
    protected LoggerInterface $logger;
    protected EntityManagerInterface $em;
    protected Twig_Environment $twig;
    protected array $parameters;
    protected RouterInterface $router;
    protected SecurityService $securityService;
    private string $absoluteUrl;
    private string $protocol;
    private string $environment;
    private CompanyRepository $companyRepository;
    private array $bcc;
    private OrderRepository $orderRepository;
    private MessageBusInterface $messageBus;

    /**
     * MailService constructor.
     *
     * @param SecurityService        $securityService ,
     * @param Mailer           $mailer          the mailer
     * @param EntityManagerInterface $em              the application entityManager
     * @param Twig_Environment       $twig            the twig template engine
     * @param RouterInterface        $router
     * @param array                  $parameters      the application parameters
     * @param string                 $absoluteUrl
     * @param string                 $protocol
     * @param string                 $environment
     * @param CompanyRepository      $companyRepository
     */
    public function __construct(
        array                  $parameters,
        string                 $absoluteUrl,
        string                 $protocol,
        string                 $environment,
        SecurityService        $securityService,
        MailerInterface           $mailer,
        EntityManagerInterface $em,
        Twig_Environment       $twig,
        RouterInterface        $router,
        CompanyRepository      $companyRepository,
        OrderRepository        $orderRepository,
        MessageBusInterface    $messageBus
    )
    {
        $this->mailer = $mailer;
        $this->securityService = $securityService;
        $this->em = $em;
        $this->twig = $twig;
        $this->router = $router;
        $this->parameters = $parameters;
        $this->absoluteUrl = $absoluteUrl;
        $this->protocol = $protocol;
        $this->environment = $environment;
        $this->companyRepository = $companyRepository;
        $this->orderRepository = $orderRepository;
        $this->messageBus = $messageBus;
    }

    /**
     * Send an email asynchronously through the Messenger queue system
     *
     * @param string      $emailIdentifier  the identifier of the email to send
     * @param string      $lang             the language of the email
     * @param mixed       $toUsers          one email address or an array of email address (array of string)
     * @param array       $data             the list of variables for the template
     * @param string|null $fromEmailAddress the email address of the sender. If null get the one from the configuration.
     * @param string|null $fromName         the name to use for the sender. If null get the one from the configuration.
     * @param array       $attachFiles      optional files to be attached to the email. List of Swift_Attachment or AttachmentLink
     * @param bool        $async            whether to send the email asynchronously (default: true)
     *
     * @return bool
     * @throws MailException
     */
    public function sendEmailMessage(
        string $emailIdentifier,
        string $lang,
               $toUsers,
        array  $data,
        string $fromEmailAddress = null,
        string $fromName = null,
               $attachFiles = [],
        bool   $async = true
    )
    {
        if ($async) {
            // Send email asynchronously through Messenger
            $message = new SendEmailMessage(
                $emailIdentifier,
                $lang,
                $toUsers,
                $data,
                $fromEmailAddress,
                $fromName,
                $attachFiles
            );

            $this->messageBus->dispatch($message);

            // Log that the email was queued
            $this->logger->info("Email queued for async processing",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::EMAIL_SENT,
                    "email_identifier" => $emailIdentifier,
                    "priority" => $message->getPriority(),
                    "to" => $toUsers,
                    "lang" => $lang
                ])
            );

            return true;
        }

        // Send email synchronously (original behavior)
        return $this->sendEmailMessageSync($emailIdentifier, $lang, $toUsers, $data, $fromEmailAddress, $fromName, $attachFiles);
    }

    /**
     * Send an email synchronously (original implementation)
     *
     * @param string      $emailIdentifier  the identifier of the email to send
     * @param string      $lang             the language of the email
     * @param mixed       $toUsers          one email address or an array of email address (array of string)
     * @param array       $data             the list of variables for the template
     * @param string|null $fromEmailAddress the email address of the sender. If null get the one from the configuration.
     * @param string|null $fromName         the name to use for the sender. If null get the one from the configuration.
     * @param array       $attachFiles      optional files to be attached to the email. List of Swift_Attachment or AttachmentLink
     *
     * @return bool
     * @throws MailException
     */
    public function sendEmailMessageSync(
        string $emailIdentifier,
        string $lang,
               $toUsers,
        array  $data,
        string $fromEmailAddress = null,
        string $fromName = null,
               $attachFiles = []
    )
    {
        //
        // AJOUTEZ --> , force_locale: 'en'
        // Dans services.yml pour activer cette fonctionnalité
        //
        // LANG OVERRIDE
        //
        if (isset($this->parameters[self::PARAM_FORCE_LOCALE])) {
            if ($this->parameters[self::PARAM_FORCE_LOCALE] !== false) {
                $lang = $this->parameters[self::PARAM_FORCE_LOCALE];
            }
        }

        //add a variable host that can be used to get the base url of the application
        $data["host"] = $this->protocol . $this->absoluteUrl;

        //load the template
        /** @var NodeRepository $nodeRepository */
        $nodeRepository = $this->em->getRepository(Node::class);

        /**
         * @var Node $node
         */
        $node = $nodeRepository->findOneEmailBySlugAndLanguage($emailIdentifier, $lang);

        if ($node === null) {
            $message = "error while getting email template: No template found or more than one template found";
            $this->triggerError($emailIdentifier, $lang, $toUsers, $message);
            return false;
        }

        //load the transactional email setting
        /** @var TransactionalEmailRepository $transactionalEmailRepository */
        $transactionalEmailRepository = $this->em->getRepository(TransactionalEmail::class);

        $transactionalEmail = $transactionalEmailRepository->findOneByEmailIdentifier($emailIdentifier);

        if ($transactionalEmail !== null && $transactionalEmail->isNotActive()) {
            $message = "error while getting email template: This email template is not active";
            $this->triggerError($emailIdentifier, $lang, $toUsers, $message);
            return false;
        }

        /**
         * @var NodeContent $template
         */
        $template = $node->getContent($lang);
        $templateRaw = str_replace('{{content}}', '{{content|nl2br}}', $template->getBody());
        $emailBody = $this->buildContentFromTwigTemplate($templateRaw, $data, $emailIdentifier);
        $emailObject = $this->buildContentFromTwigTemplate($template->getTitle(), $data, $emailIdentifier);
        if ($emailBody === null || $emailObject === null) {
            return false;
        }

        if (is_array($toUsers)) {
            $receivers = [];
            foreach ($toUsers as $key => $receiver) {
                if (!filter_var($receiver, FILTER_VALIDATE_EMAIL)) {
                    $this->logger->error("receiver email address invalid",
                        LogUtil::buildContext([
                            LogUtil::EVENT_NAME => EventNameEnum::EMAIL_ERROR,
                            "subject" => $emailBody,
                            "to" => $receiver
                        ])
                    );
                } else {
                    $receivers[] = $receiver;
                }
            }
            if (count($receivers) > 0) {
                $toUsers = $receivers;
            } else {
                return false;
            }
        } else {
            if (!filter_var($toUsers, FILTER_VALIDATE_EMAIL)) {
                $this->logger->error("receiver email address invalid",
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::EMAIL_ERROR,
                        "subject" => $emailBody,
                        "to" => $toUsers
                    ])
                );
                return false;
            }
        }

        $fromEmailParam = $fromEmailAddress;

        if ($fromEmailParam === null) {
            $fromEmailParam = $this->parameters[self::PARAM_FOUSER_FROM_EMAIL];
        }

        if (!filter_var($fromEmailParam, FILTER_VALIDATE_EMAIL)) {
            $this->logger->error("sender email address invalid",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::EMAIL_ERROR,
                    "subject" => $emailBody,
                    "to" => $toUsers
                ])
            );
            return false;
        }

        $fromNameParam = $fromName;
        if ($fromNameParam === null) {
            $fromNameParam = $this->parameters[self::PARAM_FOUSER_FROM_NAME];
        }

        if ($this->environment != null && $this->environment != '') {
            $emailObject = $this->environment . $emailObject;
        }

        if (!is_array($toUsers)) {
            $toUsers = [$toUsers];
        }


        $message = (new Email())
            ->subject($emailObject)
            ->from(new Address($fromEmailParam, $fromNameParam))
            ->to(...$toUsers)
            ->text($emailBody, "text/html");

        if ($this->hasConfiguredBcc()) {
            foreach ($this->bcc as $bccEmailAddress) {
                $message->addBcc($bccEmailAddress);
            }
        }

        //send the message
        try {
            $this->mailer->send($message);
        } catch (TransportExceptionInterface $e) {
            $this->logger->error("Error occurred while Sending email: SwiftMailer return a 0 status",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::EMAIL_ERROR,
                    "message_error" => $e->getMessage(),
                    "subject" => $emailBody,
                    "to" => $toUsers
                ])
            );
            return false;
        }

        //here, we consider, that the email has been sent, so add a log
        $this->logger->info("an email has been sent",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME => EventNameEnum::EMAIL_SENT,
                "subject" => $emailObject,
                "from" => $fromEmailParam,
                "to" => $toUsers,
                "slug" => $emailIdentifier
            ])
        );

        return true;
    }

    /**
     * validate an email template. Throw an error if a validation error occurred
     *
     * @param EmailTemplate $emailTemplate
     *
     * @return bool
     * @throws MailException
     * @throws MailValidationException
     */
    public function validateEmailTemplate(EmailTemplate $emailTemplate): bool
    {
        $content = $this->buildContentFromTwigTemplate(
            $emailTemplate->getContent(),
            $emailTemplate->getVariables(),
            $emailTemplate->getTemplateName(),
            true
        );

        $subject = $this->buildContentFromTwigTemplate(
            $emailTemplate->getSubject(),
            $emailTemplate->getVariables(),
            $emailTemplate->getTemplateName(),
            true
        );

        $validationWarnings = [];
        foreach ([$content, $subject] as $template) {
            if (preg_match('#\{.*\{.*\}.*\}#', $template)) {
                $validationWarnings[] = 'template has incorrect formatted variables: ' . $template;
            }
        }

        if (count($validationWarnings)) {
            throw new MailValidationException(implode("\n", $validationWarnings));
        }

        return true;
    }

    /**
     * Send an email to a user to confirm the account creation.
     *
     * @param UserInterface $user
     */
    public function sendConfirmationEmailMessage(UserInterface $user): void
    {
        if (!$user instanceof User) {
            throw new \InvalidArgumentException();
        }

        if ($user->getRoles()) {
            if ($this->securityService->isBuyer($user)) {
                $notificationId = self::BUYER_REGISTRATION_CONFIRMED;
            } else {
                $this->logger->info(
                    "unable to notify this user for account creation: Unknown user type: " . $user->getRoles()[0],
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::EMAIL_ERROR,
                        LogUtil::USER_NAME => $user->getUsername()
                    ])
                );
                return;
            }

            $this->sendEmailMessage(
                $notificationId,
                $user->getLocale(),
                $user->getEmail(),
                array(
                    MailService::FIRST_NAME_VAR => $user->getFirstname(),
                    MailService::LAST_NAME_VAR => $user->getLastname(),
                    "companyName" => $user->getCompany()->getName()
                )
            );
        }

        $this->logger->info(
            "unable to notify this user for account creation: User has no role",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME => EventNameEnum::EMAIL_ERROR,
                LogUtil::USER_NAME => $user->getUsername()
            ])
        );
    }

    /**
     * Send an email to the user to indicate that its account has been created
     *
     * @param UserInterface $user
     *
     * @return bool
     */
    public function sendUserConfirmationEmailMessage(UserInterface $user)
    {
        if (!$user instanceof User) {
            throw new \InvalidArgumentException();
        }

        $notificationId = self::COLLABORATOR_REGISTRATION_CONFIRMED;

        $url = $this->generateUrl('fos_user_resetting_reset', array(self::TOKEN => $user->getConfirmationToken()));
        return $this->sendEmailMessage(
            $notificationId,
            $user->getLocale(),
            $user->getEmail(),
            array(
                MailService::FIRST_NAME_VAR => $user->getFirstname(),
                MailService::LAST_NAME_VAR => $user->getLastname(),
                "url" => $url
            )
        );
    }


    /**
     * Send an email to a user to confirm the password reset.
     *
     * @param UserInterface $user
     *
     * @return bool
     */
    public function sendResettingEmailMessage(UserInterface $user): void
    {
        if (!$user instanceof User) {
            throw new \InvalidArgumentException();
        }

        $notificationId = self::USER_RESET_MDP;
        $url = $this->router->generate(
            'fos_user_resetting_reset',
            array(
                self::TOKEN => $user->getConfirmationToken()
            ),
            UrlGeneratorInterface::ABSOLUTE_URL
        );
        if ($user->isEnabled()) {
            $this->sendEmailMessage(
                $notificationId,
                $user->getLocale(),
                $user->getEmail(),
                array(
                    MailService::FIRST_NAME_VAR => $user->getFirstname(),
                    MailService::LAST_NAME_VAR => $user->getLastname(),
                    "url" => $url
                )
            );
        }
    }

    /**
     * helper to get the default from email address of the application
     * @return mixed
     */
    public function getDefaultFromEmailAddress()
    {
        return $this->parameters[self::PARAM_FOUSER_FROM_EMAIL];
    }

    /**
     * @param string     $message    the error message
     * @param string     $templateId the identifier of the template
     * @param string     $lang       the language of the template
     * @param mixed      $toUsers    the user or a list of users
     * @param array      $context    optional context (key/value)
     * @param \Exception $exception
     */
    public function logEmailError($message, $templateId, $lang, $toUsers, $context = [], \Exception $exception = null)
    {

        $data = array_merge($context, array(
            self::EMAIL_ID => $templateId,
            self::LANGUAGE => $lang,
            self::USERS => $toUsers,
            LogUtil::EVENT_NAME => EventNameEnum::EMAIL_ERROR));

        if ($exception) {
            $data[self::LOG_EXCEPTION_STR] = $exception->getTraceAsString();
        }

        $this->logger->error($message,
            LogUtil::buildContext($data));
    }


    /*************************************************************************************************
     * INTERNAL TOOLS
     ************************************************************************************************/

    /**
     * @param string $html the html content
     *
     * @return string
     */
    private function getTextFromHtml(string $html)
    {
        $options = [
            'ignore_errors' => true,
        ];

        return Html2Text::convert($html, $options);
    }

    /**
     * trigger an email error
     *
     * @param string $emailIdentifier the identifier of the email
     * @param string $lang            the language of the email
     * @param mixed  $toUsers         the list of recipients
     * @param string $message         the error message
     */
    private function triggerError(string $emailIdentifier, string $lang, $toUsers, string $message)
    {
        $this->logger->error($message,
            LogUtil::buildContext([
                LogUtil::EVENT_NAME => EventNameEnum::EMAIL_ERROR,
                self::EMAIL_ID => $emailIdentifier,
                self::LANGUAGE => $lang,
                self::USERS => $toUsers
            ])
        );
    }

    /**
     * build a content from a twig template
     *
     * @param string  $templateText   the text of the template
     * @param array   $variables      the variables of the template
     * @param boolean $throwException if true, the function throws an exception when an error occurred. If false the function returns null in an error case.
     *
     * @return string|null the message as a string
     * @throws MailException if any error occurred when the $throwException is true
     */
    private function buildContentFromTwigTemplate($templateText, $variables, $slug, $throwException = false)
    {
        try {
            $this->twig->enableStrictVariables();
            $template = $this->twig->createTemplate($templateText);
            $renderTemplate = $template->render($variables);
            $this->twig->disableStrictVariables();

        } catch (\Throwable $e) {
            $this->twig->disableStrictVariables();
            $this->logger->error(
                "Error while building email from template: " . $e->getMessage(),
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::EMAIL_ERROR,
                    self::LOG_EXCEPTION_STR => $e->getTraceAsString(),
                    'available_template_variables' => $variables,
                    'slug' => $slug
                ])
            );
            if ($throwException) {
                throw new MailException($e->getMessage(), 0, $e);
            } else {
                return null;
            }
        }

        return $renderTemplate;
    }

    public function generateUrl($routeName, $parameters)
    {
        $path = $this->router->generate($routeName, $parameters, UrlGeneratorInterface::ABSOLUTE_PATH);
        return $this->protocol . $this->absoluteUrl . $path;
    }

    public function setBcc(array $bcc): void
    {
        $this->bcc = $bcc;
    }

    public function hasConfiguredBcc(): bool
    {
        return (count($this->bcc) >= 1);
    }

    /**
     * Use to notify a buyer
     *
     * @param Company $company
     * @param string  $template the name of the email templates
     * @param array   $param    the list of the params to be used to populate the template
     * @param         $logEvent
     * @param         $id
     * @param         $text
     */
    private function sendEmailToBuyer(Company $company, string $template, $param, $logEvent, ?Order $order = null)
    {
        if(in_array($template, [self::BUYER_ORDER_CONFIRMATION, self::BUYER_MERCHANT_ORDER_PROCESSED, self::BUYER_MERCHANT_ORDER_CONFIRMED,
                    self::ORDER_REFUSED_BY_VENDOR_PRE_BANK_TRANSFER_TO_BUYER, self::ORDER_REFUSED_BY_VENDOR_PRE_CREDIT_CARD_TO_BUYER, self::ORDER_REFUSED_BY_VENDOR_TERM_BANK_TRANSFER_TO_BUYER,
                    self::AUTOMATIC_ORDER_REFUSED_PREPAYMENT_CB, self::AUTOMATIC_ORDER_REFUSED_TERM_PAYMENT, self::AUTOMATIC_ORDER_REFUSED_PREPAYMENT_BANK_TRANSFER, self::AUTOMATIC_ORDER_REFUSED_NO_PAYMENT_RECEIVED])) {
            return $this->sendOneToOneEmailToBuyer($company, $template, $param, $logEvent, $order);
        }

        $notifiedUsers = [];

        $this->logger->info(
            " + start to notify users from company izberg userID ",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_PROCESS,
                "izbergUserId" => $company->getIzbergUserId(),
                self::LOG_WPS_EVENT => $logEvent
            ])
        );

        /** @var \AppBundle\Entity\Order $orderEntity */
        $orderEntity = $order ? $this->orderRepository->findOneBy(['izbergId' => $order->getId()]) : null;
        $orderSite = $orderEntity ? $orderEntity->getSite() : null;

        if (!$orderSite) {
            $msg = sprintf("unable to notify user from this company: no site found for this order %s", $order->getId());
            $this->logger->error($msg);
            throw new RuntimeException($msg);
        }

        /** @var User $user */
        foreach ($company->getUsers() as $user) {
            if ($user !== null && $user->isEnabled() && !in_array($user->getEmail(), $notifiedUsers)) {
                $userSitesIds = array_map(function (Site $site) {
                    return $site->getId();
                }, $user->getSites()->getValues());

                $this->logger->info(
                    sprintf('ORDER EMAIL NOTIFICATION FOR ORDER SITE #%s | user %s -> sites list %s', $orderSite->getId(), $user->getId(), implode(' | ', $userSitesIds))
                );

                if ($user->isBuyerAdmin() || in_array($orderSite->getId(), $userSitesIds)) {
                    $notifiedUsers [] = $user->getEmail();
                    $param[MailService::FIRST_NAME_VAR] = $user->getFirstname();
                    $param[MailService::LAST_NAME_VAR] = $user->getLastname();
                    $this->sendEmailMessage($template,
                        $user->getLocale(),
                        $user->getEmail(),
                        $param,
                        $this->parameters[self::PARAM_FOUSER_FROM_EMAIL],
                        $this->parameters[self::PARAM_FOUSER_FROM_NAME]
                    );
                }
            }
        }
    }

    /**
     * Use to notify a buyer
     *
     * @param Order  $order
     * @param string $template the name of the email templates
     * @param array  $param    the list of the params to be used to populate the template
     * @param        $logEvent
     */
    public function sendEmailToBuyersOrder(Order $order, string $template, $param, $logEvent)
    {
        /** @var Company $company */
        $company = $this->companyRepository->findCompanyByIzbergUserId($order->getUser()->getId());

        if ($company !== null) {
            $this->sendEmailToBuyer($company, $template, $param, $logEvent, $order);
        } else {
            $this->logger->error(
                " * unable to notify users: unable to find company with this izberg userID ",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_GENERAL_ERROR,
                    "izbergUserId" => $order->getUser()->getId(),
                    WPSService::LOG_STATUS_CODE => "NOTIFICATION_ERROR",
                    self::LOG_WPS_EVENT => $logEvent,
                    'orderId' => $order->getId(),
                ])
            );
        }
    }

    /**
     * Use to notify a buyer
     *
     * @param MerchantOrder $merchantOrder
     * @param string        $template the name of the email templates
     * @param array         $param    the list of the params to be used to populate the template
     * @param               $logEvent
     */
    public function sendEmailToBuyersMerchantOrder(MerchantOrder $merchantOrder, string $template, $param, $logEvent)
    {
        /** @var Company $company */
        $company = $this->companyRepository->findCompanyByIzbergUserId($merchantOrder->getUser()->getId());

        if ($company !== null) {
            $this->sendEmailToBuyer($company, $template, $param, $logEvent);
        } else {
            $this->logger->error(" * unable to notify users: unable to find company with this izberg userID ",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_GENERAL_ERROR,
                    "izbergUserId" => $merchantOrder->getUser()->getId(),
                    WPSService::LOG_STATUS_CODE => "NOTIFICATION_ERROR",
                    self::LOG_WPS_EVENT => $logEvent,
                    'merchantOrderId' => $merchantOrder->getId()
                ])
            );
        }

    }

    /**
     * Send email to operators
     *
     * @param string $template
     * @param array  $templateVariables
     */
    public function sendEmailToOperators(string $template, array $templateVariables = [])
    {
        $operators = $this->securityService->getOperators();

        /** @var User $operator */
        foreach ($operators as $operator) {
            if ($operator->isEnabled()) {
                $this->sendEmailMessage(
                    $template,
                    "en",
                    $operator->getEmail(),
                    $templateVariables
                );
            }
        }
    }

    public function getCartByOrder(Order $order) {
        $cartId = $order->getCartId();
        if(empty($cartId)) {
            $cartId = $order->getCart()->getId();
        }

        if(empty($cartId)) {
            return NULL;
        }

        $cartRepository = $this->em->getRepository(\AppBundle\Entity\Cart::class);
        return $cartRepository->find($cartId);
    }

    private function sendOneToOneEmailToBuyer(Company $company, string $template, $param, $logEvent, ?Order $order = null)
    {
        $this->logger->info(sprintf("notify_one_to_one start sendOneToOneEmailToBuyer , template: %s", $template));

        if(empty($order)) {
            $this->logger->info(
                "notify_one_to_one, no order skip process",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_PROCESS,
                    "izbergUserId" => $company->getIzbergUserId(),
                    self::LOG_WPS_EVENT => $logEvent
                ])
            );
            return true;
        }

        $cartEntity = $this->getCartByOrder($order);
        if (!$cartEntity) {
            $msg = sprintf("notify_one_to_one unable to identify cart from the order: %s", $order->getId());
            $this->logger->error($msg);
            throw new RuntimeException($msg);
        }

        $this->logger->info(
            "notify_one_to_one start to notify one to one user from cartID ",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_PROCESS,
                "izbergUserId" => $company->getIzbergUserId(),
                self::LOG_WPS_EVENT => $logEvent
            ])
        );

        $createdUser = $cartEntity->getCreatedUser();
        if (!$createdUser) {
            $msg = sprintf("notify_one_to_one unable to identify createdUser from the cart: %s", $cartEntity->getId());
            $this->logger->error($msg);
            throw new RuntimeException($msg);
        }

        $this->logger->info(sprintf('notify_one_to_one order email notification for user %s ', $createdUser->getId()));
        $this->logger->info(sprintf('notify_one_to_one sendEmailMessage to %s', $createdUser->getEmail()));

        $param[MailService::FIRST_NAME_VAR] = $createdUser->getFirstname();
        $param[MailService::LAST_NAME_VAR] = $createdUser->getLastname();
        $this->sendEmailMessage($template,
            $createdUser->getLocale(),
            $createdUser->getEmail(),
            $param,
            $this->parameters[self::PARAM_FOUSER_FROM_EMAIL],
            $this->parameters[self::PARAM_FOUSER_FROM_NAME]
        );

        return true;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
