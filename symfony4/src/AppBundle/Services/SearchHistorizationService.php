<?php

namespace AppBundle\Services;

use AppBundle\Entity\User;
use AppBundle\FilterQueryBuilder\SearchHistorizationQueryBuilder;
use AppBundle\Entity\SearchHistorization;
use Doctrine\ORM\EntityManagerInterface;
use Knp\Component\Pager\PaginatorInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Unirest\Exception;

class SearchHistorizationService extends AbstractPaginatedService implements LoggerAwareInterface
{
    const REPO = \AppBundle\Entity\SearchHistorization::class;

    private SecurityService $securityService;
    private LoggerInterface $logger;

    public function __construct(
        EntityManagerInterface $em,
        PaginatorInterface $paginator,
        SearchHistorizationQueryBuilder $filterQueryBuilder,
        SecurityService $securityService
    )
    {
        parent::__construct($em, SearchHistorization::class, $paginator, $filterQueryBuilder);
        $this->securityService = $securityService;
    }

    /**
     * @param $id
     *
     * @return SearchHistorization|null|object
     */
    public function get($id)
    {
        return $this->em->getRepository(self::REPO)->find($id);
    }

    /**
     * @param $request
     * @param $data
     * @param int|null $nbItemPerPage
     * @return mixed
     */
    public function getAll($request, $data, ?int $nbItemPerPage = null){
        $qb = $this->em->createQueryBuilder();
        $qb->select('e')
            ->from($this->entityName, 'e')
            ->addOrderBy('e.createdAt', 'desc');

        if(!empty($data)){
            $this->filterQueryBuilder->build($qb, $data);
        }

        return $this->paginator->paginate(
            $qb->getQuery(),
            $request->query->getInt('page', 1),
            $nbItemPerPage
        );
    }

    /**
     * @param $data
     * @return mixed
     */
    public function getAllByFilter($data){
        $qb = $this->em->createQueryBuilder();
        $qb->select('e')
            ->from($this->entityName, 'e')
            ->addOrderBy('e.createdAt', 'desc');
        if(!empty($data)){
            $this->filterQueryBuilder->build($qb, $data);
        }
        $query = $qb->getQuery();
        return $query->getResult();
    }
    /**
     * @param $search_term
     * @param $filter
     * @param $nb_hits
     */
    public function trace($search_term, $filter, $nb_hits)
    {
        /** @var User $user */
        $user = $this->securityService->getUser();
        $companyName = !$this->securityService->isAnonymous() ? $user->getCompany()->getName() : '';
        $userFullName = !$this->securityService->isAnonymous() ? $user->getFirstname() . ' ' . $user->getLastname() : '';

        $entry = new SearchHistorization(
            $this->securityService->isAnonymous(),
            $companyName,
            $userFullName,
            $search_term,
            $filter,
            $nb_hits);

        try {
            $this->em->persist($entry);
            $this->em->flush();
        } catch (Exception $e) {
            $this->logger->error($e->getMessage(),['code'=>$e->getCode(), 'trace'=>$e->getTrace()]);

        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
