<?php

namespace AppBundle\Services;

use AppBundle\Entity\Cart;
use AppBundle\Entity\Company;
use AppBundle\Model\View\OrderDetail\MerchantOrder;
use AppBundle\Model\View\OrderDetail\MerchantOrderItem;
use AppBundle\Model\View\OrderDetail\Order;
use AppBundle\Model\View\OrderDetail\OrderFactory;
use AppBundle\Repository\CartRepository;
use Mpdf\Config\ConfigVariables;
use Mpdf\Config\FontVariables;
use Mpdf\Mpdf;
use Mpdf\MpdfException;
use Open\FrontBundle\Helpers\CartItemHelper;
use Twig\Environment;

class OrderPDFGenerator
{
    private OrderFactory $orderFactory;
    private OrderService $orderService;
    private Environment $twig;
    private CompanyService $companyService;
    private AddressService $addressService;
    private string $fontDir;
    private string $tmpDir;
    private CartRepository $cartRepository;

    public function __construct(
        string $tmpDir,
        OrderFactory $orderFactory,
        OrderService $orderService,
        Environment $twig,
        CompanyService $companyService,
        AddressService $addressService,
        CartRepository $cartRepository,
    )
    {
        $this->orderFactory = $orderFactory;
        $this->orderService = $orderService;
        $this->twig = $twig;
        $this->companyService = $companyService;
        $this->addressService = $addressService;
        $this->tmpDir = $tmpDir;
        $this->cartRepository = $cartRepository;
    }

    /**
     * @param string $fontDir
     * @return self
     */
    public function withFontDir(string $fontDir): self
    {
        // in Symfony 4 this method will change as documented in https://symfony.com/doc/4.4/service_container/calls.html
        $this->fontDir = $fontDir;
        return $this;
    }

    /**
     * @param $orderId
     * @param $companyId
     * @param $locale
     * @return Mpdf
     * @throws MpdfException
     */
    public function computeOrderPDF($orderId, $companyId, $locale)
    {
        ini_set("max_execution_time", 180);
        $company = $this->companyService->get($companyId);

        $izbergOrder = $this->orderService->fetchOrderById($orderId);
        $cartEntity = null;
        if ($izbergOrder->getCart()) {
            $cartEntity = $this->cartRepository->find($izbergOrder->getCart()->getId() ?? $izbergOrder->getArchivedCartId());
        }
        $order = $this->orderFactory->build($izbergOrder, $company, $locale);

        return $this->buildOrderTemplate($company, $izbergOrder, $order, null, $cartEntity, $locale);
    }

    public function computeMerchantOrderPDF(int $merchantOrderId, string $locale): Mpdf
    {
        $company = $this->orderService->retrieveCompanyFromMerchantOrderId($merchantOrderId);
        $izbergOrder = $this->orderService->fetchOrderByMerchantOrderId($merchantOrderId);
        $cartEntity = null;
        if ($izbergOrder->getCart()) {
            $cartEntity = $this->cartRepository->find($izbergOrder->getCart()->getId());
        }
        $order = $this->orderFactory->build($izbergOrder, $company, $locale);

        return $this->buildOrderTemplate($company, $izbergOrder, $order, $merchantOrderId, $cartEntity);
    }

    /**
     * @param Company $company
     * @param $izbergOrder
     * @param Order $order
     * @param int|null $merchantOrderId
     * @return Mpdf
     * @throws MpdfException
     */
    private function buildOrderTemplate(
        Company $company,
        $izbergOrder,
        Order $order,
        int $merchantOrderId = null,
        ?Cart $cart = null,
        ?string $locale = null,
    ): Mpdf {
        $merchantOrder = null;

        $merchantOrders = $order->getMerchantOrders();
        foreach ($merchantOrders as $merchant) {
            CartItemHelper::setQuantityDemandOrOnStock($merchant, "order");
        }

        if ($merchantOrderId) {
            $merchantOrders = array_filter(
                $merchantOrders,
                function (MerchantOrder $merchantOrder) use ($merchantOrderId) {
                    return ($merchantOrder->getId() === $merchantOrderId);
                }
            );
            sort($merchantOrders);

            if (count($merchantOrders)) {
                $merchantOrder = $merchantOrders[0];
            }
        }

        $address = null;

        $shippingPoint = $this->addressService->getCostCenterByIzbergAddressId($izbergOrder->getShippingAddress()->getId());
        if ($shippingPoint != null) {
            $order->setSite($shippingPoint->getSite()->getName());
            $address = $this->addressService->get($shippingPoint->getAddress()->getId());
        }

        $billingAddress = $company->getBillingAddress();
        if ($billingAddress == null) {
            $billingAddress = $company->getMainAddress();
        }

        $company->setShippingAddress($address);
        $company->setBillingAddress($this->addressService->get($billingAddress->getId()));

        $defaultConfig = (new ConfigVariables())->getDefaults();
        $fontDirs = $defaultConfig['fontDir'];
        if ($this->fontDir) {
            $fontDirs = array_merge($fontDirs, [$this->fontDir]);
        }

        $defaultFontConfig = (new FontVariables())->getDefaults();
        $fontData = $defaultFontConfig['fontdata'];

        $pdf = new Mpdf([
            'fontDir' => $fontDirs,
            'fontdata' => $fontData + [
                    'OpenSans' => [
                        'R' => 'OpenSans-Regular.ttf',
                        'I' => 'OpenSans-Italic.ttf',
                    ]
                ],
            'default_font' => 'OpenSans',
            'margin_top' => 70,
            'margin_right' => 0,
            'margin_bottom' => 30,
            'margin_left' => 0,
            'tempDir' => $this->tmpDir . '/mpdf',
            //'tempDir' => '/tmp',
        ]);

        $pdf->SetDefaultBodyCSS('background-color', '#F4F5FA');

        $headerHtml = $this->twig->render('@OpenFront/order/pdf/order_header_block.html.twig', [
            'company' => $company,
            'order' => $order,
            'locale' => $locale
        ]);

        $pdf->SetHTMLHeader($headerHtml);

        $footerHtml = $this->twig->render('@OpenFront/order/pdf/order_footer_block.html.twig');

        $pdf->SetHTMLFooter($footerHtml);

        // When merchantOrder is define we generate a pdf for a specific merchant order
        // And we display the status of the merchant order rather than the order in the order_details_block
        if ($merchantOrder) {
            $order->setStatus($merchantOrder->getStatus());
        }

        $detailsHtml = $this->twig->render('@OpenFront/order/pdf/order_details_block.html.twig', [
            'order' => $order,
            'company' => $company,
            'locale' => $locale,
            'cart' => $cart
        ]);

        $pdf->WriteHTML($detailsHtml);

        $counter = 1;
        $pages = [];
        $page = [];
        $n = 0;

        /** @var MerchantOrder $merchantOrder */
        foreach ($merchantOrders as $merchantOrder) {
            $n++;
            // To not put a merchant alone at the bottom of a page
            if ($counter == 4) {
                $pages[] = $page;
                $page = [];
                $counter = 0;
            }

            $page[] = ['type' => 'merchant', 'data' => $merchantOrder];
            $counter += 1;

            $items = $merchantOrder->getItems();

            /** @var MerchantOrderItem $item */
            foreach ($items as $item) {
                $page[] = ['type' => 'product', 'data' => $item];
                $counter += 1;

                if ($counter == 5) {
                    $pages[] = $page;
                    $page = [];
                    $counter = 0;
                }
            }
            if ($merchantOrder->getShippingTotal()) {
                $page[] = ['type' => 'shipping', 'data' => $merchantOrder];
            }
            $page[] = ['type' => 'merchantTotal', 'data' => $merchantOrder];
        }

        if (count($page) != 0) {
            $pages[] = $page;
        }

        $pageNumber = 0;

        foreach ($pages as $pdfPages) {
            $pageNumber += 1;
            $firstItem = true;
            foreach ($pdfPages as $pdfPage) {
                $html = null;
                if ($pdfPage['type'] == 'merchant') {
                    $html = $this->twig->render('@OpenFront/order/pdf/order_seller_block.html.twig', [
                        'orderId' => $order->getIdNumber(),
                        'merchantOrder' => $pdfPage['data'], // MerchantOrder
                        'first' => ($pageNumber != 1 && $firstItem)
                    ]);
                } else if ($pdfPage['type'] == 'product') {

                    $html = $this->twig->render('@OpenFront/order/pdf/order_product_block.html.twig', [
                        'merchantOrderItem' => $pdfPage['data'], // MerchantOrderItem
                        'first' => ($pageNumber != 1 && $firstItem),
                        'locale' => $locale,
                    ]);

                } else if($pdfPage['type'] == 'shipping') {

                    $html = $this->twig->render('@OpenFront/order/pdf/order_shipping_block.html.twig', [
                        'merchantOrder' => $pdfPage['data'], // MerchantOrder
                        'first' => $pageNumber != 1 && $firstItem ? true : false,
                        'locale' => $locale,
                    ]);

                } else {
                    $html = $this->twig->render('@OpenFront/order/pdf/order_seller_total_block.html.twig', [
                        'order' => $order,
                        'merchantOrder' => $pdfPage['data'], // MerchantOrder
                        'first' => ($pageNumber != 1 && $firstItem),
                        'locale' => $locale,
                    ]);
                }
                $pdf->WriteHTML($html);
                $firstItem = false;
            }

            if ($pageNumber < count($pages)) {
                $pdf->AddPage();
            }
        }

        $totalHtml = $this->twig->render('@OpenFront/order/pdf/order_total_block.html.twig', [
            'order' => $order,
            'first' => ($counter == 5),
            'locale' => $locale,
        ]);

        if ($counter == 5) {
            $pdf->AddPage();
        }

        if (!$merchantOrderId) {
            $pdf->WriteHTML($totalHtml);
        }

        $detailsHtml =  $this->twig->render('@OpenFront/order/pdf/order_additionnal_info.html.twig', [
            'order' => $order,
            'company' => $company,
            'locale' => $locale,
        ]);

        $pdf->WriteHTML($detailsHtml);

        return $pdf;
    }
}
