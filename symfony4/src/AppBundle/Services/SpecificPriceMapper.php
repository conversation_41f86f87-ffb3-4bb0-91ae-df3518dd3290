<?php

namespace AppBundle\Services;

use AppBundle\Entity\SpecificPrice;
use AppBundle\Exception\SpecificPriceMapperException;

class SpecificPriceMapper
{
    public function mapToSpecificPriceEntity(array $specificPriceAsArray): array
    {
        $dateSanitizer = function(string $date): \DateTime {
            $d = \DateTime::createFromFormat('d/m/Y', $date);

            if ($d === false) {
                throw new SpecificPriceMapperException();
            }

            $d->setTime(0,0);
            return $d;
        };

        $convertStringToFloat = function(string $value) {
            $value = str_replace(',', '.',$value);
            $value = floatval(trim($value));
            return $value;
        };

        return array_map(
            function ($row) use ($convertStringToFloat, $dateSanitizer) {
                $specificPrice = new SpecificPrice();
                $specificPrice->setCompanyIdentification($row['Buyer Identification Number'] ?? '');
                $specificPrice->setVendorReference($row['Vendor Reference'] ?? '');
                $specificPrice->setIncoterm($row['Incoterm'] ?? '');
                $specificPrice->setCountry($row['Country'] ?? '');
                $specificPrice->setBasicPrice($convertStringToFloat($row['Unit Price']));
                $specificPrice->setThreshold1(intval($row['Threshold 1']));
                $specificPrice->setPrice1($convertStringToFloat($row['Price 1']));
                $specificPrice->setThreshold2(intval($row['Threshold 2']));
                $specificPrice->setPrice2($convertStringToFloat($row['Price 2']));
                $specificPrice->setThreshold3(intval($row['Threshold 3']));
                $specificPrice->setPrice3($convertStringToFloat($row['Price 3']));
                $specificPrice->setThreshold4(intval($row['Threshold 4']));
                $specificPrice->setPrice4($convertStringToFloat($row['Price 4']));
                $specificPrice->setMoq(intval($row['MOQ']));
                $specificPrice->setDelayOfDelivery(intval($row['Total leadtime for customer']));
                $specificPrice->setFrameContract($row['Frame Contract'] ?? null);

                $validityDate = $row['Validity Date'];
                if(empty($validityDate)) {
                    $validityDate = null;
                }

                if ($validityDate !== null) {
                    $validityDate = $dateSanitizer($validityDate);
                }

                $specificPrice->setValidityDate($validityDate);

                return $specificPrice;
            },
            $specificPriceAsArray
        );
    }
}
