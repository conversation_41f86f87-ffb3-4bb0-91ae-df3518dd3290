<?php
namespace AppBundle\Services;


use AppBundle\Entity\Redirect;
use AppB<PERSON>le\FilterQueryBuilder\FilterQueryBuilderInterface;
use AppBundle\FilterQueryBuilder\UserQueryBuilder;
use Doctrine\ORM\EntityManagerInterface;
use Knp\Component\Pager\PaginatorInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;


/**
 * Class RedirectBddService
 * @package AppBundle\Services
 */
class RedirectBddService extends AbstractPaginatedService
{
    private $authorizationChecker;

    public function __construct(
        EntityManagerInterface $em,
        PaginatorInterface $paginator,
        UserQueryBuilder $filterQueryBuilder,
        AuthorizationCheckerInterface $authorizationChecker
    )
    {
        parent::__construct($em, Redirect::class, $paginator, $filterQueryBuilder);
        $this->authorizationChecker = $authorizationChecker;
    }


	public function getCustomFilteredPaginator ($page, $numberPerPage, $request, $data, $qualifier){


		$qb = $this->getQueryBuilder($qualifier);

		$this->filterQueryBuilder->build($qb, $data);

		return $this->paginator->paginate(
			$qb->getQuery(),
			$request->query->getInt('page', 1),
			$numberPerPage,
            array('defaultSortFieldName' => 'e.id', 'defaultSortDirection' => 'desc')
		);

	}


	private function getQueryBuilder ($qualifier){
		$qb = $this->em->createQueryBuilder();

		$qb->select('e')
			->from($this->entityName, 'e');

		return $qb;
	}

}
