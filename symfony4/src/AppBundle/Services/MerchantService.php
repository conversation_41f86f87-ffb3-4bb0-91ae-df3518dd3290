<?php

namespace AppBundle\Services;

use AppBundle\Entity\Country;
use AppBundle\Entity\User;
use AppBundle\Model\Merchant;
use AppBundle\Repository\MerchantRepository;
use Illuminate\Encryption\Encrypter;
use Knp\Component\Pager\Pagination\PaginationInterface;
use Open\IzbergBundle\Api\ApiException;
use Open\IzbergBundle\Api\ApiIdentity\UserIdentityApi;
use Open\IzbergBundle\Api\AttributeApi;
use Open\IzbergBundle\Api\CreateMerchantUserApi;
use Open\IzbergBundle\Api\MerchantApi;
use Open\IzbergBundle\Api\PermissionApi;
use Open\IzbergBundle\Builder\MerchantUser\MerchantUserBuilder;
use Open\IzbergBundle\Dto\RedisAttributeDTO;
use Open\IzbergBundle\Model\UserIdentity;
use Open\IzbergBundle\Service\RedisService;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;

class MerchantService implements LoggerAwareInterface
{
    private const MERCHANT_CACHE_KEY = 'MERCHANT_';
    private const UPELA_ACTIVE_KEY = 'UPELA_ACTIVE_';
    private const COUNTRY_KEY = 'COUNTRY_';
    private const MERCHANT_TTL = 60;

    private MerchantApi $merchantApi;
    private MerchantRepository $merchantRepository;
    private LoggerInterface $logger;
    private MailService $mailer;
    private CountryService $countryService;
    private SecurityService $securityService;

    /**
     * @var string $middleBaseUrl the base url of the merchant middle from izberg
     */
    private string $middleBaseUrl;

    /**
     * @var array $transportAssignments parameters for transport assigmnents
     */
    private array $transportAssignments;

    private RedisService $cache;

    private string $key;
    private RouterInterface $router;
    private PermissionApi $permissionApi;
    private AttributeApi $attributeApi;
    private AlstomCustomAttributes $customAttributes;
    private TaxService $taxService;
    private CreateMerchantUserApi $createMerchantUserApi;
    private UserIdentityApi $userIdentityApi;

    public function __construct(
        string $middleBaseUrl,
        array $transportAssignments = [],
        string $key,
        PermissionApi $permissionApi,
        MerchantApi $merchantApi,
        MerchantRepository $merchantRepository,
        MailService $mailer,
        CountryService $countryService,
        SecurityService $securityService,
        RedisService $cache,
        RouterInterface $router,
        AttributeApi $attributeApi,
        AlstomCustomAttributes $customAttributes,
        TaxService $taxService,
        CreateMerchantUserApi $createMerchantUserApi,
        UserIdentityApi $userIdentityApi,
    )
    {
        $this->merchantApi = $merchantApi;
        $this->merchantRepository = $merchantRepository;
        $this->mailer = $mailer;
        $this->countryService = $countryService;
        $this->securityService = $securityService;
        $this->middleBaseUrl = $middleBaseUrl;
        $this->transportAssignments = $transportAssignments;
        $this->cache = $cache;
        $this->key = $key;
        $this->permissionApi = $permissionApi;
        $this->router = $router;
        $this->attributeApi = $attributeApi;
        $this->customAttributes = $customAttributes;
        $this->taxService = $taxService;
        $this->createMerchantUserApi = $createMerchantUserApi;
        $this->userIdentityApi = $userIdentityApi;
    }

    /**
     * fetch a merchant from the izberg API and save it in cache from 10 minutes
     * @param int $merchantId
     * @return Merchant|null
     */
    public function findMerchantById(int $merchantId): ?Merchant
    {
        $cacheItem = $this->cache->getItem(self::MERCHANT_CACHE_KEY.$merchantId);
        if ($cacheItem !== null){
            return $cacheItem;
        }
        try {
            $alstomMerchant = $this->buildMerchantFromIzbergResponse($this->merchantApi->getMerchant($merchantId));
            $this->cache->saveItem(self::MERCHANT_CACHE_KEY.$merchantId, $alstomMerchant, self::MERCHANT_TTL);
            return $alstomMerchant;
        }catch(ApiException $e){
            $this->logger->error("unable to load merchant from izberg API: ".$e->getMessage(),
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::TECHNICAL_ERROR,
                    'id' => $merchantId
                ])
            );
            return null;
        }
    }

    public function findMerchantEntityById(int $id):? \AppBundle\Entity\Merchant
    {
        $merchant = $this->merchantRepository->find($id);
        return ($merchant instanceof \AppBundle\Entity\Merchant) ? $merchant : null;
    }

  /**
   * @param $merchantId
   *
   * @return \AppBundle\Entity\Country|null
   */
    public function getMerchantFiscalCountryByMerchantId($merchantId)
    {
        $merchant = $this->findMerchantById($merchantId);
        if ($merchant !== null){
            return $merchant->getCountry();
        }

        return null;
    }

    public function activateMerchant(\AppBundle\Entity\Merchant $merchant, ?User $author = null): bool
    {
        try {
            $this->createIzbergMerchant($merchant);
            $this->merchantRepository->update($merchant, $author);
        } catch (\Exception $e) {
            $this->logger->error(
                'error while registering a merchant with Izberg: '. $e->getMessage(),
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::TECHNICAL_ERROR,
                    LogUtil::USER_NAME=>($author)? $author->getUsername() : null,
                    'merchant_id' => $merchant->getId(),
                    'exception' => $e
                ])
            );

            return false;
        }

        $this->logger->info(
            "merchant has been validated",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME=>EventNameEnum::MERCHANT_VALIDATE,
                LogUtil::USER_NAME=>($author)? $author->getUsername() : null,
                'merchant_id' => $merchant->getId(),
                'izberg_merchant_id' => $merchant->getIzbergId(),
            ])
        );

        $this->mailer->sendEmailMessage(
            MailService::MERCHANT_ACCEPTED,
            $merchant->getCountry()->getLocale(),
            $merchant->getEmail(),
            [
                MailService::FIRST_NAME_VAR => $merchant->getFirstname(),
                MailService::LAST_NAME_VAR => $merchant->getLastname(),
                "companyName" => $merchant->getName(),
                "email" => $merchant->getEmail()
            ]
        );

        return true;
    }

    /**
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function rejectMerchant(\AppBundle\Entity\Merchant $merchant, string $reason, ?User $author = null): bool
    {
        $this->logger->info(
            'merchant has been rejected',
            LogUtil::buildContext([
                LogUtil::EVENT_NAME=>EventNameEnum::MERCHANT_REJECT,
                LogUtil::USER_NAME=>($author)? $author->getUsername() : null,
                'merchant_id' => $merchant->getId(),
                'reason' => $reason
            ])
        );

        try {
            $merchant->setStatus(\AppBundle\Entity\Merchant::STATUS_REJECTED);
            $merchant->setRejectedReason($reason);
            $this->merchantRepository->update($merchant, $author);
        } catch(\Exception $e) {
            $this->logger->error(
                'error while rejecting merchant: '. $e->getMessage(),
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::TECHNICAL_ERROR,
                    LogUtil::USER_NAME=>($author)? $author->getUsername() : null,
                    'merchant_id' => $merchant->getId(),
                    'exception' => $e
                ])
            );

            return false;
        }

        $this->mailer->sendEmailMessage(
            MailService::MERCHANT_REJECTED,
            $merchant->getCountry()->getLocale(),
            $merchant->getEmail(),
            [
                MailService::FIRST_NAME_VAR => $merchant->getFirstname(),
                MailService::LAST_NAME_VAR => $merchant->getLastname(),
                "companyName" => $merchant->getName(),
                'comment' => $reason ]
        );

        return true;
    }

    /**
     * @param \AppBundle\Entity\Merchant $merchant
     * @param User|null $author
     * @return bool
     */
    public function updateMerchantInformation(\AppBundle\Entity\Merchant $merchant, ?User $author = null): bool
    {
        try {
            $this->merchantRepository->update($merchant, $author);
        } catch (\Exception $e) {
            $this->logger->error(
                'error while saving merchant: '. $e->getMessage(),
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::TECHNICAL_ERROR,
                    LogUtil::USER_NAME=>($author)? $author->getUsername() : null,
                    'merchant_id' => $merchant->getId(),
                    'exception' => $e
                ])
            );

            return false;
        }

        $this->logger
            ->info(
                'update merchant information',
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::MERCHANT_SAVE,
                    LogUtil::USER_NAME => ($author)?$author->getUsername(): null,
                    'id' => $merchant->getId(),
                    'status' => $merchant->getStatus(),
                ])
            );

        return true;
    }

    public function registerMerchant(array $merchantDetails, $tvaChecked)
    {

        $country = $this->countryService->getCountryById(intval($merchantDetails['country']->getViewData()));

        $merchant = new \AppBundle\Entity\Merchant();
        $merchant->setCountry($country);
        $merchant->setCurrency($merchantDetails['currency']->getViewData());
        $merchant->setName($merchantDetails['raisonSociale']->getViewData());
        $merchant->setIdentification($merchantDetails['identification']->getViewData());
        $merchant->setFirstname($merchantDetails['firstname']->getViewData());
        $merchant->setLastname($merchantDetails['lastname']->getViewData());
        $merchant->setEmail($merchantDetails['email']->getViewData());
        $merchant->setRegistrationDate(new \DateTimeImmutable());

        $encrypter = new Encrypter($this->key);
        $encrypted = $encrypter->encrypt($merchantDetails['plainPassword']->getViewData());
        $merchant->setPassword($encrypted);
        $merchant->setPhoneNumber($merchantDetails['mainPhoneNumber']->getViewData());
        $merchant->setStatus(\AppBundle\Entity\Merchant::STATUS_PENDING);
        $merchant->setTvaChecked($tvaChecked);

        // Log merchant registration
        $this
            ->logger
            ->info(
                'New merchant register',
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::MERCHANT_REGISTER,
                    'merchant_email' =>$merchantDetails['email']->getViewData()
                ])
            );
        // persist
        $this->merchantRepository->save($merchant);

        /////////////////////////////////////////////////////////////////////////////////////////////
        //notify merchant
        /////////////////////////////////////////////////////////////////////////////////////////////

        if ($merchant->getCountry() !== null) {
            $this->mailer->sendEmailMessage(
                MailService::MERCHANT_REGISTRATION_CONFIRMED,
                $merchant->getCountry()->getLocale(),
                $merchant->getEmail(),
                [
                    'companyName' => $merchant->getName(),
                    MailService::FIRST_NAME_VAR => $merchant->getFirstname(),
                    MailService::LAST_NAME_VAR => $merchant->getLastname(),
                ]);
        }else{
            $this->logger->error("unable to notify merchant creation: Country doesn't exist" ,
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::MERCHANT_CREATION_NOTIFICATION_ERROR,
                    LogUtil::USER_NAME=>$merchant->getFirstname(). ' '.$merchant->getLastname(),
                    'email' => $merchant->getEmail()
                ])
            );
        }

        /////////////////////////////////////////////////////////////////////////////////////////////
        //notify operators
        /////////////////////////////////////////////////////////////////////////////////////////////

        $operators = $this->securityService->getOperators();

        /** @var User $operator */
        foreach ($operators as $operator){
            if ($operator->isEnabled()) {
                $this->mailer->sendEmailMessage(MailService::OPERATOR_MERCHANT_REGISTRATION_CONFIRMED,
                    "en", $operator->getEmail(), [
                        MailService::FIRST_NAME_VAR => $operator->getFirstname(),
                        MailService::LAST_NAME_VAR => $operator->getLastname(),
                        'merchantFirstName' => $merchant->getFirstname(),
                        'merchantLastName' => $merchant->getLastname(),
                        'merchantEmail' => $merchant->getEmail(),
                        'companyName' => $merchant->getName(),
                        'url' => $this->router->generate("admin.merchant.generalInfo", [
                            "id" => $merchant->getId()], UrlGeneratorInterface::ABSOLUTE_URL
                        )
                    ]
                );
            }
        }
    }

    /**
     * @throws \Exception
     */
    protected function createIzbergMerchant(\AppBundle\Entity\Merchant $merchant, ?User $author = null)
    {
        // First we check if this izberg user id exists

        $izbergUserId = null;
        $izbergMerchantScopes = [];

        /** @var  UserIdentity $userIdentity */
       foreach ($this->userIdentityApi->fetchUserByEmail($merchant->getEmail()) as $userIdentity) {
           if ($userIdentity->hasMerchantScopes()) {
               $izbergUserId = $userIdentity->uuid;
               $izbergMerchantScopes = $userIdentity->merchant_scopes;
               break;
           }
       }

        //if we have found izberg user
        if ($izbergUserId !== null){
            //here we can consider that the user exist
            $this->logger->info("merchant registration: the user already exists: Create the merchant and associate the existing user ",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::MERCHANT_REGISTER,
                    "izbergUserId" => $izbergUserId,
                    "merchantEmail" => $merchant->getEmail()
                ])
            );

            //we create the merchant
            $izbergMerchant = $this->merchantApi->createMerchant($merchant->getName(),
                $merchant->getCountry()->getIzbergCode(),
                'product_offers',
            );

            //set the permission for this merchant
            $this->logger->info("setting merchant permissions",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::MERCHANT_REGISTER,
                    "merchantId" => $izbergMerchant->id,
                    "userId" => $izbergUserId
                ])
            );
            // Patch the user with a new merchant
            $this->userIdentityApi->attachWithMerchant(
                uuid: $izbergUserId,
                merchantId: $izbergMerchant->id,
                merchantScopes: $izbergMerchantScopes
            );
        } else {

            /////////////////////////////////////////////////////////////////////////////////////////////
            // Create Merchant
            /////////////////////////////////////////////////////////////////////////////////////////////

            $encrypter = new Encrypter($this->key);
            $password = $encrypter->decrypt($merchant->getPassword());

            $izbergMerchant = $this->merchantApi->createMerchant(
                name: $merchant->getName(),
                countryCode: $merchant->getCountry()->getIzbergCode(),
                offerType: 'product_offers',
            );

            $merchantId = $izbergMerchant->id;

            $izbergMerchantUserData = MerchantUserBuilder::buildIzbergMerchantUser(
                $merchant->getEmail() ?? "",
                $password,
                $merchant->getFirstname() ?? "",
                $merchant->getLastname() ?? "",
                $merchantId
            );

            $this->createMerchantUserApi->createMerchantUser($izbergMerchantUserData);

            $this->logger->info("merchant registration: the user doesn't exist. User and merchant has been created ",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::MERCHANT_REGISTER,
                    "merchantId" => $izbergMerchant,
                ])
            );
        }

        $merchant->setIzbergId($izbergMerchant->id);
        $merchant->setStatus(\AppBundle\Entity\Merchant::STATUS_ACCEPTED);

        $this->setMerchantDefaultLanguage($izbergMerchant->id);
        $this->setMerchantTaxRate($izbergMerchant->id, $merchant->getCountry());

        $this->attributeApi->updateMerchantAttribute(
            $this->attributeApi->getMerchantAttributeId(new RedisAttributeDTO($this->customAttributes->getCompanyIdentificationNumber())),
            $izbergMerchant->id,
            $merchant->getIdentification()
        );

        $this->merchantRepository->update($merchant, $author);

        // Log merchant creation
        $this
            ->logger
            ->info(
                'New merchant created with id ' . $izbergMerchant->id,
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>'izberg.merchant.created',
                    LogUtil::USER_NAME=>($author) ? $author->getUsername() : null,
                    'merchant_request' => $merchant->toArray()
                ])
            );


        /////////////////////////////////////////////////////////////////////////////////////////////
        //create transport assignments
        /////////////////////////////////////////////////////////////////////////////////////////////

        if (!empty($this->transportAssignments)) {
            $izberg_merchant_carrier = $this->transportAssignments['izberg_merchant_carrier'];
            $izberg_merchant_shipping_provider = $this->transportAssignments['izberg_merchant_shipping_provider'];
            $izberg_merchant_zone = $this->transportAssignments['izberg_merchant_zone'];

            $this->merchantApi->addDefaultTransportAssignment(
                $izbergMerchant->id,
                $izberg_merchant_carrier,
                $izberg_merchant_shipping_provider,
                $izberg_merchant_zone
            );

        }

        /////////////////////////////////////////////////////////////////////////////////////////////
        //create merchant address
        /////////////////////////////////////////////////////////////////////////////////////////////

        $this->merchantApi
            ->addMerchantAddress(
                $izbergMerchant->id,
                true,
                false,
                $merchant->getFirstname(),
                $merchant->getLastname(),
                '',
                '',
                intval($merchant->getCountry()->getId()),
                $merchant->getEmail(),
                $merchant->getPhoneNumber(),
                1 // Authorized values -> mr: 1, mrs: 2, miss: 3
            );

        $merchantCompany = $this->merchantApi->getMerchantCompany($izbergMerchant->id);

        $this->merchantApi->updateMerchantCompany($merchantCompany->id,
            (int) $merchant->getCountry()->getIzbergId(),
            $merchant->getIdentification()
        );

        //set merchant as pending
        $this->merchantApi->checkActivation($izbergMerchant->id);

        //set merchant currency
        $this->merchantApi->updateMerchantCurrency($izbergMerchant->id, $merchant->getCurrency());
    }

    private function setMerchantDefaultLanguage(int $izbergMerchantId)
    {
        $this->merchantApi->updateMerchant(
            $izbergMerchantId,
            [
                'languages' => ['en'],
                'prefered_language' => 'en'
            ]
        );
    }

    private function setMerchantTaxRate(int $izbergMerchantId, Country $country)
    {
        $this->merchantApi->updateMerchantTaxRate($izbergMerchantId, $this->taxService->taxRateFromCountry($country));
    }

    /**
     * format an izberg merchant into an alstom merchant
     */
    private function buildMerchantFromIzbergResponse($izbergMerchant): Merchant
    {
        $merchant = new Merchant();
        $merchant->setId($izbergMerchant->id);
        $merchant->setName($izbergMerchant->name);
        $merchant->setLongDescription($izbergMerchant->long_description);
        $merchant->setShortDescription($izbergMerchant->description);
        $merchant->setPreferedLanguage($izbergMerchant->prefered_language);

        if (property_exists($izbergMerchant, "profile_image") &&
            $izbergMerchant->profile_image !== null &&
            property_exists($izbergMerchant->profile_image, "image_path")) {
            $merchant->setLogo($izbergMerchant->profile_image->image_path);
        }

        if (property_exists($izbergMerchant, "addresses")) {
            $addresses = $izbergMerchant->addresses;
            if (is_array($addresses) && isset($addresses[0])) {
                $mainAddress = $addresses[0];
                if (isset($mainAddress->resource_uri)) {
                    $mainAddress = $this->merchantApi->getAddress($mainAddress->id);
                }

                $merchant->setMainContactEmail($mainAddress->contact_email);
                $merchant->setMainContactFirstName($mainAddress->contact_first_name);
                $merchant->setMainContactLastName($mainAddress->contact_last_name);
                $merchant->setMainContactPhone($mainAddress->phone);
            }
        }

        $merchant->setCountry($this->getCountryFromIzbergResponse($izbergMerchant));

        return $merchant;
    }


    /***
     *
     * Returns a Country Object !
     *
     * @param $izbergMerchant
     *
     * @return \AppBundle\Entity\Country|null
     */
    public function getCountryFromIzbergResponse($izbergMerchant)
    {
        $additionalInformation = '';
        /** @var Country $country  */
        if (isset($izbergMerchant->addresses)) {
            $addresses = $izbergMerchant->addresses;
            if (is_array($addresses)) {
                foreach ($addresses as $address) {
                    if (isset($address->billing_address)) {
                        if ($address->billing_address == true) {
                            if (isset($address->country)) {
                                if (isset($address->country->id)) {
                                    $country = $this->countryService->getCountryByIzbergId($address->country->id);
                                    if ($country) {
                                        return $country;
                                    } else {
                                        $additionalInformation = 'country id = ' . $address->country->id;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // Here we do not have correct information in addresses, try the region
        if (isset($izbergMerchant->region)) {

            $this->logger->error(
                sprintf(
                'MERCHANT COUNTRY NOT FOUND - TRYING WITH MERCHANT REGION (merchantID: %d)',
                    $izbergMerchant->id
                ),
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::MISSING_DATA,
                ])
            );

            $region = $izbergMerchant->region; // 'FR' etc
            $country = $this->countryService->getCountryByIzbergCode($region);
            if ($country) {
                return $country;
            } else {
                $additionalInformation = 'region = ' . $region;
            }
        }

        $this->logger->critical('No usable country found for merchant id = ' . $izbergMerchant->id . ' [' . $additionalInformation . ']',
            LogUtil::buildContext([
                LogUtil::EVENT_NAME=>EventNameEnum::PAYMENT_GENERAL_ERROR,
            ])
        );
        return null;
    }

    public function paginatedMerchants($data, $page = 1, $numberPerPage = 10): PaginationInterface
    {
        return $this->merchantRepository->paginatedMerchants($data, $page, $numberPerPage);
    }

    public function getAcceptedMerchants() : array
    {
        $merchants = $this->merchantRepository->findBy(['izbStatus' => 'active'], ['izbMerchantName' => 'ASC']);
        return array_filter($merchants, fn(\AppBundle\Entity\Merchant $merchant) => $merchant->getIzbergId() !== null);
    }


    public function fetchCountry(int $merchantId) : ?Country
    {
        $cacheKey = self::MERCHANT_CACHE_KEY. self::COUNTRY_KEY . $merchantId;
        $cacheItem = $this->cache->getItem($cacheKey);
        if ($cacheItem !== null){
            return $cacheItem;
        }

        $country = $this->getMerchantFiscalCountryByMerchantId($merchantId);
        $this->cache->saveItem($cacheKey, $country, self::MERCHANT_TTL);

        return $country;
    }

    public function merchantIsUpelaActive(int $merchantId)
    {
        $cacheKey = self::MERCHANT_CACHE_KEY. self::UPELA_ACTIVE_KEY . $merchantId;

        $cacheItem = $this->cache->getItem($cacheKey);
        if ($cacheItem !== null){
            return $cacheItem;
        }

        $upelaActive = $this->merchantApi->getMerchantCustomAttribute($merchantId, $this->customAttributes->getUpelaIsActive());
        $upelaActive = $upelaActive ?? false;
        $this->cache->saveItem($cacheKey, $upelaActive, self::MERCHANT_TTL);

        return $upelaActive;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
