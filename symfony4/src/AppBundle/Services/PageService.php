<?php
namespace AppBundle\Services;


use AppBundle\Entity\Node;
use Doctrine\ORM\EntityManagerInterface;
use Knp\Component\Pager\PaginatorInterface;


/**
 * Class UserBddService
 * @package AppBundle\Services
 */
class PageService extends AbstractPaginatedService
{
    public function __construct(EntityManagerInterface $em, PaginatorInterface $paginator)
    {
        parent::__construct($em, Node::class, $paginator);
    }
}
