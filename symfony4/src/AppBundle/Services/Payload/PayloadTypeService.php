<?php

declare(strict_types=1);

namespace AppBundle\Services\Payload;

use AppBundle\Entity\Middleware\AbstractPayload;
use AppBundle\Repository\Payload\PayloadRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

final class PayloadTypeService implements PayloadTypeServiceInterface
{
    private EntityManagerInterface $em;
    private TranslatorInterface $translator;

    public function __construct(EntityManagerInterface $em, TranslatorInterface $translator)
    {
        $this->em = $em;
        $this->translator = $translator;
    }

    /**
     * Get all payload type of doctrine and return an array with the type in key and the FQCN in value
     * @return array<string, string>
     * @example ["PAYLOAD_TYPE" => "\Path\To\PayloadType"]
     *
     */
    public function getPayloadTypesToString(): array
    {
        $qb = $this->getPayloadRepository()->createQueryBuilder('p');
        $qb->where($qb->expr()->isInstanceOf('p', AbstractPayload::class));

        $payloads = [];
        /** @var AbstractPayload[] $results */
        $results = $qb->getQuery()->getResult();
        foreach ($results as $payload) {
            $label = $this->translator->trans($payload->label(), [], 'AppBundle');
            $payloads[$label] = \get_class($payload);
        }
        return $payloads;
    }

    private function getPayloadRepository(): PayloadRepository
    {
        return $this->em->getRepository(AbstractPayload::class);
    }

    private function getShortClassName(string $payloadClassName): string
    {
        return substr($payloadClassName, strrpos($payloadClassName, '\\') + 1);
    }
}
