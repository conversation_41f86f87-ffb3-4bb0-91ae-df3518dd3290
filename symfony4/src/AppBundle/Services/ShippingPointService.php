<?php

namespace AppBundle\Services;

use AppBundle\Entity\ShippingPoint;
use AppBundle\FilterQueryBuilder\ShippingPointQueryBuilder;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Knp\Component\Pager\PaginatorInterface;

class ShippingPointService extends AbstractPaginatedService
{

    const REPO_SITE = \AppBundle\Entity\ShippingPoint::class;

    /**
     * @var CompanyService $companyService
     */
    private $companyService;

    /**
     * @var SiteService $siteService
     */
    private $siteService;

    public function __construct(
        EntityManagerInterface $em,
        PaginatorInterface $paginator,
        ShippingPointQueryBuilder $filterQueryBuilder,
        CompanyService $companyService,
        SiteService $siteService
    )
    {
        parent::__construct($em, ShippingPoint::class, $paginator, $filterQueryBuilder);

        $this->companyService = $companyService;
        $this->siteService = $siteService;
    }

    /***
     * @param $page
     * @param $numberPerPage
     * @param $request
     * @param $data
     * @param $qualifier
     *
     * @return mixed
     */
    public function getCustomFilteredPaginator($page, $numberPerPage, $request, $data, $qualifier)
    {
        $qb = $this->getQueryBuilder($qualifier);

        if ($qualifier != 'all') {
            $data['status'] = null;
        }

        if ($qualifier == 'enabled') {
            $data['type'] = null;
        }

        $this->filterQueryBuilder->build($qb, $data);

        return $this->paginator->paginate(
            $qb->getQuery(),
            $request->query->getInt('page', 1),
            $numberPerPage,
            array('defaultSortFieldName' => 'e.createdAt', 'defaultSortDirection' => 'desc')
        );

    }

    /**
     * @param $qualifier
     *
     * @return \Doctrine\ORM\QueryBuilder
     */
    private function getQueryBuilder($qualifier)
    {
        // Should not work
        $qb = $this->em->createQueryBuilder();
        $qb->select('e')
            ->from($this->entityName, 'e')
            ->leftJoin('e.mainAddress', 'a')
            ->leftJoin('a.country', 'co');

        if ($qualifier === 'toConfirm') {
            $qb->where("e.status = 'pending'");
        } else if ($qualifier === 'enabled') {
            $qb->where("e.enabled = true and e.status = 'valid'");
        } else if ($qualifier == 'disabled') {
            $qb->where('e.enabled = false');
        }

        return $qb;
    }

    /**
     * get all users depending on the qualifier (filter)
     * @param $qualifier string how to filter the request
     * @return array the list of users
     */
    public function getByQualifier($qualifier)
    {
        $qb = $this->getQueryBuilder($qualifier);
        $query = $qb->getQuery();
        return $query->getResult();
    }

    public function get($id): ?ShippingPoint
    {
        return $this->em->getRepository(self::REPO_SITE)->find($id);
    }

    /***
     * @param $shippingPoint ShippingPoint
     *
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function delete($shippingPoint)
    {
        $site = $shippingPoint->getSite();
        $company = $site->getCompany();

        if ($shippingPoint->getAddress()) $this->em->remove($shippingPoint->getAddress());
        if ($shippingPoint->getContact()) $this->em->remove($shippingPoint->getContact());
        $this->em->remove($shippingPoint);
        $this->em->flush();

        $this->siteService->ensureHasAtLeastOneSiteAttached($company);
    }

    public function isValidShippingPointDocumentsRequests(int $shippingPointId, array $documentsRequest):bool
    {
        $shippingPoint = $this->get($shippingPointId);
        if (!$shippingPoint || count($documentsRequest) > 10) {
            return false;
        }

        $correctDocumentsCount = 0;
        foreach($documentsRequest as $document) {
            if ($shippingPoint->getDocumentationRequest1() === $document
                || $shippingPoint->getDocumentationRequest2() === $document
                || $shippingPoint->getDocumentationRequest3() === $document
                || $shippingPoint->getDocumentationRequest4() === $document
                || $shippingPoint->getDocumentationRequest5() === $document
                || $shippingPoint->getDocumentationRequest6() === $document
                || $shippingPoint->getDocumentationRequest7() === $document
                || $shippingPoint->getDocumentationRequest8() === $document
                || $shippingPoint->getDocumentationRequest9() === $document
                || $shippingPoint->getDocumentationRequest10() === $document
            ) {
                $correctDocumentsCount++;
            }
        }
        return count($documentsRequest) === $correctDocumentsCount;
    }
}
