<?php
/**
 * Created by PhpStorm.
 * User: CMA15450
 * Date: 15/01/2018
 * Time: 11:03
 */

namespace AppBundle\Services;


use AppBundle\Entity\Category;
use AppBundle\FilterQueryBuilder\FilterQueryBuilderInterface;
use Doctrine\ORM\EntityManagerInterface;
use AppBundle\FilterQueryBuilder\CompanyQueryBuilder;
use Knp\Component\Pager\PaginatorInterface;

class CompanyCategoryService extends AbstractPaginatedService
{

  const UNDEFINED=4;
  const NEW = 1;

  /***
   * CompanyCategoryService constructor.
   *
   * @param EntityManagerInterface $em
   * @param \Knp\Component\Pager\PaginatorInterface $paginator
   * @param \AppBundle\FilterQueryBuilder\CompanyQueryBuilder $filterQueryBuilder
   */
	public function __construct(EntityManagerInterface $em, PaginatorInterface $paginator,  CompanyQueryBuilder $filterQueryBuilder)
	{
		parent::__construct($em, Category::class, $paginator, $filterQueryBuilder);
	}

	/**
	 * @param $id
	 * @return null|object
	 */
	public function  getCategoryById($id)
	{
		return $this->findById($id);
	}
}
