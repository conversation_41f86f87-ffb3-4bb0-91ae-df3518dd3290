<?php

namespace AppBundle\Services;

use AppBundle\Entity\Company;
use AppBundle\Entity\CompanyCatalog;
use AppBundle\Entity\User;
use AppBundle\Model\Offer;
use AppBundle\Repository\CompanyCatalogRepository;
use AppBundle\StreamFilter\StreamFilterNewlines;
use AppBundle\Util\Locale;
use Doctrine\DBAL\Driver\PDO\MySQL\Driver as PDOMySqlDriver;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Contracts\Translation\TranslatorInterface;
use Unirest\Exception;

class CompanyCatalogService implements LoggerAwareInterface
{
    public const JOB_QUEUE_CATALOG_IMPORT = 'catalog_import';

    private CompanyCatalogRepository $companyCatalogRepository;
    private SecurityService $securityService;
    private LoggerInterface $logger;
    private TranslatorInterface $translator;
    private CSVService $CSVService;
    private string $locale;
    private JobService $jobService;
    private CatalogReferenceService $catalogReferenceService;
    private MessengerProgressionService $messengerProgressionService;

    /*
     * CompanyCatalogService constructor.
     *
     * @param \Doctrine\ORM\EntityManager $em
     * @param $securityService
     * @param $logger
     * @param \Symfony\Contracts\Translation\TranslatorInterface $translator
     * @param \Symfony\Component\DependencyInjection\ContainerInterface $container
     */
    public function __construct(
        CompanyCatalogRepository $companyCatalogRepository,
        SecurityService $securityService,
        TranslatorInterface $translator,
        CSVService $CSVService,
        RequestStack $requestStack,
        JobService $jobService,
        CatalogReferenceService $catalogReferenceService,
        MessengerProgressionService $messengerProgressionService
    ) {
        $this->companyCatalogRepository = $companyCatalogRepository;
        $this->securityService = $securityService;
        $this->translator = $translator;
        $this->CSVService = $CSVService;
        $this->jobService = $jobService;
        $this->catalogReferenceService = $catalogReferenceService;
        $this->messengerProgressionService = $messengerProgressionService;

        // If service is initialize with a request, set request locale to service

        $this->setLocale(Locale::fetchFromRequestStack($requestStack));
    }

    /**
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function findBuyerReference(?User $user, array $catalogReferences):? string
    {
        if (!$user || !$user->getCompany()) {
            return null;
        }

        // Try to find in manufacturer ref
        $myReference = $this->companyCatalogRepository->findByCompanyIdAndCatalogReference(
            $user->getCompany()->getId(),
            $catalogReferences
        );

        return $myReference ? $myReference->getRef() : null;
    }

    /**
     * @throws \Doctrine\ORM\NonUniqueResultException
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function deleteBuyerCatalogReference(?User $user, string $manufacturerReference)
    {
        if (!$user || !$user->getCompany()) {
            return null;
        }

        $buyerCatalogReference = $this->companyCatalogRepository->findByCompanyIdAndCatalogReference(
            $user->getCompany()->getId(),
            [$manufacturerReference]
        );

        if ($buyerCatalogReference) {
            $this->companyCatalogRepository->deleteCatalogReference($buyerCatalogReference);
        }
    }

    /**
     * @throws \Doctrine\ORM\NonUniqueResultException
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function saveBuyerCatalogReference(?User $user, string $manufacturerReference, string $buyerReference): ?string
    {
        if (!$user || !$user->getCompany()) {
            return '';
        }

        $catalogReference = $this->companyCatalogRepository->findByCompanyIdAndCatalogReference(
            $user->getCompany()->getId(),
            [$manufacturerReference]
        );

        if (!$catalogReference) {
            $catalogReference = new CompanyCatalog();
            $catalogReference->setCompany($user->getCompany());
            $catalogReference->setExternalRef($manufacturerReference);
        }

        $catalogReference->setRef($buyerReference);
        $catalogReference->setValid(true);

        $this->companyCatalogRepository->saveCatalogReference($catalogReference);

        return $buyerReference;
    }

    public function getHeaders()
    {
        return [
            'Manufacturer reference',
            'Reference',
            'Manufacturer name',
            'Designation of reference',
        ];
    }

    /**
     * @param Company $company
     * @param string $csvFilePath
     * @param bool $withJob
     * @return int
     * @throws \Doctrine\ORM\ORMException
     */
    public function importCompanyCatalogFromCsvFile(Company $company, string $csvFilePath)
    {
        if (($csvFilePointer = fopen($csvFilePath, 'r')) === false) {
            throw new FileException(sprintf('Cannot open %s file for importing catalog', $csvFilePath));
        }

        $headerCsvRow = fgets($csvFilePointer);
        $delimiter = array_reduce([',', ';', "\t"], function($defaultDelimiter, $delimiter) use ($headerCsvRow){
            return (4 === count(str_getcsv($headerCsvRow, $delimiter)))? $delimiter : $defaultDelimiter;
        }, ';');

        $this->deleteCompanyCatalog($company);

        $limit = 500;
        $catalogRowset = [];
        $totalCatalogReferenceImported = 0;
        $user = $this->securityService->getUser();
        if(!$user instanceof User){
            Throw new \Exception("User can not be null");
        }
        $company = $user->getCompany();
        if(!$company instanceof Company){
            Throw new \Exception("Company can not be null");
        }

        $country = $company->getFiscalCountry()->getCode();

        $this->messengerProgressionService->initProgression(MessengerProgressionService::CATALOG_MESSENGER, $company->getId());
        while(($data = fgetcsv($csvFilePointer, 500, $delimiter)) !== false) {
            [
                $manufacturerReference,
                $buyerReference,
                $manufacturerName,
                $designationOfReference,
            ] = array_pad($data, 4, null);

            if (!$manufacturerReference || !$buyerReference) {
                continue;
            }

            $catalogRowset[] = [
                $manufacturerReference,
                $buyerReference,
                $manufacturerName,
                $designationOfReference,
                $company->getId(),
            ];

            if ($limit <= count($catalogRowset)) {
                $this->jobService->importCompanyCatalogue($catalogRowset, $company->getId(), $country);
                $catalogRowset = [];
            }

            $totalCatalogReferenceImported ++;
        }
        $this->messengerProgressionService->setProgressionLimit(MessengerProgressionService::CATALOG_MESSENGER, $company->getId(),$totalCatalogReferenceImported);
        if (count($catalogRowset)) {
            $this->jobService->importCompanyCatalogue($catalogRowset, $company->getId(), $country);
        }

        fclose($csvFilePointer);

        return $totalCatalogReferenceImported;
    }

    /**
     * @param Company $company
     * @param array $dataRowset
     * @param bool $withJob
     * @throws \Doctrine\ORM\ORMException
     */
    public function importCompanyCatalogDataRowset(array $dataRowset, string $country)
    {
        $connection = $this->companyCatalogRepository->getConnection();
        // use prod environment to turn off sql log queries
        // cf: https://stackoverflow.com/questions/28191382/how-to-stop-symfony-from-logging-doctrines-sql-queries

        if ($connection->getDriver() instanceof PDOMySqlDriver) {
            $connection->executeStatement('SET foreign_key_checks=0;');
            $connection->executeStatement('SET autocommit=0;');
        }
        foreach($dataRowset as $dataRow) {
            [
                $manufacturerReference,
                $buyerReference,
                $manufacturerName,
                $designationOfReference,
                $companyId
            ] = array_pad($dataRow, 5, null);

            if (!$manufacturerReference || !$buyerReference || !$companyId) {
                continue;
            }

            $valid = (int)$this->catalogReferenceService->referenceExists($manufacturerReference, $country);

            $statement = $connection->prepare('
                INSERT INTO company_catalog
                (external_ref, ref, company_id, manufacturer_name, designation_reference, valid)
                VALUES (:external_ref, :ref, :company_id, :manufacturer_name, :designation_reference, :valid)
            ');

            $statement->executeQuery(
                [
                    ':external_ref' => $manufacturerReference,
                    ':ref' => $buyerReference,
                    ':company_id' => $companyId,
                    ':manufacturer_name' => $manufacturerName,
                    ':designation_reference' => $designationOfReference,
                    ':valid' => $valid,
                ]
            );
        }

        if ($connection->getDriver() instanceof PDOMySqlDriver) {
            $connection->executeStatement('COMMIT;');
            $connection->executeStatement('SET foreign_key_checks=1;');
            $connection->executeStatement('SET autocommit=1;');
        }

        return;
    }

    /**
     * @throws Exception
     */
    public function export()
    {
        /** @var User $user */
        $user = $this->securityService->getUser();

        $this->exportByCriteria(['company' => $user->getCompany()]);
    }

    /**
     * @throws Exception
     */
    public function exportMismatching()
    {
        /** @var User $user */
        $user = $this->securityService->getUser();

        $this->exportByCriteria(['company' => $user->getCompany(), 'valid' => false]);
    }

    /**
     * @throws Exception
     */
    public function exportMatching()
    {
        /** @var User $user */
        $user = $this->securityService->getUser();

        $this->exportByCriteria(['company' => $user->getCompany(), 'valid' => true]);
    }

    public function totalMatchingReferences(): int
    {
        $user = $this->securityService->getUser();

        return $this->companyCatalogRepository->count(['company' => $user->getCompany(), 'valid' => true]);
    }

    public function totalMismatchingReferences(): int
    {
        $user = $this->securityService->getUser();

        return $this->companyCatalogRepository->count(['company' => $user->getCompany(), 'valid' => false]);
    }

    public function topMismatchReferences(): array
    {
        return $this->companyCatalogRepository->findTopMismatchReferences();
    }

    private function exportByCriteria(array $criteria)
    {
        $fileHandle = fopen('php://output', 'w');

        stream_filter_register("newlines", StreamFilterNewlines::class);
        stream_filter_append($fileHandle, "newlines");

        fwrite($fileHandle, $bom = (chr(0xEF).chr(0xBB).chr(0xBF)));
        fputcsv($fileHandle, $this->getHeaders(), ';');

        $this->companyCatalogRepository->chunk(
            $criteria,
            1000,
            function(array $companyCatalog) use ($fileHandle){
                fputcsv(
                    $fileHandle,
                    [
                        $companyCatalog['external_ref'],
                        $companyCatalog['ref'],
                        $companyCatalog['manufacturer_name'],
                        $companyCatalog['designation_reference'],
                    ],
                    ';'
                );
            }
        );

        fclose($fileHandle);
    }

    public function deleteCompanyCatalog(Company $company)
    {
        $this->companyCatalogRepository->deleteCompanyCatalog($company->getId());
    }

    /*
     * @return string
     * @throws \Unirest\Exception
     */
    public function getData()
    {
        // Security check
        /** @var User $user */
        $user = $this->securityService->getUser();
        if ($this->securityService->isAnonymous()) {
            $this->logger->error('Call to CompanyCatalogService::getData without sufficient creds',LogUtil::buildContext([LogUtil::EVENT_NAME=>'error']));

            throw new Exception('Unauthorized');
        }

        $catalog = $this->companyCatalogRepository->findByCompany($user->getCompany());

        return json_encode($catalog);
    }

    /*
     * @return int
     * @throws \Unirest\Exception
     */
    public function countData()
    {
        // Security check
        /** @var User $user */
        $user = $this->securityService->getUser();
        if ($this->securityService->isAnonymous()) {
            $this->logger->error('Call to CompanyCatalogService::countData without sufficient creds',LogUtil::buildContext([LogUtil::EVENT_NAME=>'error']));

            throw new Exception('Unauthorized');
        }

        return $this->companyCatalogRepository->count(['company' => $user->getCompany()]);
    }

    /*
     * @return bool
     * @throws \Unirest\Exception
     */
    public function hasData()
    {
        return ($this->countData() > 0);
    }

    /**
     * delete mappings for the company held by current user.
     */
    public function delete()
    {
        // Security check
        /** @var User $user */
        $user = $this->securityService->getUser();

        if (!$this->securityService->isAdminCompany($user)) {
            $this->logger->error('Call to CompanyCatalogService::delete without sufficient creds', LogUtil::buildContext([LogUtil::EVENT_NAME=>'error']));

            throw new Exception('Unauthorized');
        }

        $companyId = $user->getCompany()->getId();

        return $this->companyCatalogRepository->deleteCompanyCatalog($companyId);
    }

    /*
     * @param string $what
     *
     * @return string
     * @throws \Unirest\Exception
     */
    public function search($what = '')
    {
        $user = $this->securityService->getUser();

        if (!$this->securityService->isBuyer($user)) {
            $this->logger->error('Call to CompanyCatalogService::search without sufficient creds', LogUtil::buildContext([LogUtil::EVENT_NAME=>'error']));

            throw new Exception('Unauthorized');
        }

        $company_id = $user->getCompany()->getId();

        // Step 1 : retrieve corresponding IZB refs
        if (empty($what)) {
            return '';
        }

        $results = $this->companyCatalogRepository->createQueryBuilder('cc')
          ->where('cc.company = :company_id')
          ->andWhere('cc.ref LIKE :ref') // for case insensitive search
          ->setParameter('company_id', $company_id)
          ->setParameter('ref', $what)
          ->getQuery()
          ->getResult();

        /** @var CompanyCatalog $result */
        foreach ($results as $result) {
            // return the first result (if any)
            return $result->getExternalRef();
        }

        return '';
    }

    /**
     * @param Company $company
     * @param Offer $offer
     * @return string|null
     */
    public function searchCompanyBuyerReference(Company $company, Offer $offer): ?string
    {
        $companyCatalogRow = $this->companyCatalogRepository->findOneBy(
            [
                'externalRef' => $offer->getManufacturerRef(),
                'company' => $company,
            ]
        );

        /** @var CompanyCatalog $companyCatalogRow*/
        if ($companyCatalogRow) {
            return $companyCatalogRow->getRef();
        }

        return null;
    }

    /**
     * @param $locale
     */
    public function setLocale($locale)
    {
        $this->locale = $locale;
    }

    /**
     * @return string
     */
    public function getLocale(): string
    {
        return $this->locale;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
