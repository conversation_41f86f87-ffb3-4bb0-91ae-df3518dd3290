<?php

namespace AppBundle\Services;

use Mpdf\MpdfException;
use Mpdf\Output\Destination;
use Open\IzbergBundle\Api\OrderApi;
use Open\IzbergBundle\Model\FetchMerchantOrdersResponse;
use Open\IzbergBundle\Model\OrderMerchant;
use Open\IzbergBundle\Service\AttributeService;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;

class MerchantOrderService implements LoggerAwareInterface
{
    private OrderApi $orderApi;
    private SerializerService $serializerService;
    private JobService $jobService;
    private int $maxDayLimitBeforeAutoCancel;
    private OrderPDFGenerator $orderPDFGenerator;
    private AttributeService $attributeService;
    private string $tmpDir;
    private LoggerInterface $logger;

    public function __construct(
        int $maxDayLimitBeforeAutoCancel,
        string $tmpDir,
        OrderApi $orderApi,
        SerializerService $serializerService,
        JobService $jobService,
        OrderPDFGenerator $orderPDFGenerator,
        AttributeService $attributeService
    )
    {
        $this->orderApi = $orderApi;
        $this->serializerService = $serializerService;
        $this->jobService = $jobService;
        $this->maxDayLimitBeforeAutoCancel = $maxDayLimitBeforeAutoCancel;
        $this->orderPDFGenerator = $orderPDFGenerator;
        $this->attributeService = $attributeService;
        $this->tmpDir = $tmpDir;
    }

    public function fetchInitialMerchantOrders(int $offset = 0, int $limit = 10): FetchMerchantOrdersResponse
    {
        return $this->orderApi->fetchMerchantOrdersByStatus(OrderApi::MERCHANT_ORDER_STATUS_INITIAL, $offset, $limit);
    }

    public function fetchMerchantOrdersIdsToCancel(int $offset = 0, int $limit = 10): array
    {
        $dateLimitBeforeAutoCancel = (new \DateTime())
            ->sub((new \DateInterval(sprintf('P%dD', $this->maxDayLimitBeforeAutoCancel))));

        return array_map(
            function(OrderMerchant $orderMerchant) {
                return $orderMerchant->getId();
            },
            array_filter(
                $this->fetchInitialMerchantOrders($offset, $limit)->getObjects()->toArray(),
                function(OrderMerchant $orderMerchant) use ($dateLimitBeforeAutoCancel){
                    return ($orderMerchant->getCreatedOn() <= $dateLimitBeforeAutoCancel);
                }
            )
        );
    }

    public function autoCancel(array $merchantOrderIds, $jobQueue = false): void
    {
        if ($jobQueue) {
            $this->jobService->autoCancelOrder($merchantOrderIds);
            return;
        }

        foreach($merchantOrderIds as $merchantOrderId) {
            $this->orderApi->cancelMerchantOrder($merchantOrderId);

            $logMessage = 'Merchant order with id ' . $merchantOrderId . ' has been successfully auto cancelled.';
            $this->logger->info($logMessage,
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::MERCHANT_ORDER_CANCEL
                ])
            );
        }
    }

    /**
     * Generate a merchant order pdf
     * and upload it to izberg in order for the buyer and the vendor to share the same PDF
     *
     * @param int $merchantOrderId
     * @throws MpdfException
     */
    public function uploadMerchantOrderPdfToMerchant(int $merchantOrderId)
    {
        $file = $this->tmpDir . '/merchant-order-' . $merchantOrderId . '.pdf';

        // generate pdf
        // todo retrieve merchant locale
        $merchantLocale = 'en';
        $pdf = $this->orderPDFGenerator->computeMerchantOrderPDF($merchantOrderId, $merchantLocale);
        $pdf->output($file, Destination::FILE);

        if (file_exists($file)) {
            $this->attributeService->updateMerchantOrderPdfAttribute($merchantOrderId, $file);
            @unlink($file);
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
