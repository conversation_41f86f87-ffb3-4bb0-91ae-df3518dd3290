<?php
namespace AppBundle\Services;

use AppBundle\Entity\Category;
use AppBundle\Entity\User;
use AppBundle\Entity\UserBafvMerchantList;
use AppBundle\FilterQueryBuilder\FilterQueryBuilderInterface;
use AppBundle\FilterQueryBuilder\UserQueryBuilder;
use AppBundle\Model\Offer;
use AppBundle\Repository\UserBafvMerchantListRepository;
use AppBundle\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;
use Knp\Component\Pager\PaginatorInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;


/**
 * Class UserBddService
 * @package AppBundle\Services
 */
class UserBddService extends AbstractPaginatedService
{
    private $authorizationChecker;

    public function __construct(
        EntityManagerInterface $em,
        PaginatorInterface $paginator,
        UserQueryBuilder $filterQueryBuilder,
        AuthorizationCheckerInterface $authorizationChecker
    )
    {
        parent::__construct($em, User::class, $paginator, $filterQueryBuilder);

        $this->authorizationChecker = $authorizationChecker;

    }

    public function findByUsername (string $username){
        return $this->repository->findOneBy(array('username' => $username));
    }

    /**
     * Fetch an user with his ID
     */
    public function findById(int $id): ?User
    {
        return $this->repository->find($id);
    }


    public function getCustomFilteredPaginator ($page, $numberPerPage, $request, $data, $qualifier){


        $qb = $this->getUserQueryBuilder($qualifier);

        $this->filterQueryBuilder->build($qb, $data);

        return $this->paginator->paginate(
            $qb->getQuery(),
            $request->query->getInt('page', 1),
            $numberPerPage,
            array('defaultSortFieldName' => 'e.createdAt', 'defaultSortDirection' => 'desc')
        );

    }

    public function getCustomUsersFilteredPaginator ($page, $numberPerPage, $request, $data, $qualifier)
    {
        $qb = $this->getUserQueryBuilder($qualifier);
        if($data != null && array_key_exists('page', $data) && $data['page'] == 'adminList'){
            // Filter with administrators
            $or = $qb->expr()->orX();
            $or->addMultiple(['e.roles LIKE :admin1',
                'e.roles LIKE :admin2']);
            $qb->andWhere($or);
            $qb->setParameter(':admin1','%SUPER_ADMIN%');
            $qb->setParameter(':admin2', '%ROLE_OPERATOR%');
        }else{
            $qb->andWhere("e.roles NOT LIKE :admin1");
            $qb->setParameter(':admin1','%SUPER_ADMIN%');
            $qb->andWhere("e.roles NOT LIKE :admin2");
            $qb->setParameter(':admin2','%ROLE_OPERATOR%');
        }


        // Filter with other data
        $this->filterQueryBuilder->build($qb, $data);

        return $this->paginator->paginate(
            $qb->getQuery(),
            $request->query->getInt('page', 1),
            $numberPerPage,
            array('defaultSortFieldName' => 'e.createdAt', 'defaultSortDirection' => 'desc')
        );
    }

    public function getUserConnectionsPaginator($page, $numberPerPage, $request, $userId){
        $qb = $this->em->createQueryBuilder();
        $qb->select('c')
            ->from(\AppBundle\Entity\Connection::class, "c")
            ->leftJoin("c.user", "u")
            ->where("u.id = :userId")
            ->orderBy('c.connectedAt', 'desc')
            ->setParameter("userId", $userId);



        return $this->paginator->paginate(
            $qb->getQuery(),
            $request->query->getInt('page', 1),
            $numberPerPage,
            array()
        );

    }



    private function getUserQueryBuilder ($qualifier){
        $qb = $this->em->createQueryBuilder();
        $qb->select('e')
            ->from($this->entityName, 'e')
            ->leftJoin('e.company', 'c');

        //add qualifier in request if needed
        if ($qualifier === "toConfirmed"){
            $qb->where("e.emailConfirmed = false");
        }
        else if ($qualifier === "enabled"){
            $qb->where("e.enabled = true");
        }
        else if ($qualifier === "disabled"){
            $qb->where("e.enabled = false");
        }

        //check operator/admin: if not admin, do not not list admin user
        if (!$this->authorizationChecker->isGranted('ROLE_SUPER_ADMIN')){
            $qb->andWhere("e.roles not like '%ROLE_SUPER_ADMIN%'");
        }


        return $qb;
    }

    /**
     * get all users depending on the qualifier (filter)
     * @param $qualifier string how to filter the request
     * @return array the list of users
     */
    public function getUsersByQualifier($qualifier){
        $qb = $this->getUserQueryBuilder($qualifier);
        $query = $qb->getQuery();
        return $query->getResult();
    }

    /**
     * get all administrators depending on the qualifier (filter)
     * @param $qualifier string how to filter the request
     * @return array the list of administrators
     */
    public function getAdminsByQualifier($qualifier){
        $qb = $this->getUserQueryBuilder($qualifier);
        // Display only administrators
        $or = $qb->expr()->orX();
        $or->addMultiple(['e.roles LIKE :admin1',
            'e.roles LIKE :admin2']);
        $qb->andWhere($or);
        $qb->setParameter(':admin1','%SUPER_ADMIN%');
        $qb->setParameter(':admin2', '%ROLE_OPERATOR%');
        $query = $qb->getQuery();
        return $query->getResult();
    }

    public function getOnlyUsersByQualifier($qualifier){
        $qb = $this->getUserQueryBuilder($qualifier);
        $qb->andWhere("e.roles NOT LIKE :admin1");
        $qb->setParameter(':admin1','%SUPER_ADMIN%');
        $qb->andWhere("e.roles NOT LIKE :admin2");
        $qb->setParameter(':admin2','%ROLE_OPERATOR%');
        $query = $qb->getQuery();
        return $query->getResult();
    }

    public function getCompanyCustomFilteredPaginator ($page, $numberPerPage, $request, $data, $companyId){
        $qb = $this->getCompanyUserQueryBuilder($companyId);

        $this->filterQueryBuilder->build($qb, $data);

        return $this->paginator->paginate(
            $qb->getQuery(),
            $request->query->getInt('page', 1),
            $numberPerPage
        );

    }

    /**
     * @param $user
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function updateUser($user){
        $this->em->persist($user);
        $this->em->flush();
    }

    /**
     * get operators
     * @return array
     */
    public function getOperatorsUsers(){
        /** @var UserRepository $userRepository */
        $userRepository = $this->em->getRepository($this->entityName);
        return $userRepository->findOperator();
    }

    public function getCompanyUsersFiltered($companyId, $data){
        $qb = $this->getCompanyUserQueryBuilder($companyId);
        $this->filterQueryBuilder->build($qb, $data);
        $query = $qb->getQuery();
        return $query->getResult();
    }

    public function getBuyerUsersByCompany($companyId) {
        /** @var UserRepository $userRepository */
        $userRepository = $this->em->getRepository(User::class);
        return $userRepository->findBuyersForCompany($companyId);
    }

    public function getUsersBySite($siteId, $userId) {
        /** @var UserRepository $userRepository */
        $userRepository = $this->em->getRepository(User::class);
        return $userRepository->findUsersForSite($siteId, $userId);
    }

    private function getCompanyUserQueryBuilder ($companyId){
        $qb = $this->em->createQueryBuilder();
        $qb->select('e')
            ->from($this->entityName, 'e')
            ->leftJoin('e.company', 'c');

        $qb->where("c.id = :companyId")->setParameter('companyId', $companyId);

        //check operator/admin: if not admin, do not not list admin user
        if (!$this->authorizationChecker->isGranted('ROLE_SUPER_ADMIN')){
            $qb->andWhere("e.roles not like '%ROLE_SUPER_ADMIN%'");
        }


        return $qb;
    }

    public function isMerchantAuthorizedForUser(User $user, int $merchantId): bool
    {
        $repository = $this->em->getRepository(UserBafvMerchantList::class);

        $company = $user->getCompany();
        $companyCategory = $company->getCategory()->getLabel();
        $authorizedMerchant = null;

        $isBafvCompany = ($companyCategory === Category::CATEGORY_BAFV);
        $isMerchantAuthorizedForUser = true;

        if ($isBafvCompany) {
            $isMerchantAuthorizedForUser = $repository->isMerchantAuthorizedForCompany($company, $merchantId);
        }

        return $isMerchantAuthorizedForUser;
    }
}
