<?php

namespace AppBundle\Services;

use AppBundle\Entity\ShippingPoint;
use AppBundle\Entity\User;
use AppBundle\FilterQueryBuilder\SiteQueryBuilder;
use AppBundle\Repository\SiteRepository;
use AppBundle\Entity\Company;
use AppBundle\Entity\Site;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\QueryBuilder;
use Knp\Component\Pager\PaginatorInterface;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Symfony\Contracts\Translation\TranslatorInterface;


class SiteService extends AbstractPaginatedService implements LoggerAwareInterface
{
    private CountryService $countryService;
    private AuthorizationCheckerInterface $authorizationChecker;
    private LoggerInterface $logger;
    private MailService $mailer;
    private TranslatorInterface $translator;
    private $user;
    private SecurityService $securityService;

    public function __construct(
        EntityManagerInterface $em,
        PaginatorInterface $paginator,
        SiteQueryBuilder $siteQueryBuilder,
        CountryService $country_service,
        AuthorizationCheckerInterface $authorizationChecker,
        MailService $mailer,
        TranslatorInterface $translator,
        TokenStorageInterface $token_storage,
        SecurityService $securityService
    )
    {
        parent::__construct($em, Site::class, $paginator, $siteQueryBuilder);

        $this->countryService = $country_service;
        $this->authorizationChecker = $authorizationChecker;
        $this->mailer = $mailer;
        $this->translator = $translator;
        $this->user = empty($token_storage->getToken()) ? null : $token_storage->getToken()->getUser();
        $this->securityService = $securityService;
    }

    /***
     * @param $page
     * @param $numberPerPage
     * @param $request
     * @param $data
     * @param $qualifier
     *
     * @return mixed
     */
    public function getCustomFilteredPaginator(
        $page,
        $numberPerPage,
        $request,
        $data,
        $qualifier
    )
    {


        $qb = $this->getQueryBuilder($qualifier);

        $this->filterQueryBuilder->build($qb, $data);

        return $this->paginator->paginate(
            $qb->getQuery(),
            $request->query->getInt('page', 1),
            $numberPerPage,
            array('defaultSortFieldName' => 'e.createdAt', 'defaultSortDirection' => 'desc')
        );

    }

    /**
     * get all users affilated to one cost center
     * @param $siteId
     * @return mixed
     */
    public function getUsersOfOneSite($siteId)
    {
        $qb = $this->em->createQueryBuilder();
        return $qb->select('e')
            ->from(\AppBundle\Entity\User::class, 'e')
            ->where(':id MEMBER OF e.sites')
            ->Andwhere('e.enabled<>0')
            ->setParameter('id', $siteId)->getQuery()->getResult();
    }


    /***
     * @param $qualifier
     *
     * @return QueryBuilder
     */
    private function getQueryBuilder($qualifier)
    {
        $qb = $this->em->createQueryBuilder();
        $qb->select('e')
            ->from($this->entityName, 'e')
            ->leftJoin('e.company', 'c');

        if ($qualifier === 'toConfirmed') {
            $qb->where("e.status = 'pending'");
        }

        return $qb;
    }

    /**
     * get all users depending on the qualifier (filter)
     *
     * @param $qualifier string how to filter the request
     *
     * @return array the list of users
     */
    public function getByQualifier($qualifier)
    {
        $qb = $this->getQueryBuilder($qualifier);
        $query = $qb->getQuery();
        return $query->getResult();
    }

    /***
     * @param $page
     * @param $numberPerPage
     * @param $request
     * @param $data
     * @param $companyId
     *
     * @return mixed
     */
    public function getCompanyCustomFilteredPaginator(
        $page,
        $numberPerPage,
        $request,
        $data,
        $companyId
    )
    {


        $qb = $this->getCompanySiteQueryBuilder($companyId);

        $this->filterQueryBuilder->build($qb, $data);

        return $this->paginator->paginate(
            $qb->getQuery(),
            $request->query->getInt('page', 1),
            $numberPerPage
        );

    }

    /**
     * Retrieve sites with at least one address for a company
     * @param int $companyId company ID
     * @return array contains sites with addresses for company
     */
    public function getSitesWithAtLeastOneAddressByCompany($companyId)
    {
        $result = [];

        /** @var SiteRepository $siteRepository */
        $siteRepository = $this->em->getRepository(Site::class);
        $sites = $siteRepository->getSitesByCompany($companyId);

        /** @var Site $site */
        foreach ($sites as $site) {
            if (count($site->getShippingPoints()) > 0) {
                $result [] = $site;
            }
        }
        return $result;
    }

    /**
     * Checking if company has sites
     * @param int $companyId company ID
     * @return boolean true if the company has sites
     */
    public function checkIfCompanyHasSites($companyId)
    {
        /** @var SiteRepository $siteRepository */
        $siteRepository = $this->em->getRepository(Site::class);
        $sites = $siteRepository->getSitesByCompany($companyId);

        return count($sites) > 0;
    }

    /**
     * @param User $user
     * @return array contains sites for company
     */
    public function getUserSitesWithAtLeastOneAddress($user)
    {
        $result = [];
        $sites = $user->getSites();
        /** @var Site $site */
        foreach ($sites as $site) {
            if (count($site->getShippingPoints()) > 0) {
                $result [] = $site;
            }
        }
        return $result;
    }

    /***
     * @param Company $company
     *
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function ensureHasAtLeastOneSiteAttached(Company $company)
    {
        $firstShippingPointName = $this->translator->trans('shipping_point.form.first', array(), 'AppBundle');

        // Il faut au moins un cost center (site) et l'dresse de livraison est celle de la company
        $sites = $company->getSites();
        $nb = count($sites);
        if ($nb == 0) {

            $firstCostCenterName = $this->translator->trans('cost_center.name.first', array(), 'AppBundle');

            $site = new Site();
            $site->setName($firstCostCenterName);
            $company->addSite($site);

            $shippingPoint = new ShippingPoint();
            $shippingPoint->setName($firstShippingPointName);
            $address = clone($company->getMainAddress());
            $contact = clone($company->getMainContact());
            $shippingPoint->setAddress($address);
            $shippingPoint->setContact($contact);
            $shippingPoint->setSite($site);
            $site->addShippingPoint($shippingPoint);

            $this->em->persist($address);
            $this->em->persist($contact);
            $this->em->persist($shippingPoint);
            $this->em->persist($site);
            $this->em->persist($company);
            $this->em->flush();
        } elseif ($nb == 1) {
            // Il faut que ce cost center ait au moins une adresse de livraison
            // dans ce cas cette address = adresse principale de la company
            /* @var Site $site  */
            $site = $sites[0];
            $shippingPoints = $site->getShippingPoints();
            if (count($shippingPoints) == 0) {
                $shippingPoint = new ShippingPoint();
                $shippingPoint->setName($firstShippingPointName);
                $address = clone($company->getMainAddress());
                $contact = clone($company->getMainContact());
                $shippingPoint->setAddress($address);
                $shippingPoint->setContact($contact);
                $shippingPoint->setSite($site);
                $site->addShippingPoint($shippingPoint);

                $this->em->persist($address);
                $this->em->persist($contact);
                $this->em->persist($shippingPoint);
                $this->em->persist($site);
                $this->em->flush();
            } elseif (count($shippingPoints) == 1) {
                // Voyons voir si il y a une adresse et/ou un contact
                /** @var ShippingPoint $shippingPoint */
                $shippingPoint = $shippingPoints[0];
                if (!$shippingPoint->getAddress()) {
                    $address = clone($company->getMainAddress());
                    $shippingPoint->setAddress($address);
                    $this->em->persist($address);
                    $this->em->persist($shippingPoint);
                    $this->em->flush();
                }
                if (!$shippingPoint->getContact()) {
                    $contact = clone($company->getMainContact());
                    $shippingPoint->setContact($contact);
                    $this->em->persist($contact);
                    $this->em->persist($shippingPoint);
                    $this->em->flush();
                }
            }
        }

    }

    /**
     * get a site object from his id
     * @param $id
     * @return Site|null
     */
    public function get($id): ?Site
    {
        return $this->em->getRepository(Site::class)->find($id);
    }

    public function getFromShippingPointId($shippingPointId): ?Site
    {
        $shippingPoint = $this->em->getRepository(ShippingPoint::class)->find($shippingPointId);

        if ($shippingPoint) {
            return $shippingPoint->getSite();
        }

        return null;
    }

    /***
     * Delete a site attached to a company, ensure consistency
     *
     * @param int $id
     *
     * @return bool
     */
    public function delete(int $id)
    {
        // Is this Id is correct ?
        /*** @var $site Site */
        $site = $this->get($id);
        if ($site) {

            // Get the attached company
            /*** @var $company Company */
            $company = $site->getCompany();
            if ($company) {

                // Check if users are associated with this Cost center (site)
                if (!$this->canDelete($id)) {
                    throw new \LogicException('Site id=' . $id . ' has associated users');
                }

                $shippingPoints = $site->getShippingPoints();
                foreach ($shippingPoints as $shippingPoint) {

                    /**  @var ShippingPoint $shippingPoint */
                    $address = $shippingPoint->getAddress();
                    if ($address) {
                        $this->em->remove($address);
                    }
                    $contact = $shippingPoint->getContact();
                    if ($contact) {
                        $this->em->remove($contact);
                    }

                    $site->removeShippingPoint($shippingPoint);
                    $this->em->remove($shippingPoint);
                }

                // delete site
                $this->em->remove($site);

                // update DB
                $this->em->flush();
                // Log this action
                $this->logger->info("Site has been disabled",
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME=>EventNameEnum::SITE_DISABLE,
                        LogUtil::USER_NAME=>$this->user->getUsername(),
                        'SITE_ID' => $id
                    ]));

                return true;
            } else {
                throw new \LogicException('No company found for site id=' . $id);
            }
        } else {
            throw new \LogicException('No site found for id=' . $id);
        }
    }


    /***
     *
     * Check if we have users attached to this site
     *
     * @param $id
     *
     * @return bool
     */
    public function canDelete($id)
    {
        $siteUsers = $this->getUsersOfOneSite($id);
        return (count($siteUsers) == 0);
    }


    private function getCompanySiteQueryBuilder($companyId)
    {
        $qb = $this->em->createQueryBuilder();
        $qb->select('e')
            ->from($this->entityName, 'e')
            ->leftJoin('e.company', 'c');

        $qb->where("c.id = :companyId")->setParameter('companyId', $companyId);

        return $qb;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
