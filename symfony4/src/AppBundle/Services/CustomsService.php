<?php

namespace AppBundle\Services;

use AppBundle\Entity\Company;
use AppBundle\Entity\TvaGroup;
use AppBundle\Entity\TvaRate;
use AppBundle\Entity\User;
use AppBundle\Model\Offer;
use AppBundle\Repository\TvaGroupRepository;
use AppBundle\Repository\TvaRateRepository;
use AppBundle\Entity\Country;
use Doctrine\ORM\EntityManagerInterface;
use Open\LogBundle\Utils\EventNameEnum;
use AppBundle\Model\CustomsInfo;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;

/**
 * Class CustomsService
 *
 * @package AppBundle\Services
 */
class CustomsService implements LoggerAwareInterface
{
    const EMPTY_LEGAL_NOTICE = '';

    private EntityManagerInterface $em;
    private MerchantService $merchantService;
    private LoggerInterface $logger;
    private SecurityService $securityService;
    private TvaRateRepository $tvaRepository;
    private TvaGroupRepository $tvaGroupRepository;
    private AlstomCustomAttributes $customAttributes;

    /***
     * CustomsService constructor.
     *
     * @param EntityManagerInterface $em
     * @param MerchantService $merchantService
     * @param SecurityService securityService
     * @param AlstomCustomAttributes $customAttributes
     */
    public function __construct(
        EntityManagerInterface $em,
        MerchantService $merchantService,
        SecurityService $securityService,
        AlstomCustomAttributes $customAttributes
    )
    {
        $this->em = $em;
        $this->merchantService = $merchantService;
        $this->securityService = $securityService;
        $this->tvaRepository = $this->em->getRepository(TvaRate::class);
        $this->tvaGroupRepository = $this->em->getRepository(TvaGroup::class);
        $this->customAttributes = $customAttributes;
    }


    /***
     * @param Country|null $merchantCountry
     * @param Country|null $buyerCountry
     *
     * @return bool
     */
    public function isInError(?Country $merchantCountry, ?Country $buyerCountry)
    {
        // Check consistency
        if (($merchantCountry == null) || ($buyerCountry == null)) {
            // Should not happen, LOG IT !
            $this->logger->critical('NULL company country',
                LogUtil::buildContext([
                LogUtil::EVENT_NAME=>EventNameEnum::PAYMENT_GENERAL_ERROR
            ])
            );
            return true;
        }
        return false;
    }

    /***
     * @param Country|null $merchantCountry
     * @param Country|null $buyerCountry
     *
     * @return bool
     */
    public function isExport(?Country $merchantCountry, ?Country $buyerCountry)
    {
        if ($this->isInError($merchantCountry, $buyerCountry)) {
            return false;
        }
        // same country
        return !($buyerCountry->getIzbergId() == $merchantCountry->getIzbergId());
    }

    /**
     * @param Country|null $merchantCountry
     * @param Country|null $buyerCountry
     *
     * @return bool
     */
    public function partnersAreInEU(?Country $merchantCountry, ?Country $buyerCountry)
    {
        if ($this->isInError($merchantCountry, $buyerCountry)) {
            return false;
        }

        return (($buyerCountry->isInEU()) && ($merchantCountry->isInEU()));
    }

    /***
     * @param Country|null $merchantCountry
     * @param Country|null $buyerCountry
     *
     * @return string
     */
    public function getAdditionalLegalNotices(?Country $merchantCountry, ?Country $buyerCountry)
    {

        if ($this->isInError($merchantCountry, $buyerCountry)) {
            return self::EMPTY_LEGAL_NOTICE;
        }

        if ($this->isExport($merchantCountry, $buyerCountry)) {
            if ($this->partnersAreInEU($merchantCountry, $buyerCountry)) {
                // Legal Notice for Export INTRA
                return empty($merchantCountry->getLegalNoticeProductExportEU()) ? self::EMPTY_LEGAL_NOTICE : $merchantCountry->getLegalNoticeProductExportEU();
            } else {
                // Legal Notice for Export NON EU
                return empty($merchantCountry->getLegalNoticeProductExportNonEU()) ? self::EMPTY_LEGAL_NOTICE : $merchantCountry->getLegalNoticeProductExportNonEU();
            }
        }

        return self::EMPTY_LEGAL_NOTICE;
    }

    /**
     *
     * Information to display in cart
     *
     * @param Country|null $merchantCountry
     * @param Country|null $buyerCountry
     *
     * @return \AppBundle\Model\CustomsInfo
     */
    public function getInfo(?Country $merchantCountry, ?Country $buyerCountry)
    {
        if (
            ($this->isInError($merchantCountry, $buyerCountry)) ||
            (!$this->isExport($merchantCountry, $buyerCountry))
        ) {
            return new CustomsInfo(true, false, false, false);
        } else {
            if ($this->partnersAreInEU($merchantCountry, $buyerCountry)) {
                return new CustomsInfo(false, true, false, true);
            } else {
                return new CustomsInfo(false, false, true, true);
            }
        }
    }


    /***
     * @param Country|null $merchantCountry
     * @param Country|null $buyerCountry
     *
     * @return bool
     */
    public function HaveToAddVAT(?Country $merchantCountry, ?Country $buyerCountry)
    {
        if ($this->isInError($merchantCountry, $buyerCountry)) {
            return true;
        }

        if (!$this->isExport($merchantCountry, $buyerCountry)) {
            // same country
            return true;
        }

        if ($this->partnersAreInEU($merchantCountry, $buyerCountry)) {
            // Both are in European Union AND countries are different
            return false; // INTRACOM
        }

        return true;
    }

    /***
     *
     * Calcule le taux de TVA à appliquer suivant les regles de l'art !
     *
     * @param Offer $offer
     * @return mixed
     */
    public function taxRateToApply(Offer $offer, ?User $buyer = null)
    {
        $buyerCountry = null;
        $merchantCountry = $this->merchantService->getMerchantFiscalCountryByMerchantId($offer->getMerchant()->getId());
        $buyerCountry = $this->securityService->getFiscalCountry($buyer);

        if ($merchantCountry->getId() === $buyerCountry->getId() && $offer->getTaxGroup() !== null) {
            /** @var TvaRate $tva */
            $taxGroup = $offer->getTaxGroup();
            $tva = $this->tvaRepository->getTaxRateFromTaxGroupAndDate($taxGroup, new \DateTimeImmutable);
            if ($tva != null) {
                return $tva->getRate();
            }
        }
        return 0;
    }

    public function shippingTaxRateToApply(Company $company, int $merchantId)
    {
        $merchantCountry = $this->merchantService->getMerchantFiscalCountryByMerchantId($merchantId);
        $buyerCountry = $company->getFiscalCountry();

        if ($merchantCountry->getId() === $buyerCountry->getId()) {

            $taxGroup = $this->fetchCountryTaxGroup($buyerCountry);

            /** @var TvaRate $tva */
            $tva = $this->tvaRepository->getTaxRateFromTaxGroupAndDate($taxGroup, new \DateTimeImmutable);
            if ($tva != null) {
                return $tva->getRate();
            }
        }

        return 0;
    }

    private function fetchCountryTaxGroup(Country $country): ?string
    {
        $taxGroup = null;
        $tvaGroups = $this->tvaGroupRepository->findBy(['country' => $country]);

        /** @var TvaGroup $tvaGroup */
        foreach($tvaGroups as $tvaGroup) {
            if(strstr($tvaGroup->getGroupName(),'-standard')) {
                return $tvaGroup->getGroupName();
            }
        }

        return $taxGroup;
    }

    public function taxRateToApplyFromOffer(Offer $offer)
    {
        $buyerCountry = null;
        $merchantCountry = $this->merchantService->getMerchantFiscalCountryByMerchantId($offer->getMerchant()->getId());
        $buyerCountry = $this->securityService->getFiscalCountry();

        // Business logic here
        if ($merchantCountry->getId() === $buyerCountry->getId() && $offer->getTaxGroup()) {
            /** @var TvaRate $tva */
            $tva = $this->tvaRepository->getTaxRateFromTaxGroupAndDate($offer->getTaxGroup(), new \DateTimeImmutable);
            if ($tva != null) {
                return $tva->getRate();
            }
        }
        return 0;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
