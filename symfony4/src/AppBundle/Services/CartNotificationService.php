<?php

namespace AppBundle\Services;

use AppBundle\Model\Cart\Cart;
use AppBundle\Model\Cart\CartItem;
use AppBundle\Model\Cart\CartNotification;
use Open\IzbergBundle\Api\CartNotificationApi;

class CartNotificationService
{
    private CartNotificationApi $cartNotificationApi;
    private CartService $cartService;

    public function __construct(CartNotificationApi $cartNotificationApi, CartService $cartService)
    {
        $this->cartNotificationApi = $cartNotificationApi;
        $this->cartService = $cartService;
    }

    public function checkCartNotification(Cart $cart)
    {
        $izbergCartNotifications = $this->cartNotificationApi->getUnreadNotifications($cart->getId());
        $izbergCartNotifications = array_filter(
            $izbergCartNotifications,
            fn(\Open\IzbergBundle\Model\CartNotification $izbergCartNotification) => $izbergCartNotification->getCartItem() !== null
        );

        $izbergCartNotifications = array_map(
            fn(\Open\IzbergBundle\Model\CartNotification $izbergCartNotification) => (new CartNotification())
                ->setMessage($izbergCartNotification->getCustomMessage())
                ->setId($izbergCartNotification->getId())
                ->setCartItemId($izbergCartNotification->getCartItem()->getId())
                ->setType($izbergCartNotification->getNotifType())
            ,
            $izbergCartNotifications
        );

        /** @var CartNotification $cartNotification */
        foreach ($izbergCartNotifications as $cartNotification) {
            $cart->addNotification($cartNotification);
        }
    }

    public function applyCartNotification(CartNotification $cartNotification)
    {
        if ($cartNotification->cartItemNotAvailable()) {
            $this->cartService->removeItem($cartNotification->getCartItemId());
        }

        if ($cartNotification->getId()) {
            $this->cartNotificationApi->readNotification($cartNotification->getId());
        }
    }

    public function getSpecificPriceCartNotifications(Cart $cart): array
    {
        $notifications = [];

        /** @var CartItem $cartItem */
        foreach($cart->getItems() as $cartItem) {
            $cartItemUpToDate = $this->isCartItemUpToDate($cartItem);

            if (!$cartItemUpToDate) {
                $notification = (new CartNotification())
                    ->setMessage('cart.notification_message.offer_price_changed')
                    ->setCartItemId($cartItem->getId())
                    ->setType(CartNotification::TYPE_OFFER_PRICE_CHANGED);

                $notifications[] = $notification;
            }
        }

        return $notifications;
    }

    private function isCartItemUpToDate(CartItem $cartItem): bool
    {
        $offer = $cartItem->getOffer();
        $offerPrice = $offer->getPriceForQuantity($cartItem->getQuantity());
        return $cartItem->getUnitPrice() == $offerPrice;
    }
}
