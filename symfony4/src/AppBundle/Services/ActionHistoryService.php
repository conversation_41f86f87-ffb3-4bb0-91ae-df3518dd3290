<?php

namespace AppBundle\Services;

use AppBundle\Entity\Category;
use Doctrine\ORM\EntityManagerInterface;
use Knp\Component\Pager\PaginatorInterface;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;

class ActionHistoryService extends AbstractPaginatedService implements LoggerAwareInterface
{
    private LoggerInterface $logger;

    /**
     * ActionHistoryService constructor.
     * @param EntityManagerInterface $em
     * @param $paginator
     */
    public function __construct(EntityManagerInterface $em, PaginatorInterface $paginator)
    {
        parent::__construct($em, Category::class, $paginator);
    }

    /**
     * @param $page
     * @param $numberPerPage
     * @param $request
     * @param $userId
     * @return mixed
     */
    public function getHistoryPaginatorForUser ($page, $numberPerPage, $request, $userId){
        $qb = $this->em->createQueryBuilder();
        $qb->select('a')
            ->from(\AppBundle\Entity\ActionHistorization::class, "a")
            ->leftJoin("a.users", "u")
            ->where("u.id = :userId")
            ->orderBy('a.createdAt', 'desc')
            ->setParameter("userId", $userId);

        try {
            $history = $qb->getQuery()->getArrayResult();
        }catch (\Exception $e){
            $this->logger->critical("unable to generate history for user with id ".$userId.": ".$e->getMessage(),
                LogUtil::buildContext([LogUtil::EVENT_NAME=>EventNameEnum::TECHNICAL_ERROR, LogUtil::TRACE=>$e->getTraceAsString()]));
            $history = [];
        }


        return $this->paginator->paginate(
            $history,
            $request->query->getInt('actionPage', 1),
            $numberPerPage,
            array(
                'pageParameterName' => 'actionPage')
        );

    }


    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
