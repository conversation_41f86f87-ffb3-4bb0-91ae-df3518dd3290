<?php

namespace AppBundle\Services\Response;

class Response
{
    /**
     * @var array
     */
    private $errors = [];

    protected function addError(Error $error)
    {
        $this->errors[] = $error;
    }

    public function hasErrors()
    {
        return count($this->errors);
    }

    /**
     * @return array
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    public function handleErrors($callable, array $errors = null)
    {
        if (!$errors) {
            $errors = $this->getErrors();
        }

        foreach($errors as $error) {
            call_user_func($callable, $error);
        }
    }
}
