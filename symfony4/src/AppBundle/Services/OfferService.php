<?php

namespace AppBundle\Services;

use Algolia\AlgoliaSearch\Exceptions\AlgoliaException;
use AppBundle\Entity\Company;
use AppBundle\Entity\User;
use AppBundle\Entity\WishList;
use AppBundle\Entity\WishListItem;
use AppBundle\Exception\InvalidSettingException;
use AppBundle\Model\DetailedOffer;
use AppBundle\Model\Merchant;
use AppBundle\Model\Offer;
use AppBundle\Model\Order\Order;
use AppBundle\Model\Order\OrderItem;
use AppBundle\Util\Locale;
use AppBundle\Util\SettingsProvider;
use Doctrine\ORM\EntityManagerInterface;
use Open\BCEBundle\Service\BCEService;
use Open\IzbergBundle\Algolia\AlgoliaField;
use Open\IzbergBundle\Algolia\AlgoliaQuery;
use Open\IzbergBundle\Algolia\AlgoliaQueryParams;
use Open\IzbergBundle\Algolia\AlgoliaService;
use Open\IzbergBundle\Api\ApiException;
use Open\IzbergBundle\Dto\AttributeDTO;
use Open\IzbergBundle\Service\AttributeService;
use Open\IzbergBundle\Service\CategoryService;
use Open\IzbergBundle\Service\RedisService;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Asset\Packages;
use Symfony\Component\HttpFoundation\RequestStack;

class OfferService implements LoggerAwareInterface
{
    public const CACHE_BEST_OFFERS = 'CACHE_BEST_OFFERS';
    public const MERCHANT = 'merchant';

    private const NB_HITS = 'nbHits';
    private const HITS = 'hits';
    private const FACET_HITS = 'facetHits';


    private Packages $manager;
    private array $algoliaServices;
    private SettingsProvider $settingsProvider;
    private RedisService $cache;
    private LoggerInterface $logger;
    private MerchantService $merchantService;
    private array $facetsToRetrieve;
    private CategoryService $categoryService;
    private SearchHistorizationService $searchHistorizationService;
    private BCEService $BCEService;
    private EntityManagerInterface $entityManager;
    private AlstomCustomAttributes $customAttributes;
    private array $ignoredAttributes;
    private AttributeService $attributeService;
    private SpecificPriceService $specificPriceService;
    private string $locale;
    private AlstomCustomAttributes $alstomCustomAttributes;
    public array $breadcrumb;
    private ?int $clearanceCategory = null;
    private BafvServiceInterface $bafvService;
    private ?User $user = null;

    public function __construct(
        array                      $algoliaServices,
        array                      $ignoredAttributes,
        ?int                       $clearanceCategory,
        Packages                   $manager,
        SettingsProvider           $settingsProvider,
        RedisService               $cache,
        MerchantService            $merchantService,
        CategoryService            $categoryService,
        SearchHistorizationService $searchHistorizationService,
        RequestStack               $requestStack,
        EntityManagerInterface     $entityManager,
        BCEService                 $BCEService,
        AlstomCustomAttributes     $customAttributes,
        AttributeService           $attributeService,
        SpecificPriceService       $specificPriceService,
        AlstomCustomAttributes     $alstomCustomAttributes,
        BafvServiceInterface       $bafvService
    )
    {
        $this->manager = $manager;
        $this->algoliaServices = $algoliaServices;
        $this->settingsProvider = $settingsProvider;
        $this->cache = $cache;
        $this->merchantService = $merchantService;
        $this->facetsToRetrieve = [];
        $this->categoryService = $categoryService;
        $this->searchHistorizationService = $searchHistorizationService;
        $this->BCEService = $BCEService;
        $this->entityManager = $entityManager;
        $this->customAttributes = $customAttributes;
        $this->ignoredAttributes = $ignoredAttributes;
        $this->attributeService = $attributeService;
        $this->specificPriceService = $specificPriceService;
        $this->alstomCustomAttributes = $alstomCustomAttributes;
        $this->clearanceCategory = $clearanceCategory;
        $this->bafvService = $bafvService;

        // If service is initialize with a request, set request locale to service
        $this->setLocale(Locale::fetchFromRequestStack($requestStack));
    }

    /**
     * Define for which user the service will be used
     * By default we consider that the service is used for an anonymous user ($user = null)
     *
     * @param User $user
     */
    public function defineUser(?User $user)
    {
        $this->user = $user;
    }

    /**
     * @return array
     */
    public function getFacetsToRetrieve(): array
    {
        return $this->facetsToRetrieve;
    }

    /**
     * @param array $facetsToRetrieve
     */
    public function setFacetsToRetrieve(array $facetsToRetrieve): void
    {
        $this->facetsToRetrieve = $facetsToRetrieve;
    }

    /**
     * @param $sort_order
     */
    public function setSortorder($sort_order)
    {
        foreach ($this->algoliaServices as $algoliaService) {
            // @var $algoliaService AlgoliaService
            $algoliaService->setRanking($sort_order);
        }
    }

    /**
     * get an array of the popular offers as set by the administrator.
     *
     * @return array an array of the popular offers as set by the administrator
     * @throws InvalidSettingException
     *
     */
    public function getPopularOffersIds()
    {
        $result = [];
        for ($i = 1; $i <= 8; ++$i) {
            $offerId = $this->settingsProvider->get('offer_' . (string)$i, 'offers');
            if (!empty($offerId)) {
                $result[] = (int)$offerId;
            }
        }

        return $result;
    }

    /**
     * @param int $offerId izberg offerId
     *
     * @return Offer|null the offer if found
     * @throws ApiException
     *
     */
    public function findOfferById(int $offerId): ?Offer
    {
        return $this->buildOfferFromAlgoliaResult($this->getRawOfferFromId($offerId));
    }

    public function findOffersByIds(array $offerIds): array
    {
        if (!count($offerIds)) {
            return [];
        }

        $offers = array_map(
            function ($rawOffer) {
                return $this->buildOfferFromAlgoliaResult($rawOffer);
            },
            $this->getArrayOfRawOfferFromAlgolia(
                new AlgoliaQuery(
                    '',
                    (new AlgoliaQueryParams())
                        ->addNumericFilters(
                            array_map(
                                function (int $offerId) {
                                    return sprintf('id=%d', $offerId);
                                },
                                array_values($offerIds)
                            )
                        )
                )
            )
        );

        return array_combine(
            array_filter(
                array_map(
                    function (Offer $offer) {
                        return $offer->getIzbergReference();
                    },
                    $offers
                )),
            $offers
        );
    }

    public function findOffersByOrder(Order $order): array
    {
        // do not include shipping offers
        $offerIds = array_filter(
            array_map(
                function (OrderItem $orderItem) {
                    if ((preg_match('/^shipment-[0-9]+$/', $orderItem->getOfferExternalId()))) {
                        return null;
                    }

                    return $orderItem->getOfferId();
                },
                $order->getItems()
            )
        );

        return $this->findOffersByIds($offerIds);
    }

    public function findOffersByWishList(WishList $wishList): array
    {
        $offerIds = array_map(
            function (WishListItem $wishListItem) {
                return $wishListItem->getOfferId();
            },
            $wishList->getItems()->toArray()
        );

        return $this->findOffersByIds($offerIds);
    }

    public function findOffersByVendorOrManufacturerReferences(array $references, ?Company $company): array
    {
        if (!count(array_filter($references))) {
            return [];
        }

        $algoliaQuery = (
        new AlgoliaQuery(
            '',
            (new AlgoliaQueryParams())
                ->typoTolerance(false)
                ->addFacetFilters(
                    AlgoliaField::STATUS . ':active',
                    AlgoliaField::PRODUCT_STATUS . ':active',
                    AlgoliaField::MERCHANT_STATUS . ':10'
                )
                ->addNumericFilters(AlgoliaField::STOCK . '>0')
                ->addFilters(
                // OR filters
                // the code below return an array used to generate or filters in algolia query
                // example ->addFilters(
                //                  [
                //                      'attributes.CAN10_Manufacturer_reference:ReferenceA',
                //                      'attributes.AAA05_Vendor_Reference:ReferenceA',
                //                      'attributes.CAN10_Manufacturer_reference:ReferenceB',
                //                      'attributes.AAA05_Vendor_Reference:ReferenceB',
                //                      ...
                //                  ]
                //          )

                    array_merge(
                        [],
                        ...array_map(
                            function (string $reference): array {
                                return [
                                    sprintf(
                                        '%s:%s',
                                        AlstomCustomAttributes::createFullAttributeName(
                                            $this->customAttributes->getManufacturerReference()
                                        ),
                                        $reference
                                    ),
                                    sprintf(
                                        '%s:%s',
                                        AlstomCustomAttributes::createFullAttributeName(
                                            $this->customAttributes->getVendorReference()
                                        ),
                                        $reference
                                    ),
                                ];
                            },
                            array_values($references)
                        )
                    )
                )
        )
        );

        /** @var AlgoliaService $algoliaService */
        $algoliaService = current($this->algoliaServices);
        if ($algoliaService->getTransportCategory()) {
            $algoliaQuery->getQueryParams()->addNotFilters(AlgoliaField::PRODUCT_CATEGORY . ':' . $algoliaService->getTransportCategory());
        }

        $this->limitDAPToBuyerCountry($company, $algoliaQuery);

        return array_filter(
            array_map(
                function ($rawOffer) {
                    return $this->buildOfferFromAlgoliaResult($rawOffer);
                },
                $this->getArrayOfRawOfferFromAlgolia($algoliaQuery)
            )
        );
    }

    public function findOfferByVendorReference(string $vendorReference): ?Offer
    {
        $algoliaQuery = (
        new AlgoliaQuery(
            $vendorReference,
            (new AlgoliaQueryParams())
                ->typoTolerance(false)
                ->addRestrictSearchableAttributes(
                    AlstomCustomAttributes::createFullAttributeName($this->customAttributes->getVendorReference())
                )
        )
        );

        return $this->buildOfferFromAlgoliaResult(
            $this->getRawOfferFromAlgolia($algoliaQuery)
        );
    }

    public function findOfferByManufacturerReference(string $manufacturerReference): ?Offer
    {
        $algoliaQuery = (
        new AlgoliaQuery(
            $manufacturerReference,
            (new AlgoliaQueryParams())
                ->typoTolerance(false)
                ->addRestrictSearchableAttributes(AlstomCustomAttributes::createFullAttributeName($this->customAttributes->getManufacturerReference()))
        )
        );

        return $this->buildOfferFromAlgoliaResult(
            $this->getRawOfferFromAlgolia(
                $algoliaQuery
            )
        );
    }

    /**
     * @param int          $offerId
     * @param Company|null $company
     *
     * @return DetailedOffer|null
     */
    public function findDetailedOfferById(int $offerId, Company $company = null): ?DetailedOffer
    {
        $rawOffer = $this->getRawOfferFromId($offerId);

        $this->buildBreadcrumb($rawOffer);

        $parsedOffer = $this->buildOfferFromAlgoliaResult($rawOffer, true);

        if (null === $parsedOffer) {
            return null;
        }

        $detailedOffer = new DetailedOffer();
        $detailedOffer->setOffer($parsedOffer);

        $overwriteAttribute = [];
        $hiddenAttributes = [
            $this->customAttributes->getDeliveryTime(),
            $this->customAttributes->getCountryOfDelivery(),
            $this->customAttributes->getTotalDelayForCustomer(),
            $this->customAttributes->getLastBuyOrderDate(),
        ];

        $isCompanyAllowToSeeMerchantOfferDetails = false;
        if ($company !== null) {
            $isCompanyAllowToSeeMerchantOfferDetails =
                $company->hasValidSpecificPricesForOffer($parsedOffer->getSellerRef(), $parsedOffer->getIncoterm(), $parsedOffer->getIncotermCountry()) ||
                $this->bafvService->isCompanyAllowToSeeMerchantOffersDetails(
                    $company,
                    $detailedOffer->getOffer()->getMerchant()->getId()
                );
        }

        if ($isCompanyAllowToSeeMerchantOfferDetails) {
            $detailedOffer->setOffer($this->specificPriceService->updateOfferSpecificPrices($company, $parsedOffer));
            $overwriteAttribute[$this->customAttributes->getMoq()] = $detailedOffer->getOffer()->getMoq();
        }

        if ($isCompanyAllowToSeeMerchantOfferDetails && !$detailedOffer->getOffer()->hasNoPrice()) {
            $hiddenAttributes = [];
        }

        $detailedOffer->setTechnicalAttributes(
            array_merge(
                $this->getAttributesForDetailedOffer($rawOffer, 'C'),
                $this->getAttributesForDetailedOffer($rawOffer, 'D')
            )
        );

        $shippingAttributes = [
            $this->customAttributes->getMoq(),
            $this->customAttributes->getBatchSize(),
            $this->customAttributes->getQuantityPerSku(),
            $this->customAttributes->getMadeIn(),
            $this->customAttributes->getDangerousProduct(),
            $this->customAttributes->getTransportType(),
            $this->customAttributes->getSkuUnit(),
        ];

        $leadTimeAttribute = $this->customAttributes->getTotalDelayForCustomer();

        $detailedOffer->setLogisticAttributes(
            array_filter(
                array_map(
                    function (AttributeDTO $attribute) use ($overwriteAttribute, $hiddenAttributes, $shippingAttributes, $company, $detailedOffer, $leadTimeAttribute) {
                        if (!strlen(trim($attribute->getValue()))) {
                            return null;
                        }

                        if (in_array($attribute->getKey(), $hiddenAttributes)) {
                            return null;
                        }

                        // hide shipping attributes for anonymous users
                        if (!$company && in_array($attribute->getKey(), $shippingAttributes)) {
                            return null;
                        }

                        $offer = $detailedOffer->getOffer();

                        // hide shipping attributes for no price offer
                        if ($offer->hasNoPrice() && in_array($attribute->getKey(), $shippingAttributes)) {
                            return null;
                        }

                        // hide delivery date attribute for no price offer
                        if ($offer->hasNoPrice() && $attribute->getKey() == $leadTimeAttribute) {
                            return null;
                        }

                        // use offer delivery time attribute as it can be overwritten by specific price feature
                        if ($attribute->getKey() == $leadTimeAttribute) {
                            $attribute->setValue($offer->getDeliveryTime());
                        }

                        if (array_key_exists($attribute->getKey(), $overwriteAttribute)) {
                            $attribute->setValue($overwriteAttribute[$attribute->getKey()]);
                        }

                        return $attribute;
                    },
                    $this->getAttributesForDetailedOffer($rawOffer, 'B')
                )
            )
        );

        $detailedOffer->setAssociatedServices($this->getAssociatedServices($rawOffer, $company));
        $detailedOffer->setAssociatedProducts($this->getAssociatedProducts($rawOffer, $company));

        return $detailedOffer;
    }

    /**
     * @param AlgoliaQueryParams $algoliaQueryParams
     * @param string             $search
     * @param false              $cacheResult
     * @param string             $cacheKey
     * @param int                $cacheTTL
     *
     * @return array|mixed
     */
    public function findOffers(AlgoliaQueryParams $algoliaQueryParams, $search = '', $cacheResult = false, string $cacheKey = '', int $cacheTTL = 60)
    {
        if ($cacheResult) {
            $cacheItem = $this->cache->getItem($cacheKey);
            if (null !== $cacheItem) {
                return $cacheItem;
            }
        }

        try {
            //$algoliaQueryParams->addFacets(...$this->facetsToRetrieve);
            $algoliaQueryParams->addFacets('*');

            $response = $this->algoliaServices[$this->locale]->search(
                $search,
                $algoliaQueryParams->toArray()
            );
            $result = [];
            $result[AlgoliaService::ALGOLIA_NB_HITS_PARAM] = 0;
            $result[AlgoliaService::ALGOLIA_HITS_PARAM] = [];

            foreach ($response[self::HITS] as $hit) {
                $parsedOffer = $this->buildOfferFromAlgoliaResult($hit);
                if (null !== $parsedOffer) {
                    array_push($result[AlgoliaService::ALGOLIA_HITS_PARAM], $parsedOffer);
                }
            }
            $result[AlgoliaService::ALGOLIA_NB_HITS_PARAM] = count($result[AlgoliaService::ALGOLIA_HITS_PARAM]);
            $result[AlgoliaService::ALGOLIA_NB_PAGES_PARAM] = $response[AlgoliaService::ALGOLIA_NB_PAGES_PARAM];

            // Retrieve FACETS
            $result[AlgoliaService::ALGOLIA_FACETS] = $response[AlgoliaService::ALGOLIA_FACETS];

            if ($cacheResult) {
                $this->cache->saveItem($cacheKey, $result, $cacheTTL);
            }

            return $result;
        } catch (AlgoliaException $e) {
            throw new ApiException('error while requesting algolia with following filter ' . json_encode($algoliaQueryParams->toArray()) . ':' . $e->getMessage(), 500, $e);
        }
    }

    public function getAllFacetValues($facetName): array
    {
        $response = $this->algoliaServices[$this->locale]->searchForFacetValues($facetName, '');

        $facetValues = [];
        foreach ($response[self::FACET_HITS] as $facetValue) {
            $facetValues[$facetValue['value']] = $facetValue['count'];
        }

        return $facetValues;
    }

    public function getAllAttributeValues(AlgoliaQueryParams $algoliaQueryParams, $search, $attribute)
    {
        $response = $this->algoliaServices[$this->locale]->search(
            $search,
            $algoliaQueryParams->toArray(),
            $attribute
        );
        $result = [];

        foreach ($response[self::HITS] as $hit) {
            $explodedAttribute = explode('.', $attribute);
            $temp = $hit;
            foreach ($explodedAttribute as $elem) {
                $temp = $temp[$elem];
            }

            $result[] = $temp;
        }

        return $result;
    }

    /**
     * perform a search with pagination: No cache here.
     *
     * @param AlgoliaQueryParams $algoliaQueryParams
     * @param string             $search
     * @param int                $hitsPerPage
     * @param int                $page
     * @param bool               $transformToAutocomplete
     * @param bool               $disjunctiveSearch
     *
     * @return array
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function findOffersPaginated(
        AlgoliaQueryParams $algoliaQueryParams,
        string             $search = '',
        int                $hitsPerPage = 20,
        int                $page = 0,
        bool               $transformToAutocomplete = false,
        bool               $disjunctiveSearch = false
    )
    {
        $algoliaQueryParams->addFacets('*');

        $response = $this->algoliaServices[$this->locale]->paginatedSearch(
            $search,
            $disjunctiveSearch,
            $algoliaQueryParams,
            $hitsPerPage,
            $page
        );


        $result = [];
        $result[AlgoliaService::ALGOLIA_NB_HITS_PARAM] = 0;
        $result[AlgoliaService::ALGOLIA_HITS_PARAM] = [];

        foreach ($response[self::HITS] as $hit) {
            if ($transformToAutocomplete) {
                $parsedOffer = $hit;
            } else {
                $parsedOffer = $this->buildOfferFromAlgoliaResult($hit);
            }
            if (null !== $parsedOffer) {
                array_push($result[AlgoliaService::ALGOLIA_HITS_PARAM], $parsedOffer);
            }
        }

        $result[AlgoliaService::ALGOLIA_NB_HITS_PARAM] = $response[AlgoliaService::ALGOLIA_NB_HITS_PARAM];
        $result[AlgoliaService::ALGOLIA_NB_PAGES_PARAM] = $response[AlgoliaService::ALGOLIA_NB_PAGES_PARAM] ?? 0;

        // Retrieve FACETS
        $result[AlgoliaService::ALGOLIA_FACETS] = $response[AlgoliaService::ALGOLIA_FACETS];

        // Log user search in DB
        // Not blank searches
        if ('' !== $search) {
            $this->searchHistorizationService->trace($search, json_encode($algoliaQueryParams->toArray()), $response['nbHits']);
        }

        return $result;
    }

    public function getRawOfferFromId(int $offerId): ?array
    {
        return $this->getRawOfferFromAlgolia(
            new AlgoliaQuery(
                '',
                (new AlgoliaQueryParams())
                    ->addNumericFilters(sprintf('id=%d', $offerId))
            )
        );
    }

    public function getBreadcrumb(): ?array
    {
        return $this->breadcrumb;
    }

    public function getAlgoliaBrowseIterator($locale, AlgoliaQueryParams $algoliaQueryParams)
    {
        return $this->algoliaServices[$locale]->browse('', $algoliaQueryParams->toArray());
    }

    public function getAllLocale()
    {
        return array_keys($this->algoliaServices);
    }

    private function getRawOfferFromAlgolia(AlgoliaQuery $algoliaQuery): ?array
    {
        $result = $this->algoliaServices[$this->locale]->search(
            $algoliaQuery->getSearch(),
            $algoliaQuery->getQueryParams()->toArray()
        );
        if (!$result[self::NB_HITS]) {
            return null;
        }

        return $result['hits'][0];
    }

    private function getArrayOfRawOfferFromAlgolia(AlgoliaQuery $algoliaQuery): ?array
    {
        $result = $this->algoliaServices[$this->locale]->search(
            $algoliaQuery->getSearch(),
            $algoliaQuery->getQueryParams()->toArray()
        );

        return $result['hits'];
    }

    /**
     * @param array $offer the offer as returned by algolia
     *
     * @return Offer|null the offer in the application model
     */
    public function buildOfferFromAlgoliaResult(?array $offer, bool $checkPdf = false): ?Offer
    {
        if (null === $offer) {
            return null;
        }

        $noPrice = false;

        $price = $offer['price'];
        if (empty($price)) {
            $noPrice = true;
        }

        try {
            $prices = [];
            switch (strtoupper(trim($offer['currency']))) {
                case BCEService::CURR_EUR:
                    $prices = ['EUR' => $offer['price'], 'USD' => $this->BCEService->fromEURToUSD($offer['price'])];
                    break;
                case BCEService::CURR_USD:
                    $prices = ['EUR' => $this->BCEService->fromUSDToEUR($offer['price']), 'USD' => $offer['price']];
                    break;
                default:
                    break;
            }

            $attributes = $offer['attributes'] ?? [];
            $manufacturerRef = $attributes[$this->customAttributes->getManufacturerReference()] ?? '';
            $sellerRef = $attributes[$this->customAttributes->getVendorReference()] ?? '';
            $moq = $attributes[$this->customAttributes->getMoq()] ?? 0;
            $taxGroup = $attributes[$this->customAttributes->getTaxGroup()] ?? null;

            $madeIn = $attributes[$this->customAttributes->getMadeIn()] ?? '';

            $isRestrictedProduct = false;
            if (array_key_exists($this->customAttributes->getRestrictedProductBoolean(), $attributes)) {
                $isRestrictedProduct = $attributes[$this->customAttributes->getRestrictedProductBoolean()] === 'Yes';
            }
            $restrictedProductIdent = [];
            for ($i = 1; $i <= $this->customAttributes->getRestrictedProductFieldsCount(); $i++) {
                $attributeSearch = str_replace(':i:', strval($i), $this->customAttributes->getRestrictedProductField());
                if (array_key_exists($attributeSearch, $attributes)) {
                    array_push($restrictedProductIdent, $attributes[$attributeSearch]);
                }
            }

            $product = $offer['product'] ?? [];
            $manufacturer = $product['manufacturer'] ?? '';
            $productId = $product['id'] ?? '';

            $offerDescription = $offer['description'] ?? '';

            $offerName = '';

            if (array_key_exists('product', $offer) && array_key_exists('name', $offer['product'])) {
                $offerName = $offer['product']['name'];
            }

            if (array_key_exists('name', $offer)) {
                $offerName = $offer['name'];
            }

            //////////////////////////////////
            //pictures
            /////////////////////////////////
            $assignedImages = $offer['assigned_images'] ?? [];

            $imagePlaceHolder = 'images/no_image_available.svg';
            $isClearanceCaterory = false;

            if (in_array($this->clearanceCategory, $offer['product']['application_categories'])) {
                $isClearanceCaterory = true;
                $imagePlaceHolder = 'images/stock_clearance.jpg';
            }

            $pictures = array_merge(
                [$offer['default_image'] && strlen(trim($offer['default_image'])) > 0 ? $offer['default_image'] : $imagePlaceHolder],
                array_map(function ($picture) {
                    return $picture['image_path'];
                }, $assignedImages)
            );

            ///////////////////////////////////////
            //we don't want duplicate images
            $pictures = array_unique($pictures);

            //incoterm
            $incoterm = $attributes[$this->customAttributes->getIncoTerm()] ?? '';

            //country
            $country = $attributes[$this->customAttributes->getCountryOfDelivery()] ?? '';
            if (is_array($country)) {
                $country = $country[0];
            }

            //thresholds
            $thresholds = [];
            $thresholdAttributes = [
                $this->customAttributes->getThreshold1() => $this->customAttributes->getThreshold1Price(),
                $this->customAttributes->getThreshold2() => $this->customAttributes->getThreshold2Price(),
                $this->customAttributes->getThreshold3() => $this->customAttributes->getThreshold3Price(),
                $this->customAttributes->getThreshold4() => $this->customAttributes->getThreshold4Price(),
            ];

            foreach ($thresholdAttributes as $thresholdAttribute => $thresholdPriceAttribute) {
                $threshold = $attributes[$thresholdAttribute] ?? null;
                $thresholdPrice = $attributes[$thresholdPriceAttribute] ?? null;

                if ($threshold === null || $thresholdPrice === null) {
                    break;
                }

                $thresholds[$threshold] = $thresholdPrice;
            }

            //batch_size
            $batchSize = $attributes[$this->customAttributes->getBatchSize()] ?? null;

            //total delay for customer
            $deliveryTime = $attributes[$this->customAttributes->getTotalDelayForCustomer()] ?? null;
            $deliveryTimeDelayBeforeShiping = $attributes[$this->customAttributes->getDelayBeforeShipping()] ?? null;

            //delivery time
            $trueDeliveryTime = $attributes[$this->customAttributes->getDeliveryTime()] ?? null;

            //merchant
            $offerMerchant = $offer[self::MERCHANT] ?? [];
            $merchant = new Merchant();
            $merchant->setId($offerMerchant['id'] ?? 0);
            $merchant->setName($offerMerchant['name'] ?? '');
            $merchant->setShortDescription($offerMerchant['description'] ?? '');
            $merchant->setLongDescription($offerMerchant['long_description'] ?? '');
            $merchant->setTacLink($offerMerchant['tac'] ?? '');
            $merchant->setStatus($offerMerchant['status'] ?? '');

            $merchant->setCountry($this->merchantService->fetchCountry($offerMerchant['id']));

            $merchant->setUpelaActive($this->merchantService->merchantIsUpelaActive($offerMerchant['id']));

            //data sheet
            $dataSheet = $attributes[$this->customAttributes->getDataSheet()] ?? null;
            if ($dataSheet !== null && $checkPdf) {
                if (!$this->isPdf($dataSheet)) {
                    $dataSheet = null;
                }
            }

            //quantityPerSku
            $quantityPerSku = $attributes[$this->customAttributes->getQuantityPerSku()] ?? null;

            //skuUnit
            $skuUnit = $attributes[$this->customAttributes->getSkuUnit()] ?? null;

            $convertAttributeValueToBool = function ($attributeValue): bool {
                if (is_bool($attributeValue)) {
                    return $attributeValue;
                }

                if (is_int($attributeValue)) {
                    return (bool)$attributeValue;
                }

                if (strtolower(trim($attributeValue)) === 'yes') {
                    return true;
                }
                return strtolower(trim($attributeValue)) !== 'no';
            };

            $dangerousProduct = call_user_func($convertAttributeValueToBool, $attributes[$this->customAttributes->isDangerousProduct()] ?? null);
            $packageWidthMoq = $attributes[$this->customAttributes->getPackageWidthMoq()] ?? null;
            $packageHeightMoq = $attributes[$this->customAttributes->getPackageHeightMoq()] ?? null;
            $packageLengthMoq = $attributes[$this->customAttributes->getPackageLengthMoq()] ?? null;
            $packageWeightMoq = $attributes[$this->customAttributes->getPackageWeightMoq()] ?? null;

            // Need to convert all dimension in cm and kg
            $extractConvertedDimension = function (?array $dimension) {
                if (!$dimension) {
                    return null;
                }

                $lenghtUnit = [
                    'mm' => 1, // was 'mm' => 0.1, but unit is not considered for the station one catalogue. everything is in cm
                    'cm' => 1,
                    'm' => 1, // was 'm' => 100, but unit is not considered for the station one catalogue. everything is in cm
                ];

                $weightUnit = [
                    'mg' => 1, // was 'mg' => 0.000001, but unit is not considered for the station one catalogue. everything is in kg
                    'g' => 1, // was 'g' => 0.001, but unit is not considered for the station one catalogue. everything is in kg
                    'kg' => 1,
                ];

                $number = current($dimension);
                $unit = key($dimension);

                if (in_array($unit, array_keys($lenghtUnit))) {
                    return $number * $lenghtUnit[$unit];
                }

                if (in_array($unit, array_keys($weightUnit))) {
                    return $number * $weightUnit[$unit];
                }

                throw new \InvalidArgumentException($number);
            };

            $packageWidth = call_user_func($extractConvertedDimension, $offer['product']['package_width'] ?? null);
            $packageHeight = call_user_func($extractConvertedDimension, $offer['product']['package_height'] ?? null);
            $packageLength = call_user_func($extractConvertedDimension, $offer['product']['package_length'] ?? null);
            $packageWeight = call_user_func($extractConvertedDimension, $offer['product']['package_weight'] ?? null);

            $fcaAddress = $attributes[$this->customAttributes->getFcaAddress()] ?? null;
            $fcaZipCode = $attributes[$this->customAttributes->getFcaZipCode()] ?? null;
            $fcaZipTown = $attributes[$this->customAttributes->getFcaZipTown()] ?? null;

            $transportType = $attributes[$this->customAttributes->getTransportType()] ?? null;
            $stackability = call_user_func($convertAttributeValueToBool, $attributes[$this->customAttributes->getStackability()] ?? null);
            $customTariffCode = $attributes[$this->customAttributes->getCustomTariffCode()] ?? null;

            $stockAvailability = $attributes[$this->customAttributes->getStockAvailability()] ?? null;
            $realStock = $attributes[$this->customAttributes->getRealStock()] ?? null;
            $warrantyPeriod = $attributes[$this->customAttributes->getWarrantyPeriod()] ?? null;
            $priceValidityDate = $attributes[$this->customAttributes->getPriceValidityDate()] ?? null;
            if ($priceValidityDate !== null) {
                $priceValidityDate = \DateTimeImmutable::createFromFormat('Y-m-d', $priceValidityDate);
                $now = new \DateTimeImmutable('now');
                if ($priceValidityDate->format('Y-m-d') < $now->format('Y-m-d')) {
                    $noPrice = true;
                }
            }

            $convertAttributeValueToBool = function ($attributeValue): bool {
                if (is_bool($attributeValue)) {
                    return $attributeValue;
                }

                if (is_int($attributeValue)) {
                    return (bool)$attributeValue;
                }

                if (strtolower(trim($attributeValue)) === 'yes') {
                    return true;
                }
                return strtolower(trim($attributeValue)) !== 'no';
            };

            $stockManagement = call_user_func($convertAttributeValueToBool, $attributes[$this->customAttributes->getStockManagement()] ?? false);

            $minimumOrderAmount = $attributes[$this->customAttributes->getMinimumOrderAmount()] ?? null;

            $offerModel = (new Offer())
                ->setMerchant($merchant)
                ->setStatus($offer['status'])
                ->setOfferPictures($pictures)
                ->setIzbergReference((string)($offer['id']))
                ->setBuyerRef(null)
                ->setSellerRef($sellerRef)
                ->setManufacturerRef($manufacturerRef)
                ->setManufacturerName($manufacturer)
                ->setOfferTitle($offerName)
                ->setShortDescription($offerDescription)
                ->setCurrency($offer['currency'])
                ->setPrices($prices)
                ->setMoq($moq)
                ->setIncoterm($incoterm)
                ->setIncotermCountry($country)
                ->setQuantity($offer['stock'])
                ->setThresholds($thresholds)
                ->setBatchSize($batchSize)
                ->setProductId($productId)
                ->setDeliveryTime($deliveryTime)
                ->setDeliveryTimeDelayBeforeShipping($deliveryTimeDelayBeforeShiping)
                ->setDataSheet($dataSheet)
                ->setQuantityPerSku($quantityPerSku)
                ->setSkuUnit($skuUnit)
                ->setFcaAddress($fcaAddress)
                ->setFcaZipCode($fcaZipCode)
                ->setFcaZipTown($fcaZipTown)
                ->setTransportType($transportType)
                ->setTrueDeliveryTime($trueDeliveryTime)
                ->setStockAvailability($stockAvailability)
                ->setStockManagement($stockManagement)
                ->setTaxGroup($taxGroup)
                ->setDangerousProduct($dangerousProduct)
                ->setPackageWidth($packageWidth)
                ->setPackageHeight($packageHeight)
                ->setPackageLength($packageLength)
                ->setPackageWeight($packageWeight)
                ->setPackageWidthMoq($packageWidthMoq)
                ->setPackageHeightMoq($packageHeightMoq)
                ->setPackageLengthMoq($packageLengthMoq)
                ->setPackageWeightMoq($packageWeightMoq)
                ->setStackability($stackability)
                ->setCustomTariffCode($customTariffCode)
                ->setUser($this->user)
                ->setIsOfferRestricted($isRestrictedProduct)
                ->setRestrictedProductBuyerIdent($restrictedProductIdent)
                ->setNoPrice($noPrice)
                ->setStockClearance($isClearanceCaterory)
                ->setMinimumOrderAmount($minimumOrderAmount)
                ->setMadeIn($madeIn)
                ->setPriceValidityDate($priceValidityDate)
                ->setRealStock($realStock)
                ->setWarrantyPeriod($warrantyPeriod)
            ;

            return $offerModel;

        } catch (\Exception $e) {
            $offerId = '?';
            if (array_key_exists('id', $offer)) {
                $offerId = $offer['id'];
            }
            $this->logger->error('unable to parse offer from IZBERG: ' . $e->getMessage(),
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::IZBERG_OFFER_PARSE_ERROR,
                    'offerId' => $offerId
                ])
            );

            return null;
        }
    }

    /**
     * @param $algoliaOffer
     * @param $keysAsString
     *
     * @return mixed
     */
    public function getValueFromAlgoliaOffer($algoliaOffer, $keysAsString)
    {
        $keys = explode('.', $keysAsString);

        try {
            $result = $algoliaOffer[$keys[0]];
            if (count($keys) > 1) {
                array_shift($keys);
                foreach ($keys as $key) {
                    $result = $result[$key];
                }
            }

            return $result;
        } catch (\Exception $e) {
            return null;
        }
    }

    public function setLocale($locale)
    {
        $this->locale = $locale;
    }

    public function findCatalogReferences(int $offerId): array
    {
        $rawOffer = $this->getRawOfferFromId($offerId);

        $attributes = $rawOffer['attributes'] ?? [];

        return array_filter(array_merge(
            [
                $attributes[$this->customAttributes->getManufacturerReference()] ?? null,
                $attributes[$this->customAttributes->getVendorReference()] ?? null,
            ],
            array_map(
                function ($compatibleAttributes) use ($attributes) {
                    return $attributes[$compatibleAttributes] ?? null;
                },
                $this->customAttributes->getSupplierProductCompatibilities()
            )
        ));
    }

    /**
     * check if a file provided by an URL is a PDF or not.
     *
     * @param string $url
     *
     * @return bool
     */
    public static function isPdf(string $url): bool
    {
        try {
            foreach (get_headers($url) as $header) {
                if (str_contains($header, "Content-Type: application/pdf")) {
                    return true;
                }
            }
        } catch (\Exception $e) {
            return false;
        }
        return false;
    }

    private function getNamefromHit($hit)
    {
        if (array_key_exists('name', $hit)) {
            return $hit['name'];
        }
        if (array_key_exists('product', $hit) && array_key_exists('name', $hit['product'])) {
            return $hit['product']['name'];
        }

        return '';
    }

    public function getAssociatedProducts(array $rawOffer, ?Company $company): array
    {
        return $this->fetchProductsByReferenceAttribute($this->customAttributes->getAssociatedProducts(), $rawOffer, $company);
    }

    public function getAssociatedServices(array $rawOffer, ?Company $company): array
    {
        return $this->fetchProductsByReferenceAttribute($this->customAttributes->getAssociatedServices(), $rawOffer, $company);
    }

    private function fetchProductsByReferenceAttribute(array $referenceAttributes, array $rawOffer, ?Company $company): array
    {
        $offerAttributes = $rawOffer['attributes'];

        $relatedProductReferenceKeys = array_filter(
            array_keys($offerAttributes),
            function ($attribute) use ($referenceAttributes) {
                return in_array($attribute, $referenceAttributes);
            }
        );

        $productReferences = array_filter(
            array_intersect_key(
                $offerAttributes,
                array_combine($relatedProductReferenceKeys, $relatedProductReferenceKeys)
            )
        );

        return array_filter(
            $this->findOffersByVendorOrManufacturerReferences($productReferences, $company),
            function (Offer $offer) use ($rawOffer) {
                return ($offer->getMerchant()->getId() === $rawOffer['merchant']['id']);
            }
        );
    }

    /**
     * @param array  $rawOffer rawOffer from algolia
     * @param string $prefix   prefix to search in attributes
     *
     * @return array the attributes and their values
     */
    private function getAttributesForDetailedOffer(array $rawOffer, string $prefix): array
    {
        $result = [];
        if (array_key_exists('attributes', $rawOffer)) {
            $attributes = $rawOffer['attributes'];
            ksort($attributes);
            foreach ($attributes as $attribute => $value) {
                if (!in_array($attribute, $this->ignoredAttributes)) {
                    if (substr($attribute, 0, 1) === $prefix) {
                        $attrDTO = $this->attributeService->getAttribute($attribute, $this->locale);
                        if (is_array($value) && !empty($value)) {
                            $attrDTO->setValue($value[0]);
                        } else {
                            $attrDTO->setValue((string)$value);
                        }
                        $result[] = $attrDTO;
                    }
                }
            }
        }
        return $result;
    }

    private function limitDAPToBuyerCountry(?Company $company, AlgoliaQuery $query)
    {
        $country = 'france';
        if ($company) {
            $mainAddressCountry = $company->getMainAddress()->getCountry();
            if($mainAddressCountry) {
                $country = $company->getMainAddress()->getCountry()->getCode();
            }
        }

        $orClauseFacetFilter = [
            sprintf(
                '%s:%s',
                AlstomCustomAttributes::createFullAttributeName($this->alstomCustomAttributes->getIncoTerm()),
                '-DAP'
            ),
            sprintf(
                '%s:%s',
                AlstomCustomAttributes::createFullAttributeName($this->alstomCustomAttributes->getCountryOfDelivery()),
                strtolower($country)
            ),
        ];
        $query->getQueryParams()->addFacetFilters($orClauseFacetFilter);
    }

    private function buildBreadcrumb($rawOffer)
    {
        $categoryName = [];

        if (isset($rawOffer['_highlightResult']['product']['application_categories_dict'])) {
            foreach ($rawOffer['_highlightResult']['product']['application_categories_dict'] as $item) {
                $categoryName[] = $item['name']['value'];
            }
        }

        if (isset($rawOffer['product']['application_categories'])) {
            foreach ($rawOffer['product']['application_categories'] as $key => $category) {
                $this->breadcrumb[$category] = $categoryName[$key];
            }
        }
    }

    public function getParcels(Offer $offer, int $quantity): ?array
    {
        $parcels = null;

        if ($offer->isDangerousProduct()) return null;

        return $this->computeParcel($offer, $quantity);
    }

    private function computeParcel(Offer $offer, int $quantity): ?array
    {
        $parcels = [];
        if (!empty($offer->getPackageWeight()) &&
            !empty($offer->getPackageLength()) &&
            !empty($offer->getPackageHeight()) &&
            !empty($offer->getPackageWidth()) &&
            !empty($offer->getPackageWeightMoq()) &&
            !empty($offer->getPackageLengthMoq()) &&
            !empty($offer->getPackageHeightMoq()) &&
            !empty($offer->getPackageWidthMoq()) &&
            !empty($offer->getBatchSize())
        ) {

            $mobQuantity = intdiv($quantity, $offer->getMoq());

            if ($mobQuantity > 0) {
                $parcels[] = $this->buildParcelArrayStructure(
                    $mobQuantity,
                    $offer->getPackageWeightMoq(),
                    $offer->getPackageWidthMoq(),
                    $offer->getPackageHeightMoq(),
                    $offer->getPackageLengthMoq()
                );
            }

            $batchSizeQuantity = ($quantity % $offer->getMoq());

            if ($batchSizeQuantity > 0) {
                $parcels[] = $this->buildParcelArrayStructure(
                    $batchSizeQuantity,
                    $offer->getPackageWeight(),
                    $offer->getPackageWidth(),
                    $offer->getPackageHeight(),
                    $offer->getPackageLength()
                );
            }

        } elseif (!empty($offer->getPackageWeight()) &&
            !empty($offer->getPackageLength()) &&
            !empty($offer->getPackageHeight()) &&
            !empty($offer->getPackageWidth()) &&
            !empty($offer->getBatchSize())
        ) {
            $mobQuantity = intdiv($quantity, $offer->getMoq());
            $batchSizeQuantity = ($quantity % $offer->getMoq());

            if ($batchSizeQuantity > 0) {
                $parcels[] = $this->buildParcelArrayStructure(
                    $batchSizeQuantity,
                    $offer->getPackageWeight(),
                    $offer->getPackageWidth(),
                    $offer->getPackageHeight(),
                    $offer->getPackageLength()
                );
            }
            if ($mobQuantity > 0) {
                $parcels[] = $this->buildParcelArrayStructure(
                    $mobQuantity,
                    $offer->getPackageWeight(),
                    $offer->getPackageWidth(),
                    $offer->getPackageHeight(),
                    $offer->getPackageLength()
                );
            }
        }

        return $parcels;
    }

    private function buildParcelArrayStructure(int $number, $weight, $width, $height, $length): array
    {
        return [
            'number' => $number,
            'weight' => $weight,
            "x" => $width,
            "y" => $height,
            "z" => $length
        ];
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

}
