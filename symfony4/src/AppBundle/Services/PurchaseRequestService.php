<?php

namespace AppBundle\Services;

use AppBundle\Command\CsvToArrayService;
use AppBundle\Entity\BddFile;
use AppBundle\Entity\PurchaseRequest;
use AppBundle\Entity\PurchaseRequestItem;
use AppBundle\Entity\SearchResult;
use AppBundle\Entity\User;
use AppBundle\Exception\PurchaseRequestException;
use AppBundle\Model\Offer;
use AppBundle\Repository\PurchaseRequestRepository;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\ORMException;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class PurchaseRequestService
{
    private PurchaseRequestRepository $purchaseRequestRepository;
    private EntityManagerInterface $em;
    private SearchService $searchService;
    private CartService $cartService;
    private OfferService $offerService;
    private CompanyCatalogService $companyCatalogService;
    private ShippingService $shippingService;
    private CsvToArrayService $csvToArrayService;
    private SpecificPriceService $specificPriceService;
    private BafvServiceInterface $bafvService;

    /**
     * @var string[]
     */
    private $validKeys = [
        'buyerReference' => 'buyer reference number requested',
        'manufacturerReference' => 'manufacturer reference number requested',
        'manufacturerName' => 'manufacturer name',
        'productName' => 'product name',
        'quantityExpected' => 'qty expected',
        'unitPriceOfReference' => 'unit price of reference',
        'currencyOfReference' => 'currency of reference',
        'expectedDeliveryDate' => 'expected delivery date',
        'costCenter' => 'cost center',
        'purchaseRequestNumber' => 'purchase request number',
        'buyerOrderNumber' => 'buyer order number',
        'orderLine' => 'order line',
    ];

    /**
     * @var string[]
     */
    private $foundProductExport = [
        'buyerReference' => 'Buyer reference number requested',
        'manufacturerReference' => 'Manufacturer reference number requested',
        'quantityExpected' => 'Quantity expected',
        'sellerRef' => 'Vendor reference',
        'price' => 'Price',
        'quantityPerSku' => 'Quantity within SKU',
        'vendorName' => 'Vendor name',
        'productName' => 'Product name',
        'incoterm' => 'Incoterm',
        'moq' => 'MOQ',
        'deliveryTime' => 'Delivery time',
        'shippable' => 'Transport offer available',
        'frameContract' => 'Contract with supplier'
    ];

    public function __construct(
        PurchaseRequestRepository $purchaseRequestRepository,
        EntityManagerInterface $em,
        SearchService $searchService,
        CartService $cartService,
        OfferService $offerService,
        CompanyCatalogService $companyCatalogService,
        ShippingService $shippingService,
        CsvToArrayService $csvToArrayService,
        SpecificPriceService $specificPriceService,
        BafvService $bafvService
    )
    {
        $this->purchaseRequestRepository = $purchaseRequestRepository;
        $this->em = $em;
        $this->searchService = $searchService;
        $this->cartService = $cartService;
        $this->offerService = $offerService;
        $this->companyCatalogService = $companyCatalogService;
        $this->shippingService = $shippingService;
        $this->csvToArrayService = $csvToArrayService;
        $this->specificPriceService = $specificPriceService;
        $this->bafvService = $bafvService;
    }

    /**
     * @param User $user
     * @param UploadedFile $file
     * @return PurchaseRequest
     * @throws PurchaseRequestException
     */
    public function uploadPurchaseRequestFromCsv(User $user, UploadedFile $file): PurchaseRequest
    {
        $data = $this->csvToArrayService->convert($file->getRealPath(), PurchaseRequest::CSV_COLUMN_PER_ROW);
        $dataIsValid = function(array $data): bool {

            $first = $data[0] ?? [];
            $firstKeys = array_keys($first);
            $firstKeys = array_map(function(string $key) {return trim(strtolower($key));}, $firstKeys);

            return (join("-", $firstKeys) === join("-", $this->validKeys));
        };

        if (!$data || !$dataIsValid($data)) {
           throw new PurchaseRequestException('imported data empty or invalid');
        }

        $previousPurchaseRequests = $this->purchaseRequestRepository->findBy([
            'user' => $user,
            'status' => PurchaseRequest::STATUS_INITIALISED
        ]);

        foreach($previousPurchaseRequests as $previousPurchaseRequest) {
            $previousPurchaseRequest->setStatus(PurchaseRequest::STATUS_ARCHIVED);
            $this->em->persist($previousPurchaseRequest);
        }

        $bddFile = (new BddFile())
            ->setName($file->getClientOriginalName())
            ->setData(file_get_contents($file->getRealPath()))
            ->setSize($file->getSize())
            ->setType($file->getMimeType())
            ->setCreatedAt(new \DateTime());

        $purchaseRequest = (new PurchaseRequest())
            ->setUser($user)
            ->setStatus(PurchaseRequest::STATUS_INITIALISED)
            ->setImportFile($bddFile)
            ->setCreatedAt(new \DateTime());

        $this->em->persist($purchaseRequest);
        $this->em->flush();

        foreach($data as $item) {
            [
                $buyerReference,
                $manufacturerReference,
                $manufacturerName,
                $productName,
                $quantityExpected,
                $unitPriceOfReference,
                $currencyOfReference,
                $expectedDeliveryDate,
                $costCenter,
                $purchaseRequestNumber,
                $buyerOrderNumber,
                $orderLine,
            ] = array_values($item);

            $item = (new PurchaseRequestItem())
                ->setBuyerReference($buyerReference)
                ->setManufacturerReference($manufacturerReference)
                ->setManufacturerName($manufacturerName)
                ->setProductName($productName)
                ->setQuantityExpected((int)$quantityExpected)
                ->setUnitPriceOfReference($unitPriceOfReference)
                ->setCurrencyOfReference($currencyOfReference)
                ->setExpectedDeliveryDate($expectedDeliveryDate)
                ->setCostCenter($costCenter)
                ->setPurchaseRequestNumber($purchaseRequestNumber)
                ->setBuyerOrderNumber($buyerOrderNumber)
                ->setOrderLine($orderLine)
                ->setPurchaseRequest($purchaseRequest);

            $purchaseRequest->addItem($item);
            $this->em->persist($item);
        }

        $this->em->flush();

        return $purchaseRequest;
    }

    public function clearOnGoingUserPurchaseRequest(User $user)
    {
        $previousPurchaseRequests = $this->purchaseRequestRepository->findBy([
            'user' => $user,
            'status' => PurchaseRequest::STATUS_INITIALISED
        ]);

        foreach($previousPurchaseRequests as $previousPurchaseRequest) {
            $previousPurchaseRequest->setStatus(PurchaseRequest::STATUS_ARCHIVED);
            $this->em->persist($previousPurchaseRequest);
        }

        $this->em->flush();
    }

    /**
     * @param PurchaseRequestItem $purchaseRequestItem
     * @param string $locale
     * @param int $page
     * @return SearchResult
     */
    public function searchOffers(PurchaseRequestItem $purchaseRequestItem, string $locale, int $page = 1): SearchResult
    {
        $user = $purchaseRequestItem->getPurchaseRequest()->getUser();
        $hitsPerPage = 999;

        $searchResult = $this->searchService->searchByBuyerAndManufacturerReference(
            $user,
            $purchaseRequestItem->getBuyerReference(),
            $purchaseRequestItem->getManufacturerReference(),
            $hitsPerPage,
            $page - 1, // algolia will consider page 1 to be 0
            $locale
        );

        $offers = $searchResult->getOffers();
        $offers = $this->specificPriceService->bulkUpdateOfferSpecificPrices($user->getCompany(), $offers);

        $offers = array_map(
            fn(Offer $offer) =>
                $offer->setBafvAuthorized(
                    $this->bafvService->isCompanyAllowToSeeMerchantOffersDetails(
                        $user->getCompany(),
                        $offer->getMerchant()->getId()
                    )
                ),
            $offers
        );

        $searchResult->setOffers($offers);
        return $searchResult;
    }

    /**
     * @param User $user
     * @param PurchaseRequestItem $purchaseRequestItem
     * @param int $offerId
     * @param int $quantity
     * @throws PurchaseRequestException
     * @throws \Doctrine\ORM\NonUniqueResultException
     * @throws ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function addToUserCart(User $user, PurchaseRequestItem $purchaseRequestItem, int $offerId, int $quantity)
    {
        $purchaseRequestItem->setOfferId($offerId);
        $purchaseRequestItem->setQuantity($quantity);

        $this->em->persist($purchaseRequestItem);
        $this->em->flush();

        $company = $user->getCompany();

        if (empty($offerId)) {
            throw new PurchaseRequestException('OfferId must be selected');
        }

        $offer = $this->offerService->findDetailedOfferById($offerId, $company);

        if (!$offer) {
            throw new PurchaseRequestException('offer cannot be found in catalogue');
        }

        if ($offer->getOffer()->isLimited()) {
            throw new PurchaseRequestException('offer cannot be added to cart');
        }

        if (!($quantity >= 1)) {
            throw new PurchaseRequestException('quantity must be >= 1');
        }

        if ($offer->getOffer()->getBatchSize() != null && $quantity % $offer->getOffer()->getBatchSize() > 0) {
            throw new PurchaseRequestException('quantity does not match the batchsize');
        }

        $buyerCatalogRef = $this->companyCatalogService->findBuyerReference(
            $user,
            $this->offerService->findCatalogReferences($offerId)
        );

        $purchaseRequestId = $purchaseRequestItem->getPurchaseRequestNumber();
        $buyerInternalRef = $purchaseRequestItem->getBuyerReference() ?? $buyerCatalogRef;
        $orderLine = $purchaseRequestItem->getOrderLine();

        $cartId = $this->cartService->addOfferToCart(
            $offer->getOffer(),
            $user,
            $quantity,
            null,
            false,
            [
                'Buyer-internal-ref' => $buyerInternalRef,
                'Purchase-request-id' => $purchaseRequestId,
                'order-line' => $orderLine,
            ]
        );

        $purchaseRequestItem->setCartId($cartId);
        $this->cartService->updateCartBuyerOrderId($user, $cartId, $purchaseRequestItem->getBuyerOrderNumber());

        $this->em->persist($purchaseRequestItem);
        $this->em->flush();

        $this->shippingService->deleteCache(
            $user->getId(),
            $cartId,
            $offer->getOffer()->getMerchant()->getId()
        );
    }

    /**
     * @param User $user
     * @param PurchaseRequestItem $purchaseRequestItem
     */
    public function removeFromUserCart(User $user, PurchaseRequestItem $purchaseRequestItem)
    {
        $offerId = $purchaseRequestItem->getOfferId();
        $cartId = $purchaseRequestItem->getCartId();

        if($offerId && $cartId) {
            $userCartIds = array_filter([
                $user->getCartEURId(),
                $user->getCartUSDId()
            ]);

            // check if cartId is in current user carts
            if (in_array($cartId, $userCartIds)) {
                $this->cartService->removeOfferFromCart($user, $offerId, $cartId);
            }
        }

        $purchaseRequestItem->setCartId(null);
        $purchaseRequestItem->setOfferId(null);
        $purchaseRequestItem->setQuantity(null);

        $this->em->persist($purchaseRequestItem);
        $this->em->flush();
    }

    public function removeItem(User $user, PurchaseRequestItem $purchaseRequestItem)
    {
        $this->removeFromUserCart($user, $purchaseRequestItem);

        $this->em->remove($purchaseRequestItem);
        $this->em->flush();
    }

    public function csvMappingGenerator(): array {
        return $this->validKeys;
    }

    public function foundCsvMappingGenerator(): array {
        return $this->foundProductExport;
    }
}
