<?php

namespace AppBundle\Services;

use AppB<PERSON>le\Controller\MkoController;
use AppBundle\Entity\BafvRequest;
use AppBundle\Entity\Category;
use AppBundle\Entity\Company;
use AppBundle\Entity\User;
use AppBundle\Exception\BafvException;
use AppBundle\Repository\BafvRequestRepository;
use AppBundle\Repository\UserBafvMerchantListRepository;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class BafvService implements BafvServiceInterface
{
    /**
     * @var UserBafvMerchantListRepository
     */
    private $userBafvMerchantListRepository;

    /**
     * @var UserBddService
     */
    private $userService;

    /**
     * @var BafvRequestRepository
     */
    private $bafvRequestRepository;

    /**
     * @var TranslatorInterface
     */
    private $translator;

    public function __construct(UserBafvMerchantListRepository $userBafvMerchantListRepository, UserBddService $userService, BafvRequestRepository $bafvRequestRepository, TranslatorInterface $translator)
    {
        $this->userBafvMerchantListRepository = $userBafvMerchantListRepository;
        $this->userService = $userService;
        $this->bafvRequestRepository = $bafvRequestRepository;
        $this->translator = $translator;
    }

    public function isCompanyAllowToSeeMerchantOffersDetails(Company $company, int $merchantId): bool
    {
        if ($company->getCategory()->getLabel() !== Category::CATEGORY_BAFV) {
            return true;
        }

        $userBafvMerchantEntity = $this->userBafvMerchantListRepository->findOneBy(['company' => $company, 'merchantId' => $merchantId]);
        return (bool) $userBafvMerchantEntity;
    }

    /**
     * @param Company $company
     * @return array
     * @throws BafvException
     */
    public function getMerchantIdsAllowedByBafvCompany(Company $company): array
    {
        if ($company->getCategory()->getLabel() !== Category::CATEGORY_BAFV) {
            throw new BafvException(sprintf('company with id %s is not a bafv company', $company->getId()));
        }

        $list = $this->userBafvMerchantListRepository->findBy(['company' => $company]);
        $ids = [];

        foreach ($list as $item) {
            $ids[] = $item->getMerchantId();
        }

        return $ids;
    }

    /**
     * @param User $user
     * @return bool
     */
    public function isUserABafv(User $user): bool
    {
        return ($user->getCompany() && $user->getCompany()->getCategory()->getLabel() === Category::CATEGORY_BAFV);
    }

    public function fetchBafvRequestDetails(?UserInterface $user, array $offers): ?array
    {
        $bafv = [];

        if (!$user instanceof User) {
            return $bafv;
        }

        $company = $user->getCompany();
        if(!$company instanceof Company){
            return $bafv;
        }
        $companyIsBafv = ($company->getCategory()->getLabel() === Category::CATEGORY_BAFV);

        if (!$companyIsBafv) {
            return $bafv;
        }

        $bafv['merchant'] = [];

        foreach ($offers as $offer) {
            $merchantId = $offer->getMerchant()->getId();

            $bafv['showBtn'][$merchantId] = true;
            $bafv['label'][$merchantId] = $this->translator->trans('offer_detail.ask_vendor', array(), MkoController::TRANSLATION_DOMAIN);

            $bafvRequest = $this->bafvRequestRepository->findOneBy(['company' => $company, 'merchantId' => $merchantId]);

            if($bafvRequest !== null) {
                $bafv['merchant'][] = $offer->getMerchant()->getId();
                if($bafvRequest->getStatus() === BafvRequest::STATUS_PENDING){
                    $bafv['label'][$merchantId] =   $this->translator->trans('offer_detail.ask_vendor_pending', array(), self::TRANSLATION_DOMAIN);
                }elseif($bafvRequest->getStatus() === BafvRequest::STATUS_REJECT){
                    $bafv['label'][$merchantId] =   $this->translator->trans('offer_detail.ask_vendor_rejeted', array(), self::TRANSLATION_DOMAIN);
                }elseif($bafvRequest->getStatus() === BafvRequest::STATUS_ACCEPTED){
                    $bafv['showBtn'][$merchantId] = false;
                } else {
                    $isMerchantAuthorizedForUser = $this->userService->isMerchantAuthorizedForUser($user, $merchantId);
                    if ($isMerchantAuthorizedForUser) {
                        $bafv['showBtn'][$merchantId] = false;
                    }
                }
            }
        }

        if (!empty($bafv['merchant'])) {
            $bafv['merchant'] = array_unique($bafv['merchant']);
        }
        return $bafv;
    }

    /**
     * @param User $user
     * @param int $merchantId
     * @return bool
     */
    public function hasBafvRequest(User $user, int $merchantId) :bool {
        $company = $user->getCompany();

        $isMerchantAuthorizedForUser = $this->userService->isMerchantAuthorizedForUser($user, $merchantId);

        if (!$isMerchantAuthorizedForUser) {
            // check if bafv request exists ?
            $bafvRequest = $this->bafvRequestRepository->findOneBy(['company' => $company, 'merchantId' => $merchantId]);

            return ($bafvRequest !== null);
        }

        return true;
    }

    public function isUserCompanyAllowToSeeMerchantOffersDetails(User $user, int $merchantId): bool
    {
        return $this->isCompanyAllowToSeeMerchantOffersDetails($user->getCompany(), $merchantId);
    }
}
