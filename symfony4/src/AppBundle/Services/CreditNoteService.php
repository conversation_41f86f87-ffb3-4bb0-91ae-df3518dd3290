<?php

namespace AppBundle\Services;

use AppBundle\Entity\CreditNote;
use AppBundle\Repository\CompanyRepository;
use AppBundle\Repository\CreditNoteRepository;
use DateTimeImmutable;
use Open\IzbergBundle\Api\CreditNoteApi;
use Open\IzbergBundle\Api\IzbergUtils;
use Open\IzbergBundle\Api\OrderApi;
use Open\IzbergBundle\Model\CreditNoteLine;
use Open\IzbergBundle\Model\FetchCreditNotesResponse;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;

class CreditNoteService implements LoggerAwareInterface
{
    private CreditNoteApi $creditNoteApi;
    private SerializerService $serializerService;
    private JobService $jobService;
    private CreditNoteRepository $creditNoteRepository;
    private CompanyRepository $companyRepository;
    private OrderApi $orderApi;
    private LoggerInterface $logger;

    public function __construct(
        CreditNoteApi $creditNoteApi,
        SerializerService $serializerService,
        JobService $jobService,
        CreditNoteRepository $creditNoteRepository,
        CompanyRepository $companyRepository,
        OrderApi $orderApi
    )
    {
        $this->creditNoteApi = $creditNoteApi;
        $this->serializerService = $serializerService;
        $this->jobService = $jobService;
        $this->creditNoteRepository = $creditNoteRepository;
        $this->companyRepository = $companyRepository;
        $this->orderApi = $orderApi;
    }

    public function fetchCreditNotePdfUrl($creditNoteId)
    {
        return $this->creditNoteApi->fetchCustomerCreditNotePdf($creditNoteId);
    }

    public function fetchIzbergCreditNotes(int $offset = 0, int $limit = 10): FetchCreditNotesResponse
    {
        return $this->creditNoteApi->fetchAllCreditNotes($offset, $limit);
    }

    public function sync(FetchCreditNotesResponse $creditNoteResult)
    {
        /** @var \Open\IzbergBundle\Model\CreditNote $creditNote */
        foreach ($creditNoteResult->getObjects() as $creditNote) {
            $creditNoteEntity = $this->creditNoteRepository->findOneBy(['izbergId' => $creditNote->getId()]);

            if (null === $creditNoteEntity) {
                $creditNoteEntity = new CreditNote();

                $creditNoteEntity->setIzbergId($creditNote->getId());
                $creditNoteEntity->setNumberId($creditNote->getIdNumber());

                $company = $this->companyRepository->findOneBy(['izbergUserId' => $creditNote->getReceiver()->getId()]);
                $creditNoteEntity->setCompany($company);
            }

            $creditNoteEntity->setPdfFile(!empty($creditNote->getPdfFile()));
            $creditNoteEntity->setIssuerName($creditNote->getIssuerName());
            $creditNoteEntity->setCreatedOn(new DateTimeImmutable($creditNote->getCreatedOn()));

            $this->updateCreditNoteOrder($creditNoteEntity, $creditNote);

            $creditNoteEntity->setTotalAmountWithTaxes($creditNote->getTotalAmountWithTaxes());

            $creditNoteEntity->setCurrency(call_user_func(function () use ($creditNote) {
                // invoice->getCurrency() returns a url like "https://api.sandbox.iceberg.technology/v1/currency/EUR/"
                // we want to keep only the last part "EUR"
                $currency = substr($creditNote->getCurrency(), 0, -1);
                return substr($currency, strrpos($currency, '/') + 1);
            }));

            $creditNoteEntity->setPaymentStatus($creditNote->getPaymentStatus());
            $creditNoteEntity->setStatus($creditNote->getStatus());

            $this->creditNoteRepository->save($creditNoteEntity);
        }
    }

    public function updateCreditNoteOrder(CreditNote $creditNoteEntity, \Open\IzbergBundle\Model\CreditNote $creditNote)
    {
        if (!count($creditNote->getCreditNoteLines())) {
            return;
        }

        $merchantOrderId = call_user_func(function () use ($creditNote) {
            $creditNoteLines = $creditNote->getCreditNoteLines();

            /** @var CreditNoteLine $creditNoteLine */
            foreach ($creditNoteLines as $creditNoteLine) {
                $orderItem = $creditNoteLine->getOrderItem();
                if ($orderItem != null) {
                    $orderItemId = IzbergUtils::parseIzbergResourceAndGetId($orderItem);
                    $izbOrderItem = $this->orderApi->fetchOrderItemById($orderItemId);
                    return IzbergUtils::parseIzbergResourceAndGetId($izbOrderItem->getMerchantOrder());
                }
            }
            return null;
        });

        if ($merchantOrderId) {
            $merchantOrder = $this->orderApi->fetchMerchantOrderById($merchantOrderId);
            $creditNoteEntity->setOrderId($merchantOrder->getOrder()->getId());
            $creditNoteEntity->setNumOrder($merchantOrder->getOrder()->getIdNumber() . ' - ' . $merchantOrder->getMerchant()->getId());
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
