<?php


namespace AppBundle\Services;


use Open\IzbergBundle\Service\RedisService;

class MessengerProgressionService
{
    public const CATALOG_MESSENGER = 'CATALOG_MESSENGER';

    public const CURRENT_ITEM = 'currentItem';

    public const MAX_ITEM = 'maxItem';

    public const EXPIRATION_TTL = 15;

    private RedisService $redisService;

    /**
     * MessengerProgressionService constructor.
     *
     * @param RedisService $redisService
     */
    public function __construct(RedisService $redisService)
    {
        $this->redisService = $redisService;
    }

    /**
     * This function will init the progression for this messenger
     *
     * @param string $key
     * @param int   $companyId
     *
     * @param int    $expiration
     *
     * @return bool
     */
    public function initProgression(string $key, int $companyId, int $expiration = self::EXPIRATION_TTL):bool
    {
        $formatedKey = $this->formatKey($key, $companyId);
        $progress = [self::CURRENT_ITEM=>0, self::MAX_ITEM=>0];
        return $this->redisService->saveItem($formatedKey, $progress, $expiration);
    }

    /**
     * This function will define the limit for the progression.
     *
     * @param string $key
     * @param int $companyId
     * @param int    $max
     * @param int    $expiration
     *
     * @return bool
     */
    public function setProgressionLimit(string $key, int $companyId,int $max, int $expiration = self::EXPIRATION_TTL):bool
    {
        $item = $this->getProgression($key, $companyId);
        if($item === null){
            return false;
        }
        $item[self::MAX_ITEM] = $max;
        $formatedKey = $this->formatKey($key, $companyId);

        return $this->redisService->saveItem($formatedKey, $item, $expiration);
    }

    /**
     * This function will increment the progression counter.
     *
     * @param string $key
     * @param int $companyId
     * @param int    $numberToIncrement
     * @param int    $expiration
     *
     * @return bool
     */
    public function incrementProgression(string $key, int $companyId, int $numberToIncrement = 1, int $expiration = self::EXPIRATION_TTL):bool
    {
        $item = $this->getProgression($key, $companyId);
        if($item === null){
            return false;
        }
        $item[self::CURRENT_ITEM] += $numberToIncrement;
        $formatedKey = $this->formatKey($key, $companyId);
        if($item[self::CURRENT_ITEM] >= $item[self::MAX_ITEM]){
            $this->redisService->removeItem($formatedKey);
            return true;
        }

        return $this->redisService->saveItem($formatedKey, $item, $expiration);
    }

    /**
     * This function will check if a message is in progress or not.
     *
     * @param string $key
     * @param int $companyId
     *
     * @return bool
     */
    public function hasMessageInProgress(string $key, int $companyId):bool
    {
        return $this->getProgression($key, $companyId) !== null;
    }

    /**
     * @param string $key
     * @param int $companyId
     *
     * @return array|null
     */
    public function getProgression(string $key, int $companyId):?array
    {
        $formatedKey = $this->formatKey($key, $companyId);
        return $this->redisService->getItem($formatedKey);
    }

    /**
     * This function will format the storage key.
     *
     * @param string $key
     * @param int $companyId
     *
     * @return string
     */
    private function formatKey(string $key, int $companyId):string
    {
        return $key."_".$companyId;
    }
}
