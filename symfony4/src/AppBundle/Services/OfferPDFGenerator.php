<?php

namespace AppBundle\Services;

use AppBundle\Entity\Address;
use AppBundle\Entity\Company;
use AppBundle\Model\DetailedOffer;
use Mpdf\Config\ConfigVariables;
use Mpdf\Config\FontVariables;
use Mpdf\Mpdf;
use Mpdf\MpdfException;
use Twig\Environment;

final class OfferPDFGenerator
{
    private const WHITE_BACKGROUND_COLOR = '#fff';

    private string $fontDir;

    public function __construct(
        private string $tmpDir,
        private Environment $twig
    ) {
    }

    public function withFontDir(string $fontDir): self
    {
        // in Symfony 4 this method will change as documented in https://symfony.com/doc/4.4/service_container/calls.html
        $this->fontDir = $fontDir;
        return $this;
    }

    /**
     * @param $locale
     * @return Mpdf
     * @throws MpdfException
     */
    public function computeOfferProformaPDF(
        DetailedOffer $offer,
        $merchant,
        $customAttr,
        $merchantCompany,
        $userCompany,
        $quantity,
        $total,
        $locale
    ): Mpdf {
        return $this->buildOfferProformaTemplate($offer, $merchant, $customAttr, $merchantCompany, $userCompany, $quantity, $total, $locale);
    }

    /**
     * @param array<array-key, DetailedOffer> $offers
     * @throws MpdfException
     */
    public function computeOffersProformaPDF(
        array $pdfData,
        Company $userCompany,
        string $locale,
        ?Address $billingAddress
    ): Mpdf {
        $pdf = $this->preparePdfTemplate();
        uasort($pdfData, function ($a, $b) {
            return count($a["offers"]) < count($b["offers"]) ? 1 : -1;
        });
        $totalVatExcluded = 0.0;
        $currency = '';
        $cartTaxes = [];
        $cartTotal = 0.0;
        foreach ($pdfData as $pdfDatum) {
            $totalVatExcluded += $pdfDatum['totalVatExcl'];
            $currency = $pdfDatum['currency'];
            $cartTaxes = $pdfDatum['cartTaxes'];
            $cartTotal = $pdfDatum['cartTotal'];
        }
        $body =  $this->twig->render('@OpenFront/offer_detail/proforma/pdf/offers.html.twig', [
            'data' => $pdfData,
            'userCompany' => $userCompany,
            'locale' => $locale,
            'quantity' => null,
            'total' => null,
            'totalVatExcluded' => $totalVatExcluded,
            'currency' => $currency,
            'billingAddress' => $billingAddress,
            'cartTaxes' => $cartTaxes,
            'cartTotal' => $cartTotal
        ]);

        $pdf->WriteHTML($body);

        return $pdf;
    }

    /**
     * @throws MpdfException
     */
    private function buildOfferProformaTemplate(
        DetailedOffer $offer,
        $merchant,
        $customAttr,
        $merchantCompany,
        Company $userCompany,
        $quantity,
        $total,
        string $locale
    ): Mpdf {
        $pdf = $this->preparePdfTemplate();

        $body =  $this->twig->render('@OpenFront/offer_detail/proforma/pdf/body.html.twig', [
            'offer' => $offer,
            'merchant' => $merchant,
            'merchantAttribute' => $customAttr,
            'merchantCompany' => $merchantCompany,
            'userCompany' => $userCompany,
            'quantity' => $quantity,
            'total' => $total,
            'locale' => $locale,
        ]);

        $pdf->WriteHTML($body);

        return $pdf;
    }

    private function preparePdfTemplate(array $config = []): Mpdf
    {
        $defaultConfig = (new ConfigVariables())->getDefaults();
        $fontDirs = $defaultConfig['fontDir'];

        if ($this->fontDir) {
            $fontDirs = array_merge($fontDirs, [$this->fontDir]);
        }

        $defaultFontConfig = (new FontVariables())->getDefaults();
        $fontData = $defaultFontConfig['fontdata'];

        $pdf = new Mpdf($config + [
            'fontDir' => $fontDirs,
            'fontdata' => $fontData + [
                    'OpenSans' => [
                        'R' => 'OpenSans-Regular.ttf',
                        'I' => 'OpenSans-Italic.ttf',
                    ]
                ],
            'default_font' => 'OpenSans',
            'margin_top' => 30,
            'margin_right' => 0,
            'margin_bottom' => 30,
            'margin_left' => 0,
            'margin_header' => 10,
            'margin_footer' => 0,
            'tempDir' => $this->tmpDir . '/mpdf',
//            'tempDir' => '/tmp',
        ]);

        $pdf->showImageErrors = true;
        $pdf->img_dpi = 96;

        $pdf->SetDefaultBodyCSS('background-color', self::WHITE_BACKGROUND_COLOR);

        $headerHtml = $this->twig->render('@OpenFront/offer_detail/proforma/pdf/header.html.twig');

        $pdf->SetHTMLHeader($headerHtml);

        $footerHtml = $this->twig->render('@OpenFront/offer_detail/proforma/pdf/footer.html.twig');

        $pdf->SetHTMLFooter($footerHtml);

        return $pdf;
    }
}
