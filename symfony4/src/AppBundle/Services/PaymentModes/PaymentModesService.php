<?php

declare(strict_types=1);

namespace AppBundle\Services\PaymentModes;

use AppBundle\Entity\Company;
use AppBundle\Factory\PaymentModeFactoryInterface;
use AppBundle\Model\PaymentMode\PreCardPayment;
use AppBundle\Model\PaymentMode\PreWirePayment;
use AppBundle\Model\PaymentMode\TermPayment;
use AppBundle\Model\PaymentModes;
use Symfony\Component\HttpFoundation\RequestStack;

final class PaymentModesService implements PaymentModesServiceInterface
{
    private RequestStack $requestStack;

    private PaymentModeFactoryInterface $paymentModeFactory;

    public function __construct(
        RequestStack $requestStack,
        PaymentModeFactoryInterface $paymentModeFactory
    ){
        $this->requestStack = $requestStack;
        $this->paymentModeFactory = $paymentModeFactory;
    }

    public function computePaymentModesOfCompany(Company $company): array
    {
        $paymentModesList = [];
        $paymentModes = new PaymentModes();
        $paymentModes->setPreCreditCart($company->getPrepaymentCreditcardEnabled());
        $paymentModes->setPreWireTransfer($company->getPrepaymentMoneyTransfertEnabled());

        if ($paymentModes->isPreCreditCart()) {
            $paymentModesList[] = $this->paymentModeFactory->toArray(new PreCardPayment());
        }

        if ($paymentModes->isPreWireTransfer()) {
            $paymentModesList[] = $this->paymentModeFactory->toArray(new PreWirePayment());
        }

        if ($company->getTermpaymentMoneyTransfertEnabled()){
            $paymentModes->setTimePayment(true);
            $paymentModesList[] = $this->paymentModeFactory->toArray(new TermPayment());
        }

        return $paymentModesList;
    }
}
