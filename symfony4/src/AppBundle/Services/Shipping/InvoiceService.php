<?php

namespace AppBundle\Services\Shipping;

use AppBundle\Exception\UnexpectedValueException;
use AppBundle\Model\Shipping\ShipmentOrderItem;
use AppBundle\Request\InvoiceCreationRequest;
use AppBundle\Services\OrderService;
use Open\IzbergBundle\Api\InvoiceApi;
use Open\IzbergBundle\Api\InvoiceLineApi;
use Open\IzbergBundle\Api\OrderApi;
use Open\IzbergBundle\Api\OrderItemApi;
use Open\IzbergBundle\Model\OrderItem;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;

final class InvoiceService implements LoggerAwareInterface
{
    private OrderService $orderService;
    private OrderApi $orderApi;
    private InvoiceApi $invoiceApi;
    private OrderItemApi $orderItemApi;
    private InvoiceLineApi $invoiceLineApi;
    private LoggerInterface $logger;

    public function __construct(
        OrderService $orderService,
        OrderApi $orderApi,
        InvoiceApi $invoiceApi,
        OrderItemApi $orderItemApi,
        InvoiceLineApi $invoiceLineApi
    )
    {
        $this->orderService = $orderService;
        $this->orderApi = $orderApi;
        $this->invoiceApi = $invoiceApi;
        $this->orderItemApi = $orderItemApi;
        $this->invoiceLineApi = $invoiceLineApi;
    }

    /**
     * @param InvoiceCreationRequest[] $invoiceCreationRequests
     * @return bool
     */
    public function invoice(array $invoiceCreationRequests): bool
    {
        foreach($invoiceCreationRequests as $request) {
            try {
                $this->createInvoice($request);
            } catch(UnexpectedValueException $unexpectedValueException) {
                $this->logger->error($unexpectedValueException->getMessage(), [LogUtil::EVENT_NAME=>EventNameEnum::INVOICE]);
            }
        }

        return true;
    }

    private function createInvoice(InvoiceCreationRequest $request)
    {
        // retrieve merchant order
        $merchantOrder = $this->orderService->fetchMerchantsOrderByOrderId($request->getMerchantOrderId());

        if (!$merchantOrder) {
            throw new UnexpectedValueException(sprintf('Cannot find merchant order with id %s', $request->getMerchantOrderId()));
        }

        // retrieve shipping order item to be invoiced
        $items = $merchantOrder->getItems()->toArray();

        $items = array_map(
            function(OrderItem $orderItem): OrderItem {
                $izbergOrderItem = $this->orderApi->fetchOrderItemById($orderItem->getId());

                return $izbergOrderItem;
            },
            $items
        );

        $requestOrderItems = array_map(
            function(ShipmentOrderItem $shipmentOrderItem) {
                return $shipmentOrderItem->getId();
            },
            $request->getOrderItems()
        );

        // récupérer shippingOption of given orderItem
        $shippingOptions = $this->orderApi->fetchMerchantOrderShippingOptions($merchantOrder->getId());

        $shipmentIds = [];
        foreach ($shippingOptions as $shippingOption) {
            $shippingOptionsOrderItems = array_map(
                function($orderItem) {
                    return $orderItem->id;
                },
                $shippingOption->order_items
            );

            $matchingOrderItems = array_intersect($requestOrderItems, $shippingOptionsOrderItems);
            if (count($matchingOrderItems) > 0) {
                $shipmentIds[] = $shippingOption->options->shipmentId;
            }
        }

        // shippingOption->shippingId === orderItem->offer_external_id
        $shippingOrderItems = [];
        if (count($shipmentIds) > 0) {
            $shippingOrderItems = array_filter(
                $items,
                function(OrderItem $item) use ($shipmentIds) {
                    $shipmentOfferExternalIds = array_map(
                        function(int $shipmentId) {
                            return sprintf('shipment-%s',$shipmentId);
                        },
                        $shipmentIds
                    );

                    return in_array($item->getOfferExternalId(), $shipmentOfferExternalIds);
                }
            );
        }

        $orderItems = array_filter(
            $items,
            function(OrderItem $item) use ($requestOrderItems) {
                return (in_array($item->getId(), $requestOrderItems));
            }
        );

        if (empty($orderItems)) {
            $this->logger->error(
                'Cannot find order item to invoice in merchant order',LogUtil::buildContext([LogUtil::EVENT_NAME=>EventNameEnum::INVOICE])
            );
            return false;
        }

        if (empty($shippingOrderItems)) {
            $this->logger->error(
                'Cannot find SHIPPING order item to invoice in merchant order',LogUtil::buildContext([LogUtil::EVENT_NAME=>EventNameEnum::INVOICE])
            );
            return false;
        }


        $orderItemsToBeInvoiced = array_map(
            function(OrderItem $orderItem) use ($request): array {
                $quantity = array_reduce(
                    $request->getOrderItems(),
                    function(int $quantity, ShipmentOrderItem $shipmentOrderItem) use ($orderItem) {
                        if ($shipmentOrderItem->getId() === $orderItem->getId()) {
                            $quantity = $shipmentOrderItem->getQuantity();
                        }

                        return $quantity;
                    },
                    0
                );

                return [$orderItem, $quantity];
            },
            $orderItems
        );

        $shippingItemsToBeInvoiced = array_map(
            function(OrderItem $shippingOrderItem): array{
                return [$shippingOrderItem, 1];
            },
            $shippingOrderItems
        );

        $itemToBeInvoiced = array_merge($orderItemsToBeInvoiced, $shippingItemsToBeInvoiced);


        $itemToBeInvoiced = array_filter(
            $itemToBeInvoiced,
            function(array $invoiceableItem) {
                /**
                 * @var OrderItem $orderItem
                 * @var int $quantity
                 */
                [$orderItem] = $invoiceableItem;

                return ($orderItem->getInvoicedQuantity() < $orderItem->getQuantity());
            }
        );

        if (!count($itemToBeInvoiced)) {
            $this->logger->error(
                'Nothing to invoice',LogUtil::buildContext([LogUtil::EVENT_NAME=>EventNameEnum::INVOICE])
            );
            return false;
        }

        // invoice creation
        $merchantId = $merchantOrder->getMerchant()->getId();
        $userId = $merchantOrder->getUser()->getId();

        // Passer la merchant order de confirmed a processed
        if ($merchantOrder->getStatus() == 80) {
            $this->orderApi->processMerchantOrder($merchantOrder->getId());
        }

        // 1- we create the invoice
        $invoice = $this->invoiceApi->createInvoice($merchantId, $userId);

        /**
         * @var OrderItem $orderItem
         * @var int $quantity
         */
        foreach ($itemToBeInvoiced as [$orderItem, $quantity]) {

            // 2 - we define the order_item that need to be invoiced (need to precise the quantity)
            $this->orderItemApi->patch($orderItem->getId(), ['invoiceable_quantity' => $quantity]);

            // 3 - we add the order_item to the invoice
            $this->invoiceLineApi->createInvoiceLine($invoice->getId(), $orderItem->getId());
        }

        // 4 - valider la facture
        $this->invoiceApi->submitInvoice($invoice->getId());
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
