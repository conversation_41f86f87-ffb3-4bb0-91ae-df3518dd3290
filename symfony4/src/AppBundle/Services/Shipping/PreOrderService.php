<?php

namespace AppBundle\Services\Shipping;

use Open\IzbergBundle\Model\Item;
use Open\IzbergBundle\Model\MerchantOrder;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Upela\Api\Exception;
use Upela\Api\PreOrderApi;
use Upela\Api\ProductApi;
use Upela\Model\Product;
use Upela\Request\PreOrder\CreateRequestBuilderInterface;
use Upela\Request\Product\CancelRequestBuilderInterface;

final class PreOrderService implements LoggerAwareInterface
{
    private CreateRequestBuilderInterface $createRequestBuilder;
    private PreOrderApi $preOrderApi;
    private CancelRequestBuilderInterface $productCancelRequestBuilder;
    private ProductApi $productApi;
    private LoggerInterface $logger;

    public function __construct(
        CreateRequestBuilderInterface $createRequestBuilder,
        PreOrderApi $preOrderApi,
        CancelRequestBuilderInterface $productCancelRequestBuilder,
        ProductApi $productApi
    )
    {
        $this->createRequestBuilder = $createRequestBuilder;
        $this->preOrderApi = $preOrderApi;
        $this->productCancelRequestBuilder = $productCancelRequestBuilder;
        $this->productApi = $productApi;
    }

    /**
     * Create an Upela Preorder
     *
     * @param MerchantOrder $merchantOrder
     * @throws \Upela\Api\Exception
     */
    public function createPreOrder(MerchantOrder $merchantOrder)
    {
        $request = $this->createRequestBuilder->build($merchantOrder);
        $this->preOrderApi->create($request);
    }

    /**
     * Cancel Preorder products
     *
     * @param Item[] $orderItems
     * @throws \Upela\Api\Exception
     */
    public function cancelPreOrderProducts(int $preOrderId, array $orderItems)
    {
        try {
            $preOrder = $this->preOrderApi->fetchPreorder($preOrderId);

            $preOrderProductIds = array_map(
                function(Product $product) {
                    return $product->getProductId();
                },
                $preOrder->getProducts()
            );

            $orderItems = array_filter(
                $orderItems,
                function(Item $orderItem) use ($preOrderProductIds) {
                    return in_array($orderItem->getId(), $preOrderProductIds);
                }
            );

            $cancelRequest = $this->productCancelRequestBuilder->build($orderItems);
            $this->productApi->cancelProducts($cancelRequest);

        } catch(Exception $upelaApiException) {
            $this->logger->info(
                sprintf('UPELA preorder %s not accessible', $preOrderId),
                LogUtil::buildContext([LogUtil::EVENT_NAME=>EventNameEnum::UPELA_CANCEL])
            );
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
