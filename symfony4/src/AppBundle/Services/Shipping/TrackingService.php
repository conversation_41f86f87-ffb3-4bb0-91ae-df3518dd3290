<?php

namespace AppBundle\Services\Shipping;

use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Upela\Api\Exception;
use Upela\Api\ProductApi;

class TrackingService implements LoggerAwareInterface
{
    private ProductApi $productApi;
    private LoggerInterface $logger;

    public function __construct(ProductApi $productApi)
    {
        $this->productApi = $productApi;
    }

    /**
     * @param array $productIds
     * @return array
     * @throws Exception
     */
    public function fetchProductByIds(array $productIds): array
    {
        $products = [];
        try {
            $products = $this->productApi->fetchProductByIds($productIds);
        } catch(Exception $exception) {
            // 404 code is not an error we should return an empty array
            if ($exception->getCode() != 404) {
                $this->logger->error(
                    sprintf(
                        'UPELA FETCHING PRODUCTS; %s',
                        $exception->getMessage()
                    ),
                    LogUtil::buildContext([LogUtil::EVENT_NAME=>EventNameEnum::UPELA_API_PRODUCT_REQUEST])
                );

                throw $exception;
            }
        }

        return $products;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
