<?php

declare(strict_types=1);

namespace AppBundle\Services\Offer;

use AppBundle\Model\Offer;
use AppBundle\Services\OfferService;

final class AlgoliaOfferFinder implements OfferFinderInterface
{
    public function __construct(private readonly OfferService $offerService)
    {
    }

    public function findOfferById(int $offerId): ?Offer
    {
        return $this->offerService->findOfferById(offerId: $offerId);
    }
}
