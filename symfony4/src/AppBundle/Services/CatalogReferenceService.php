<?php

namespace AppBundle\Services;

use Algolia\AlgoliaSearch\Exceptions\AlgoliaException;
use Open\IzbergBundle\Algolia\AlgoliaField;
use Open\IzbergBundle\Algolia\AlgoliaQueryParams;
use Open\IzbergBundle\Algolia\AlgoliaServiceEn;
use Open\IzbergBundle\Service\RedisService;

class CatalogReferenceService
{
    private const CATALOG_REFERENCE_REDIS_CACHE_KEY = 'CATALOG_REFERENCE_REDIS_CACHE_KEY';

    /**
     * @var RedisService
     */
    private $redisService;

    /**
     * @var AlgoliaServiceEn
     */
    private $algoliaServiceEn;

    /**
     * @var AlstomCustomAttributes
     */
    private $customAttributes;

    public function __construct(RedisService $redisService, AlgoliaServiceEn $algoliaServiceEn, AlstomCustomAttributes $customAttributes)
    {
        $this->redisService = $redisService;
        $this->algoliaServiceEn = $algoliaServiceEn;
        $this->customAttributes = $customAttributes;
    }

    /**
     * @throws AlgoliaException
     */
    public function syncCatalogReference()
    {
        $catalogReferences = [];

        $queryParams = (new AlgoliaQueryParams())->addFacetFilters(
            AlgoliaField::STATUS . ':active',
            AlgoliaField::PRODUCT_STATUS . ':active',
            AlgoliaField::MERCHANT_STATUS . ':10'
        );

        if ($this->algoliaServiceEn->getTransportCategory()) {
            $queryParams->addNotFilters(AlgoliaField::PRODUCT_CATEGORY . ':' . $this->algoliaServiceEn->getTransportCategory());
        }

        foreach ($this->algoliaServiceEn->browse('', $queryParams->toArray()) as $hit) {
            $attributes = $hit['attributes'] ?? [];

            $incoterm = $attributes[$this->customAttributes->getIncoTerm()] ?? null;
            $countryOfDelivery = $attributes[$this->customAttributes->getCountryOfDelivery()] ?? null;

            $buildReference = function(?string $reference, ?string $incoterm, ?string $countryOfDelivery) {
                if (null === $reference) {
                    return null;
                }

                if (null === $incoterm) {
                    return $reference;
                }

                $incoterm = strtolower($incoterm);
                $reference = $reference . '_' . $incoterm;

                if ($incoterm === 'dap') {
                    if (null === $countryOfDelivery) {
                        return $reference;
                    }

                    $countryOfDelivery = strtolower($countryOfDelivery);
                    $reference = $reference . '_' . $countryOfDelivery;
                }

                return $reference;
            };

            $manufacturerReference = $attributes[$this->customAttributes->getManufacturerReference()] ?? null;
            $vendorReference = $attributes[$this->customAttributes->getVendorReference()] ?? null;

            $manufacturerReference = $buildReference($manufacturerReference, $incoterm, $countryOfDelivery);
            $vendorReference = $buildReference($vendorReference, $incoterm, $countryOfDelivery);

            $references = array_merge(
                [
                    $manufacturerReference,
                    $vendorReference,
                ],
                array_map(
                    function($supplierProductCompatibility) use ($attributes, $buildReference, $incoterm, $countryOfDelivery) {
                        $supplierProductCompatibility = $attributes[$supplierProductCompatibility] ?? null;
                        return $buildReference($supplierProductCompatibility, $incoterm, $countryOfDelivery);
                    },
                    $this->customAttributes->getSupplierProductCompatibilities()
                )
            );

            $catalogReferences = array_filter(
                array_unique(
                    array_merge(
                        $catalogReferences,
                        $references
                    )
                )
            );
        }

        // Store this array in redis
        $this->redisService->saveItem(self::CATALOG_REFERENCE_REDIS_CACHE_KEY, $catalogReferences);
    }

    /**
     * @param string $reference
     * @return bool
     */
    public function referenceExists(string $reference, string $country): bool
    {

        // build reference based on user country
        // exemple of keys
        // 456544564_fca
        // 456544564_dap_france

        $referenceFca = $reference . '_fca';
        $referenceDap = $reference . '_dap_' . $country;

        $catalogReferences = $this->redisService->getItem(self::CATALOG_REFERENCE_REDIS_CACHE_KEY);

        if(!$catalogReferences) {
            return false;
        }

        return in_array($referenceFca, $catalogReferences) || in_array($referenceDap, $catalogReferences);
    }
}
