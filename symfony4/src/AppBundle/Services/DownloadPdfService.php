<?php

namespace AppBundle\Services;

use Symfony\Component\HttpClient\HttpClient;

class DownloadPdfService
{
    private string $tmpDir;

    public function __construct(string $privateTmpDir)
    {
        $path = $privateTmpDir . "/download_pdf";
        if (!is_dir($path)) {
            mkdir($path, 0777, true);
        }
        $this->tmpDir = $path;
    }

    public function downloadPdf(string $url, string $filename): string
    {
        $filepath = $this->tmpDir . '/' . $filename;

        $httpClient = HttpClient::create();
        $response = $httpClient->request('GET', $url, [
            'buffer' => false,
        ]);

        if (200 !== $response->getStatusCode()) {
            throw new \Exception('Failed to create a request');
        }

        $fileHandler = fopen($filepath, 'w');
        foreach ($httpClient->stream($response) as $chunk) {
            fwrite($fileHandler, $chunk->getContent());
        }

        return $filepath;
    }
}
