<?php

namespace AppBundle\Services;

class AlstomCustomAttributes
{
    /**
     * @var array
     */
    private $associatedProducts;

    /**
     * @var array
     */
    private $associatedServices;

    /**
     * @var string
     */
    private $manufacturerReference;

    /**
     * @var string
     */
    private $incoTerm;

    /**
     * @var string
     */
    private $countryOfDelivery;

    /**
     * @var array
     */
    private $supplierProductCompatibilities;

    /**
     * @var string
     */
    private $threshold1;

    /**
     * @var string
     */
    private $threshold1Price;

    /**
     * @var string
     */
    private $threshold2;

    /**
     * @var string
     */
    private $threshold2Price;

    /**
     * @var string
     */
    private $threshold3;

    /**
     * @var string
     */
    private $threshold3Price;

    /**
     * @var string
     */
    private $threshold4;

    /**
     * @var string
     */
    private $threshold4Price;

    /**
     * @var string
     */
    private $moq;

    /**
     * @var string
     */
    private $totalDelayForCustomer;

    /**
     * @var string
     */
    private $standard;

    /**
     * @var array
     */
    private $fitFormFunctions;

    /**
     * @var array
     */
    private $manufacturerObsoleteReferences;

    /**
     * @var string
     */
    private $unspscCode;

    /**
     * @var string
     */
    private $taxGroup;

    /**
     * @var string
     */
    private $batchSize;

    /**
     * @var string
     */
    private $stockAvailability;

    /**
     * @var string
     */
    private $stockManagement;

    /**
     * @var string
     */
    private $pspMerchantId;

    /**
     * @var string
     */
    private $vendorReference;

    /**
     * @var string
     */
    private $deliveryTime;

    /**
     * @var string
     */
    private $dataSheet;

    /**
     * @var string
     */
    private $quantityPerSku;

    /**
     * @var string
     */
    private $skuUnit;

    /**
     * @var string
     */
    private $fcaAddress;

    /**
     * @var string
     */
    private $vatNumber;

    /**
     * @var string
     */
    private $merchantLegalNotice;

    /**
     * @var string
     */
    private $companyIdentificationNumber;

    /**
     * @var string
     */
    private $minimumOrderAmount;

    /** @var string */
    private $dangerousProduct;

    /** @var string */
    private $packageHeightMoq;

    /** @var string */
    private $packageWidthMoq;

    /** @var string */
    private $packageLengthMoq;

    /** @var string */
    private $packageWeightMoq;

    /** @var string */
    private $fcaZipCode;

    /** @var string */
    private $fcaZipTown;

    /** @var string */
    private $stackability;

    /** @var string */
    private $transportType;

    /** @var string */
    private $customTariffCode;

    /** @var string */
    private $upelaIsActive;

    /**
     * @var string
     */
    private $madeIn;

    private $delayBeforeShipping;

    /**
     * @var string
     */
    private $restrictedProductBoolean;

    /**
     * @var string
     */
    private $restrictedProductField;

    /**
     * @var int
     */
    private $restrictedProductFieldsCount;

    /**
     * @var string
     */
    private $lastBuyOrderDate;

    /**
     * @var string
     */
    private $fireSmoke;

    private string $shareCapital;

    private string $vendorVatNumber;

    private string $merchantOrderUrlPdf;

    private string $priceValidityDate;
    private string $realStock;
    private string $warrantyPeriod;



    public function __construct(array $customAttributes)
    {
        $this->associatedProducts = $customAttributes['associated_products'];
        $this->associatedServices = $customAttributes['associated_services'];
        $this->manufacturerReference = $customAttributes['manufacturer_reference'];
        $this->incoTerm = $customAttributes['incoterm'];
        $this->countryOfDelivery = $customAttributes['country_of_delivery'];
        $this->supplierProductCompatibilities = $customAttributes['supplier_product_compatibilities'];
        $this->threshold1 = $customAttributes['threshold_1'];
        $this->threshold1Price = $customAttributes['threshold_1_price'];
        $this->threshold2 = $customAttributes['threshold_2'];
        $this->threshold2Price = $customAttributes['threshold_2_price'];
        $this->threshold3 = $customAttributes['threshold_3'];
        $this->threshold3Price = $customAttributes['threshold_3_price'];
        $this->threshold4 = $customAttributes['threshold_4'];
        $this->threshold4Price = $customAttributes['threshold_4_price'];
        $this->moq = $customAttributes['moq'];
        $this->totalDelayForCustomer = $customAttributes['total_delay_for_customer'];
        $this->standard = $customAttributes['standard'];
        $this->fitFormFunctions = $customAttributes['fit_form_functions'];
        $this->manufacturerObsoleteReferences = $customAttributes['manufacturer_obsolete_references'];
        $this->unspscCode = $customAttributes['unspsc_code'];
        $this->taxGroup = $customAttributes['tax_group'];
        $this->batchSize = $customAttributes['batch_size'];
        $this->stockAvailability = $customAttributes['stock_availability'];
        $this->stockManagement = $customAttributes['stock_management'];
        $this->pspMerchantId = $customAttributes['psp_merchant_id'];
        $this->vendorReference = $customAttributes['vendor_reference'];
        $this->deliveryTime = $customAttributes['delivery_time'];
        $this->dataSheet = $customAttributes['data_sheet'];
        $this->quantityPerSku = $customAttributes['quantity_per_sku'];
        $this->skuUnit = $customAttributes['sku_unit'];
        $this->fcaAddress = $customAttributes['fca_address'];
        $this->fcaZipCode = $customAttributes['fca_zip_code'];
        $this->fcaZipTown = $customAttributes['fca_zip_town'];
        $this->companyIdentificationNumber = $customAttributes['company_identification_number'];
        $this->merchantLegalNotice = $customAttributes['merchant_legal_notice'];
        $this->minimumOrderAmount = $customAttributes['minimum_order_amount'];
        $this->dangerousProduct = $customAttributes['dangerous_product'];
        $this->packageHeightMoq = $customAttributes['package_height_moq'];
        $this->packageWidthMoq = $customAttributes['package_width_moq'];
        $this->packageLengthMoq = $customAttributes['package_length_moq'];
        $this->packageWeightMoq = $customAttributes['package_weight_moq'];
        $this->stackability = $customAttributes['stackability'];
        $this->transportType = $customAttributes['transport_type'];
        $this->customTariffCode = $customAttributes['custom_tariff_code'];
        $this->upelaIsActive = $customAttributes['upela_is_active'];
        $this->madeIn = $customAttributes['made_in'];
        $this->delayBeforeShipping = $customAttributes['delay_before_shipping'];
        $this->lastBuyOrderDate = $customAttributes['last_buy_order_date'];
        $this->restrictedProductBoolean = $customAttributes['restricted_product_boolean'];
        $this->restrictedProductField = $customAttributes['restricted_product_field'];
        $this->restrictedProductFieldsCount = $customAttributes['restricted_product_fields_count'];
        $this->fireSmoke = $customAttributes['fire_smoke'];
        $this->shareCapital = $customAttributes['share_capital'];
        $this->vendorVatNumber = $customAttributes['vendor_vat_number'];
        $this->merchantOrderUrlPdf = $customAttributes['merchant_order_url_pdf'];
        $this->priceValidityDate = $customAttributes['price_validity_date'];
        $this->realStock = $customAttributes['real_stock'];
        $this->warrantyPeriod = $customAttributes['warranty_period'];
    }

    public static function createFullAttributeName($attributeName): string
    {
        return sprintf('attributes.%s', $attributeName);
    }

    public function getIncoTerm(): string
    {
        return $this->incoTerm;
    }

    public function getCountryOfDelivery(): string
    {
        return $this->countryOfDelivery;
    }

    public function getAssociatedProducts(): array
    {
        return $this->associatedProducts;
    }

    public function getAssociatedServices(): array
    {
        return $this->associatedServices;
    }

    public function getManufacturerReference(): string
    {
        return $this->manufacturerReference;
    }

    public function getSupplierProductCompatibilities(): array
    {
        return $this->supplierProductCompatibilities;
    }

    public function getThreshold1(): string
    {
        return $this->threshold1;
    }

    public function getThreshold1Price(): string
    {
        return $this->threshold1Price;
    }

    public function getThreshold2(): string
    {
        return $this->threshold2;
    }

    public function getThreshold2Price(): string
    {
        return $this->threshold2Price;
    }

    public function getThreshold3(): string
    {
        return $this->threshold3;
    }

    public function getThreshold3Price(): string
    {
        return $this->threshold3Price;
    }

    public function getThreshold4(): string
    {
        return $this->threshold4;
    }

    public function getThreshold4Price(): string
    {
        return $this->threshold4Price;
    }

    public function getMoq(): string
    {
        return $this->moq;
    }

    public function getTotalDelayForCustomer(): string
    {
        return $this->totalDelayForCustomer;
    }

    public function getStandard(): string
    {
        return $this->standard;
    }

    public function getFitFormFunctions(): array
    {
        return $this->fitFormFunctions;
    }

    public function getManufacturerObsoleteReferences(): array
    {
        return $this->manufacturerObsoleteReferences;
    }

    public function getUnspscCode(): string
    {
        return $this->unspscCode;
    }

    public function getTaxGroup() : string
    {
        return $this->taxGroup;
    }

    public function getBatchSize()
    {
        return $this->batchSize;
    }

    public function getStockAvailability(): string
    {
        return $this->stockAvailability;
    }

    public function getStockManagement(): string
    {
        return $this->stockManagement;
    }

    public function getPspMerchantId(): string
    {
        return $this->pspMerchantId;
    }

    public function getVendorReference(): string
    {
        return $this->vendorReference;
    }

    public function getDeliveryTime(): string
    {
        return $this->deliveryTime;
    }

    public function getDataSheet(): string
    {
        return $this->dataSheet;
    }

    public function getQuantityPerSku(): string
    {
        return $this->quantityPerSku;
    }

    public function getSkuUnit(): string
    {
        return $this->skuUnit;
    }

    public function getFcaAddress(): string
    {
        return $this->fcaAddress;
    }

    public function getCompanyIdentificationNumber(): string
    {
        return $this->companyIdentificationNumber;
    }

    public function getVatNumber(): string
    {
        return $this->vatNumber;
    }

    public function getMerchantLegalNotice(): string
    {
        return $this->merchantLegalNotice;
    }

    public function getMinimumOrderAmount(): ?string
    {
        return $this->minimumOrderAmount;
    }

    public function isDangerousProduct(): string
    {
        return $this->dangerousProduct;
    }

    public function getPackageHeightMoq(): string
    {
        return $this->packageHeightMoq;
    }

    public function getPackageWidthMoq(): string
    {
        return $this->packageWidthMoq;
    }

    public function getPackageLengthMoq(): string
    {
        return $this->packageLengthMoq;
    }

    public function getPackageWeightMoq(): string
    {
        return $this->packageWeightMoq;
    }

    public function getFcaZipCode(): string
    {
        return $this->fcaZipCode;
    }

    public function getFcaZipTown(): string
    {
        return $this->fcaZipTown;
    }

    public function getStackability(): string
    {
        return $this->stackability;
    }

    public function getDangerousProduct(): string
    {
        return $this->dangerousProduct;
    }

    public function getTransportType(): string
    {
        return $this->transportType;
    }

    public function getCustomTariffCode()
    {
        return $this->customTariffCode;
    }

    public function getUpelaIsActive(): string
    {
        return $this->upelaIsActive;
    }

    public function getMadeIn(): string
    {
        return $this->madeIn;
    }

    public function getDelayBeforeShipping()
    {
        return $this->delayBeforeShipping;
    }

    public function getLastBuyOrderDate(): string
    {
        return $this->lastBuyOrderDate;
    }

    /**
     * @return string
     */
    public function getRestrictedProductBoolean(): string
    {
        return $this->restrictedProductBoolean;
    }

    /**
     * @return string
     */
    public function getRestrictedProductField(): string
    {
        return $this->restrictedProductField;
    }

    /**
     * @return int
     */
    public function getRestrictedProductFieldsCount(): int
    {
        return $this->restrictedProductFieldsCount;
    }

    /**
     * @return string
     */
    public function getFireSmoke(): string
    {
        return $this->fireSmoke;
    }

    public function getShareCapital(): string
    {
        return $this->shareCapital;
    }

    public function getVendorVatNumber(): string
    {
        return $this->vendorVatNumber;
    }

    public function getMerchantOrderUrlPdf(): string
    {
        return $this->merchantOrderUrlPdf;
    }

    public function getPriceValidityDate(): string
    {
        return $this->priceValidityDate;
    }

    public function getRealStock(): string
    {
        return $this->realStock;
    }

    public function getWarrantyPeriod(): string
    {
        return $this->warrantyPeriod;
    }
}
