<?php

namespace AppBundle\Services;

use AppBundle\Entity\Company;
use AppBundle\Entity\Address;
use AppBundle\Entity\Contact;
use AppBundle\Exception\PaymentException;
use AppBundle\Factory\TransactionFactory;
use AppBundle\Model\Transaction;
use AppBundle\Util\DateUtil;
use Doctrine\ORM\EntityManagerInterface;
use AppBundle\Util\Locale;
use Exception;
use Open\FrontBundle\Form\PaymentModeSelectForm;
use Open\Izberg\Model\CustomerAttribute;
use Open\IzbergBundle\Api\GatewayApi;
use Open\IzbergBundle\Api\MerchantApi;
use Open\IzbergBundle\Api\OrderApi;
use Open\IzbergBundle\Model\MerchantOrder;
use Open\IzbergBundle\Service\AttributeService;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Open\WebhelpBundle\Model\AutomaticResponse;
use Open\WebhelpBundle\Model\SubTransactionResponse;
use Open\WebhelpBundle\Model\TransactionResponse;
use Open\WebhelpBundle\Api\TransactionApi;
use Open\WebhelpBundle\Api\WPSException;
use Open\WebhelpBundle\ApiException;
use Open\WebhelpBundle\WPSPushException;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Open\WebhelpBundle\Api\CustomerApi;
use Symfony\Contracts\Translation\TranslatorInterface;

class WPSService implements LoggerAwareInterface
{

    const LOG_STATUS_CODE = "status_code";

    private const LOG_WPS_EVENT = "wps_event";

    const LOG_PAYMENT_STEP = "step";

    const LOG_PAYMENT_MODE = "paymentMode";

    //LOG_PAYMENT_MODE must be one of this values:
    const LOG_TIME_MODE = "timeMode";
    const LOG_PRE_CC_MODE = "preCCMode";
    const LOG_PRE_WIRE_MODE = "preWWireMode";

    //WPS Payment conditions
    const WPS_PC_PE_VIREMENT = "PE_VIREMENT";
    const WPS_PC_PP_VIREMENT = "PP_VIREMENT";
    const WPS_PC_PP_CB = "PP_CB";

    public const CODE_QUALIFIER_CART = "cart/";
    public const CODE_ORDER = "order";
    public const CODE_QUALIFIER_INVOICE = "invoice/";
    public const CODE_QUALIFIER_MERCHANT_ORDER = "merchantOrder/";

    const HASH_ALGORITHM = "sha256";

    private EntityManagerInterface $em;
    private SecurityService $securityService;
    private LoggerInterface $logger;
    private TranslatorInterface $translator;
    private CustomerApi $customerAPI;
    private TransactionApi $transactionAPI;
    private OrderApi $orderAPI;
    private string $locale;
    private array $gatewayTypeMapping;
    private MerchantApi $merchantApi;
    private GatewayApi $gatewayApi;
    private PaymentService $paymentService;
    private AttributeService $attributeService;
    private MailService $mailService;
    private int $paymentNbHoursBeforeRefund;
    private AlstomCustomAttributes $customAttributes;
    private OrderService $orderService;
    private TransactionFactory $transactionFactory;

    public function __construct(
        $paymentNbHoursBeforeRefund,
        EntityManagerInterface $em,
        SecurityService $securityService,
        TranslatorInterface $translator,
        RequestStack $requestStack,
        CustomerApi $customerAPI,
        OrderApi $orderApi,
        TransactionApi $transactionApi,
        MerchantApi $merchantApi,
        GatewayApi $gatewayApi,
        PaymentService $paymentService,
        AttributeService $attributeService,
        MailService $mailService,
        AlstomCustomAttributes $customAttributes,
        OrderService $orderService,
        TransactionFactory $transactionFactory
    )
    {

        $this->gatewayTypeMapping = [
            PaymentModeSelectForm::PAYMENT_TERM => GatewayApi::TYPE_TERM_PAYMENT,
            PaymentModeSelectForm::PAYMENT_PRE_WIRE => GatewayApi::TYPE_PREPAYMENT,
            PaymentModeSelectForm::PAYMENT_PRE_CARD => GatewayApi::TYPE_PREPAYMENT
        ];

        $this->attributeService = $attributeService;
        $this->customAttributes = $customAttributes;
        $this->em = $em;
        $this->securityService = $securityService;
        $this->translator = $translator;
        $this->customerAPI = $customerAPI;
        $this->orderAPI = $orderApi;
        $this->transactionAPI = $transactionApi;
        $this->merchantApi = $merchantApi;
        $this->gatewayApi = $gatewayApi;
        $this->paymentService = $paymentService;
        $this->mailService = $mailService;
        $this->paymentNbHoursBeforeRefund = $paymentNbHoursBeforeRefund;
        $this->orderService = $orderService;
        $this->transactionFactory = $transactionFactory;

        // If service is initialize with a request, set request locale to service
        $this->setLocale(Locale::fetchFromRequestStack($requestStack));
    }

    /**
     * get WPS Customer for the specified company
     * @param Company $company
     * @return string
     * @throws WPSException
     */
    public function createCustomerWPS(Company $company): string
    {
        $customerWPSId = $company->getIdCustomerWPS();
        if ($customerWPSId) {
		        $this->savePspCustomerCustomAttributes(
			        userId: (int) $company->getIzbergUserId(),
			        corporateName: $company->getName(),
			        companyExternalId: $company->getIdentification(),
			        language: $this->getLocale(),
			        idCustomer: $customerWPSId
		        );

            return $customerWPSId;
        }

        /** @var Contact $mainContact */
        $mainContact = $company->getMainContact();

        ////////////////////////////////////////////////////////////////
        /// Get billing address
        ////////////////////////////////////////////////////////////////
        /** @var Address $address  */
        $address = $company->getBillingAddress();
        $countryCode = null;
        if ($address === null){
            $address = $company->getMainAddress();
        }

        if ($address === null){
            $this->logger->error("unable to get valid shipping email address for company with id ".$company->getId(),
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::PAYMENT_GENERAL_ERROR,
                    LogUtil::USER_NAME=>$this->securityService->getCurrentUserName(),
                    self::LOG_STATUS_CODE => "WPS_USER_CREATION",
                    "companyId" => $company->getId()
                ])
        );
            throw new WPSException("unable to get valid shipping email address for company with id ".$company->getId());
        }

        ////////////////////////////////////////////////////////////////
        /// Get country code
        ////////////////////////////////////////////////////////////////

        /** @var string $countryCode */
        $countryCode = null;
        if ($company->getMainAddress()->getCountry() !== null ){
          $countryCode = $company->getMainAddress()->getCountry()->getIzbergCode();
        }
        if ($countryCode === null){
            $this->logger->error("unable to get valid country code for company with id ".$company->getId(),
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::PAYMENT_GENERAL_ERROR,
                    LogUtil::USER_NAME=>$this->securityService->getCurrentUserName(),
                    "companyId" => $company->getId(),
                    self::LOG_STATUS_CODE => "WPS_ERROR"
                ])
            );
            throw new WPSException("unable to get valid country code for company with id ".$company->getId());
        }

        ////////////////////////////////////////////////////////////////
        /// Get WPS code
        ////////////////////////////////////////////////////////////////
        $wpsCode = $this->generateWpsCode($company);

        //CALL WSP API
        $id = $this->customerAPI->createCustomer(
            $wpsCode,
            $mainContact->getEmail(),
            $company->getName(),
            $company->getIdentification(),
            $this->getLocale(),
            $address->getCity(),
            $countryCode,
            $address->getAddress(),
            $address->getZipCode()
        );

        $this->savePspCustomerCustomAttributes(
            userId: (int) $company->getIzbergUserId(),
            corporateName: $company->getName(),
            companyExternalId: $company->getIdentification(),
            language: $this->getLocale(),
            idCustomer: $id
        );

        $this->logger->info(
            "WPS user created",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME=>EventNameEnum::PAYMENT_GENERAL_ERROR,
                LogUtil::USER_NAME=>$this->securityService->getCurrentUserName(),
                "idCustomerWPS" => $id,
                "wpsCode" => $wpsCode,
                self::LOG_STATUS_CODE => "WPS_USER_CREATION"
            ])
        );

        // Save it in DB
        $company->setIdCustomerWPS($id);
        $company->setWpsCode($wpsCode);
        $this->em->flush();
        return $id;
    }

    /**
     * @param \AppBundle\Model\Order\Order $order
     * @param Company $company
     * @param string|null $returnUrl
     * @return Transaction
     * @throws WPSException
     * @throws PaymentException
     */
    public function createTransaction(\AppBundle\Model\Order\Order $order, Company $company, ?string $returnUrl = null): Transaction
    {
        $this->createCustomerWPS($company);
        $payment = $this->paymentService->paymentForOrder($order);

        $numTransaction = count($order->getMerchantOrders());
        //if we need to compute some installments
        $installments =[];

        ///////////////////////////////////////////////////////////
        /// CHECKING PAYMENT CONDITIONS
        ///////////////////////////////////////////////////////////
        $paymentCondition = null;
        if ($payment->isTermPayment()) {
            $term = $this->paymentService->fetchPaymentTerm("en");
            $installments[] = DateUtil::addDaysEndOfMonth(new \DateTime(), $term->getDays());
            $paymentCondition = self::WPS_PC_PE_VIREMENT;
        }

        if ($payment->isPrePaymentByCreditCard()) {
            $paymentCondition = self::WPS_PC_PP_CB;
        }

        if ($payment->isPrePaymentByBankWireTransfer()) {
            $paymentCondition = self::WPS_PC_PP_VIREMENT;
        }

        ///////////////////////////////////////////////////////////
        /// NOW WE CAN CREATE OUR TRANSACTION
        ///////////////////////////////////////////////////////////

        /**
        * @var TransactionResponse $transaction
        */
        $transactionResponse = $this->createRawTransaction(
            $order->getAmountVatIncluded(),
            self::CODE_ORDER ."/",
            strval($order->getIzbergId()),
            $order->getCurrency(),
            $company->getIdCustomerWPS(),
            $this->getLocale(),
            $installments,
            $paymentCondition,
            $numTransaction,
            $returnUrl,
            false
        );

        ///////////////////////////////////////////////////////////
        /// LOG TRANSACTION CREATION
        ///////////////////////////////////////////////////////////
        $this->logger->info(
            "WPS transaction created",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME=>EventNameEnum::PAYMENT_PROCESS,
                LogUtil::USER_NAME=>$this->securityService->getCurrentUserName(),
                self::LOG_PAYMENT_STEP => "createTransaction",
                "amount" => $order->getAmountVatIncluded(),
                "transactionCode" => self::CODE_ORDER . strval($order->getIzbergId()),
                "currency" => $order->getCurrency(),
                "paymentCondition" => $paymentCondition,
                "transactionId" => $transactionResponse->getCodeTransactionWps(),
                'orderId' => $order->getIzbergId(),
            ])
        );

        $transaction = $this->transactionFactory->buildFromTransactionResponse($transactionResponse, $order->getIzbergId(), $payment->getIzbergId());

        $this->paymentService->saveTransactionToPayment($transaction, $payment);

        return $transaction;
    }

    /**
     * Just invoke wps to create the transaction
     *
     * @param $amount
     * @param $codeQualifier
     * @param $code
     * @param $currency
     * @param $idCustomerWPS
     * @param $customerLanguage
     * @param array $installments (could be an array of DateTime)
     * @param $paymentCondition (PE_SEPA, PP_CB, PE_VIREMENT)
     * @param $subTransationsNumber (minimum 1)
     * @param string|null $normalReturnUrl The url to which the customer will be redirected after a payment with SIPS, required if CB
     *
     * @return TransactionResponse
     *
     * @throws Exception
     */
    public function createRawTransaction(
        $amount,
        $codeQualifier,
        $code,
        $currency,
        $idCustomerWPS,
        $customerLanguage,
        array $installments,
        $paymentCondition,
        $subTransationsNumber,
        ?string $normalReturnUrl = null,
        bool $logEnable = true
    ) :TransactionResponse
    {
        $transaction = $this->transactionAPI->createTransaction(
            $amount,
            $codeQualifier.$code,
            $currency,
            $idCustomerWPS,
            $customerLanguage,
            $installments,
            $paymentCondition,
            $subTransationsNumber,
            $normalReturnUrl
        );

        if (!$transaction) {
            throw new Exception('Cannot create webhelp transaction');
        }

        if ($logEnable) {
            //log transaction
            $this->logger->info("WPS transaction created",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::PAYMENT_PROCESS,
                    LogUtil::USER_NAME=>$this->securityService->getCurrentUserName(),
                    self::LOG_PAYMENT_STEP => "createTransaction",
                    "amount" => $amount,
                    "transactionCode" => $codeQualifier.$code,
                    "currency" => $currency,
                    "paymentCondition" => $paymentCondition,
                    "transactionId" => $transaction->getCodeTransactionWps()
                ])
            );
        }

        return $transaction;
    }


    /***
     *
     * Create a sub-transaction : a part of the transaction, one for each merchant. Charge if payment without card.
     *
     * @param $amount
     * @param string $code Must be unique per transaction, will not be used elsewhere. (max length 255)
     * @param string $codeTransactionWps The transaction code used for the communication with WPS. (length max 35, alphanumeric)
     * @param string $idMerchantWps The merchant code used for the communication with WPS
     * @param string $orderId The order id. (max length 255)
     *
     * @return SubTransactionResponse
     */
    public function createSubTransactions($amount, $code, $codeTransactionWps, $idMerchantWps, $orderId)
    {
        $this->logger->info(
            'WPS subtransaction creation process started',
            LogUtil::buildContext([
                LogUtil::EVENT_NAME=>EventNameEnum::PAYMENT_PROCESS,
                LogUtil::USER_NAME=>$this->securityService->getCurrentUserName(),
                self::LOG_PAYMENT_STEP => "createSubTransaction started",
                'amount' => $amount,
                'wpsTransactionId' => $codeTransactionWps,
                'izbergMerchantId' => $idMerchantWps,
                'orderId' => $orderId,
            ])
        );

        $subTransaction = $this->transactionAPI->createSubTransaction($amount, $code, $codeTransactionWps, $idMerchantWps, $orderId);

        //log sub transaction creation
        $this->logger->info("WPS subtransaction created",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME=>EventNameEnum::PAYMENT_PROCESS,
                LogUtil::USER_NAME=>$this->securityService->getCurrentUserName(),
                self::LOG_PAYMENT_STEP => "createSubTransaction",
                'amount' => $amount,
                'wpsTransactionId' => $codeTransactionWps,
                'izbergMerchantId' => $idMerchantWps,
                'subTransactionId' => $subTransaction->getCodeSubTransactionWps(),
                'reason' => $subTransaction->getReason(),
                'status' => $subTransaction->getStatus(),
                'orderId' => $orderId,
            ])
        );

        return $subTransaction;
    }


    /**
     * Create all sub transactions needed for this izberg order
     * @param Transaction $transaction
     * @param \AppBundle\Model\Order\Order $order
     * @param string $gatewayType
     */
    public function createSubTransactionsForOrder(
        Transaction $transaction,
        \AppBundle\Model\Order\Order $order,
        string $gatewayType
    )
    {
        ////////////////////////////////////////////////////////////////////////
        //iterate on each merchant order and create associated sub transactions
        ////////////////////////////////////////////////////////////////////////
        /** @var \AppBundle\Model\Order\MerchantOrder $merchantOrder */
        foreach ($order->getMerchantOrders() as $merchantOrder){

            //fetch the merchant
            /** @var \stdClass $merchant */
            $merchant = $this->merchantApi->getMerchant($merchantOrder->getMerchantId());

            $pspMerchantIdAttributeName = $this->customAttributes->getPspMerchantId();
            $pspMerchantId = $this->merchantApi->getMerchantCustomAttribute($merchant->id, $pspMerchantIdAttributeName);

            if (!$pspMerchantId) {
                $errorMessage = "createSubTransaction: error while creating sub transaction: merchant has no wps identifier";

                $this->logger->error(
                    $errorMessage,
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME=>EventNameEnum::PAYMENT_GENERAL_ERROR,
                        LogUtil::USER_NAME=>$this->securityService->getCurrentUserName(),
                        "merchantId" => $merchant->id,
                        self::LOG_STATUS_CODE => "createSubTransactionError",
                        "transactionCode" => $transaction->getWpsTransactionCode()
                    ])
                );

                throw new ApiException($errorMessage);
            }

            ////////////////////////////////////////////////////////////////////////
            //CREATE WPS sub transactions
            ////////////////////////////////////////////////////////////////////////
            $subTransaction = $this->transactionAPI->createSubTransaction(
                $merchantOrder->getAmountVatIncluded(),
                self::CODE_QUALIFIER_MERCHANT_ORDER.strval($merchantOrder->getId()),
                $transaction->getWpsTransactionCode(),
                $pspMerchantId,
                $order->getIdNumber()
            );

            //log sub transaction creation
            $this->logger->info("WPS subtransaction created",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::PAYMENT_PROCESS,
                    LogUtil::USER_NAME=>$this->securityService->getCurrentUserName(),
                    self::LOG_PAYMENT_STEP => "createSubTransaction",
                    "amount" => $merchantOrder->getAmountVatIncluded(),
                    "transactionCode" =>  $transaction->getWpsTransactionCode(),
                    "izbergMerchantId" => $merchant->id,
                    "subTransactionId" => $subTransaction->getCodeSubTransactionWps()
                ]));

            ////////////////////////////////////////////////////////////////////////
            //Create izberg gateway
            ////////////////////////////////////////////////////////////////////////
            $gatewayId = $this->gatewayApi->createGateway($subTransaction->getCodeSubTransactionWps(),
                $gatewayType,
                GatewayApi::QUALIFIER_MERCHANT_ORDER,
                strval($merchantOrder->getId()));

            //log PSP Gateway creation
            $this->logger->info("Izberg PSP Gateway created",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::PAYMENT_PROCESS,
                    LogUtil::USER_NAME=>$this->securityService->getCurrentUserName(),
                    self::LOG_PAYMENT_STEP => "createPSPGateway",
                    "gatewayQualifier" => GatewayApi::QUALIFIER_MERCHANT_ORDER,
                    "value" => $merchantOrder->getId(),
                    "gatewayType" => $gatewayType,
                    "gatewayId" => $gatewayId,
                    "gatewayExternalId" => $subTransaction->getCodeSubTransactionWps(),
                    'merchantOrderId' => $merchantOrder->getId(),
                ])
            );

            ////////////////////////////////////////////////////////////////////////
            //Update reconciliation Key on the izberg merchant Order
            ////////////////////////////////////////////////////////////////////////
            $this->attributeService->updateReconciliationAttribute(
                $this->attributeService->getReconciliationAttributeId(),
                $merchantOrder->getId(),
                $transaction->getReconciliationKey()
            );
        }
    }

    /**
     * business code run when receiving an automaticResponse push from WSP with the "COMPLETE" status
     * @param AutomaticResponse $automaticResponse
     * @throws WPSPushException
     */
    public function manageCompleteAutomaticResponse(AutomaticResponse $automaticResponse){
        $order = null;
        try {
            $this->logAutomaticTransaction($automaticResponse);

            //get the payment object from izberg api and check it
            $payment = $this->paymentService->findIzbergPaymentFromWpsTransaction($automaticResponse->getCodeTransactionWps());
            if ($payment === null || $payment->order === null || $payment->order->id === null) {
                $this->logger->error(" * unable to get payment from izberg for this transactionCode: payment or payment.order is null",
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME=>EventNameEnum::WEBHELP_PUSH,
                        "transactionCode" => $automaticResponse->getCodeTransactionWps()
                    ])
                );
                throw new WPSException('unable to get payment from izberg for this transactionCode: ' . $automaticResponse->getCodeTransactionWps());
            }


            //now we can fetch the order
            $order = $this->orderAPI->fetchOrder($payment->order->id);
            $this->paymentService->changePaymentStatusToPaymentPendingAuthorization($order->getPayment());

            //we can now create sub transactions for each merchant order of our order
            /** @var MerchantOrder $merchantOrder */

            //$merchantToNotify will be used later to notify the merchant: avoid to perform useless call to the izberg api
            $merchantsToNotify= [];
            foreach ($order->getMerchantOrders() as $merchantOrder) {

                /** @var MerchantOrder $fetchedMerchantOrder */
                $fetchedMerchantOrder = $this->orderAPI->fetchMerchantOrderById($merchantOrder->getId());

                /** @var \stdClass $merchant */
                $merchant = $this->merchantApi->getMerchant($fetchedMerchantOrder->getMerchant()->getId());

                //if merchant is valid add him to be notify later (if all subtransac are ok)
                if (count($merchant->addresses) > 0 && $merchant->addresses[0]->contact_email != null) {
                    $merchantsToNotify [$merchant->addresses[0]->contact_email] =
                        [
                            MailService::FIRST_NAME_VAR => $merchant->addresses[0]->contact_first_name,
                            MailService::LAST_NAME_VAR => $merchant->addresses[0]->contact_last_name,
                            'orderNumber' => $order->getIdNumber(),
                            'paymentNbHoursBeforeRefund' => $this->paymentNbHoursBeforeRefund,
                            'local' => $merchant->prefered_language,
                            'buyer' => $fetchedMerchantOrder->getUser()->getFirstName()
                        ];
                } else {
                    $this->logger->error(
                        "can't find merchant email address",
                        LogUtil::buildContext([
                            LogUtil::EVENT_NAME=>EventNameEnum::PAYMENT_PROCESS,
                            "merchant" => $merchant->id,
                            'merchantOrderId' => $merchantOrder->getId(),
                        ])
                    );

                }

                $pspMerchantIdAttributeName = $this->customAttributes->getPspMerchantId();
                $pspMerchantId = $this->merchantApi->getMerchantCustomAttribute($merchant->id, $pspMerchantIdAttributeName);

                if (!$pspMerchantId) {
                    $errorMessage = sprintf(
                        " * the merchant with id %s has no attribute %s => can't create transaction",
                        $merchant->id,
                        $pspMerchantIdAttributeName
                    );

                    $this->logger->error(
                        $errorMessage,
                        LogUtil::buildContext([
                            "merchantId" => $merchant->id,
                            "transactionCode" => $automaticResponse->getCodeTransactionWps()
                        ])
                    );

                    throw new WPSPushException($errorMessage);
                }

                ////////////////////////////////////////////////////////////////////////
                /// Create subTransaction
                ////////////////////////////////////////////////////////////////////////
                $subTransaction = $this->createSubTransactions(
                    $fetchedMerchantOrder->getAmountVatIncluded(),
                    self::CODE_QUALIFIER_MERCHANT_ORDER . strval($fetchedMerchantOrder->getId()),
                    $automaticResponse->getCodeTransactionWps(),
                    $pspMerchantId,
                    $order->getIdNumber()
                );

                // Ok
                if ($subTransaction->getStatus() == 'CAPTURED') {
                    ////////////////////////////////////////////////////////////////////////
                    //Create izberg gateway
                    ////////////////////////////////////////////////////////////////////////
                    $this->gatewayApi->createGateway($subTransaction->getCodeSubTransactionWps(),
                        GatewayApi::TYPE_PREPAYMENT,
                        GatewayApi::QUALIFIER_MERCHANT_ORDER,
                        $fetchedMerchantOrder->getId());


                } else {
                    $this->logger->error(" * Invalid WPS status for creating sub transaction",
                        LogUtil::buildContext([
                            LogUtil::EVENT_NAME=>EventNameEnum::WEBHELP_PUSH,
                            "transactionCode" => $automaticResponse->getCodeTransactionWps(),
                            "createSubTransactionStatus" => $subTransaction->getStatus()
                        ])
                );
                    throw new ApiException("manageCompleteAutomaticResponse - Invalid WPS status for creating sub transaction: ".$subTransaction->getStatus());
                }

            }

            //here all sub transactions has been successfully created! We wan authorize the order
            $this->orderService->authorizeOrder($order);

// DISABLE EMAIL NOTIFICATION - MANAGED BY IZBERG (https://jira.open-groupe.com/browse/S1TMA-112)
//            //and we also notify all sellers
//            foreach ($merchantsToNotify as $merchantEmail => $params) {
//                $this->mailService->sendEmailMessage(MailService::SELLER_NEW_COMMAND_PAID,
//                    $params['local'], $merchantEmail, $params
//                );
//            }



        }catch(Exception $e){
            $this->logger->error(" * Unknown error occurred while processing WPS automatic response push ",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::WEBHELP_PUSH,
                            "message" => $e->getMessage(),
                    "transactionCode" => $automaticResponse->getCodeTransactionWps()
                        ])
            );
            //if order has been identified, we cancel it
            if ($order !== null) {
                $this->logger->error(" * Cancelling order from izberg...",
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME=>EventNameEnum::WEBHELP_PUSH,
                        "orderId" => $order->getId(),
                        "transactionCode" => $automaticResponse->getCodeTransactionWps()
                    ])
                );
                $this->orderAPI->cancelOrder($order->getId());
            }
            throw new WPSPushException("Unknown error occurred while processing WPS automatic response push: ".$e->getMessage());
        }

    }

    /**
     * business code run when receiving an automaticResponse push from WSP with the "REFUSED" status
     * @param AutomaticResponse $automaticResponse
     * @throws WPSPushException
     */
    public function manageRefusedAutomaticResponse(AutomaticResponse $automaticResponse){
        //writing a log
        $this->logAutomaticTransaction($automaticResponse);

        //cancelling order
        $this->cancelOrderFromAutomaticResponse($automaticResponse);
    }

    /**
     * business code run when receiving an automaticResponse push from WSP with the "ABORTED" status
     * @param AutomaticResponse $automaticResponse
     * @throws WPSPushException
     */
    public function manageAbortedAutomaticResponse(AutomaticResponse $automaticResponse){
        //writing a log
        $this->logAutomaticTransaction($automaticResponse);

        //cancelling order
        $this->cancelOrderFromAutomaticResponse($automaticResponse);
    }

    /**
     * @param AutomaticResponse $automaticResponse
     * @throws WPSPushException
     */
    private function cancelOrderFromAutomaticResponse(AutomaticResponse $automaticResponse)
    {
        //get the payment object from izberg api and check it
        $payment = $this->paymentService->findIzbergPaymentFromWpsTransaction($automaticResponse->getCodeTransactionWps());

        if ($payment === null || $payment->order === null || $payment->order->id === null) {
            $this->logger->error(" * enable to get payment from izberg for this transactionCode: payment or payment.order is null",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::WEBHELP_PUSH,
                    "transactionCode" => $automaticResponse->getCodeTransactionWps()
                ])
            );
            throw new WPSPushException('enable to get payment from izberg for this transactionCode: ' . $automaticResponse->getCodeTransactionWps());
        }

        $this->logger->info(" + Cancelling order from izberg...",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME=>EventNameEnum::WEBHELP_PUSH,
                "orderId" => $payment->order->id,
                "transactionCode" => $automaticResponse->getCodeTransactionWps()
            ])
        );

        $this->orderService->cancelOrderById($payment->order->id);
    }

    /**
     * write a log to keep information about the state of a transaction when receiving push notification from wps
     * @param AutomaticResponse $automaticResponse
     */
    private function logAutomaticTransaction (AutomaticResponse $automaticResponse){
        $this->logger->info(" + transaction has been ".$automaticResponse->getStatus(),
            LogUtil::buildContext([
                LogUtil::EVENT_NAME=>EventNameEnum::WEBHELP_PUSH,
                self::LOG_WPS_EVENT => "+ automaticResponsePushAction",
                "transactionId" => $automaticResponse->getCodeTransactionWps(),
                "reason" => $automaticResponse->getReason(),
                "ticketCard" => $automaticResponse->getTicketCard(),
                "status" => $automaticResponse->getStatus()
            ])
        );
    }


    /***
    * @param $locale
    */
    public function setLocale($locale)
    {
        $this->locale = $locale;
    }

    /***
    * @return string
    */
    public function getLocale()
    {
        return $this->locale;
    }

    /**
     * @param Company $company generate a unique code for this company
     * @return string unique code for this company
     */
    private function generateWpsCode($company){
        return hash(self::HASH_ALGORITHM, strval($company->getId()).strval(time()) );
    }

    public function charge($amount, $codeSubTransactionWPS){
        return $this->transactionAPI->charge($amount, $codeSubTransactionWPS);
    }

    public function getSubTransactionGateway($external_id){
        return $this->gatewayApi->getGateway($external_id);
    }

    public function payGateway($external_id){
        return $this->gatewayApi->payGateway($external_id);
    }

    public function changeGatewayExtraData($external_id, $data){
        return $this->gatewayApi->changeGatewayExtraData($external_id, $data);
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    private function savePspCustomerCustomAttributes(
        int $userId,
        string $corporateName,
        string $companyExternalId,
        string $language,
        string $idCustomer,
    ): void {
        $attributes = [
            new \Open\IzbergBundle\Model\CustomerAttribute("corporate_name", $corporateName),
            new \Open\IzbergBundle\Model\CustomerAttribute("customer_external_id", $companyExternalId),
            new \Open\IzbergBundle\Model\CustomerAttribute("default_language", $language),
            new \Open\IzbergBundle\Model\CustomerAttribute("psp_customer_id", $idCustomer),
        ];

        try {
            $this->attributeService->createOrUpdateCustomerAttributes($userId, $attributes);
        } catch (Exception $exception) {
            $this->logger->alert(
                'Adding customer custom attributes failed when creating PSP customer',
                [
                    'custom-attribute-values' => $attributes,
                    'izberg-user-id' => $userId,
                    'exceptionCode' => $exception->getCode(),
                    'exceptionMessage' => $exception->getMessage(),
                    'exceptionTrace' => $exception->getTrace(),
                ]
            );
        }
    }
}
