<?php

namespace AppBundle\Services;

use Open\IzbergBundle\Api\MerchantOrderCommissionRuleApi;

class FeesOperatorShippingService
{
    /**
     * @var int|null
     */
    private $euroCommisionId;

    /**
     * @var int|null
     */
    private $usdCommissionId;

    /**
     * @var array
     */
    private $fees;

    /**
     * @var MerchantOrderCommissionRuleApi
     */
    private $merchantOrderCommissionRuleApi;

    public function __construct(MerchantOrderCommissionRuleApi $merchantOrderCommissionRuleApi)
    {
        $this->merchantOrderCommissionRuleApi = $merchantOrderCommissionRuleApi;
        $this->euroCommisionId = null;
        $this->usdCommissionId = null;
        $this->fees = null;
    }

    public function findByCurrency(string $currency)
    {
        $this->initFees();
        $currency = strtoupper($currency);
        return $this->fees[$currency] ?? 0;
    }

    public function setEuroCommisionId(?int $euroCommisionId): self
    {
        $this->euroCommisionId = $euroCommisionId;
        return $this;
    }

    public function setUsdCommissionId(?int $usdCommissionId): self
    {
        $this->usdCommissionId = $usdCommissionId;
        return $this;
    }

    private function initFees()
    {
        if ($this->fees) {
            return $this->fees;
        }

        $this->fees = [];

        $fetchCommissionRateValue = function(string $currency, int $commissionId) {
            $commission = $this->merchantOrderCommissionRuleApi->find($commissionId, true);
            if ($commission) {
                $this->fees[$currency] = $commission->getCommissionRateValue();
            }
        };

        // init Euro fee
        if ($this->euroCommisionId) {
            call_user_func($fetchCommissionRateValue, 'EUR', $this->euroCommisionId);
        }

        // init USD fee
        if ($this->usdCommissionId) {
            call_user_func($fetchCommissionRateValue, 'USD', $this->usdCommissionId);
        }
    }
}
