<?php

namespace AppBundle\Services;

use AppBundle\Entity\Storage;
use AppBundle\Repository\StorageRepository;

class StorageService
{
    /**
     * @var StorageRepository
     */
    private $storageRepository;

    public function __construct(StorageRepository $storageRepository)
    {
        $this->storageRepository = $storageRepository;
    }

    public function get(string $key): ?string
    {
        return $this->storageRepository->getStorageValueFromKey($key);
    }

    public function set(string $key, string $value)
    {
        $storage = $this->storageRepository->findOneBy(['key' => $key]);

        if ($storage === null) {
            $storage = (new Storage())
                ->setKey($key);
        }

        $storage->setValue($value);
        $this->storageRepository->persist($storage);
        $this->storageRepository->flush();
    }

    public function remove(string $key)
    {
        $storage = $this->storageRepository->findOneBy(['key' => $key]);
        if ($storage !== null) {
            $this->storageRepository->remove($storage);
        }
    }
}
