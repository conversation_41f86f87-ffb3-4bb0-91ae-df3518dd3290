<?php

namespace AppBundle\Services;

use AppBundle\Command\CsvToArrayService;
use AppBundle\Entity\Company;
use AppBundle\Entity\Merchant;
use AppBundle\Entity\SpecificPrice;
use AppBundle\Entity\User;
use AppBundle\Entity\Vendor;
use AppBundle\Exception\SpecificPriceException;
use AppBundle\Model\Offer;
use AppBundle\Repository\CompanyRepository;
use AppBundle\Repository\MerchantRepository;
use AppBundle\Repository\SpecificPriceRepository;
use AppBundle\StreamFilter\StreamFilterNewlines;
use DateTime;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use InvalidArgumentException;
use Open\BCEBundle\Service\BCEService;
use Open\FrontVendorBundle\Model\BuyerSpecificPrice;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Throwable;

class SpecificPriceService implements LoggerAwareInterface
{
    private const CSV_MIME_TYPE = ['text/csv', 'text/plain', 'application/vnd.ms-excel'];
    private const SPECIFIC_PRICE_CSV_HEADER = [
        'Buyer Identification Number',
        'Vendor Reference',
        'Incoterm',
        'Country',
        'Unit Price',
        'Threshold 1',
        'Price 1',
        'Threshold 2',
        'Price 2',
        'Threshold 3',
        'Price 3',
        'Threshold 4',
        'Price 4',
        'MOQ',
        'Validity Date',
        'Total leadtime for customer',
        'Frame Contract'
    ];

    private LoggerInterface $logger;
    private EntityManagerInterface $entityManager;
    private SpecificPriceRepository $specificPriceRepository;
    private BCEService $BCEService;
    private UserBddService $userService;
    private CsvToArrayService $csvToArrayService;
    private SpecificPriceMapper $specificPriceMapper;
    private CompanyRepository $companyRepository;
    private MerchantRepository $merchantRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        BCEService $BCEService,
        UserBddService $userService,
        CsvToArrayService $csvToArrayService,
        SpecificPriceMapper $specificPriceMapper,
        CompanyRepository $companyRepository
    )
    {
        $this->entityManager = $entityManager;
        $this->specificPriceRepository = $entityManager->getRepository(SpecificPrice::class);
        $this->BCEService = $BCEService;
        $this->userService = $userService;
        $this->specificPriceMapper = $specificPriceMapper;
        $this->csvToArrayService = $csvToArrayService;
        $this->companyRepository = $companyRepository;
        $this->merchantRepository = $entityManager->getRepository(Merchant::class);
    }

    public function cleanPerMerchantId($merchantId)
    {
        $q = $this->entityManager
            ->createQueryBuilder()
            ->delete(\AppBundle\Entity\SpecificPrice::class, 'a')
            ->where('a.IZBmerchantId = :merchantId')
            ->setParameter('merchantId', $merchantId)
            ->getQuery();

        return $q->getScalarResult();
    }

    /***
     * @param $price
     *
     */
    public function save($price)
    {
        $this->entityManager->persist($price);
        $this->entityManager->flush();
    }

    public function bulkUpdateOfferSpecificPrices(Company $company, array $offers): array
    {
        return array_map(fn (Offer $offer) => $this->updateOfferSpecificPrices($company, $offer), $offers);
    }

    public function updateOfferSpecificPrices(Company $company, Offer $offer): Offer
    {
        $specificPrice = $this->specificPriceRepository->findOneBy(
            [
                'companyIdentification' => $company->getIdentification(),
                'vendorReference' => $offer->getSellerRef(),
                'IZBmerchantId' => $offer->getMerchant()->getId(),
                'incoterm' => $offer->getIncoterm(),
                'country' => $offer->getIncotermCountry(),
            ]
        );

        if (!$specificPrice) {
            return $offer;
        }

        // check validity date
        // when validity date is null then the validity date is ignored
        // when validity date is over then no specific price is applied to the offer
        $validityDate = $specificPrice->getValidityDate();
        if ($validityDate !== null) {
            $now = new DateTimeImmutable();
            if ($validityDate < $now) {
                return $offer;
            }
        }

        if ($specificPrice->getMoq()) {
            $offer->setMoq($specificPrice->getMoq());
        }

        if ($specificPrice->getBasicPrice()) {
            $usdBasicPrice = $specificPrice->getBasicPrice();
            $eurBasicPrice = $this->BCEService->fromUSDToEUR($usdBasicPrice);

            if ($offer->getCurrency() == BCEService::CURR_EUR) {
                $eurBasicPrice = $specificPrice->getBasicPrice();
                $usdBasicPrice = $this->BCEService->fromEURToUSD($eurBasicPrice);
            }

            $offer->setPrice(BCEService::CURR_EUR, $eurBasicPrice);
            $offer->setPrice(BCEService::CURR_USD, $usdBasicPrice);

            $offer->removeThresholds();

            foreach ($specificPrice->getThresholdsAndPrices() as [$threshold, $price]) {
                $offer->addThreshold($threshold, $price);
            }
        }

        $delayOfDelivery = $specificPrice->getDelayOfDelivery();
        if ($delayOfDelivery !== null && $delayOfDelivery > 0) {
            $offer->setDeliveryTime($specificPrice->getDelayOfDelivery());
        }
        $offer->setNoPrice(false);

        $offer->setFrameContract($specificPrice->getFrameContract());

        $validityDate = $specificPrice->getValidityDate();
        if ($validityDate !== null) {
            $validityDate = DateTimeImmutable::createFromMutable($validityDate);
        }
        $offer->setPriceValidityDate($validityDate);

        return $offer;
    }

    public function injectSpecificPriceComparisonSheet(User $user, $items)
    {
        $company = $user->getCompany();
        foreach ($items as &$item) {
            $specificPrice = null;
            $limited = $item['limited'];

            if (!$limited) {
                $specificPrice = $this->specificPriceRepository->findOneBy(
                    [
                        'companyIdentification' => $company->getIdentification(),
                        'vendorReference' => $item['merchant_reference'],
                        'IZBmerchantId' => $item['merchant_id'],
                        'incoterm' => $item['incoterm'],
                        'country' => $item['incoterm_country'],
                    ]
                );
            }

            if (!$specificPrice) {
                continue;
            }

            // check validity date
            // when validity date is null then the validity date is ignored
            // when validity date is over then no specific price is applied to the offer
            $validityDate = $specificPrice->getValidityDate();
            if ($validityDate !== null) {
                $now = new DateTimeImmutable();
                if ($validityDate < $now) {
                    continue;
                }
            }

            $item["moq"] = $specificPrice->getMoq();
            if ($item['currency'] == BCEService::CURR_EUR) {
                $item['prices'] = array(BCEService::CURR_EUR => $specificPrice->getBasicPrice(), BCEService::CURR_USD => $this->BCEService->fromEURToUSD($specificPrice->getBasicPrice()));
            } else {
                $item['prices'] = array(BCEService::CURR_USD => $specificPrice->getBasicPrice(), BCEService::CURR_EUR => $this->BCEService->fromUSDToEUR($specificPrice->getBasicPrice()));
            }

            $item['unitPrice'] = $specificPrice->getBasicPrice();
            $delayOfDelivery = $specificPrice->getDelayOfDelivery();
            if ($delayOfDelivery !== null && $delayOfDelivery > 0) {
                $item['delivery_time'] = $specificPrice->getDelayOfDelivery();
            }
        }

        return $items;
    }

    public function specificPrice(Company $company, Offer $offer, int $quantity): float
    {
        $offer = $this->updateOfferSpecificPrices($company, $offer);
        return $offer->getPriceForQuantity($quantity);
    }

    private function isSpecificPriceRemovable(SpecificPrice $specificPrice): bool
    {
        return (
            !empty($specificPrice->getCompanyIdentification()) &&
            empty($specificPrice->getVendorReference()) &&
            empty($specificPrice->getIncoterm()) &&
            empty($specificPrice->getCountry()) &&
            empty($specificPrice->getBasicPrice()) &&
            empty($specificPrice->getThreshold1()) &&
            empty($specificPrice->getPrice1()) &&
            empty($specificPrice->getThreshold2()) &&
            empty($specificPrice->getPrice2()) &&
            empty($specificPrice->getThreshold3()) &&
            empty($specificPrice->getPrice3()) &&
            empty($specificPrice->getThreshold4()) &&
            empty($specificPrice->getPrice4()) &&
            empty($specificPrice->getMoq()) &&
            empty($specificPrice->getValidityDate()) &&
            empty($specificPrice->getDelayOfDelivery()) &&
            empty($specificPrice->getFrameContract())
        );
    }

    private function isSpecificPriceAddable(SpecificPrice $specificPrice): bool
    {
        return (
            !empty($specificPrice->getCompanyIdentification()) &&
            !empty($specificPrice->getVendorReference()) &&
            !empty($specificPrice->getIncoterm()) &&
            !empty($specificPrice->getCountry()) &&
            !empty($specificPrice->getBasicPrice())
        );
    }

    private function alreadyExistKey(SpecificPrice $specificPrice): bool
    {
        $alreadyExist = false;
        $row = $this->specificPriceRepository->findExistingPrice(
                $specificPrice->getCompanyIdentification(),
                $specificPrice->getVendorReference(),
                $specificPrice->getIncoterm(),
                $specificPrice->getCountry());
        if(!empty($row)){
            $alreadyExist = true;
        }
        return $alreadyExist;
    }

    private function isSpecificPriceSingleRemovable(SpecificPrice $specificPrice): bool
    {
        return (
            !empty($specificPrice->getCompanyIdentification()) &&
            !empty($specificPrice->getVendorReference()) &&
            !empty($specificPrice->getIncoterm()) &&
            !empty($specificPrice->getCountry()) &&
            empty($specificPrice->getBasicPrice()) &&
            empty($specificPrice->getThreshold1()) &&
            empty($specificPrice->getPrice1()) &&
            empty($specificPrice->getThreshold2()) &&
            empty($specificPrice->getPrice2()) &&
            empty($specificPrice->getThreshold3()) &&
            empty($specificPrice->getPrice3()) &&
            empty($specificPrice->getThreshold4()) &&
            empty($specificPrice->getPrice4()) &&
            empty($specificPrice->getMoq()) &&
            empty($specificPrice->getValidityDate()) &&
            empty($specificPrice->getDelayOfDelivery()) &&
            empty($specificPrice->getFrameContract())
        );
    }

    public function removeExistingSpecificPrice($buyerReference, $vendorRef, $incoterm, $country)
    {
        $this
            ->specificPriceRepository
            ->createQueryBuilder('sp')
            ->delete(SpecificPrice::class, 'sp')
            ->where('sp.companyIdentification = :buyerReference')
            ->andWhere('sp.vendorReference = :vendorRef')
            ->andWhere('sp.incoterm = :incoterm')
            ->andWhere('sp.country = :country')
            ->setParameter('buyerReference', $buyerReference)
            ->setParameter('vendorRef', $vendorRef)
            ->setParameter('incoterm', $incoterm)
            ->setParameter('country', $country)
            ->getQuery()
            ->execute();

    }

    public function removeBuyerSpecificPrices(string $buyerReference, int $merchantId)
    {
        $this
            ->specificPriceRepository
            ->createQueryBuilder('sp')
            ->delete(SpecificPrice::class, 'sp')
            ->where('sp.companyIdentification = :buyerReference')
            ->andWhere('sp.IZBmerchantId = :merchantId')
            ->setParameter('buyerReference', $buyerReference)
            ->setParameter('merchantId', $merchantId)
            ->getQuery()
            ->execute();
    }

    public function removeBuyerSpecificPrice(SpecificPrice $specificPrice, int $merchantId): void
    {
        $this
            ->specificPriceRepository
            ->createQueryBuilder('sp')
            ->delete(SpecificPrice::class, 'sp')
            ->where('sp.companyIdentification = :buyerReference')
            ->andWhere('sp.IZBmerchantId = :merchantId')
            ->andWhere('sp.vendorReference = :vendorReference')
            ->andWhere('sp.incoterm = :incoterm')
            ->andWhere('sp.country = :country')
            ->setParameter('buyerReference', $specificPrice->getCompanyIdentification())
            ->setParameter('merchantId', $merchantId)
            ->setParameter('vendorReference', $specificPrice->getVendorReference())
            ->setParameter('incoterm', $specificPrice->getIncoterm())
            ->setParameter('country', $specificPrice->getCountry())
            ->getQuery()
            ->execute();
    }


    public function processImportAutomation($specificPricesPath) {

        // @TODO ENV CONFIG
        $ftp_server = 'ha-ftp-dev.lundimatin.biz';
        $ftp_user_name = 'ha_ftp_devu0001';
        $ftp_user_pass = 'iixhZqiz';

        $fileSystem = new Filesystem();
        if (!$fileSystem->exists($specificPricesPath)) {
            $fileSystem->mkdir($specificPricesPath);
        }
        $downloadDirectory = $specificPricesPath . 'download/';

        if (!$fileSystem->exists($downloadDirectory)) {
            $fileSystem->mkdir($downloadDirectory);
        }

        try {
            $ftp = \ftp_connect($ftp_server);
            $login_result = ftp_login($ftp, $ftp_user_name, $ftp_user_pass);
        } catch (Throwable $e) {
            $this->logger->error("processImportAutomation - " . " err connection: " . $e->getMessage());
            throw $e;
        }

        ftp_pasv($ftp, true);

        foreach($this->merchantRepository->findAll() as $merchant) {
            $merchantDownloadDirectory = $specificPricesPath . 'download/' . $merchant->getId() . '/';
            $remoteMerchantDirectory = "/www/IN/STATION1_PREPROD/". $merchant->getId() . '/';

            try {
                $files = ftp_nlist($ftp, $remoteMerchantDirectory . "*.csv");
            } catch (Throwable $e) {
                $this->logger->error("processImportAutomation - : idMerchant: " . $merchant->getId() . " err: " . $e->getMessage());
                $files = [];
            }

            if(!is_array($files)) {
                continue;
            }

            $this->logger->info("processImportAutomation - : idMerchant: " . $merchant->getId() . " nbFiles: " . count($files));

            foreach ($files as $file) {
                $this->logger->info("processImportAutomation - : idMerchant: " . $merchant->getId() . " processFile");

                if (!$fileSystem->exists($merchantDownloadDirectory)) {
                    $fileSystem->mkdir($merchantDownloadDirectory);
                }
                ftp_get($ftp, $merchantDownloadDirectory.basename($file), $file, FTP_BINARY);
                ftp_delete($ftp, $file);
            }
        }
        ftp_close($ftp);

        foreach($this->merchantRepository->findAll() as $merchant){
            $merchantDownloadDirectory = $specificPricesPath . 'download/'.$merchant->getId().'/';
            if(!is_dir($merchantDownloadDirectory)) {
                continue;
            }
            $merchantArchiveDirectory = $specificPricesPath . 'download/'.$merchant->getId().'/archives/';
            if (!$fileSystem->exists($merchantArchiveDirectory)) {
                $fileSystem->mkdir($merchantArchiveDirectory);
            }

            $files = array_diff(scandir($merchantDownloadDirectory), array('..', '.'));
            foreach ($files as $fileName) {
                $filePath = $merchantDownloadDirectory.$fileName;
                if(is_dir($filePath)) {
                    continue;
                }

                try{
                    $file = new UploadedFile($filePath, $fileName, 'text/csv');
                    $this->updateSpecificPrices($file, $merchant->getId());
                    $fileSystem->rename($filePath, $merchantArchiveDirectory.$fileName.'.'.uniqid("",true));
                } catch (Throwable $e) {
                    $this->logger->error("processImportAutomation - " . " err: " . $e->getMessage());
                    $fileSystem->rename($filePath, $merchantArchiveDirectory.$fileName.'.'.uniqid("",true));
                }
            }
        }
    }

    /**
     * @param UploadedFile $specificPricesUploadedFile
     * @param int $merchantId
     * @throws SpecificPriceException
     */
    public function updateSpecificPrices(UploadedFile $specificPricesUploadedFile, int $merchantId)
    {
        try {
            if (!in_array($specificPricesUploadedFile->getClientMimeType(), self::CSV_MIME_TYPE)) {
                throw new FileException('Invalid uploaded file. File type is not csv');
            }

            $specificPricesAsArray = $this->csvToArrayService->convert(
                $specificPricesUploadedFile->getPathname(),
                17,
                self::SPECIFIC_PRICE_CSV_HEADER
            );

            $rows = $this->specificPriceMapper->mapToSpecificPriceEntity($specificPricesAsArray);

            /** @var SpecificPrice $specificPrice */
            foreach ($rows as $specificPrice) {
                if ($this->isSpecificPriceRemovable($specificPrice)) {
                    $this->removeBuyerSpecificPrices($specificPrice->getCompanyIdentification(), $merchantId);
                }

                if ($this->isSpecificPriceSingleRemovable($specificPrice)) {
                    $this->removeBuyerSpecificPrice($specificPrice, $merchantId);
                }

                if ($this->isSpecificPriceAddable($specificPrice)) {
                    if($this->alreadyExistKey($specificPrice)){
                        $this->removeExistingSpecificPrice($specificPrice->getCompanyIdentification(),
                            $specificPrice->getVendorReference(),
                            $specificPrice->getIncoterm(),
                            $specificPrice->getCountry());
                    }
                    $specificPrice->setIZBmerchantId($merchantId);
                    $specificPrice->setCreatedAt(new DateTime());

                    $company = $this->companyRepository->findOneBy([
                        'identification' => $specificPrice->getCompanyIdentification()
                    ]);

                    $specificPrice->setCompany($company);
                    $company->addSpecificPrice($specificPrice);

                    $this->entityManager->persist($specificPrice);
                }

            }
            $this->entityManager->flush();

        } catch (InvalidArgumentException | FileException | ORMException | OptimisticLockException $exception) {
            $msg = "Error occurred during persisting specific prices";
            $this->logger->error($msg,
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>'SPECIFIC_PRICES_PERSISTENCE',
                    'message' => $exception->getMessage()
                ])
            );
            throw new SpecificPriceException($exception->getMessage());
        }
    }

    public function getSpecificPricesAsBuyerSpecificPrices(Vendor $merchant): array
    {
        $queryBuilder = $this->specificPriceRepository->createQueryBuilder('sp');
        $queryBuilder
            ->select('sp.companyIdentification, count(sp.companyIdentification) as nbProducts')
            ->where('sp.IZBmerchantId = :merchantId')
            ->setParameter('merchantId', $merchant->getId())
            ->groupBy('sp.companyIdentification');

        return array_map(
            function ($row) use ($merchant) {
                $company = $this->companyRepository->findOneBy([
                    'identification' => $row['companyIdentification']
                ]);

                $queryBuilder = $this->specificPriceRepository->createQueryBuilder('sp');
                $queryBuilder
                    ->select(' max(sp.createdAt) as lastUpload')
                    ->where('sp.IZBmerchantId = :merchantId')
                    ->andWhere('sp.companyIdentification = :companyIdentification')
                    ->setParameter('merchantId', $merchant->getId())
                    ->setParameter('companyIdentification', $row['companyIdentification']);

                $lastUploadDate = $queryBuilder->getQuery()->getResult();
                $lastUploadDate = array_shift($lastUploadDate)['lastUpload'];
                $lastUploadDate = $lastUploadDate ? (new DateTime($lastUploadDate))->format('d-m-Y') : '-';
                return (new BuyerSpecificPrice())
                    ->setCompanyCode($row['companyIdentification'])
                    ->setCompanyName($company ? $company->getName() : '-')
                    ->setLastUpload($lastUploadDate)
                    ->setNbProduct($row['nbProducts']);
            },
            $queryBuilder->getQuery()->getResult()
        );
    }

    public function getSpecificPricesBuyersCount(Vendor $merchant): int
    {
        $queryBuilder = $this->specificPriceRepository->createQueryBuilder('sp');
        $queryBuilder
            ->select('sp.companyIdentification')
            ->where('sp.IZBmerchantId = :merchantId')
            ->setParameter('merchantId', $merchant->getId())
            ->distinct();
        return count($queryBuilder->getQuery()->getResult());
    }

    public function getSpecificPricesProductsCount(Vendor $merchant): int
    {
        return count($this->specificPriceRepository->findBy([
            'IZBmerchantId' => $merchant->getId()
        ]));
    }

    public function exportCompanySpecificPricesToCsv(Vendor $merchant, string $companyCode)
    {
        $this->exportSpecificPricesToCSV(
            $this->specificPriceRepository->findBy([
                'IZBmerchantId' => $merchant->getId(),
                'companyIdentification' => $companyCode
            ])
        );
    }

    public function exportAllSpecificPricesToCsv(Vendor $merchant)
    {
        $this->exportSpecificPricesToCSV(
            $this->specificPriceRepository->findBy([
                'IZBmerchantId' => $merchant->getId()
            ])
        );
    }

    private function exportSpecificPricesToCSV(array $specificPrices)
    {
        $fileHandle = fopen('php://output', 'w');
        stream_filter_register('newlines', StreamFilterNewlines::class);
        stream_filter_append($fileHandle, 'newlines');

        fwrite($fileHandle, $bom = (chr(0xEF) . chr(0xBB) . chr(0xBF)));

        fputcsv($fileHandle, self::SPECIFIC_PRICE_CSV_HEADER, ';');
        /** @var SpecificPrice $specificPrice */
        foreach ($specificPrices as $specificPrice) {
            $validityDate = $specificPrice->getValidityDate();
            if ($validityDate !== null) {
                $validityDate = $validityDate->format('d/m/Y');
            }

            $data = [
                $specificPrice->getCompanyIdentification(),
                $specificPrice->getVendorReference(),
                $specificPrice->getIncoterm(),
                $specificPrice->getCountry(),
                $specificPrice->getBasicPrice(),
                $specificPrice->getThreshold1(),
                $specificPrice->getPrice1(),
                $specificPrice->getThreshold2(),
                $specificPrice->getPrice2(),
                $specificPrice->getThreshold3(),
                $specificPrice->getPrice3(),
                $specificPrice->getThreshold4(),
                $specificPrice->getPrice4(),
                $specificPrice->getMoq(),
                $validityDate,
                $specificPrice->getDelayOfDelivery(),
                $specificPrice->getFrameContract()
            ];

            fputcsv($fileHandle, $data, ';');
        }

        fclose($fileHandle);
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    public function getOfferPrice(Offer $offer, Company $buyerCompany): ?SpecificPrice
    {
        return $this->specificPriceRepository->findOneBy([
            'company' => $buyerCompany,
            'companyIdentification' => $buyerCompany->getIdentification(),
            'vendorReference' => $offer->getSellerRef()
        ]);
    }
}
