<?php

namespace AppBundle\Services;

use AppBundle\Entity\Middleware\AbstractPayload;
use AppB<PERSON>le\FilterQueryBuilder\PayloadFilterQueryBuilder;
use AppBundle\Repository\Payload\PayloadRepository;
use Doctrine\ORM\EntityManagerInterface;
use Knp\Component\Pager\Pagination\PaginationInterface;
use Knp\Component\Pager\PaginatorInterface;
use Open\BackBundle\Dto\FilterPayloadData;

class PayloadsService extends AbstractPaginatedService
{
    private PayloadRepository $payloadRepository;

    public function __construct(
        EntityManagerInterface    $em,
        PaginatorInterface        $paginator,
        PayloadFilterQueryBuilder $filterQueryBuilder
    )
    {
        parent::__construct($em, AbstractPayload::class, $paginator, $filterQueryBuilder);
        $this->payloadRepository = $em->getRepository(AbstractPayload::class);
        $this->paginator = $paginator;
    }

    public function getAllPayloads(FilterPayloadData $filterData, int $limit = 20, array $options = []): PaginationInterface
    {
        $query = $this->payloadRepository->createQueryBuilder('p');
        $this->filterQueryBuilder->build($query, $filterData);
        return $this->paginator->paginate(
            $query,
            $filterData->page,
            $limit,
            $options
        );
    }
}
