<?php

namespace AppBundle\Services;

use JMS\Serializer\SerializerBuilder;

class SerializerService
{
    private $serializer;

    public function __construct()
    {
        $this->serializer = SerializerBuilder::create()->build();
    }

    public function serialize($object): string
    {
        return $this->serializer->serialize($object, 'json');
    }

    public function deserialize(string $serializedObject, string $objectClass)
    {
        return $this->serializer->deserialize($serializedObject, $objectClass, 'json');
    }
}
