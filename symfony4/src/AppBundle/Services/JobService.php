<?php

namespace AppBundle\Services;

use AppBundle\Message\AutoCancelOrder;
use AppBundle\Message\ImportCompanyCatalogue;
use AppBundle\Message\PaymentBankTransfer;
use AppBundle\Message\PaymentTerm;
use AppBundle\Message\SetMerchantOrderExtraInfo;
use AppBundle\Message\SyncCreditNote;
use AppBundle\Message\SyncInvoice;
use AppBundle\Message\SyncOrder;
use DateTime;
use Open\IzbergBundle\Model\FetchCreditNotesResponse;
use Open\IzbergBundle\Model\FetchInvoicesResponse;
use Open\IzbergBundle\Model\FetchOrdersResponse;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Messenger\Stamp\DelayStamp;

class JobService
{
    private MessageBusInterface $messageBus;

    public function __construct(MessageBusInterface $messageBus)
    {
        $this->messageBus = $messageBus;
    }

    public function autoCancelOrder(array $merchantOrderIds): void
    {
        $message = new AutoCancelOrder($merchantOrderIds);
        $this->messageBus->dispatch($message);
    }

    public function setMerchantOrderExtraInfo(array $merchantOrdersIds, array $attributes): void
    {
        $message = new SetMerchantOrderExtraInfo($merchantOrdersIds, $attributes);
        $this->messageBus->dispatch($message);
    }

    public function syncCreditNote(FetchCreditNotesResponse $creditNoteResult): void
    {
        $message = new SyncCreditNote($creditNoteResult);
        $this->messageBus->dispatch($message);
    }

    public function syncInvoice(FetchInvoicesResponse $invoicesResponse): void
    {
        $message = new SyncInvoice($invoicesResponse);
        $this->messageBus->dispatch($message);
    }

    public function syncOrder(FetchOrdersResponse $ordersResponse): void
    {
        $message = new SyncOrder($ordersResponse);
        $this->messageBus->dispatch($message);
    }

    public function importCompanyCatalogue(array $dataRowset, int $companyId, string $country): void
    {
        $message = new ImportCompanyCatalogue($dataRowset, $companyId, $country);
        $this->messageBus->dispatch($message, [new DelayStamp(5000)]);
    }

    public function paymentBankTransfer(int $cartId, int $userId, ?string $validationNumber, ?string $accountingEmail): void
    {
        $message = new PaymentBankTransfer($cartId, $userId, $validationNumber, $accountingEmail);
        $this->messageBus->dispatch($message);
    }

    public function paymentTerm(int $cartId, int $userId, ?string $validationNumber, ?string $accountingEmail): void
    {
        $message = new PaymentTerm($cartId, $userId, $validationNumber, $accountingEmail);
        $this->messageBus->dispatch($message);
    }

    public function failedJobsSince(DateTime $dateTime): array
    {
        // todo replace as jobs will not be stored anymore

        return [];
    }

    public function succedJobsSince(DateTime $dateTime): array
    {
        // todo replace as jobs will not be stored anymore

        return [];
    }

}
