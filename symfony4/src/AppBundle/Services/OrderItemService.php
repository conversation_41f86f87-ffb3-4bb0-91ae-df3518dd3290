<?php


namespace AppBundle\Services;


use AppBundle\Model\Offer;
use AppBundle\Repository\OrderItemRepository;
use Open\IzbergBundle\Model\OrderItem;

class OrderItemService
{
    private OrderItemRepository $orderItemRepository;
    private OfferService $offerService;

    /**
     * OrderItemService constructor.
     * @param OrderItemRepository $orderItemRepository
     */
    public function __construct(OrderItemRepository $orderItemRepository, OfferService $offerService)
    {
        $this->orderItemRepository = $orderItemRepository;
        $this->offerService = $offerService;
    }

    /**
     * @param \AppBundle\Entity\MerchantOrder $merchantOrderEntity
     */
    public function syncOrderItem(OrderItem $orderItem, \AppBundle\Entity\MerchantOrder $merchantOrderEntity): bool{
        $izbergId = $orderItem->getId();
        $offer = $this->offerService->findOfferById($orderItem->getOfferId());
        $sellerRef = null;
        $productName = null;
        $orderItemExtraInfo = $orderItem->getExtraInfo();
        $buyerRef = $orderItemExtraInfo['Buyer-internal-ref'] ?? null;
        $frameContractNumber = $orderItemExtraInfo['frame_contract'] ?? null;
        $orderLine = $orderItemExtraInfo['order-line'] ?? null;

        if($offer instanceof Offer){
            $sellerRef = $offer->getSellerRef();
            $productName = $offer->getOfferTitle();
        }

        $OrderItemEntity = $this->orderItemRepository->findOneBy(["izbergId"=>$izbergId, "merchantOrder"=>$merchantOrderEntity]);
        if(!$OrderItemEntity instanceof \AppBundle\Entity\OrderItem){
            $OrderItemEntity = new \AppBundle\Entity\OrderItem();
        }

        $deliveryDates = $orderItem->getDeliveryDates();
        if($deliveryDates != null && count($deliveryDates) > 0) {
            $deliveryDate = $deliveryDates[0]["expected_delivery_date"];
            $OrderItemEntity->setExpectedDeliveryDate(new \DateTimeImmutable($deliveryDate));

        }

        $OrderItemEntity->setIzbergId($izbergId)
            ->setVendorReference($sellerRef)
            ->setBuyerReference($buyerRef)
            ->setFrameContractNumber($frameContractNumber)
            ->setQuantity($orderItem->getQuantity())
            ->setCurrency($orderItem->getCurrency())
            ->setUnitPrice($orderItem->getPrice())
            ->setMerchantOrder($merchantOrderEntity)
            ->setOrderLine($orderLine)
            ->setProductName($productName);

        $this->orderItemRepository->save($OrderItemEntity);

        return true;
    }

    public function syncOrderLine(int $izbergOrderItemId, ?string $orderLine) : bool
    {
        $orderItemEntity = $this->orderItemRepository->findOneBy(["izbergId"=>$izbergOrderItemId]);
        if(!$orderItemEntity instanceof \AppBundle\Entity\OrderItem){
            return false;
        }

        $orderItemEntity->setOrderLine($orderLine);
        $this->orderItemRepository->save($orderItemEntity);

        return true;
    }

}
