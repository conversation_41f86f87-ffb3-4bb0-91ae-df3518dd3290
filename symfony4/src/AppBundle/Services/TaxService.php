<?php

namespace AppBundle\Services;

use AppBundle\Entity\Country;
use AppBundle\Repository\TvaGroupRepository;
use AppBundle\Repository\TvaRateRepository;

class TaxService
{
    /** @var TvaGroupRepository */
    private $tvaGroupRepository;

    /** @var TvaRateRepository */
    private $tvaRateRepository;

    public function __construct(TvaGroupRepository $tvaGroupRepository, TvaRateRepository $tvaRateRepository)
    {
        $this->tvaGroupRepository = $tvaGroupRepository;
        $this->tvaRateRepository = $tvaRateRepository;
    }


    public function taxRateFromCountry(Country $country)
    {
        if ($country->getCode() !== 'france') {
            return 0;
        }

        $taxRate = $this->tvaRateRepository->getTaxRateFromTaxGroupAndDate('FRA-standard', new \DateTimeImmutable());
        if (!$taxRate) {
            return 0;
        }


        return $taxRate->getRate();
    }
}
