<?php

namespace AppBundle\Services;

use AppBundle\Entity\User;
use AppBundle\Repository\UserRepository;

class ComparisonSheetService
{
    private CartService $cartService;
    private UserRepository $userRepository;
    private OfferService $offerService;
    private SpecificPriceService $specificPriceService;
    private int $comparisonSheetMaxItem;

    public function __construct(
        int $comparisonSheetMaxItem,
        CartService $cartService,
        UserRepository $userRepository,
        OfferService $offerService,
        SpecificPriceService $specificPriceService
    )
    {
        $this->comparisonSheetMaxItem = $comparisonSheetMaxItem;
        $this->cartService = $cartService;
        $this->userRepository = $userRepository;
        $this->offerService = $offerService;
        $this->specificPriceService = $specificPriceService;
    }

    /**
     * Add offer to comparison sheet
     * returns 0 if offer has been successfully added to comparison sheet
     * returns 1 if number of item allowed in the comparison sheet is reached
     * returns 2 if comparison sheet got already the offer
     * returns 3 if offer cannot been added to the comparison sheet due to limitation (bafv, no price, business everywhere)
     *
     * @param User $user
     * @param int $offerId
     * @param string|null $currency
     * @return int
     */
    public function addOfferToComparisonSheet(User $user, int $offerId, ?string $currency = null): int
    {
        $this->offerService->defineUser($user);

        $comparisonSheetCart = null;
        $selectedCurrency = 'EUR';
        $selectedCurrency = ($currency === 'USD') ? 'USD' : $selectedCurrency;

        $comparisonSheetCartId = ($selectedCurrency === 'USD') ? $user->getComparisonSheetUSDId() : $user->getComparisonSheetEURId();
        $itemInComparisonSheet = (int)$user->getItemInComparisonSheet();

        if ($itemInComparisonSheet >= $this->comparisonSheetMaxItem) {
            return 1;
        }

        if ($comparisonSheetCartId === null) {
            // create IZB cart
            $cart = $this->cartService->createCart($selectedCurrency);
            $comparisonSheetCart = $this->cartService->findCart($user, $cart->getId());
            $comparisonSheetCartId = $comparisonSheetCart->getId();

            if ($selectedCurrency === 'USD') {
                $user->setComparisonSheetUSDId(strval($comparisonSheetCartId));
            }

            if ($selectedCurrency === 'EUR') {
                $user->setComparisonSheetEURId(strval($comparisonSheetCartId));
            }

            $this->userRepository->save($user);
        }

        if ($comparisonSheetCart === null) {
            $comparisonSheetCart = $this->cartService->findCart($user, $comparisonSheetCartId);
        }

        if ($comparisonSheetCart->hasOfferId($offerId)) {
            return 2;
        }

        $offer = $this->offerService->findOfferById($offerId);
        $this->specificPriceService->updateOfferSpecificPrices($user->getCompany(), $offer);
        if ($offer->isLimited()) {
            return 3;
        }

        $this->cartService->addProductToCart($comparisonSheetCartId, $offerId, $selectedCurrency);
        $user->setItemInComparisonSheet($itemInComparisonSheet + 1);
        $this->userRepository->save($user);

        return 0;
    }

}
