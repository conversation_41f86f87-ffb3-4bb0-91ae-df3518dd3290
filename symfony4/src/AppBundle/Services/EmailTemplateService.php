<?php

namespace AppBundle\Services;

use AppBundle\Exception\TemplateException;
use AppBundle\Model\EmailTemplate;
use Open\IzbergBundle\Service\RedisService;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Yaml\Yaml;

class EmailTemplateService implements LoggerAwareInterface
{
    private const CACHE_KEY = "EMAIL_TEMPLATES";

    private string $configFile;
    private LoggerInterface $logger;
    private RedisService $cacheService;

    public function __construct($configFile, RedisService $cacheService){
        $this->cacheService = $cacheService;
        $this->configFile = $configFile;
        $templates = Yaml::parseFile($this->configFile);
        $this->cacheService->saveItem(self::CACHE_KEY, $templates);
    }

    /**
     * get a template from its name
     * @param string $templateName
     * @return EmailTemplate|null the email template if found, null if the specified template doesn't exist
     * @throws TemplateException
     */
    public function getTemplate (string $templateName){
        $templates = $this->cacheService->getItem(self::CACHE_KEY);

        if ($templates === null){
            $this->logger->error("enable to get template: Templates has not been loaded in cache",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::EMAIL_ERROR,
                    "templateName" => $templateName
                ])
            );
            throw new TemplateException("enable to get template: Templates has not been loaded in cache");
        }

        if (array_key_exists($templateName, $templates)){
            return $this->parseTemplate($templateName, $templates[$templateName]);
        }
        else{
            $this->logger->error("enable to get template: it doesn't exist",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::EMAIL_ERROR,
                    "templateName" => $templateName
                ]));
            return null;
        }

    }

    /**
     * get the list of the names of all the email templates
     * @return array the list of the names of all the email templates
     * @throws TemplateException
     */
    public function fetchAllTemplateNames (){
        $templates = $this->cacheService->getItem(self::CACHE_KEY);

        if ($templates === null){
            $this->logger->error("error while fetching the list of templates: Templates has not been loaded in cache",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME=>EventNameEnum::EMAIL_ERROR
            ]));
            throw new TemplateException("error while fetching the list of templates: Templates has not been loaded in cache");
        }

        return array_keys($templates);
    }


    /**
     * parse a template from the cache and return a Template object
     * @param array $templateFromCache
     * @param string $templateName
     * @return EmailTemplate
     */
    public function parseTemplate (string $templateName, array $templateFromCache){
        $result = new EmailTemplate();
        $result->setTemplateName($templateName);
        $result->setContent($templateFromCache['body']);

        $variables = [];
        foreach ($templateFromCache['variables'] as $variable){
            if (array_key_exists("default", $variable)) {
                $variables[$variable['name']] = $variable['default'];
            }
            else{
                $items = [];
                //for testing template, just add 2 elements
                for ($i=0; $i<2; $i++){
                    $item = [];
                    foreach ($variable['children'] as $child) {
                        $item[$child['name']] = $child['default'];
                    }

                    $items [] = (object)$item;
                }
                $variables[$variable['name']] = $items;
            }
        }
        $result->setVariables($variables);

        return $result;
    }


    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
