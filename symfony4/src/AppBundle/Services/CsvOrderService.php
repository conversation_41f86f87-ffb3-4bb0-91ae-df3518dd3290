<?php

namespace AppBundle\Services;

use AppBundle\StreamFilter\StreamFilterNewlines;
use DateTimeImmutable;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\ParameterType;
use Symfony\Contracts\Translation\TranslatorInterface;

class CsvOrderService
{
    private ?TranslatorInterface $translator;
    private Connection $dbConnection;

    /**
     * CsvOrderService constructor.
     * @param TranslatorInterface $translator
     */
    public function __construct(TranslatorInterface $translator, Connection $dbConnection)
    {
        $this->translator = $translator;
        $this->dbConnection = $dbConnection;
    }

    public function exportOrders(
        int $companyId,
        ?DateTimeImmutable $fromDate = null,
        ?DateTimeImmutable $toDate = null
    ): void
    {
        $fileHandle = fopen('php://output', 'w');

        stream_filter_register("newlines", StreamFilterNewlines::class);
        stream_filter_append($fileHandle, "newlines");

        fwrite($fileHandle, $bom = (chr(0xEF).chr(0xBB).chr(0xBF)));

        // write headers
        $headers = [
            $this->translator->trans("orders.export.subOrderId", [], 'AppBundle'),
            $this->translator->trans("orders.export.id", [], 'AppBundle'),
            $this->translator->trans("orders.export.internalBuyerOrderId", [], 'AppBundle'),
            $this->translator->trans("orders.export.orderLine", [], 'AppBundle'),
            $this->translator->trans("orders.export.vendorName", [], 'AppBundle'),
            $this->translator->trans("orders.export.buyerRef", [], 'AppBundle'),
            $this->translator->trans("orders.export.vendorRef", [], 'AppBundle'),
            $this->translator->trans("orders.export.productName", [], 'AppBundle'),
            $this->translator->trans("orders.export.frameContractNumber", [], 'AppBundle'),
            $this->translator->trans("orders.export.unitPrice", [], 'AppBundle'),
            $this->translator->trans("orders.export.quantity", [], 'AppBundle'),
            $this->translator->trans("orders.export.itemPrice", [], 'AppBundle'),
            $this->translator->trans("orders.export.amount", [], 'AppBundle'),
            $this->translator->trans("orders.export.amountVat", [], 'AppBundle'),
            $this->translator->trans("orders.export.amountTaxIncluded", [], 'AppBundle'),
            $this->translator->trans("orders.export.currency", [], 'AppBundle'),
            $this->translator->trans("orders.export.expectedDeliveryDate", [], 'AppBundle'),
            $this->translator->trans("orders.export.validationNumber", [], 'AppBundle'),
            $this->translator->trans("orders.export.date", [], 'AppBundle'),
            $this->translator->trans("orders.export.costCenter", [], 'AppBundle'),
            $this->translator->trans("orders.export.address", [], 'AppBundle'),
            $this->translator->trans("orders.export.paymentTerms", [], 'AppBundle'),
            $this->translator->trans("orders.export.status", [], 'AppBundle'),
            $this->translator->trans("orders.export.packagingRequirement1", [], 'AppBundle'),
            $this->translator->trans("orders.export.packagingRequirement2", [], 'AppBundle'),
            $this->translator->trans("orders.export.packagingRequirement3", [], 'AppBundle'),
            $this->translator->trans("orders.export.documentRequirement1", [], 'AppBundle'),
            $this->translator->trans("orders.export.documentRequirement2", [], 'AppBundle'),
            $this->translator->trans("orders.export.documentRequirement3", [], 'AppBundle'),
            $this->translator->trans("orders.export.documentRequirement4", [], 'AppBundle'),
            $this->translator->trans("orders.export.documentRequirement5", [], 'AppBundle'),
            $this->translator->trans("orders.export.documentRequirement6", [], 'AppBundle'),
            $this->translator->trans("orders.export.documentRequirement7", [], 'AppBundle'),
            $this->translator->trans("orders.export.documentRequirement8", [], 'AppBundle'),
            $this->translator->trans("orders.export.documentRequirement9", [], 'AppBundle'),
            $this->translator->trans("orders.export.documentRequirement10", [], 'AppBundle'),

        ];
        fputcsv( $fileHandle, $headers, ';');

        // write body content
        $offset = 0;
        $limit = 500;
        if (null === $fromDate) {
            $fromDate = new DateTimeImmutable('2017-01-01');
        }

        if (null === $toDate) {
            $toDate = new DateTimeImmutable();
            $toDate = $toDate->add(new \DateInterval('P1D'));
        }

        $statusFormatter = fn($status) => $this->translator->trans('orders.status.status_'.$status, [], 'AppBundle');

        do {
            $sql = <<<SQL
                SELECT
                       CONCAT(o.number_id,' - ',mo.vendor_id) as subOrderId,
                       o.number_id as id,
                       mo.buyer_internal_order_id as internalBuyerOrderId,
                       oi.order_line as orderLine,
                       mo.vendor_name as vendorName,
                       oi.buyer_reference as buyerRef,
                       oi.vendor_reference as vendorRef,
                       oi.product_name as productName,
                       oi.frame_contract_number as frameContractNumber,
                       oi.unit_price as unitPrice,
                       oi.quantity as quantity,
                       0 as itemPrice, -- item price replaced further by PHP
                       o.amount as amount,
                       o.amount_vat as amountVat,
                       o.amount_vat_included as amountTaxIncluded,
                       oi.currency as currency,
                       oi.expected_delivery_date as expectedDeliveryDate,
                       o.validation_number as validationNumber,
                       o.created_on as date,
                       s.name as costCenter,
                       sp.name as address,
                       o.payment_terms as paymentTerms,
                       o.izberg_status as status,
                       mo.packaging_request1 as packagingRequirement1,
                       mo.packaging_request2 as packagingRequirement2,
                       mo.packaging_request3 as packagingRequirement3,
                       mo.documentation_request1 as documentRequirement1,
                       mo.documentation_request2 as documentRequirement2,
                       mo.documentation_request3 as documentRequirement3,
                       mo.documentation_request4 as documentRequirement4,
                       mo.documentation_request5 as documentRequirement5,
                       mo.documentation_request6 as documentRequirement6,
                       mo.documentation_request7 as documentRequirement7,
                       mo.documentation_request8 as documentRequirement8,
                       mo.documentation_request9 as documentRequirement9,
                       mo.documentation_request10 as documentRequirement10
                FROM orders as o
                LEFT JOIN shipping_points as sp ON sp.id = o.shipping_point_id
                LEFT JOIN site as s ON sp.site_id = s.id
                LEFT JOIN merchant_order as mo ON mo.order_id = o.id
                LEFT JOIN order_item as oi ON oi.merchantOrder_id = mo.id
                WHERE
                      o.company_id = :companyId
                      AND o.created_on > :fromDate
                      AND o.created_on < :toDate
                LIMIT :offset, :limit
            SQL;

            $statement = $this->dbConnection->prepare($sql);
            $statement->bindValue('companyId', $companyId, ParameterType::INTEGER);
            $statement->bindValue('fromDate', $fromDate->format('Y-m-d'));
            $statement->bindValue('toDate', $toDate->format('Y-m-d'));
            $statement->bindValue('offset', $offset, ParameterType::INTEGER);
            $statement->bindValue('limit', $limit, ParameterType::INTEGER);
            $query = $statement->executeQuery();

            $result = $query->fetchAllAssociative();

            if (!empty($result)) {
                foreach($result as $row) {
                    // item price multiplication of quantity and unit price
                    $row['itemPrice'] = $row['unitPrice'] * $row['quantity'];

                    // litteral status
                    $row['status'] = $statusFormatter($row['status']);

                    fputcsv( $fileHandle, $row, ';');
                }
            }

            $offset += $limit;
        } while (count($result) > 0);

        fclose($fileHandle);
    }
}
