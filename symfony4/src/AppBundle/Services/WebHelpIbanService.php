<?php

namespace AppBundle\Services;

class WebHelpIbanService
{
    private const CURRENCY_EUR = 'eur';
    private const CURRENCY_USD = 'usd';

    /** @var string */
    private $accountNameUsd;

    /** @var string  */
    private $ibanUsd;

    /** @var string */
    private $accountNameEur;

    /** @var string  */
    private $ibanEur;

    public function __construct(
        string $accountNameUsd,
        string $ibanUsd,
        string $accountNameEur,
        string $ibanEur
    )
    {
        $this->accountNameUsd = $accountNameUsd;
        $this->ibanUsd = $ibanUsd;
        $this->accountNameEur = $accountNameEur;
        $this->ibanEur = $ibanEur;
    }

    public function getIbanFromCurrency(string $currency): ?string
    {
        $currency = strtolower($currency);

        if ($currency === self::CURRENCY_EUR) {
            return $this->ibanEur;
        }

        if ($currency === self::CURRENCY_USD) {
            return $this->ibanUsd;
        }

        return null;
    }

    public function getIbanAccountNameFromCurrency(string $currency): ?string
    {
        $currency = strtolower($currency);

        if ($currency === self::CURRENCY_EUR) {
            return $this->accountNameEur;
        }

        if ($currency === self::CURRENCY_USD) {
            return $this->accountNameUsd;
        }

        return null;
    }
}
