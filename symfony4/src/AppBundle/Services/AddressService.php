<?php
/**
 * Created by PhpStorm.
 * User: QAR14123
 * Date: 04/06/2018
 * Time: 15:28
 */

namespace AppBundle\Services;


use AppBundle\Entity\Company;
use AppBundle\Repository\AddressRepository;
use Doctrine\ORM\EntityManagerInterface;
use Open\IzbergBundle\Model\Address;
use Open\IzbergBundle\Model\Country;
use AppBundle\Entity\ShippingPoint;
use Open\IzbergBundle\Api\AddressApi;
use Unirest\Exception;

class AddressService
{

    const API_COUNTRY_URL = '/v1/country/';

    const REPO_ADDRESS = \AppBundle\Entity\Address::class;

    /**
     * @var AddressApi
     */
    private $addressApi;


    private $em;

    public function __construct(AddressApi $apiAddress, EntityManagerInterface $em)
    {
        $this->em = $em;
        $this->addressApi = $apiAddress;
    }

    public function get($id)
    {
        return $this->em->getRepository(self::REPO_ADDRESS)->find($id);
    }

    public function createIzbergBillingAddress(\AppBundle\Entity\Address $addressEntity, $countryMainAddress)
    {
        $izbergAddress = $this->buildIzbergAddress($addressEntity, $countryMainAddress);
        $izbergAddress->setName('billing Address');
        $this->saveIzbergAddress($addressEntity, $izbergAddress);
    }

    public function createIzbergShippingAddress(\AppBundle\Entity\Address $addressEntity, $countryMainAddress)
    {
        $izbergAddress = $this->buildIzbergAddress($addressEntity, $countryMainAddress);
        $izbergAddress->setName($addressEntity->getShippingPoints()->get(0)->getName());
        $this->saveIzbergAddress($addressEntity, $izbergAddress);
    }

    private function buildIzbergAddress(\AppBundle\Entity\Address $addressEntity, $countryMainAddress): Address
    {
        // Izberg Address
        $izbergAddress = new Address();
        $izbergAddress->setAddress($addressEntity->getAddress());
        $izbergAddress->setAddress2($addressEntity->getAddress2());
        $izbergAddress->setZipcode($addressEntity->getZipcode());
        $izbergAddress->setCity($addressEntity->getCity());
        $country = new Country();

        // API is expecting a resource uri and not a Country id
        if($addressEntity->getCountry() != null){
            $country->setResourceUri(self::API_COUNTRY_URL . $addressEntity->getCountry()->getIzbergId() . '/');
        } else {
            $country->setResourceUri(self::API_COUNTRY_URL . $countryMainAddress->getIzbergId() . '/');
        }

        $izbergAddress->setCountry($country);

        return $izbergAddress;
    }

    private function saveIzbergAddress(\AppBundle\Entity\Address $addressEntity, Address $izbergAddress): \AppBundle\Entity\Address
    {
        // No Id ? then create the address
        if ($addressEntity->getIzbergAddressId() == null) {

            $addressEntity->setIzbergAddressId(
                $this->addressApi->createAddress($izbergAddress)
            );

            $this->em->persist($addressEntity);
            $this->em->flush();
        } else {
            $this->addressApi->updateAddress($addressEntity->getIzbergAddressId(), $izbergAddress);
        }

        return $addressEntity;
    }

    public function createIzbergAddress(\AppBundle\Entity\Address $addressEntity, $operator, $countryMainAddress): \AppBundle\Entity\Address
    {
        // Izberg Address
        $izbergAddress = new Address();
        $izbergAddress->setName($addressEntity->getShippingPoints()->get(0)->getName());
        $izbergAddress->setAddress($addressEntity->getAddress());
        $izbergAddress->setAddress2($addressEntity->getAddress2());
        $izbergAddress->setZipcode($addressEntity->getZipcode());
        $izbergAddress->setCity($addressEntity->getCity());
        $country = new Country();

        // API is expecting a resource uri and not a Country id
        if($addressEntity->getCountry() != null){
            $country->setResourceUri(self::API_COUNTRY_URL . $addressEntity->getCountry()->getIzbergId() . '/');
        } else {
            $country->setResourceUri(self::API_COUNTRY_URL . $countryMainAddress->getIzbergId() . '/');
        }


        $izbergAddress->setCountry($country);

        // BUG FIX sanatize izbergAddressId
        $izbergAddressId = $addressEntity->getIzbergAddressId();
        if (preg_match('/^[0-9]+$/', $izbergAddressId)) {
            $izbergAddressId = intval($izbergAddressId);
        } else {
            $izbergAddressId = null;
        }


        // No Id ? then create the address
        if (!$izbergAddressId) {
            $izbergAddressId = $this->addressApi->createAddress($izbergAddress);

            $addressEntity->setIzbergAddressId($izbergAddressId);

            $this->em->persist($addressEntity);
            $this->em->flush();
        } else {
            $this->addressApi->updateAddress($izbergAddressId, $izbergAddress);
        }

        return $addressEntity;
    }

    public function getCostCenterByIzbergAddressId($izbAddressId)
    {
        /**
         * @var AddressRepository $addressRepository
         */
        $addressRepository = $this->em->getRepository(\AppBundle\Entity\Address::class);
        return $addressRepository->getCostCenterByIzbergAddressId($izbAddressId);
    }

    public function getCompanyBillingAddress(Company $company): ?\AppBundle\Entity\Address
    {
        if (!$company->getBillingAddress()) {
            return $company->getMainAddress();
        }

        return $company->getBillingAddress();
    }
}
