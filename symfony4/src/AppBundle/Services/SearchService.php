<?php

namespace AppBundle\Services;

use AppBundle\Entity\SearchFacet;
use AppBundle\Entity\SearchFacetValue;
use AppBundle\Entity\SearchResult;
use AppBundle\Entity\User;
use AppBundle\Model\Offer;
use AppBundle\Repository\CompanyCatalogRepository;
use Open\FrontBundle\Form\SearchBarForm;
use Open\IzbergBundle\Algolia\AlgoliaField;
use Open\IzbergBundle\Algolia\AlgoliaQuery;
use Open\IzbergBundle\Algolia\AlgoliaQueryParams;
use Open\IzbergBundle\Algolia\AlgoliaService;
use Open\IzbergBundle\Service\AttributeService;
use Open\IzbergBundle\Service\CategoryService;
use Symfony\Component\HttpFoundation\Request;

class SearchService
{
    public const OPTION_LIMIT_DAP_COUNTRY = 'option_limit_dap_country';

    /**
     * @var OfferService
     */
    private $offerService;

    /**
     * @var CategoryService
     */
    private $categoryService;

    /**
     * @var AlstomCustomAttributes
     */
    private $alstomCustomAttribute;

    /**
     * @var CompanyCatalogRepository
     */
    private $companyCatalogRepository;

    /**
     * @var AttributeService
     */
    private $facetAttributesService;

    /**
     * @var BafvService
     */
    private $bafvService;

    /**
     * @var array
     */
    private $querySuggestionsServices;

    /**
     * @var array
     */
    private $availableLocales;

    public function __construct(
        array $querySuggestionServices,
        array $availableLocales,
        OfferService $offerService,
        CategoryService $categoryService,
        AlstomCustomAttributes $alstomCustomAttribute,
        CompanyCatalogRepository $companyCatalogRepository,
        AttributeService $facetAttributesService,
        BafvService $bafvService
    )
    {
        $this->offerService = $offerService;
        $this->categoryService = $categoryService;
        $this->alstomCustomAttribute = $alstomCustomAttribute;
        $this->companyCatalogRepository = $companyCatalogRepository;
        $this->facetAttributesService = $facetAttributesService;
        $this->bafvService = $bafvService;
        $this->querySuggestionsServices = $querySuggestionServices;
        $this->availableLocales = $availableLocales;
    }

    public function getSearchableAttributes()
    {
        return array_merge(
            [
                AlstomCustomAttributes::createFullAttributeName($this->alstomCustomAttribute->getVendorReference()),
                AlstomCustomAttributes::createFullAttributeName($this->alstomCustomAttribute->getManufacturerReference()),
            ],
            array_map(
                [AlstomCustomAttributes::class, 'createFullAttributeName'],
                $this->alstomCustomAttribute->getManufacturerObsoleteReferences()
            ),
            [
                AlgoliaField::NAME,
                AlgoliaField::MERCHANT_NAME,
                AlgoliaField::PRODUCT_MANUFACTURER,
                AlgoliaField::PRODUCT_KEYWORDS,
            ],
            array_map(
                [AlstomCustomAttributes::class, 'createFullAttributeName'],
                $this->alstomCustomAttribute->getFitFormFunctions()
            ),
            [
                AlstomCustomAttributes::createFullAttributeName($this->alstomCustomAttribute->getUnspscCode()),
                AlgoliaField::PRODUCT_GTIN,
                AlstomCustomAttributes::createFullAttributeName($this->alstomCustomAttribute->getStandard()),
                AlgoliaField::DESCRIPTION,
                AlgoliaField::PRODUCT_CATEGORY_NAME,
            ],
            array_map(
                [AlstomCustomAttributes::class, 'createFullAttributeName'],
                $this->alstomCustomAttribute->getSupplierProductCompatibilities()
            )
        );
    }

    public function findQuerySuggestionFrom(string $text, string $locale): array
    {
        return $this->querySuggestionsServices[$locale]->search($text);
    }

    /**
     * @param User|null $user
     * @param string|null $text
     * @param string|null $categoryId
     * @param string $searchType
     * @param array $facetFilters
     * @param array $filters
     * @param int $hitsPerPage
     * @param int $page
     * @param string $sortBy
     * @param string $locale
     * @param bool $autocomplete
     * @param AlgoliaQueryParams|null $queryParams
     * @param array $options
     * @return SearchResult
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function search(
        ?User $user,
        ?string $text,
        ?string $categoryId,
        string $searchType,
        array $facetFilters,
        array $filters,
        int $hitsPerPage,
        int $page,
        string $sortBy,
        string $locale,
        bool $autocomplete = false,
        ?AlgoliaQueryParams $queryParams = null,
        array $options = []
    ): SearchResult
    {
        $defaultOptions = [
            self::OPTION_LIMIT_DAP_COUNTRY => true,
        ];

        $options = $options + $defaultOptions;
        $limitDapCountryIsActive = $options[self::OPTION_LIMIT_DAP_COUNTRY];

        if (!$queryParams) {
            $queryParams = new AlgoliaQueryParams();
        }

        $query = new AlgoliaQuery((string)$text, $queryParams);
        $query->getQueryParams()->addFacetFilters(
            AlgoliaField::STATUS . ':active',
            AlgoliaField::PRODUCT_STATUS . ':active',
            AlgoliaField::MERCHANT_STATUS . ':10'
        );

        $query->getQueryParams()->addNumericFilters(
            AlgoliaField::STOCK . '>0'
        );

        if ($limitDapCountryIsActive) {
            $this->limitDAPToBuyerCountry($user, $query);
        }

        $this->limitRestrictedProduct($user, $query);

        $this->addFacetFilters($facetFilters, $query);
        $this->addFacetFilters($filters, $query);

        $this->setSortOrder($sortBy);
        $this->setSearchType($searchType, $query, ($user && $user->getCompany()) ? $user->getCompany()->getId() : null);

        $searchResult = new SearchResult();
        if ($categoryId) {
            $searchResult->setCategory($this->categoryService->find($categoryId));
            $searchResult->setParentCategory($this->categoryService->getParent($categoryId));

            $query->getQueryParams()->addFacetFilters(AlgoliaField::PRODUCT_CATEGORY . ':' . $categoryId);
        }

        $offers = [];
        $commonFacets = [];
        $specificFacets = [];
        $numberOfPages = 0;
        $selectedFacets = [];
        $totalHits = 0;

        if ($query->isActive()) {
            $this->offerService->setLocale($locale);

            $offersPaginated = $this->offerService->findOffersPaginated(
                $query->getQueryParams(),
                $query->getSearch(),
                $hitsPerPage,
                $page,
                $autocomplete
            );

            $offers = $offersPaginated[AlgoliaService::ALGOLIA_HITS_PARAM];

            $numberOfPages = $offersPaginated[AlgoliaService::ALGOLIA_NB_PAGES_PARAM];
            $totalHits = $offersPaginated[AlgoliaService::ALGOLIA_NB_HITS_PARAM];
            $searchResultFacets = $offersPaginated[AlgoliaService::ALGOLIA_FACETS] ?? [];
            $commonFacets = $this->buildSearchResultCommonFacets($searchResultFacets, $searchType, $locale);
            $specificFacets = $this->buildSearchResultSpecificFacets($searchResultFacets, $locale);
            $selectedFacets = $this->buildSearchResultSelectedFacets($facetFilters, $locale);

            if($query->getQueryParams()->hasDisjunctiveFilters()) {
                $offersPaginated = $this->offerService->findOffersPaginated(
                    $query->getQueryParams(),
                    $query->getSearch(),
                    $hitsPerPage,
                    $page,
                    $autocomplete,
                    true
                );

                $searchResultFacets = $offersPaginated[AlgoliaService::ALGOLIA_FACETS] ?? [];

                $commonFacets = $this->mergeFacets(
                    $commonFacets,
                    $this->buildSearchResultCommonFacets($searchResultFacets, $searchType, $locale)
                );

                $specificFacets = $this->mergeFacets(
                    $specificFacets,
                    $this->buildSearchResultSpecificFacets($searchResultFacets, $locale)
                );

                $selectedFacets = $this->mergeFacets(
                    $selectedFacets,
                    $this->buildSearchResultSelectedFacets($facetFilters, $locale)
                );
            }
        }

        $searchResult->setOffers($offers);
        $searchResult->setNumberOfPages($numberOfPages);
        $searchResult->setCurrentPage($page);
        $searchResult->setCommonFacets($commonFacets);
        $searchResult->setSpecificFacets($specificFacets);
        $searchResult->setFilters($this->buildSearchResultFilters($query->getQueryParams()->getFilters(), $locale));
        $searchResult->setSelectedFacets($selectedFacets);
        $searchResult->setTotalHits($totalHits);

        if (!$user) {
            $this->anonymousSearchResult($searchResult);
        }

        return $searchResult;
    }

    public function searchByBuyerAndManufacturerReference(
        User $user,
        ?string $buyerReference,
        ?string $manufacturerReference,
        int $hitsPerPage,
        int $page,
        string $locale
    ) : SearchResult
    {
        $this->setSortOrder(AlgoliaService::RANKING_BY_PRICE_ASC);

        $referenceFilters = false;

        $queryParams = new AlgoliaQueryParams();

        $query = new AlgoliaQuery('', $queryParams);
        $query->getQueryParams()->addFacetFilters(
            AlgoliaField::STATUS . ':active',
            AlgoliaField::PRODUCT_STATUS . ':active',
            AlgoliaField::MERCHANT_STATUS . ':10'
        );

        $query->getQueryParams()->addNumericFilters(
            AlgoliaField::STOCK . '>0'
        );

        $this->limitDAPToBuyerCountry($user, $query);
        $this->limitRestrictedProduct($user, $query);

        $query->disable();

        $createFilter = function($reference) {
            return array_merge(
                [
                    AlstomCustomAttributes::createFullAttributeName(
                        $this->alstomCustomAttribute->getManufacturerReference()
                    ) . ':' . $reference,
                    AlstomCustomAttributes::createFullAttributeName(
                        $this->alstomCustomAttribute->getVendorReference()
                    ) . ':' . $reference,
                ],
                array_map(
                    function($productCompatibleAttribute) use ($reference) {
                        return AlstomCustomAttributes::createFullAttributeName(
                                $productCompatibleAttribute
                            ) . ':' . $reference;
                    },
                    $this->alstomCustomAttribute->getSupplierProductCompatibilities()
                )
            );
        };

        $manufacturerReferenceFilters = [];
        $buyerReferenceFilters = [];
        $buyerReferenceOriginalFilters = [];

        // build manufacturer reference filters
        if (!empty($manufacturerReference)) {
            $manufacturerReferenceFilters = $createFilter($manufacturerReference);
        }

        // build buyer reference filters
        $companyId = $user->getCompany()->getId();
        $references = [];
        if ($companyId && !empty($buyerReference)) {
            $references = $this->companyCatalogRepository->findByCompanyIdAndReference(
                $companyId,
                $buyerReference,
                true
            );
        }

        if (count($references)) {
            $externalReference = $references[0]->getExternalRef();
            $buyerReferenceFilters = $createFilter($externalReference);
        }

        if (!empty($buyerReference)) {
            $buyerReferenceOriginalFilters = $createFilter($buyerReference);
        }

        $filters = array_merge(
            $manufacturerReferenceFilters,
            $buyerReferenceFilters,
            $buyerReferenceOriginalFilters
        );

        if (count($filters)) {
            $query->getQueryParams()
                ->addFilters($filters)
                ->typoTolerance(false);
            $referenceFilters = true;
        }

        $enableQuery = ($referenceFilters);

        if ($enableQuery) {
            $query->enable();
        }

        $query->setSearch('');

        $searchResult = new SearchResult();

        $offers = [];
        $commonFacets = [];
        $specificFacets = [];
        $numberOfPages = 0;
        $selectedFacets = [];
        $totalHits = 0;

        if ($query->isActive()) {
            $this->offerService->setLocale($locale);
            $offersPaginated = $this->offerService->findOffersPaginated(
                $query->getQueryParams(),
                $query->getSearch(),
                $hitsPerPage,
                $page
            );

            $offers = $offersPaginated[AlgoliaService::ALGOLIA_HITS_PARAM];

            $numberOfPages = $offersPaginated[AlgoliaService::ALGOLIA_NB_PAGES_PARAM];
            $totalHits = $offersPaginated[AlgoliaService::ALGOLIA_NB_HITS_PARAM];
        }

        $searchResult->setOffers($offers);
        $searchResult->setNumberOfPages($numberOfPages);
        $searchResult->setCurrentPage($page);
        $searchResult->setCommonFacets($commonFacets);
        $searchResult->setSpecificFacets($specificFacets);
        $searchResult->setFilters($this->buildSearchResultFilters($query->getQueryParams()->getFilters(), $locale));
        $searchResult->setSelectedFacets($selectedFacets);
        $searchResult->setTotalHits($totalHits);

        return $searchResult;
    }

    private function anonymousSearchResult(SearchResult $searchResult)
    {
        $searchResult->setOffers(
            array_map(
                function(Offer $offer) {
                    if (strtolower($offer->getIncoterm()) === 'dap') {
                        $offer->setIncotermCountry(null);
                    }
                    return $offer;
                },
                $searchResult->getOffers()
            )
        );
    }

    /**
     * @param SearchResult $searchResult
     * @return array
     */
    public function fetchDepartmentsFromSearchResults(SearchResult $searchResult): array
    {
        $departments = [];

        $productCategoriesFacet = null;
        /** @var SearchFacet $facet */
        foreach($searchResult->getCommonFacets() as $facet) {
            if ($facet->getName() === AlgoliaField::PRODUCT_CATEGORY) {
                $productCategoriesFacet = $facet;
                break;
            }
        }

        if (!$productCategoriesFacet) {
            return $departments;
        }

        $childrenCategories = $this->categoryService->fetchSubLevelCategories(
            $searchResult->getCategory(),
            $productCategoriesFacet->getValues()
        );

        arsort($childrenCategories);

        foreach ($childrenCategories as $id => $quantity) {
            $departments[] = [
                'id' => $id,
                'label' => $this->categoryService->getCategorieName($id),
                'quantity' => $quantity,
            ];
        }

        return $departments;
    }

    private function buildSearchResultFilters(array $searchResultFilters, string $locale): array
    {
        $filters = [];
        foreach($searchResultFilters as $searchResultFilter) {

            if (is_array($searchResultFilter)) {
                continue;
            }

            [$filterName, $filterValue] = explode(':', $searchResultFilter);
            $filter = new SearchFacet();
            $filter->setName($filterName);
            $attributeFacet = $this->facetAttributesService->getAttribute(str_replace('attributes.', '', $filterName), $locale);
            $filter->setLabel($attributeFacet->getLabel());

            $filter
                ->addValue(
                    (new SearchFacetValue())
                        ->setValue(str_replace('"', '', $filterValue))
                );

            $filters[] = $filter;
        }

        return $filters;
    }

    private function buildSearchResultFacets(array $facetFilter, array $searchResultFacets, string $locale): array
    {
        $facets = [];

        foreach($facetFilter as [$facetName, $allValues, $orderByTotalResults]) {
            $searchFacet = new SearchFacet();

            $attributeFacet = $this->facetAttributesService->getAttribute(str_replace('attributes.', '', $facetName), $locale);
            $searchFacet->setName($facetName);
            $searchFacet->setLabel($attributeFacet->getLabel());

            $facetValues = ($allValues) ? $this->offerService->getAllFacetValues($facetName) : $searchResultFacets[$facetName] ?? [];

            // ASC Alphabetical order
            uksort(
                $facetValues,
                function($facetValueNameA, $facetValueNameB) {
                    $facetValueNameA = strtolower($facetValueNameA);
                    $facetValueNameB = strtolower($facetValueNameB);

                    if (is_int($facetValueNameA) && is_int($facetValueNameB)) {
                        return ($facetValueNameA < $facetValueNameB) ? -1 : 1;
                    }

                    return strcmp($facetValueNameA, $facetValueNameB);
                }
            );

            // ASC numeric order
            if ($orderByTotalResults) {
                uasort(
                    $facetValues,
                    function($facetValueTotalA, $facetValueTotalB) {
                        return  ($facetValueTotalA > $facetValueTotalB) ? -1 : 1;
                    }
                );
            }

            foreach ($facetValues as $facetValueName => $facetValueTotal) {
                $searchFacet->addValue(
                    (new SearchFacetValue())
                        ->setValue($facetValueName)
                        ->setTotal($facetValueTotal)
                );
            }

            if (count($searchFacet->getValues())) {
                $facets[] = $searchFacet;
            }
        }

        return $facets;
    }

    public function mergeFacets(array $facets1, array $facets2): array
    {
        $mergeFacetValues = function(SearchFacet $facet1, SearchFacet $facet2): SearchFacet {
            /** @var SearchFacetValue $facetValue */
            foreach($facet2->getValues() as $facetValue) {
                if (!$facet1->hasFacetValue($facetValue)) {
                    $facet1->addValue($facetValue);
                }
            }

            return $facet1;
        };

        /** @var SearchFacet $searchFacetToMerge */
        foreach($facets2 as $searchFacetToMerge) {

            for($i = 0; $i < count($facets1) ; $i++) {
                if ($searchFacetToMerge->getName() === $facets1[$i]->getName()) {
                    $facets1[$i] = call_user_func($mergeFacetValues, $facets1[$i], $searchFacetToMerge);
                    break;
                }
            }
        }

        return $facets1;
    }

    private function buildSearchResultCommonFacets(array $searchResultFacets, string $searchType, string $locale): array
    {
        $facetFilter = [
            [ AlgoliaField::PRODUCT_CATEGORY, false, false],
            [ AlgoliaField::PRODUCT_MANUFACTURER, false, false],
            [ AlgoliaField::MERCHANT_NAME, false, false],
            [ AlgoliaField::CURRENCY, false, false],
            [ AlstomCustomAttributes::createFullAttributeName($this->alstomCustomAttribute->getIncoTerm()), false, false],
            [ AlstomCustomAttributes::createFullAttributeName($this->alstomCustomAttribute->getMoq()), false, false],
        ];

        if ($searchType !== SearchBarForm::SEARCH_TYPE_IN_MARKETPLACE_ANONYMOUS) {
            $facetFilter[] = [ AlstomCustomAttributes::createFullAttributeName($this->alstomCustomAttribute->getTotalDelayForCustomer()), false, false];
        }

        return $this->buildSearchResultFacets($facetFilter, $searchResultFacets, $locale);
    }

    private function buildSearchResultSpecificFacets(array $searchResultFacets, string $locale): array
    {
        $facetFilter = array_map(function($facetFilter) {
            return [$facetFilter, false, false];
        }, $this->facetAttributesService->fetchTechnicalAttributes($locale));

        $facetFilter = array_slice($facetFilter, 0, 25);

        return $this->buildSearchResultFacets($facetFilter, $searchResultFacets, $locale);
    }

    private function buildSearchResultSelectedFacets(array $facetFilters, string $locale): array
    {
        $selectedFacets = [];

        foreach($facetFilters as $facetName => $facetValues) {
            $facetName = str_replace(':', '.', $facetName);

            $selectedFacet = new SearchFacet();
            $selectedFacet->setName($facetName);
            $attributeFacet = $this->facetAttributesService->getAttribute(str_replace('attributes.', '', $facetName), $locale);
            $selectedFacet->setLabel($attributeFacet->getLabel());

            foreach ($facetValues as $facetValue) {
                $selectedFacet
                    ->addValue(
                        (new SearchFacetValue())
                            ->setValue($facetValue)
                    );
            }

            $selectedFacets[] = $selectedFacet;
        }

        return $selectedFacets;
    }

    public static function buildActiveSearchFacets(SearchResult $searchResult, Request $request): array
    {
        $result = [];
        $params = $request->query->all();
        $filters = array_filter($searchResult->getFilters(), function(SearchFacet $filter) {
            return ($filter->getName() === 'merchant.name');
        });
        $category = $searchResult->getCategory();

        if ($category) {
            $result[] = (new SearchFacetValue())
                ->setLabel('search.sidebar.category')
                ->setValue($category->getName())
                ->setLinkParameters(self::removeCategoryFromParams($params, $category->getId()));
        }

        /** @var SearchFacet $filter */
        foreach($filters as $filter) {
            /** @var SearchFacetValue $filterValue */
            foreach ($filter->getValues() as $filterValue) {
                $filterValue
                    ->setLabel($filter->getLabel())
                    ->setLinkParameters(self::removeFiltersFromParams($params));

                $result[] = $filterValue;
            }
        }

        /** @var SearchFacet $selectedFacet */
        foreach ($searchResult->getSelectedFacets() as $selectedFacet) {
            /** @var SearchFacetValue $facetValue */
            foreach ($selectedFacet->getValues() as $facetValue) {
                $name = str_replace('.', ':', $selectedFacet->getName());
                $facetValue
                    ->setLabel($selectedFacet->getLabel())
                    ->setLinkParameters(self::removeFacetFilterFromParams($params, $name, $facetValue->getValue()));

                $result[] = $facetValue;
            }
        }

        return $result;
    }

    public static function removeFacetFilterFromParams(array $params, string $facetFilterName, string $facetFilterValue): array
    {

        $facetFilterFields = [
            'commonFacetFilters',
            'specificFacetFilters',
        ];

        foreach($facetFilterFields as $facetFilterField) {
            if (isset($params[$facetFilterField]) && isset($params[$facetFilterField][$facetFilterName])) {
                if (false !== $key = array_search($facetFilterValue, $params[$facetFilterField][$facetFilterName])) {
                    unset($params[$facetFilterField][$facetFilterName][$key]);
                }

                if (!count($params[$facetFilterField][$facetFilterName])) {
                    unset($params[$facetFilterField][$facetFilterName]);
                }
            }
        }

        return $params;
    }

    public static function removeFiltersFromParams(array $params): array
    {
        if (isset($params['filters'])) {
            unset($params['filters']);
        }

        return $params;
    }

    public static function removeCategoryFromParams(array $params, int $categoryId): array
    {
        if (isset($params['category']) && $params['category'] == $categoryId) {
            unset($params['category']);
        }

        return $params;
    }

    public function getAvailableLocales(): array
    {
        return $this->availableLocales;
    }

    private function limitDAPToBuyerCountry(?User $user, AlgoliaQuery $query)
    {
        $country = 'france';
        if ($user) {
            $mainAddressCountry = $user->getCompany()->getMainAddress()->getCountry();
            if($mainAddressCountry) {
                $country = $user->getCompany()->getMainAddress()->getCountry()->getIzbFcaCountry();
            }
        }

        $orClauseFacetFilter = [
            sprintf(
                '%s:%s',
                AlstomCustomAttributes::createFullAttributeName($this->alstomCustomAttribute->getIncoTerm()),
                '-DAP'
            ),
            sprintf(
                '%s:%s',
                AlstomCustomAttributes::createFullAttributeName($this->alstomCustomAttribute->getCountryOfDelivery()),
                strtolower($country)
            ),
        ];
        $query->getQueryParams()->addFacetFilters($orClauseFacetFilter);
    }

    private function limitRestrictedProduct(?User $user, AlgoliaQuery $query) {
        $restrictedProductClause = [];
        // Get all product is no restricted (No or null)
        $restrictedProductClause = array_merge($restrictedProductClause, [
            sprintf(
                '%s:%s',
                AlstomCustomAttributes::createFullAttributeName($this->alstomCustomAttribute->getRestrictedProductBoolean()),
                '-Yes'
            ),
        ]);

        if ($user) {
            $tvaCode = $user->getCompany()->getIdentification();
            $restrictedProductFieldsCount = $this->alstomCustomAttribute->getRestrictedProductFieldsCount();
            // find restrited product have compagny ident on one of field
            for ($i=1; $i<=$restrictedProductFieldsCount; $i++) {
                $restrictedProductClause = array_merge($restrictedProductClause, [
                    sprintf(
                        '%s:%s',
                        str_replace(':i:',strval($i),AlstomCustomAttributes::createFullAttributeName($this->alstomCustomAttribute->getRestrictedProductField())),
                        $tvaCode
                    )
                ]);
            }
        }

        $query->getQueryParams()->addFacetFilters($restrictedProductClause);
    }

    private function addFacetFilters(array $facetFilters, AlgoliaQuery $query)
    {
        $disjunctiveFacets = [
            AlgoliaField::PRODUCT_MANUFACTURER,
            AlgoliaField::MERCHANT_NAME,
        ];

        foreach($facetFilters as $facetName => $facetValues) {
            $facetName = str_replace(':','.', $facetName);
            $facetValues = array_map(
                function($facetValue) use ($facetName) {
                    return $facetName . ':' . $facetValue;
                },
                $facetValues
            );

            if (in_array($facetName, $disjunctiveFacets)) {
                $query->getQueryParams()->addDisjunctiveFilters(...$facetValues);
            } else {
                $query->getQueryParams()->addFacetFilters(...$facetValues);
            }
        }
    }

    private function addFilters(array $filters, AlgoliaQuery $query)
    {
        $query->getQueryParams()->addFilters(...$filters);
    }

    private function setSortOrder(string $sortBy)
    {
        $this->offerService->setSortorder($sortBy);
    }

    private function setSearchType(string $searchType, AlgoliaQuery $query, ?int $companyId)
    {
        $allSearchableAttributes = $this->getSearchableAttributes();
        $supplierProductCompatibilities = array_map(
            [AlstomCustomAttributes::class, 'createFullAttributeName'],
            $this->alstomCustomAttribute->getSupplierProductCompatibilities()
        );

        if ($searchType === SearchBarForm::SEARCH_TYPE_IN_CATALOG) {
            $references = [];
            $query->disable();

            if ($companyId) {
                $references = $this->companyCatalogRepository->findByCompanyIdAndReference(
                    $companyId,
                    $query->getSearch(),
                    true
                );
            }

            if (count($references)) {
                $query->setSearch($references[0]->getExternalRef());
                $query->getQueryParams()
                    ->addFilters(
                        array_merge(
                            [
                                AlstomCustomAttributes::createFullAttributeName(
                                    $this->alstomCustomAttribute->getManufacturerReference()
                                ) . ':' . $query->getSearch(),
                                AlstomCustomAttributes::createFullAttributeName(
                                    $this->alstomCustomAttribute->getVendorReference()
                                ) . ':' . $query->getSearch(),
                            ],
                            array_map(
                                function($productCompatibleAttribute) use ($query) {
                                    return AlstomCustomAttributes::createFullAttributeName(
                                            $productCompatibleAttribute
                                        ) . ':' . $query->getSearch();
                                },
                                $this->alstomCustomAttribute->getSupplierProductCompatibilities()
                            )
                        )
                    )
                    ->typoTolerance(false);
                $query->enable();
            }
        }

        if ($searchType === SearchBarForm::SEARCH_TYPE_IN_MARKETPLACE_ANONYMOUS){
            $query->getQueryParams()
                ->addRestrictSearchableAttributes(
                    AlgoliaField::PRODUCT_MANUFACTURER,
                    AlstomCustomAttributes::createFullAttributeName(
                        $this->alstomCustomAttribute->getManufacturerReference()
                    ),
                    AlstomCustomAttributes::createFullAttributeName(
                        $this->alstomCustomAttribute->getVendorReference()
                    ),
                    AlgoliaField::MERCHANT_NAME,
                    AlgoliaField::DESCRIPTION,
                    AlgoliaField::NAME,
                    AlgoliaField::PRODUCT_CATEGORY_NAME,
                    AlgoliaField::PRODUCT_KEYWORDS
                )
                ->typoTolerance(true)
                ->orQuery($query->getSearch());
        }

        if ($searchType === SearchBarForm::SEARCH_TYPE_IN_MARKETPLACE){
            $query->getQueryParams()
                ->addRestrictSearchableAttributes(...$allSearchableAttributes)
                ->typoTolerance(true)
                ->orQuery($query->getSearch());
        }

        if ($searchType === SearchBarForm::SEARCH_TYPE_IN_PRODUCT_COMPATIBILITY) {
            $query->getQueryParams()
                ->addRestrictSearchableAttributes(
                    ...$supplierProductCompatibilities
                )
                ->typoTolerance(false);
        }
    }
}
