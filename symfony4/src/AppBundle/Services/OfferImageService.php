<?php

namespace AppBundle\Services;

use Open\IzbergBundle\Api\ProductOfferApi;
use Open\IzbergBundle\Model\Image;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;

class OfferImageService implements LoggerAwareInterface
{
    private ProductOfferApi $productOfferApi;
    private LoggerInterface $logger;

    /**
     * OfferImageService constructor.
     * @param ProductOfferApi $productOfferApi
     */
    public function __construct(ProductOfferApi $productOfferApi)
    {
        $this->productOfferApi = $productOfferApi;
    }

    public function removeImageFromOffer(int $offerId)
    {
        // retrieve images
        $images = array_map(
            fn(Image $image) => $image->getId(),
            $this->productOfferApi->getProductOfferImages($offerId)
        );

        if (count($images) === 0) {
            $this->logger->info('No image found for offer ID: ' . $offerId,
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::OFFER_IMAGE_NOT_FOUND,
                    'offerId' => $offerId
                ])
            );
        }

        // unassign image
        $this->productOfferApi->unassignImagesFromProductOffer($offerId, $images);
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }


}
