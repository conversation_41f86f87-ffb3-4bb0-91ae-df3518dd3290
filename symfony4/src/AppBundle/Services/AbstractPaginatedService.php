<?php

namespace AppBundle\Services;

use AppBundle\FilterQueryBuilder\FilterQueryBuilderInterface;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\QueryBuilder;
use Symfony\Component\HttpFoundation\Request;

class AbstractPaginatedService extends AbstractBddService
{
    protected $paginator;
    protected $filterQueryBuilder;

    public function __construct(EntityManagerInterface $em, string $entityName, $paginator, FilterQueryBuilderInterface $filterQueryBuilder = null)
    {
        parent::__construct($em, $entityName);
        $this->paginator = $paginator;
        $this->filterQueryBuilder = $filterQueryBuilder;
    }

    /**
     * get the default query builder for the entity
     * @return \Doctrine\ORM\QueryBuilder
     */
    public function getDefaultQueryBuilder(){
        $qb = $this->em->createQueryBuilder();
        $qb->select('e')
            ->from($this->entityName, 'e');

        return $qb;
    }


    /***
    * get a paginator based on the specified query builder
    * @param $qb
    * @param $page
    * @param $numberPerPage
    * @param $request
    * @param array $options
    *
    * @return mixed
    */
    public function getPaginatorByQb($qb, $page, $numberPerPage, $request, $options = []){
        return $this->paginator->paginate(
            $qb->getQuery(),
            $request->query->getInt('page', 1),
            $numberPerPage,
            $options
        );
    }

    /**
    * get a default paginator for this entity
    * @param $page
    * @param $numberPerPage
    * @param $request
    *
    * @return mixed
    */
    public function getPaginator($page, $numberPerPage, $request){
        return $this->getPaginatorByQb($this->getDefaultQueryBuilder(), $page, $numberPerPage, $request);
    }

    public function getFilteredPaginator ($page, $numberPerPage, $request, $data){
        $qb = $this->em->createQueryBuilder();
        $qb->select('e')
            ->from($this->entityName, 'e');

        $this->filterQueryBuilder->build($qb, $data);

        return $this->paginator->paginate(
            $qb->getQuery(),
            $request->query->getInt('page', 1),
            $numberPerPage
        );
    }
}
