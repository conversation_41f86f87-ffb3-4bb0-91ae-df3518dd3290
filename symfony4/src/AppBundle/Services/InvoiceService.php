<?php

namespace AppBundle\Services;

use AppBundle\Command\Payment\Utils\PaymentUtils;
use AppBundle\Entity\Company;
use AppBundle\Entity\User;
use Knp\Component\Pager\PaginatorInterface;
use Open\IzbergBundle\Model\MerchantOrder;
use AppBundle\Repository\CompanyRepository;
use AppBundle\Repository\CreditNoteRepository;
use AppBundle\Repository\InvoiceRepository;
use Open\IzbergBundle\Api\InvoiceApi;
use Open\IzbergBundle\Api\IzbergUtils;
use Open\IzbergBundle\Api\OrderApi;
use Open\IzbergBundle\Model\FetchInvoicesResponse;
use Open\IzbergBundle\Model\Invoice;
use Open\IzbergBundle\Model\InvoiceLine;
use Open\LogBundle\Utils\EventNameEnum;
use Open\IzbergBundle\Model\Order;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;

class InvoiceService implements LoggerAwareInterface
{
    public const JOB_QUEUE_INVOICE_SYNC = 'invoice_sync';

    public const LAST_SYNC = 'invoice_last_sync';

    private InvoiceApi $invoiceApi;
    private PaginatorInterface $paginator;
    private SerializerService $serializerService;
    private JobService $jobService;
    private InvoiceRepository $invoiceRepository;
    private CompanyRepository $companyRepository;
    private OrderApi $orderApi;
    private MailService $mailService;
    private WebHelpIbanService $webHelpIbanService;
    private CreditNoteRepository $creditNoteRepository;
    private string $izbergDatePattern;
    private string $fromEmail;
    private string $fromName;
    private LoggerInterface $logger;

    /**
     * InvoiceService constructor.
     * @param string $izbergDatePattern
     * @param string $fromEmail
     * @param string $fromName
     * @param InvoiceApi $invoiceApi
     * @param PaginatorInterface $paginator
     * @param SerializerService $serializerService
     * @param JobService $jobService
     * @param InvoiceRepository $invoiceRepository
     * @param CompanyRepository $companyRepository
     * @param OrderApi $orderApi
     * @param MailService $mailService
     * @param WebHelpIbanService $webHelpIbanService
     * @param CreditNoteRepository $creditNoteRepository
     */
    public function __construct(
        string $izbergDatePattern,
        string $fromEmail,
        string $fromName,
        InvoiceApi $invoiceApi,
        PaginatorInterface $paginator,
        SerializerService $serializerService,
        JobService $jobService,
        InvoiceRepository $invoiceRepository,
        CompanyRepository $companyRepository,
        OrderApi $orderApi,
        MailService $mailService,
        WebHelpIbanService $webHelpIbanService,
        CreditNoteRepository $creditNoteRepository
    )
    {
        $this->invoiceApi = $invoiceApi;
        $this->paginator = $paginator;
        $this->serializerService = $serializerService;
        $this->jobService = $jobService;
        $this->invoiceRepository = $invoiceRepository;
        $this->companyRepository = $companyRepository;
        $this->orderApi = $orderApi;
        $this->mailService = $mailService;
        $this->webHelpIbanService = $webHelpIbanService;
        $this->creditNoteRepository = $creditNoteRepository;
        $this->izbergDatePattern = $izbergDatePattern;
        $this->fromEmail = $fromEmail;
        $this->fromName = $fromName;
    }

    public function fetchInvoiceByReceiverId($receiverId, array $options = []){
        return $this->invoiceApi->fetchInvoiceByReceiverId($receiverId, $options);
    }

    /**
     * @param int $offset
     * @param int $limit
     * @param \DateTimeImmutable|null $fromDate
     * @return FetchInvoicesResponse
     */
    public function fetchIzbergInvoices(int $offset = 0, int $limit = 10, ?\DateTimeImmutable $fromDate = null): FetchInvoicesResponse
    {
        return $this->invoiceApi->fetchAllInvoices($offset, $limit, $fromDate);
    }

    public function paginateUserInvoicesByPaymentStatus(User $user, string $paymentStatus, string $search = null, $page = 1, $limit = 10, $options = [])
    {
        $parameters = [];
        $targetInvoice = $this->invoiceRepository->createQueryBuilder('i');
        $targetCreditNote = $this->creditNoteRepository->createQueryBuilder('cn');

        $targetInvoice
            ->where('i.status = :emittedStatus')
            ->andWhere('i.paymentStatus = :paymentStatus')
            ->andWhere('i.company = :company')
            ->orderBy('i.createdOn', 'DESC');


        $targetCreditNote
            ->where('cn.status = :emittedStatus')
            ->andWhere('cn.paymentStatus = :paymentStatus')
            ->andWhere('cn.company = :company')
            ->orderBy('cn.createdOn', 'DESC');

        if ($search != null) {
            /** @psalm-suppress TooManyArguments */
            $targetInvoice
                ->andWhere(
                    $targetInvoice->expr()->orX(
                        $targetInvoice->expr()->like('i.numberId', ':search'),
                        $targetInvoice->expr()->like('i.issuerName', ':search'),
                        $targetInvoice->expr()->like('i.createdOn', ':search'),
                        $targetInvoice->expr()->like('i.numOrder', ':search')
                    )
                );
            $parameters['search'] = '%' .$search . '%';

            /** @psalm-suppress TooManyArguments */
            $targetCreditNote
                ->andWhere(
                    $targetCreditNote->expr()->orX(
                        $targetCreditNote->expr()->like('cn.numberId', ':search'),
                        $targetCreditNote->expr()->like('cn.issuerName', ':search'),
                        $targetCreditNote->expr()->like('cn.createdOn', ':search'),
                        $targetCreditNote->expr()->like('cn.numOrder', ':search')
                    )
                );
            $parameters['search'] = '%' .$search . '%';
        }

        $parameters['emittedStatus'] = 'emitted';
        $parameters['paymentStatus'] = $paymentStatus;
        $parameters['company'] = $user->getCompany();

        $targetInvoice->setParameters($parameters);
        $targetInvoiceResult = $targetInvoice->getQuery()->getResult();
        $targetCreditNote->setParameters($parameters);
        $targetCreditNoteResult = $targetCreditNote->getQuery()->getResult();

        $target = array_merge($targetInvoiceResult, $targetCreditNoteResult);

        usort($target, function ($targetA, $targetB) {
            return intval($targetA->getCreatedOn() < $targetB->getCreatedOn());
        });

        return $this->paginator->paginate($target, $page, $limit, $options);
    }

    public function totalToPayEUR(User $user): ?float
    {
        return $this->totalToPay($user, \AppBundle\Entity\Invoice::CURRENCY_EUR);
    }

    public function totalToPayUSD(User $user): ?float
    {
        return $this->totalToPay($user, \AppBundle\Entity\Invoice::CURRENCY_USD);
    }

    public function totalToPay(User $user, string $currency): ?float
    {
        $queryBuilder = $this->invoiceRepository->createQueryBuilder('i')
            ->select('SUM(i.remainingAmount)');

        $queryBuilder
            ->where('i.status = :emittedStatus')
            ->andWhere('i.paymentStatus = :paymentStatus')
            ->andWhere('i.company = :company')
            ->andWhere('i.currency = :currency');

        $parameters['emittedStatus'] = 'emitted';
        $parameters['paymentStatus'] = 'not_paid';
        $parameters['company'] = $user->getCompany();
        $parameters['currency'] = $currency;

        $queryBuilder->setParameters($parameters);

        $invoiceRemainingamount =  $queryBuilder->getQuery()->getSingleScalarResult();

        $queryBuilderCreditNote = $this->creditNoteRepository->createQueryBuilder('i')
            ->select('SUM(i.totalAmountWithTaxes)');

        $queryBuilderCreditNote
            ->where('i.status = :emittedStatus')
            ->andWhere('i.paymentStatus = :paymentStatus')
            ->andWhere('i.company = :company')
            ->andWhere('i.currency = :currency');

        $parameters['emittedStatus'] = 'emitted';
        $parameters['paymentStatus'] = 'not_paid';
        $parameters['company'] = $user->getCompany();
        $parameters['currency'] = $currency;

        $queryBuilderCreditNote->setParameters($parameters);

        $creditNoteAmount = $queryBuilderCreditNote->getQuery()->getSingleScalarResult();

        return $invoiceRemainingamount - $creditNoteAmount;

    }

    public function dueDateTotalToPayEUR(User $user): ?float
    {
        return $this->dueDateTotalToPay($user, \AppBundle\Entity\Invoice::CURRENCY_EUR);
    }

    public function dueDateTotalToPayUSD(User $user): ?float
    {
        return $this->dueDateTotalToPay($user, \AppBundle\Entity\Invoice::CURRENCY_USD);
    }

    public function dueDateTotalToPay(User $user, string $currency): ?float
    {
        $queryBuilder = $this->invoiceRepository->createQueryBuilder('i')
            ->select('SUM(i.remainingAmount)');

        $queryBuilder
            ->where('i.status = :emittedStatus')
            ->andWhere('i.paymentStatus = :paymentStatus')
            ->andWhere('i.company = :company')
            ->andWhere('i.currency = :currency')
            ->andWhere('i.dueOn < :now');

        $parameters['emittedStatus'] = 'emitted';
        $parameters['paymentStatus'] = 'not_paid';
        $parameters['company'] = $user->getCompany();
        $parameters['currency'] = $currency;
        $parameters['now'] = new \DateTimeImmutable();

        $queryBuilder->setParameters($parameters);

        return $queryBuilder->getQuery()->getSingleScalarResult();
    }

    public function toPayThisMonthEUR(User $user): ?float
    {
        return $this->toPayThisMonth($user, \AppBundle\Entity\Invoice::CURRENCY_EUR);
    }

    public function toPayThisMonthUSD(User $user): ?float
    {
        return $this->toPayThisMonth($user, \AppBundle\Entity\Invoice::CURRENCY_USD);
    }

    public function toPayThisMonth(User $user, string $currency): ?float
    {
        $now = new \DateTimeImmutable();
        $firstDayOfCurrentMonth = \DateTimeImmutable::createFromFormat('Y-n', $now->format('Y-n'));
        $firstDayOfNextMonth = $firstDayOfCurrentMonth->add(new \DateInterval('P1M'));

        $queryBuilder = $this->invoiceRepository->createQueryBuilder('i')
            ->select('SUM(i.remainingAmount)');

        $queryBuilder
            ->where('i.status = :emittedStatus')
            ->andWhere('i.paymentStatus = :paymentStatus')
            ->andWhere('i.company = :company')
            ->andWhere('i.currency = :currency')
            ->andWhere('i.dueOn >= :firstDayOfCurrentMonth')
            ->andWhere('i.dueOn < :firstDayOfNextMonth');

        $parameters['emittedStatus'] = 'emitted';
        $parameters['paymentStatus'] = 'not_paid';
        $parameters['company'] = $user->getCompany();
        $parameters['currency'] = $currency;
        $parameters['firstDayOfCurrentMonth'] = $firstDayOfCurrentMonth;
        $parameters['firstDayOfNextMonth'] = $firstDayOfNextMonth;

        $queryBuilder->setParameters($parameters);

        return $queryBuilder->getQuery()->getSingleScalarResult();
    }

    public function latePaymentEUR(User $user): ?float
    {
        return $this->latePayment($user, \AppBundle\Entity\Invoice::CURRENCY_EUR);
    }

    public function latePaymentUSD(User $user): ?float
    {
        return $this->latePayment($user, \AppBundle\Entity\Invoice::CURRENCY_USD);
    }

    public function latePayment(User $user, string $currency): ?float
    {
        $queryBuilder = $this->invoiceRepository->createQueryBuilder('i')
            ->select('SUM(i.remainingAmount)');

        $queryBuilder
            ->where('i.status = :emittedStatus')
            ->andWhere('i.paymentStatus = :paymentStatus')
            ->andWhere('i.company = :company')
            ->andWhere('i.currency = :currency')
            ->andWhere('i.dueOn < :now');

        $parameters['emittedStatus'] = 'emitted';
        $parameters['paymentStatus'] = 'not_paid';
        $parameters['company'] = $user->getCompany();
        $parameters['currency'] = $currency;
        $parameters['now'] = new \DateTimeImmutable();

        $queryBuilder->setParameters($parameters);

        $invoiceRemainingamount =  $queryBuilder->getQuery()->getSingleScalarResult();

        $queryBuilderCreditNote = $this->creditNoteRepository->createQueryBuilder('i')
            ->select('SUM(i.totalAmountWithTaxes)');

        $queryBuilderCreditNote
            ->where('i.status = :emittedStatus')
            ->andWhere('i.paymentStatus = :paymentStatus')
            ->andWhere('i.company = :company')
            ->andWhere('i.currency = :currency')
            ->andWhere('i.createdOn < :now');

        $parameters['emittedStatus'] = 'emitted';
        $parameters['paymentStatus'] = 'not_paid';
        $parameters['company'] = $user->getCompany();
        $parameters['currency'] = $currency;
        $parameters['now'] = new \DateTimeImmutable();

        $queryBuilderCreditNote->setParameters($parameters);

        $creditNoteAmount = $queryBuilderCreditNote->getQuery()->getSingleScalarResult();

        return $invoiceRemainingamount - $creditNoteAmount;
    }

    public function sync(FetchInvoicesResponse $invoiceResult): void
    {
        /** @var Invoice $invoice */
        foreach ($invoiceResult->getObjects() as $invoice) {
            $invoiceEntity = $this->invoiceRepository->findOneBy(['izbergId' => $invoice->getId()]);

            if (null === $invoiceEntity) {
                $invoiceEntity = new \AppBundle\Entity\Invoice();

                $invoiceEntity->setIzbergId($invoice->getId());

                $company = $this->companyRepository->findOneBy(['izbergUserId' => $invoice->getReceiver()->getId()]);
                $invoiceEntity->setCompany($company);
            }

            $invoiceEntity->setNumberId($invoice->getIdNumber());
            $invoiceEntity->setPdfFile(!empty($invoice->getPdfFile()));
            $invoiceEntity->setIssuerName($invoice->getIssuerName());
            $invoiceEntity->setCreatedOn(new \DateTimeImmutable($invoice->getCreatedOn()));

            $this->updateInvoiceOrder($invoiceEntity, $invoice);

            $invoiceEntity->setTotalAmountWithTaxes($invoice->getTotalAmountWithTaxes());

            $invoiceEntity->setCurrency($invoice->getCurrency());

            $invoiceEntity->setDueOn(new \DateTimeImmutable($invoice->getDueOn()));
            $invoiceEntity->setRemainingAmount($invoice->getRemainingAmount());
            $invoiceEntity->setPaymentStatus($invoice->getPaymentStatus());
            $invoiceEntity->setStatus($invoice->getStatus());

            $this->invoiceRepository->save($invoiceEntity);

            $this->logger->info(
                sprintf('invoice with izbergId %s synchronized',  $invoice->getId()),
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::INVOICE,
                    'izbergId' => $invoiceEntity->getIzbergId(),
                    'numberId' => $invoiceEntity->getNumberId(),
                    'lastModified' => $invoice->getLastModified()
                ])
            );
        }
    }

    public function updateInvoiceOrder(\AppBundle\Entity\Invoice $invoiceEntity, Invoice $invoice)
    {
        if (!count($invoice->getInvoiceLines())) {
            return;
        }

        $merchantOrderId = call_user_func(function() use ($invoice) {
            $invoiceLines = $invoice->getInvoiceLines();

            /** @var InvoiceLine $invoiceLine */
            foreach ($invoiceLines as $invoiceLine) {
                $orderItem = $invoiceLine->getOrderItem();
                if ($orderItem) {
                    return IzbergUtils::parseIzbergResourceAndGetId($orderItem->getMerchantOrder());
                }
            }

            return null;
        });

        if ($merchantOrderId) {
            $merchantOrder = $this->orderApi->fetchMerchantOrderById($merchantOrderId);

            $invoiceEntity->setOrderId($merchantOrder->getOrder()->getId());
            $invoiceEntity->setOrderCreatedOn(new \DateTimeImmutable($merchantOrder->getOrder()->getCreatedOn()));
            $invoiceEntity->setNumOrder($merchantOrder->getOrder()->getIdNumber().' - '. $merchantOrder->getMerchant()->getId());
        }
    }

    /**
     * @param $invoiceId
     * @return string|null
     */
    public function fetchInvoicePdfUrl($invoiceId, $hasPdfFile): ?string
    {
        return $this->invoiceApi->fetchCustomerInvoicePdf($invoiceId, $hasPdfFile);
    }

    /**
     * @param $invoice
     * @param $template
     * @throws \AppBundle\Command\Payment\Exception\PaymentCommandException
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function sendInvoiceReminderByInvoice($invoice, $template){

        /** @var MerchantOrder $merchantOrder */
        $merchantOrder = $this->orderApi->fetchMerchantOrderById(IzbergUtils::parseIzbergResourceAndGetId(PaymentUtils::getMerchantOrderFromInvoice($invoice)));

        /** @var Order $order */
        $order = $this->orderApi->fetchOrder($merchantOrder->getOrder()->getId());

        /////////////////////////////////////////////////////////////////////////
        /// Send reminder email
        /////////////////////////////////////////////////////////////////////////

        $invoiceCreatedOn = IzbergUtils::parseIzbergDate($invoice->issued_on, $this->izbergDatePattern);
        $invoiceDueOn = IzbergUtils::parseIzbergDate($invoice->due_on, $this->izbergDatePattern);
        $orderCreatedOn = IzbergUtils::parseIzbergDate($order->getCreatedOn(), $this->izbergDatePattern);

        $now = time(); // or your date as well
        $your_date = strtotime($order->getCreatedOn());
        $datediff = $now - $your_date;

        $days = round($datediff / (60 * 60 * 24));

        $notifiedUsers = [];

        /** @var Company $company */
        $company = $this->companyRepository->findCompanyByIzbergUserId($merchantOrder->getUser()->getId());

        /** @var User $user */
        if ($company != null) {
            foreach ($company->getUsers() as $user) {
                if ($user !== null && $user->isEnabled() && !in_array($user->getEmail(), $notifiedUsers)) {
                    $notifiedUsers [] = $user->getEmail();
                    $this->mailService->sendEmailMessage($template,
                        $user->getLocale(),
                        $user->getEmail(),
                        [
                            MailService::FIRST_NAME_VAR => $user->getFirstname(),
                            MailService::LAST_NAME_VAR => $user->getLastname(),
                            'orderNumber' => $order->getIdNumber(),
                            'ibanAccountName' => $this->webHelpIbanService->getIbanAccountNameFromCurrency($order->getCurrency()),
                            'iban' => $this->webHelpIbanService->getIbanFromCurrency($order->getCurrency()),
                            'reconciliationKey' => $invoice->payment_details,
                            'invoiceDate' => $invoiceCreatedOn->format("d/m/Y"),
                            'nbDays' => $days,
                            'remainingAmount' => $invoice->remaining_amount,
                            'currency' => PaymentUtils::getCurrencyFromInvoice($invoice),
                            'orderDate' => $orderCreatedOn->format("d/m/Y"),
                            'invoiceNumber' => $invoice->id_number,
                            'invoiceAmount' => $invoice->expected_amount,
                            'dueDate' => $invoiceDueOn->format("d/m/Y")
                        ],
                        $this->fromEmail,
                        $this->fromName
                    );
                }
            }
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
