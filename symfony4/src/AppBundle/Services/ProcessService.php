<?php

namespace AppBundle\Services;

use AppBundle\Factory\ProcessIdFactory;
use AppBundle\Model\ProcessId;

class ProcessService
{
    private ProcessIdFactory $processIdFactory;
    private ProcessIdProvider $processIdProvider;

    public function __construct(ProcessIdFactory $processIdFactory, ProcessIdProvider $processIdProvider)
    {
        $this->processIdFactory = $processIdFactory;
        $this->processIdProvider = $processIdProvider;
    }

    public function processBeginFor($object)
    {
        $processId = $this->processIdFactory->build($object);
        $this->processIdProvider->setProcessId($processId);
    }

    public function processEndFor()
    {
        $this->processIdProvider->setProcessId(null);
    }
}
