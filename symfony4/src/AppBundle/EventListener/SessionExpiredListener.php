<?php

namespace AppBundle\EventListener;

use AppBundle\Services\SecurityService;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Session\Session;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\HttpKernelInterface;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;

class SessionExpiredListener
{
    /**
     * @var int
     */
    private $maxIdleTime;

    /**
     * @var SessionInterface
     */
    private $session;

    private $tokenStorage;

    private $translator;

    /**
     * @var AuthorizationCheckerInterface
     */
    private $authorizationChecker;

    /**
     * @var RouterInterface
     */
    private $router;

    private $requestStack;

    public function __construct(
        RequestStack $requestStack,
        AuthorizationCheckerInterface $authorizationChecker,
        TokenStorageInterface $tokenStorage,
        RouterInterface $router,
        $translator,
        int $maxIdleTime = 0
    )
    {
        $this->requestStack = $requestStack;
        $this->authorizationChecker = $authorizationChecker;
        $this->tokenStorage = $tokenStorage;
        $this->translator = $translator;
        $this->router = $router;
        $this->maxIdleTime = $maxIdleTime;
    }

    public function onKernelRequest(RequestEvent $event)
    {
        if (HttpKernelInterface::MAIN_REQUEST != $event->getRequestType()) {
            return;
        }

        if ($this->maxIdleTime > 0) {
            $this->session = $this->requestStack->getSession();

            $this->session->start();
            $lapse = time() - $this->session->getMetadataBag()->getLastUsed();

            if ($lapse > $this->maxIdleTime) {

                $logoutRouteName = 'fos_user_security_logout';
                if($this->authorizationChecker->isGranted([SecurityService::ROLE_SUPER_ADMIN, SecurityService::ROLE_OPERATOR])){
                    $logoutRouteName = 'admin_logout';
                }
                $this->tokenStorage->setToken(null);
                if($this->session instanceof Session){
                    $this->session->getFlashBag()->add('info', $this->translator->trans('session.logout', [], 'AppBundle'));
                }


                //Redirect URL to logout
                $event->setResponse(new RedirectResponse($this->router->generate($logoutRouteName)));
            }
        }
    }
}
