<?php

namespace AppBundle\EventListener;

use AppBundle\Entity\ActionHistorization;

use AppBundle\Entity\Node;
use AppBundle\Entity\NodeContent;
use AppBundle\Entity\User;
use Doctrine\ORM\Event\OnFlushEventArgs;
use Doctrine\ORM\ORMException;
use Doctrine\ORM\UnitOfWork;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

class DoctrineEventListener implements LoggerAwareInterface
{
    private $entitiesPath;

    const ACTION_CREATE = "create";
    const ACTION_UPDATE = "update";
    const ACTION_DELETE = "delete";

    private LoggerInterface $logger;
    private TokenStorageInterface $securityTokenStorage;
    private array $typesToBeChecked;

    /**
     * DoctrineEventListener constructor.
     * @param array $typesToBeChecked
     * @param array $entitiesPath
     * @param TokenStorageInterface $securityTokenStorage
     */
    public function __construct(
        array $typesToBeChecked,
        array $entitiesPath,
        TokenStorageInterface $securityTokenStorage
    )
    {
        $this->securityTokenStorage = $securityTokenStorage;
        $this->typesToBeChecked = $typesToBeChecked;
        $this->entitiesPath = $entitiesPath;
    }

    public function onFlush(OnFlushEventArgs $args)
    {

        $em = $args->getEntityManager();
        $unitOfWork = $em->getUnitOfWork();

        //list CREATE operations
        foreach ($unitOfWork->getScheduledEntityInsertions() as $keyEntity => $entity) {
            if ($this->checkIfHistorizedEntity($entity)) {
                $this->generateHistory($entity, self::ACTION_CREATE, $keyEntity, $unitOfWork, $em);
            }
        }


        //list UPDATE operations
        foreach ($unitOfWork->getScheduledEntityUpdates() as $keyEntity => $entity) {

            if ($this->checkIfHistorizedEntity($entity)) {
                $this->generateHistory($entity, self::ACTION_UPDATE, $keyEntity, $unitOfWork, $em);
            }
        }

        //list DELETE operations
        foreach ($unitOfWork->getScheduledEntityDeletions() as $keyEntity => $entity) {

            if ($this->checkIfHistorizedEntity($entity)) {
                $this->generateHistory($entity, self::ACTION_DELETE, $keyEntity, $unitOfWork, $em);
            }
        }
    }

    /**
     * cheched if the actions on this entity must be historized
     * @param mixed $entity
     * @return bool true if the actions on this entity must be historized. false otherwise.
     */
    private function checkIfHistorizedEntity($entity)
    {
        foreach ($this->typesToBeChecked as $typeTobeCheck) {
            if ($entity instanceof $typeTobeCheck) {
                return true;
            }
        }
        return false;
    }

    /**
     * @param $entity
     * @param $type
     * @param $unitOfWork
     * @param $em
     * @param $hash
     */
    private function generateHistory($entity, $type, $hash, UnitOfWork $unitOfWork, $em)
    {
        $history = new ActionHistorization();
        $history->setRefEntityId($entity->getTechnicalId());
        $history->setClassName(strval($this->normalizeEntityClassName(get_class($entity))));

        if ($entity instanceof Node) {
            $history->setClassName($history->getClassName() . '\\' . $entity->getType());
        } else if ($entity instanceof NodeContent && $entity->getNode()) {
            $history->setClassName($history->getClassName() . '\\' . $entity->getNode()->getType());
        }
        $history->setType($type);

        //we only want to get change set on update modification type
        if ($type === self::ACTION_UPDATE) {
            try {
                $changes = $unitOfWork->getEntityChangeSet($entity);
                if ($changes != null && is_array($changes)) {
                    $reflectionObject = new \ReflectionClass(get_class($entity));
                    $object = $reflectionObject->newInstance();
                    foreach ($changes as $key => $value) {
                        //first element is old value, second is new value
                        if (is_array($value) && count($value) === 2) {
                            $property = $reflectionObject->getProperty($key);
                            $property->setAccessible(true);
                            $property->setValue($object, $value[1]);
                        }
                    }
                    $history->setChangeSet(json_encode($object));
                }
            } catch (\Exception $e) {
                //we generate empty history
                $history->setChangeSet(json_encode(new \stdClass()));
            }
        } else if ($type === self::ACTION_CREATE) {
            $history->setChangeSet(json_encode($entity));
        }

        $history->setCreatedAt(new \DateTime());

        $user = $this->getCurrentUser();

        if ($user instanceof User) {
            $history->addUser($user);
        }


        try {
            //write legal log. If action is create, the log is written later with the id
            if ($type !== self::ACTION_CREATE) {
                $this->writeLog($user, $history);
            }

            $em->persist($history);
            // Instead of $em->flush() because we are already in flush process

            /**
             * in onFlush context we can only persist doctrine entity using:
             * $unitOfWork->computeChangeSet($em->getClassMetadata(get_class($history)), $history);
             *
             * @psalm-suppress InternalMethod
             */
            $unitOfWork->computeChangeSet($em->getClassMetadata(get_class($history)), $history);
        } catch (ORMException $e) {
            $this->logger->error("error while persisting history: " . $e->getMessage(),
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::TECHNICAL_ERROR,
                    "exception" => $e->getTraceAsString()
                ])
            );
        }
    }

    /**
     * write a log
     * @param User $user the user (or null)
     * @param ActionHistorization $history the action history to log
     */
    private function writeLog($user, $history)
    {

        if ($user !== null && $user instanceof User) {
            $this->logger->info("the following operation occurred on an entity: " . $history->getType(),
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::ACTION_HISTORY,
                    LogUtil::USER_NAME=>$user->getUsername(),
                    "entity_id" => $history->getRefEntityId(),
                    "class_name" => $history->getClassName(),
                    "operation_type" => $history->getType(),
                    "change_set" => $history->getChangeSet()
                ])
            );
        } else {
            $this->logger->info("the following operation occurred on an entity: " . $history->getType(),
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::ACTION_HISTORY,
                    LogUtil::USER_NAME=>'anonymous',
                    "entity_id" => $history->getRefEntityId(),
                    "class_name" => $history->getClassName(),
                    "operation_type" => $history->getType(),
                    "change_set" => $history->getChangeSet()
                ])
                );
        }
    }

    /**
     * @return User the current user of null
     */
    private function getCurrentUser(): ?User
    {
        $user = null;
        if ($this->securityTokenStorage->getToken() !== null) {

            $user = $this->securityTokenStorage->getToken()->getUser();
        }

        return ($user instanceof User) ? $user : null;
    }


    /**
     * Used to avoid to log proxified class name
     * @param string $className name of the class (can be a proxified entity)
     * @return bool|string
     */
    private function normalizeEntityClassName($className)
    {

        foreach ($this->entitiesPath as $path) {
            $pos = strpos($className, $path);
            if ($pos !== false) {
                return substr($className, $pos, strlen($className));
            }
        }
        return $className;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
