<?php

namespace AppBundle\EventSubscriber;

use AppBundle\Entity\Connection;
use AppBundle\Services\SecurityService;
use AppBundle\Util\SettingsProvider;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\ORMException;
use FOS\UserBundle\Event\UserEvent;
use FOS\UserBundle\FOSUserEvents;
use FOS\UserBundle\Model\UserManagerInterface;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use ReCaptcha\ReCaptcha;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\AuthenticationEvents;
use Symfony\Component\Security\Core\Exception\CustomUserMessageAuthenticationException;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\Security\Http\Event\InteractiveLoginEvent;
use Symfony\Component\Security\Http\SecurityEvents;
use AppBundle\Entity\User;
use Symfony\Contracts\Translation\TranslatorInterface;


class AuthenticationSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private EntityManagerInterface $em;
    private UserManagerInterface $um;
    private SettingsProvider $settings;
    private LoggerInterface $logger;
    private TranslatorInterface $translator;
    private RequestStack $requestStack;
    private RouterInterface $router;
    private SecurityService $securityService;
    private SessionInterface $session;
    private string $administratorToken;
    private ReCaptcha $recaptcha;

    /***
     * AuthenticationSubscriber constructor.
     *
     * @param EntityManagerInterface $em
     * @param UserManagerInterface $um
     * @param SettingsProvider $sp
     * @param TranslatorInterface $translator
     * @param RequestStack $requestStack
     * @param RouterInterface $router
     * @param SecurityService $securityService
     * @param ReCaptcha $reCaptcha
     */
    public function __construct(
        EntityManagerInterface $em,
        UserManagerInterface $um,
        SettingsProvider $sp,
        TranslatorInterface $translator,
        RequestStack $requestStack,
        RouterInterface $router,
        SecurityService $securityService,
        ReCaptcha $reCaptcha
    )
    {

        $this->em = $em;
        $this->um = $um;
        $this->settings = $sp;
        $this->translator = $translator;
        $this->requestStack = $requestStack;
        $this->router = $router;
        $this->securityService = $securityService;
        $this->recaptcha = $reCaptcha;
    }

    /**
     * Build an array of events we want to listen to
     */
    public static function getSubscribedEvents(): array
    {
        return array(
            \Symfony\Component\Security\Http\Event\LoginFailureEvent::class => 'onLoginFailure',
            FOSUserEvents::SECURITY_IMPLICIT_LOGIN => 'onImplicitLogin',
            SecurityEvents::INTERACTIVE_LOGIN => 'onInteractiveLogin',
            KernelEvents::REQUEST => ['beforeFirewall', 10]
        );
    }


    /**
     * intercept request before firewall
     * @param RequestEvent $event
     */
    public function beforeFirewall(RequestEvent $event)
    {
        $this->session = $this->requestStack->getSession();
        $request = $event->getRequest();
        if ($request->isMethod(Request::METHOD_POST)) {
            if ($request->get('_route') === 'login_check') {

                // check recaptcha
                $remoteip = $request->getClientIp();
                $answer = $request->get('g-recaptcha-response');

                // Verify user response with Google
                $response = $this->recaptcha->verify($answer, $remoteip);

                if (!$response->isSuccess()) {
                    $exception = new CustomUserMessageAuthenticationException($this->translator->trans('buyer.security.login.error.captcha'));
                    $request->getSession()->set('security.last_error', $exception);
                    $event->setResponse(new RedirectResponse($this->router->generate('front.login.failure')));
                }

                /** @var User $user */
                $user = $this->um->findUserByUsernameOrEmail($request->get('_username'));

                if ($user &&
                    $this->isUserAdmin($user) &&
                    $user->getLoginAttempt() >= $this->settings->get('security.login_attempt_max')) {

                    //from here, all non meeting requirements must redirect user to authentication failure
                    $adminToken = $request->get('_admin_token');

                    if (!$adminToken || $adminToken !== $this->settings->get('security.login_administrator_token')) {
                        $this->logger->error("admin user has exceed the max login attempt and has entered a bad token",
                            LogUtil::buildContext([
                                LogUtil::EVENT_NAME=>EventNameEnum::SECURITY_EVENT,
                                "username" => $user->getUsername(),
                                "login_attempt" => $user->getLoginAttempt(),
                                "expectedToken" => $this->administratorToken,
                                "actualToken" => $adminToken
                            ])
                        );
                        $event->setResponse(new RedirectResponse($this->router->generate('front.login.failure')));
                        $this->session->set("SESSION_USE_TOKEN", true);
                    }
                }

            }

        }

    }

    public function onLoginFailure()
    {
        $this->session = $this->requestStack->getSession();
        /** @var Request $request */
        $request = $this->requestStack->getCurrentRequest();

        // Get the username or email used in the form
        $username_email = $request->get('_username');

        /** @var User $user */
        $user = $this->um->findUserByUsernameOrEmail($username_email);

        // If the user exists
        if ($user) {
            $max_login_attempt = $this->settings->get('security.login_attempt_max');

            // Get number of failed attempt
            $login_count = $user->getLoginAttempt();

            // Increase
            $login_count++;
            $user->setLoginAttempt($login_count);
            $user->setLastFailedLogin(new DateTime("now"));

            // Lock account if more than authorized
            if ($login_count >= $max_login_attempt) {
                if ($this->isUserAdmin($user)) {
                    $this->session->set("SESSION_USE_TOKEN", true);
                } else {
                    $user->setEnabled(false);
                    $user->setDisabledAt(new DateTime("now"));
                }
            }
            //update user data
            $this->um->updateUser($user);

            //in all case if user is not admin, we want to clear the session
            if (!$this->isUserAdmin($user) || $user->getLoginAttempt() < $max_login_attempt) {
                $this->session->remove("SESSION_USE_TOKEN");
            }

        } else {
            $this->session->remove("SESSION_USE_TOKEN");
        }


    }

    /**
     * Login event callback
     * @param UserEvent $event
     */
    public function onImplicitLogin($event)
    {
        // Retrieve current user
        /**
         * @var User $user
         */
        $user = $event->getUser();
        $this->logger->info("Successful user authentication",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME=>EventNameEnum::USER_AUTHENTICATED,
                LogUtil::USER_NAME=>$user->getUsername()

            ])
        );

        $this->updateLastUserConnection($user);
        $this->addConnectionHistory($user);
    }

    /**
     * Login event callback
     * @param InteractiveLoginEvent $event
     */
    public function onInteractiveLogin(InteractiveLoginEvent $event)
    {
        // Retrieve current user
        /**
         * @var User $user
         */
        $user = $event->getAuthenticationToken()->getUser();

        if (!$user instanceof User) {
            return;
        }

        $this->updateLastUserConnection($user);
        $this->addConnectionHistory($user);

    }

    /**
     * @param User $user
     */
    private function addConnectionHistory($user)
    {


        $request = $this->requestStack->getCurrentRequest();

        $connection = new Connection();
        try {
            $cap = get_browser($request->headers->get('User-Agent'));
            $connection->setBrowser($cap->browser);
            $connection->setBrowserVersion($cap->version);
            $connection->setDeviceType($cap->device_type);
            $connection->setPlatform($cap->platform);
            $connection->setUserAgent($request->headers->get('User-Agent'));

        } catch (\Exception $e) {
            $this->logger->error("Unable to get browser cap: " . $e->getMessage(),
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::TECHNICAL_ERROR,
                    LogUtil::USER_NAME=>$user->getUsername(),
                    "exception" => $e

                ])
            );
        }


        $connection->setConnectedAt(new DateTime());
        $connection->setUser($user);

        //getClientIp use X-Forwarded-For header only for trusted proxy
        $connection->setIp($request->getClientIp());

        $user->addConnection($connection);


        try {
            $this->em->persist($connection);
            $this->em->flush();
        } catch (ORMException $e) {
            $this->logger->error("Unable to add connection history to the user",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::TECHNICAL_ERROR,
                    LogUtil::USER_NAME=>$user->getUsername(),
                    "exception" => $e

                ])
                );
        }
    }

    /**
     * @param $user User the
     */
    private function updateLastUserConnection($user)
    {
        $this->session = $this->requestStack->getSession();
        //update user last connection
        $user->setLastLogin(new DateTime());

        $user->setLoginAttempt(0);
        $this->session->remove("SESSION_USE_TOKEN");

        //update company last connection
        if ($user->getCompany() != null) {
            $user->getCompany()->setLastConnexion(new DateTime());
        }

        try {
            $this->em->merge($user);
            $this->em->flush();
        } catch (ORMException $e) {
            $this->logger->error("Unable update last connexionDate for user",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::TECHNICAL_ERROR,
                    LogUtil::USER_NAME=>$user->getUsername()

                ])
            );
        }
    }

    /**
     * @return RequestStack
     */
    public function getRequestStack(): RequestStack
    {
        return $this->requestStack;
    }

    /**
     * @param RequestStack $requestStack
     */
    public function setRequestStack(RequestStack $requestStack): void
    {
        $this->requestStack = $requestStack;
    }

    /**
     * @return string
     */
    public function getAdministratorToken(): string
    {
        return $this->administratorToken;
    }

    /**
     * @param string $administratorToken
     */
    public function setAdministratorToken(string $administratorToken): void
    {
        $this->administratorToken = $administratorToken;
    }

    private function isUserAdmin(?User $user)
    {
        $isAdmin = false;

        if ($user) {
            if (in_array('ROLE_OPERATOR', $user->getRoles())) {
                $isAdmin = true;
            }

            if (in_array('ROLE_SUPER_ADMIN', $user->getRoles())) {
                $isAdmin = true;
            }
        }

        return $isAdmin;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
