<?php

namespace AppBundle\EventSubscriber;

use A<PERSON><PERSON><PERSON><PERSON>\Controller\MkoController;
use App<PERSON><PERSON>le\Entity\User;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorage;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

class BuyerRegistrationSubscriber implements EventSubscriberInterface
{

    /**
     * @var RouterInterface $router
     */
    private $router;

    /**
     * @var TokenStorageInterface $tokenStorage
     */
    private $tokenStorage;


    public function __construct(
        RouterInterface $router,
        TokenStorageInterface $tokenStorage
    )
    {
        $this->router = $router;
        $this->tokenStorage = $tokenStorage;
    }

    /**
     * Build an array of events we want to listen to
     */
    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::REQUEST => ['redirectToCompanyRegistrationForm', 0],
        ];
    }

    public function redirectToCompanyRegistrationForm(RequestEvent $event)
    {
        $request = $event->getRequest();

        if (!$event->isMainRequest() || preg_match('#/(js/routing|company/cgu)#', $request->getRequestUri())) {
            return;
        }

        $user = $this->getCurrentUser();

        if($user && $user->getCompany() && $user->getCompany()->getStep() < 3){
            $request->getSession()->set(MkoController::SESSION_ACCOUNT_CREATION, true);
            if(!strpos($request->getRequestUri(), "/company/info")){
                $event->setResponse(new RedirectResponse($this->router->generate('front.company.info')));
            }
        } else {
            if ($request->getSession()->get(MkoController::SESSION_ACCOUNT_CREATION)) {
                $request->getSession()->set(MkoController::SESSION_ACCOUNT_CREATION, false);
            }
        }
    }

    private function getCurrentUser(): ?User
    {
        $user = null;
        if ($this->tokenStorage !== null &&
            $this->tokenStorage->getToken() !== null ) {

            $user = $this->tokenStorage->getToken()->getUser();
        }

        return ($user instanceof User) ? $user : null;
    }
}
