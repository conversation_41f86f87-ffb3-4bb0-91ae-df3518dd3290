<?php

namespace AppBundle\EventSubscriber;

use App<PERSON>undle\Entity\User;
use AppBundle\Repository\CartRepository;
use AppBundle\Services\OfferService;
use Open\TicketBundle\Services\TicketService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

class KernelEventSubscriber implements EventSubscriberInterface
{
    /**
     * @var SessionInterface $session
     */
    private $session;

    /**
     * @var TokenStorageInterface $tokenStorage
     */
    private $tokenStorage;

    /**
     * @var TicketService $ticketService
     */
    private $ticketService;

    /**
     * @var CartRepository $cartRepository
     */
    private $cartRepository;

    /**
     * @var OfferService
     */
    private $offerService;

    private $requestStack;

    public function __construct(
        RequestStack $requestStack,
        TokenStorageInterface $tokenStorage,
        TicketService $ticketService,
        CartRepository $cartRepository,
        OfferService $offerService
    )
    {
        $this->requestStack = $requestStack;
        $this->tokenStorage = $tokenStorage;
        $this->ticketService = $ticketService;
        $this->cartRepository = $cartRepository;
        $this->offerService = $offerService;
    }

    /**
     * Build an array of events we want to listen to
     */
    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::REQUEST => [
                ['defineRequestLocale', 20], // must be registered before (i.e. with a higher priority than) the default Locale listener
                ['updateUserCount'],
                ['defineUserForServices'],
            ],
        ];
    }

    public function defineRequestLocale(RequestEvent $event)
    {
        $request = $event->getRequest();
        if (!$request->hasPreviousSession()) {
            return;
        }
        // try to see if the locale has been set as a _locale routing parameter
        if ($locale = $request->attributes->get('_locale')) {
            $request->getSession()->set('_locale', $locale);
        } else {

            // Find users preferred language from the browser
            $preferedLanguage = $request->getPreferredLanguage(['en','fr','es', 'nl']);

            // if no explicit locale has been set on this request, use one from the session
            $request->setLocale($request->getSession()->get('_locale', $preferedLanguage));
        }
    }

    public function updateUserCount(RequestEvent $event)
    {
        $this->session = $this->requestStack->getSession();

        if (!$event->isMainRequest()) {
            return;
        }

        if ($this->isApi($event->getRequest())) {
            return;
        }

        $user = $this->getCurrentUser();

        if ($user && $user->getCompany()) {
            $unreadMessage = $this->ticketService->getCountUnreadMessage(
                $user->getCompany()->getIzbergUserId(),
                $user->getCompany()->getId()
            );
            $this->session->set('unreadMessage', $unreadMessage);

            $pendingCart = $this->cartRepository->getPendingCartByUserId($user->getId());
            $this->session->set('pendingCart', count($pendingCart));
        }
    }

    public function defineUserForServices(RequestEvent $event)
    {
        if (!$event->isMainRequest()) {
            return;
        }

        $user = $this->getCurrentUser();
        $this->offerService->defineUser($user);
    }

    private function getCurrentUser(): ?User
    {
        $user = null;
        if ($this->tokenStorage !== null &&
            $this->tokenStorage->getToken() !== null ) {

            $user = $this->tokenStorage->getToken()->getUser();
        }

        return ($user instanceof User) ? $user : null;
    }

    public function isApi(Request $request): bool
    {
        return $request->getContentTypeFormat() === 'json';
    }
}
