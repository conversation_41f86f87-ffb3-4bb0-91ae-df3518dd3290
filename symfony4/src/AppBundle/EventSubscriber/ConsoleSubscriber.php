<?php

namespace AppBundle\EventSubscriber;

use Open\IzbergBundle\Api\ApiClientManager;
use Open\IzbergBundle\Api\ApiConfigurator;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\ConsoleEvents;
use Symfony\Component\Console\Event\ConsoleCommandEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class ConsoleSubscriber implements EventSubscriberInterface
{
    private ApiConfigurator $apiConfigurator;
    private ApiClientManager $apiClientManager;

    public function __construct(
        ApiConfigurator $apiConfigurator,
        ApiClientManager $apiClientManager
    )
    {
        $this->apiClientManager = $apiClientManager;
        $this->apiConfigurator = $apiConfigurator;
    }

    public static function getSubscribedEvents()
    {
        return [
            ConsoleEvents::COMMAND => 'consoleCommand',
        ];
    }

    public function consoleCommand(ConsoleCommandEvent $event)
    {
        $command = $event->getCommand();
        $this->configureIzbergApiConnection($command);
    }

    private function configureIzbergApiConnection(Command $command)
    {
        $connection = (method_exists($command, 'isAsynchronousProcess'))?  'webhook' : 'console';
        $this->apiClientManager->useConnection($connection);
        $this->apiConfigurator->configure($this->apiClientManager);
    }
}
