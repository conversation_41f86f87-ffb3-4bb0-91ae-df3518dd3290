<?php
namespace AppBundle\Validator\Constraints;

use Symfony\Component\Form\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;

class SlugFormatValidator extends ConstraintValidator
{
    /**
     * Validate the slug format and existance.
     *
     * @param mixed      $value
     * @param Constraint $constraint
     */
    public function validate($value, Constraint $constraint)
    {
        if (!$constraint instanceof SlugFormat) {
            throw new UnexpectedTypeException($constraint, SlugFormat::class);
        }
        // Check for invalid characters
        if (preg_match('%^[^/][a-z0-9A-Z/_-]*$%i', $value, $matches) === 0) {
            $this
                ->context
                ->buildViolation($constraint->getMessage())
                ->setParameter('%string%', $value)
                ->addViolation();
        }

    }
}
