<?php
namespace AppBundle\Validator\Constraints;

use AppBundle\Doctrine\NodeStatusFilter;
use AppBundle\Entity\Node;
use AppBundle\Repository\NodeRepository;
use AppBundle\Repository\RedirectRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Form\Exception\UnexpectedTypeException;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;


class SlugExistValidator extends ConstraintValidator
{

    /** @var RouterInterface */
    protected $router;

    /** @var  NodeRepository */
    protected $node_repository;

    /** @var RedirectRepository */
    protected $redirect_repository;

    /** @var  EntityManagerInterface */
    protected $entity_manager;

    /** @var RequestStack */
    protected $request_stack;

    public function __construct(RouterInterface $router, EntityManagerInterface $em, NodeRepository $nr, RedirectRepository $rr, RequestStack $rs)
    //public function __construct(Router $router, EntityManager $em, NodeRepository $nr, RedirectRepository $rr, RequestStack $rs)
    {
        $this->router = $router;

        $this->entity_manager = $em;

        $this->node_repository = $nr;

        $this->redirect_repository = $rr;

        $this->request_stack = $rs;
    }

    /**
     * Validate the slug format and existance
     * @param $value
     * @param Constraint $constraint
     */
    public function validate($value, Constraint $constraint)
    {
        if (!$constraint instanceof SlugExist) {
            throw new UnexpectedTypeException($constraint, SlugExist::class);
        }

        $valid = true;

		// Ignore empty value because another rule take care of that
		if (!empty($value)) {
            $valid = false;
            $route = $this->router->match('/fr/' . $value); // i18nRouter throw exception if we don't provide a language

            // The route exist somewhere
            if ($route['_route'] === 'catch_all') {
                // If the route does not exist then our catch all route will reply to it
                // It means we have to look in the db for matching content
                /** @var NodeStatusFilter $filter */
                // Filter by published nodes
                $this->entity_manager->getFilters()->enable('published_node');

                /** @var Node $node */
                $node = $this->node_repository->findBySlug($value);

                //This make assumption that the slug validation is done on a url that have the id
                $node_id = intval($this->request_stack->getCurrentRequest()->get('id'));

                // If the node found is not the same as the one we are editing then it is an error
                if (!($node && $node->getId() !== $node_id)) {
					//This make assumption that the slug validation is done on a url that have the id
					$redirect_id = intval($this->request_stack->getCurrentRequest()->get('id'));


                    // Find a redirect starting from this slug
                    $redirect = $this->redirect_repository->findByOrigin($value);

                    // If there is a redirection starting from this slug
                    // the request might end up looping or lead to a 404
                    // We cannot allow this
                    if (!($redirect && $redirect->getId() !== $redirect_id)) {
                        $valid = true;
                    }
                }

            }
		}

        // Raise validation error if needed
        if (!$valid) {
            $this->context->buildViolation($constraint->getMessage())
                ->setParameter('%string%', $value)
                ->addViolation();
        }
    }
}
