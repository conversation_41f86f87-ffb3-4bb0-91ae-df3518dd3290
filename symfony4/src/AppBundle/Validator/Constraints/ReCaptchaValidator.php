<?php
namespace AppBundle\Validator\Constraints;

use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Form\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Contracts\Translation\TranslatorInterface;
use Unirest;

class ReCaptchaValidator extends ConstraintValidator implements LoggerAwareInterface
{
	private LoggerInterface $logger;
	private TranslatorInterface $translator;

	public function __construct(TranslatorInterface $translator) {
		$this->translator = $translator;
	}

    /**
     * Validate the slug format and existance
     * @param $value
     * @param Constraint $constraint
     */
    public function validate($value, Constraint $constraint)
    {
        if (!$constraint instanceof ReCaptcha) {
            throw new UnexpectedTypeException($constraint, ReCaptcha::class);
        }
        /** @var  ReCaptcha $reCaptcha */
        $reCaptcha = $constraint;

    	$data = array(
			'secret' => $reCaptcha->getCaptchaSecret(),
			'response' => $value
		);

		$response = Unirest\Request::post(
			'https://www.google.com/recaptcha/api/siteverify',
			[],
			$data
		);

		$this->logger->debug('ReCaptcha verification Request',
            LogUtil::buildContext(array_merge([
                LogUtil::EVENT_NAME=>EventNameEnum::CAPTCHA_VERIFY_REQUEST,
                LogUtil::USER_NAME=>"anonymous"
                ], $data)
            )
        );
		$this->logger->debug('ReCaptcha verification Response',
            LogUtil::buildContext(array_merge([
                LogUtil::EVENT_NAME => EventNameEnum::CAPTCHA_VERIFY_RESPONSE,
                LogUtil::USER_NAME => "anonymous"], (array) $response->body)
            )
        );

		if ($response->code != 200 || ($response->code == 200 && !$response->body->success)) {

			// Raise validation error if needed
			$this
				->context
				->buildViolation($this->translator->trans($constraint->getMessage(), [], 'validators'))
				->addViolation();
		}
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
