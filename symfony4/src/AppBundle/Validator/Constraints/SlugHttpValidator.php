<?php
namespace AppBundle\Validator\Constraints;

use Symfony\Component\Form\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;

class SlugHttpValidator extends ConstraintValidator
{

    /**
     * Validate the slug format and existance.
     *
     * @param mixed      $value
     * @param Constraint $constraint
     */
    public function validate($value, Constraint $constraint)
    {
        if (!$constraint instanceof SlugHttp) {
            throw new UnexpectedTypeException($constraint, SlugHttp::class);
        }
        // Check format first
        if (preg_match('/^(http|https):\/\//', $value, $matches)) {
            // Raise validation error if needed
            $this
                ->context
                ->buildViolation($constraint->getMessage())
                ->setParameter('%string%', $value)
                ->addViolation();
        }
    }
}
