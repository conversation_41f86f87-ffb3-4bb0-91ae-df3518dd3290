<?php
namespace AppBundle\Validator\Constraints;

use AppBundle\Exception\MissingOptionException;
use Symfony\Component\Validator\Constraint;

/**
 * @Annotation
 */
class ReCaptcha extends Constraint
{
    public $message = 'captcha.error';

	protected $captchaSecret;

	public function __construct($options)
	{
		if($options['captcha_secret'])
		{
			$this->captchaSecret = $options['captcha_secret'];
		} else {
			throw new MissingOptionException('Captcha API Key is missing');
		}
	}

	public function getCaptchaSecret()
	{
		return $this->captchaSecret;
	}

    /**
     * @return string
     */
    public function getMessage(): string
    {
        return $this->message;
    }

}
