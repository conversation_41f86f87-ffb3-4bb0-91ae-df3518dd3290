<?php


namespace AppBundle\Model\View\PurchaseRequest;


class PurchaseRequestItem
{
    /**
     * @var int
     */
    private $id;

    /**
     * @var int
     */
    private $quantityExpected;

    /**
     * @var string
     */
    private $productName;

    /**
     * @var string
     */
    private $buyerReference;

    /**
     * @var string
     */
    private $manufacturerReference;

    /**
     * @var string
     */
    private $manufacturerName;

    /**
     * @var string
     */
    private $expectedDeliveryDate;

    /**
     * @var string
     */
    private $costCenter;

    /**
     * @var string
     */
    private $purchaseRequestNumber;

    /**
     * @var string
     */
    private $buyerOrderNumber;

    /**
     * @var string
     */
    private $orderLine;

    /**
     * @var string
     */
    private $unitPriceOfReference;

    /**
     * @var Offer|null
     */
    private $offer;

    /**
     * @var string|null
     *
     */
    private $currencyOfReference;

    /**
     * @return string|null
     */
    public function getCurrencyOfReference(): ?string
    {
        return $this->currencyOfReference;
    }

    /**
     * @param string|null $currencyOfReference
     */
    public function setCurrencyOfReference(?string $currencyOfReference): self
    {
        $this->currencyOfReference = $currencyOfReference;
        return $this;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getQuantityExpected(): int
    {
        return $this->quantityExpected;
    }

    public function setQuantityExpected(int $quantityExpected): self
    {
        $this->quantityExpected = $quantityExpected;
        return $this;
    }

    public function getProductName(): string
    {
        return $this->productName;
    }

    public function setProductName(string $productName): self
    {
        $this->productName = $productName;
        return $this;
    }

    public function getBuyerReference(): string
    {
        return $this->buyerReference;
    }

    public function setBuyerReference(string $buyerReference): self
    {
        $this->buyerReference = $buyerReference;
        return $this;
    }

    public function getManufacturerReference(): string
    {
        return $this->manufacturerReference;
    }

    public function setManufacturerReference(string $manufacturerReference): self
    {
        $this->manufacturerReference = $manufacturerReference;
        return $this;
    }

    public function getManufacturerName(): string
    {
        return $this->manufacturerName;
    }

    public function setManufacturerName(string $manufacturerName): self
    {
        $this->manufacturerName = $manufacturerName;
        return $this;
    }

    public function getExpectedDeliveryDate(): string
    {
        return $this->expectedDeliveryDate;
    }

    public function setExpectedDeliveryDate(string $expectedDeliveryDate): self
    {
        $this->expectedDeliveryDate = $expectedDeliveryDate;
        return $this;
    }

    public function getCostCenter(): string
    {
        return $this->costCenter;
    }

    public function setCostCenter(string $costCenter): self
    {
        $this->costCenter = $costCenter;
        return $this;
    }

    public function getPurchaseRequestNumber(): string
    {
        return $this->purchaseRequestNumber;
    }

    public function setPurchaseRequestNumber(string $purchaseRequestNumber): self
    {
        $this->purchaseRequestNumber = $purchaseRequestNumber;
        return $this;
    }

    public function getBuyerOrderNumber(): string
    {
        return $this->buyerOrderNumber;
    }

    public function setBuyerOrderNumber(string $buyerOrderNumber): self
    {
        $this->buyerOrderNumber = $buyerOrderNumber;
        return $this;
    }

    public function getOrderLine(): string
    {
        return $this->orderLine;
    }

    public function setOrderLine(string $orderLine): self
    {
        $this->orderLine = $orderLine;
        return $this;
    }

    public function getUnitPriceOfReference(): string
    {
        return $this->unitPriceOfReference;
    }

    public function setUnitPriceOfReference(string $unitPriceOfReference): self
    {
        $this->unitPriceOfReference = $unitPriceOfReference;
        return $this;
    }

    public function getOffer(): ?Offer
    {
        return $this->offer;
    }

    public function setOffer(?Offer $offer): self
    {
        $this->offer = $offer;
        return $this;
    }
}
