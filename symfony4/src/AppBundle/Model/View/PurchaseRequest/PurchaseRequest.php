<?php

namespace AppBundle\Model\View\PurchaseRequest;

class PurchaseRequest
{
    /**
     * @var PurchaseRequestItem[]
     */
    private $items;

    public function __construct()
    {
        $this->items = [];
    }

    /**
     * @return PurchaseRequestItem[]
     */
    public function getItems(): array
    {
       return $this->items;
    }

    /**
     * @param PurchaseRequestItem[] $items
     * @return $this
     */
    public function setItems(array $items): self
    {
        $this->items = $items;
        return $this;
    }
}
