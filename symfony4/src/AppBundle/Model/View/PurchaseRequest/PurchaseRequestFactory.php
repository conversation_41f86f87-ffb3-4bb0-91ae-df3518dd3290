<?php

namespace AppBundle\Model\View\PurchaseRequest;

use AppBundle\Entity\PurchaseRequest;
use AppBundle\Entity\PurchaseRequestItem;
use AppBundle\Model\Offer;
use AppBundle\Model\View\PurchaseRequest\PurchaseRequest as PurchaseRequestViewModel;
use AppBundle\Model\View\PurchaseRequest\PurchaseRequestItem as PurchaseRequestItemViewModel;
use AppBundle\Model\View\PurchaseRequest\Offer as OfferViewModel;
use AppBundle\Services\CompanyCatalogService;
use AppBundle\Services\OfferService;
use AppBundle\Services\SpecificPriceService;

class PurchaseRequestFactory
{
    /**
     * @var OfferService
     */
    private $offerService;

    /**
     * @var CompanyCatalogService
     */
    private $companyCatalogService;

    /**
     * @var SpecificPriceService
     */
    private $specificPriceService;

    public function __construct(OfferService $offerService, CompanyCatalogService $companyCatalogService, SpecificPriceService $specificPriceService)
    {
        $this->offerService = $offerService;
        $this->companyCatalogService = $companyCatalogService;
        $this->specificPriceService = $specificPriceService;
    }

    /**
     * @param PurchaseRequest|null $purchaseRequest
     */
    public function build(PurchaseRequest $purchaseRequest = null): PurchaseRequestViewModel
    {
        $purchaseRequestViewModel = new PurchaseRequestViewModel();

        if (!$purchaseRequest) {
            return $purchaseRequestViewModel;
        }

        $offers = $this->retrieveOffers($purchaseRequest);

        $items = array_map(
            function(PurchaseRequestItem $purchaseRequestItem) use ($offers): PurchaseRequestItemViewModel {
                $offer = $this->buildOffer($purchaseRequestItem, $offers);

                $purchaseRequestItemViewModel = (new PurchaseRequestItemViewModel())
                    ->setId($purchaseRequestItem->getId())
                    ->setQuantityExpected($purchaseRequestItem->getQuantityExpected())
                    ->setProductName($purchaseRequestItem->getProductName())
                    ->setBuyerReference($purchaseRequestItem->getBuyerReference())
                    ->setManufacturerReference($purchaseRequestItem->getManufacturerReference())
                    ->setManufacturerName($purchaseRequestItem->getManufacturerName())
                    ->setExpectedDeliveryDate($purchaseRequestItem->getExpectedDeliveryDate())
                    ->setCostCenter($purchaseRequestItem->getCostCenter())
                    ->setPurchaseRequestNumber($purchaseRequestItem->getPurchaseRequestNumber())
                    ->setBuyerOrderNumber($purchaseRequestItem->getBuyerOrderNumber())
                    ->setOrderLine($purchaseRequestItem->getOrderLine())
                    ->setUnitPriceOfReference($purchaseRequestItem->getUnitPriceOfReference())
                    ->setCurrencyOfReference($purchaseRequestItem->getCurrencyOfReference())
                    ->setOffer($offer);

                return $purchaseRequestItemViewModel;
            },
            $purchaseRequest->getItems()
        );

        $purchaseRequestViewModel->setItems($items);

        return $purchaseRequestViewModel;
    }

    /**
     * @param PurchaseRequestItem|null $purchaseRequestItem
     * @param Offer[] $offers
     * @return OfferViewModel|null
     */
    private function buildOffer(?PurchaseRequestItem $purchaseRequestItem, array $offers): ?OfferViewModel
    {
        if (!$purchaseRequestItem) {
            return null;
        }

        if (!$purchaseRequestItem->getOfferId()) {
            return null;
        }

        $offerId = $purchaseRequestItem->getOfferId();
        $offers = array_filter(
            $offers,
            function(Offer $offer) use ($offerId) {
                return ((int)$offer->getIzbergReference() === $offerId);
            }
        );

        $offer = array_shift($offers);
        if (!$offer) {
            return null;
        }

        $merchant = (new Merchant())
            ->setName($offer->getMerchant()->getName());

        $offerViewModel = (new OfferViewModel())
            ->setId($offerId)
            ->setIzbergReference(intval($offer->getIzbergReference()))
            ->setPurchaseRequestItemId($purchaseRequestItem->getId())
            ->setQuantity(strval($purchaseRequestItem->getQuantity())) // not sure about this one
            ->setOfferPictures($offer->getOfferPictures())
            ->setName($offer->getOfferTitle())
            ->setOfferTitle($offer->getOfferTitle())
            ->setBatchSize($offer->getBatchSize())
            ->setSelected(true)
            ->setUserQuantity($purchaseRequestItem->getQuantity()) // not sure about this one
            ->setQuantityExpected($purchaseRequestItem->getQuantityExpected())
            ->setSellerRef($offer->getSellerRef())
            ->setQuantityPerSku($offer->getQuantityPerSku())
            ->setSkuUnit($offer->getSkuUnit())
            ->setMoq($offer->getMoq())
            ->setCurrency($offer->getCurrency())
            ->setIncoterm($offer->getIncoterm())
            ->setPrices($offer->getPrices())
            ->setMerchant($merchant)
            ->setIncotermCountry($offer->getIncotermCountry())
            ->setShippable($offer->isShippable());

        return $offerViewModel;
    }

    /**
     * @param PurchaseRequest $purchaseRequest
     * @return Offer[]
     */
    private function retrieveOffers(PurchaseRequest $purchaseRequest): array
    {
        $company = $purchaseRequest->getUser()->getCompany();

        $offerIds = array_map(
            function(PurchaseRequestItem $item) {
                return $item->getOfferId();
            },
            $purchaseRequest->getItems()
        );

        $offerIds = array_filter($offerIds);

        $offers = $this->offerService->findOffersByIds($offerIds);

        $offers = array_map(
            function(Offer $offer) use ($company) {
                $offer->setBuyerRef($this->companyCatalogService->searchCompanyBuyerReference($company, $offer));
                $offer = $this->specificPriceService->updateOfferSpecificPrices($company, $offer);
                return $offer;
            },
            $offers
        );

        return $offers;
    }
}
