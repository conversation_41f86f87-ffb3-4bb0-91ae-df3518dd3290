<?php

namespace AppBundle\Model\View\PurchaseRequest;

class Offer
{
    /**
     * @var int
     */
    private $id;

    /**
     * @var int
     */
    private $izbergReference;

    /**
     * @var int
     */
    private $purchaseRequestItemId;

    /**
     * @var string
     */
    private $quantity;

    /**
     * @var array
     */
    private $offerPictures;

    /**
     * @var string
     */
    private $name;

    /**
     * @var string
     */
    private $offerTitle;

    /**
     * @var string
     */
    private $currency;

    /**
     * @var int
     */
    private $batchSize;

    /**
     * @var bool
     */
    private $selected;

    /**
     * @var int
     */
    private $userQuantity;

    /**
     * @var int
     */
    private $quantityExpected;

    /**
     * @var string
     */
    private $sellerRef;

    /**
     * @var int
     */
    private $quantityPerSku;

    /**
     * @var string
     */
    private $skuUnit;

    /**
     * @var int
     */
    private $moq;

    /**
     * @var string
     */
    private $incoterm;

    /**
     * @var Merchant
     */
    private $merchant;

    /**
     * @var array
     */
    private $prices;

    /**
     * @var string $incotermCountry the country for the incoterm.
     */
    private $incotermCountry;

    /**
     * @var bool
     */
    private $shippable;

    public function __construct()
    {
        $this->offerPictures = [];
        $this->selected = false;
        $this->merchant = new Merchant();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getIzbergReference(): int
    {
        return $this->izbergReference;
    }

    public function setIzbergReference(int $izbergReference): self
    {
        $this->izbergReference = $izbergReference;
        return $this;
    }

    public function getPrices(): array
    {
        return $this->prices;
    }

    public function setPrices(array $prices): self
    {
        $this->prices = $prices;
        return $this;
    }

    public function getPurchaseRequestItemId(): int
    {
        return $this->purchaseRequestItemId;
    }

    public function setPurchaseRequestItemId(int $purchaseRequestItemId): self
    {
        $this->purchaseRequestItemId = $purchaseRequestItemId;
        return $this;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function setCurrency(string $currency): self
    {
        $this->currency = $currency;
        return $this;
    }

    public function getQuantity(): string
    {
        return $this->quantity;
    }

    public function setQuantity(string $quantity): self
    {
        $this->quantity = $quantity;
        return $this;
    }

    public function getOfferPictures(): array
    {
        return $this->offerPictures;
    }

    public function setOfferPictures(array $offerPictures): self
    {
        $this->offerPictures = $offerPictures;
        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;
        return $this;
    }

    public function getOfferTitle(): string
    {
        return $this->offerTitle;
    }

    public function setOfferTitle(string $offerTitle): self
    {
        $this->offerTitle = $offerTitle;
        return $this;
    }

    public function getBatchSize(): int
    {
        return $this->batchSize;
    }

    public function setBatchSize(int $batchSize): self
    {
        $this->batchSize = $batchSize;
        return $this;
    }

    public function isSelected(): bool
    {
        return $this->selected;
    }

    public function setSelected(bool $selected): self
    {
        $this->selected = $selected;
        return $this;
    }

    public function getUserQuantity(): int
    {
        return $this->userQuantity;
    }

    public function setUserQuantity(int $userQuantity): self
    {
        $this->userQuantity = $userQuantity;
        return $this;
    }

    public function getQuantityExpected(): int
    {
        return $this->quantityExpected;
    }

    public function setQuantityExpected(int $quantityExpected): self
    {
        $this->quantityExpected = $quantityExpected;
        return $this;
    }

    public function getSellerRef(): string
    {
        return $this->sellerRef;
    }

    public function setSellerRef(string $sellerRef): self
    {
        $this->sellerRef = $sellerRef;
        return $this;
    }

    public function getQuantityPerSku(): int
    {
        return $this->quantityPerSku;
    }

    public function setQuantityPerSku(int $quantityPerSku): self
    {
        $this->quantityPerSku = $quantityPerSku;
        return $this;
    }

    public function getSkuUnit(): string
    {
        return $this->skuUnit;
    }

    public function setSkuUnit(string $skuUnit): self
    {
        $this->skuUnit = $skuUnit;
        return $this;
    }

    public function getMoq(): int
    {
        return $this->moq;
    }

    public function setMoq(int $moq): self
    {
        $this->moq = $moq;
        return $this;
    }

    public function getIncoterm(): string
    {
        return $this->incoterm;
    }

    public function setIncoterm(string $incoterm): self
    {
        $this->incoterm = $incoterm;
        return $this;
    }

    public function getIncotermCountry(): string
    {
        return $this->incotermCountry;
    }

    public function setIncotermCountry(string $incotermCountry): self
    {
        $this->incotermCountry = $incotermCountry;
        return $this;
    }

    public function getMerchant(): Merchant
    {
        return $this->merchant;
    }

    public function setMerchant(Merchant $merchant): self
    {
        $this->merchant = $merchant;
        return $this;
    }

    /**
     * @return bool
     */
    public function isShippable(): bool
    {
        return $this->shippable;
    }

    /**
     * @param bool $shippable
     */
    public function setShippable(bool $shippable): self
    {
        $this->shippable = $shippable;
        return $this;
    }
}
