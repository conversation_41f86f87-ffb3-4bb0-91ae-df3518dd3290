<?php

namespace AppBundle\Model\View\OrderDetail;

class MerchantOrder
{
    private $id;

    private $merchantId;

    private $merchantName;

    /**
     * @var array<MerchantOrderItem>
     */
    private $items;

    private $status;

    /**
     * @var bool
     */
    private $hasDisputes;

    private $amount;

    private $amountVatIncluded;

    /**
     * Array of subtotal vat with ['vat' => 'total']
     * @var array
     */
    private $subTotalVat;

    /**
     * @var float
     */
    private $shippingTotal;

    /**
     * @var string
     */
    private $shippingName;

    /**
     * @var Shipment[]
     */
    private $shipments;

    /**
     * @var string|null $buyerInternalOrderId
     */
    private $buyerInternalOrderId;

    /**
     * @var bool
     */
    private $trackingError;

    /**
     * @var string
     */
    private $currency;

    public function __construct()
    {
        $this->trackingError = false;
    }

    public function getId()
    {
        return $this->id;
    }

    public function setId($id)
    {
        $this->id = $id;
        return $this;
    }

    public function getMerchantId()
    {
        return $this->merchantId;
    }

    public function setMerchantId($merchantId)
    {
        $this->merchantId = $merchantId;
        return $this;
    }

    public function getMerchantName()
    {
        return $this->merchantName;
    }

    public function setMerchantName($merchantName)
    {
        $this->merchantName = $merchantName;
        return $this;
    }

    public function getItems(): array
    {
        return $this->items;
    }

    public function setItems(array $items): self
    {
        $this->items = $items;
        return $this;
    }

    public function getStatus()
    {
        return $this->status;
    }

    public function setStatus($status)
    {
        $this->status = $status;
        return $this;
    }

    public function hasDisputes(): bool
    {
        return $this->hasDisputes;
    }

    public function setHasDisputes(bool $hasDisputes): self
    {
        $this->hasDisputes = $hasDisputes;
        return $this;
    }

    public function getAmount()
    {
        return $this->amount;
    }

    public function setAmount($amount)
    {
        $this->amount = $amount;
        return $this;
    }

    public function getAmountVatIncluded()
    {
        return $this->amountVatIncluded;
    }

    public function setAmountVatIncluded($amountVatIncluded)
    {
        $this->amountVatIncluded = $amountVatIncluded;
        return $this;
    }

    public function getSubTotalVat(): array
    {
        return $this->subTotalVat;
    }

    public function setSubTotalVat(array $subTotalVat): self
    {
        $this->subTotalVat = $subTotalVat;
        return $this;
    }

    public function getShippingTotal(): ?float
    {
        return $this->shippingTotal;
    }

    public function setShippingTotal(?float $shippingTotal): self
    {
        $this->shippingTotal = $shippingTotal;
        return $this;
    }

    public function getShippingName(): ?string
    {
        return $this->shippingName;
    }

    public function setShippingName(?string $shippingMode): self
    {
        $this->shippingName = $shippingMode;
        return $this;
    }

    /**
     * @return Shipment[]
     */
    public function getShipments(): array
    {
        return $this->shipments;
    }

    /**
     * @param Shipment[] $shipments
     * @return $this
     */
    public function setShipments(array $shipments): self
    {
        $this->shipments = $shipments;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getBuyerInternalOrderId(): ?string
    {
        return $this->buyerInternalOrderId;
    }

    /**
     * @param string|null $buyerInternalOrderId
     * @return MerchantOrder
     */
    public function setBuyerInternalOrderId(?string $buyerInternalOrderId): self
    {
        $this->buyerInternalOrderId = $buyerInternalOrderId;
        return $this;
    }

    public function isTrackingError(): bool
    {
        return $this->trackingError;
    }

    public function setTrackingError(bool $trackingError): self
    {
        $this->trackingError = $trackingError;
        return $this;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function setCurrency(string $currency): self
    {
        $this->currency = $currency;
        return $this;
    }


}
