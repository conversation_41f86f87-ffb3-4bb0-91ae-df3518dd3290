<?php

namespace AppBundle\Model\View\OrderDetail;

use DateTimeImmutable;

class MerchantOrderItem
{
    private $id;

    private $name;

    private $offerId;

    private $imageUrl;

    private $incoterm;

    private $sellerRef;

    private $status;

    private $price;

    private $currency;

    private $quantity;

    private $deliveryDate;

    /**
     * @var string|null
     */
    private $country;

    /**
     * @var bool
     */
    private $toBeShipped;

    /**
     * @var bool
     */
    private $dangerousProduct;

    /**
     * @var bool
     */
    private $partiallyDelivered;

    /**
     * @var bool
     */
    private $fullyDelivered;

    /**
     * @var \DateTimeImmutable | null
     */
    private $lastDeliveredDate;

    /**
     * @var string|null $orderLine
     */
    private $orderLine;
    private $cartItemComment;

    private $cartItemSplitDelivery;

    private $realStock;

    private $deliveryTimeDelayBeforeShipping;

    private DateTimeImmutable $deliveryDateDelayBeforeShipping;

    /**
     * @var string|null $buyerInternalReference
     */
    private $buyerInternalReference;

    /**
     * @var string|null $frameContract
     */
    private $frameContract;

    public function __construct()
    {
        $this->toBeShipped = false;
        $this->dangerousProduct = false;
        $this->partiallyDelivered = false;
        $this->fullyDelivered = false;
    }

    public function getId()
    {
        return $this->id;
    }

    public function setId($id)
    {
        $this->id = $id;
        return $this;
    }

    public function getName()
    {
        return $this->name;
    }

    public function setName($name)
    {
        $this->name = $name;
        return $this;
    }

    public function getOfferId()
    {
        return $this->offerId;
    }

    public function setOfferId($offerId)
    {
        $this->offerId = $offerId;
        return $this;
    }

    public function getImageUrl()
    {
        return $this->imageUrl;
    }

    public function setImageUrl($imageUrl)
    {
        $this->imageUrl = $imageUrl;
        return $this;
    }

    public function getIncoterm()
    {
        return $this->incoterm;
    }

    public function setIncoterm($incoterm)
    {
        $this->incoterm = $incoterm;
        return $this;
    }

    public function getSellerRef()
    {
        return $this->sellerRef;
    }

    public function setSellerRef($sellerRef)
    {
        $this->sellerRef = $sellerRef;
        return $this;
    }

    public function getStatus()
    {
        return $this->status;
    }

    public function setStatus($status)
    {
        $this->status = $status;
        return $this;
    }

    public function getPrice()
    {
        return $this->price;
    }

    public function setPrice($price)
    {
        $this->price = $price;
        return $this;
    }

    public function getCurrency()
    {
        return $this->currency;
    }

    public function setCurrency($currency)
    {
        $this->currency = $currency;
        return $this;
    }

    public function getQuantity()
    {
        return $this->quantity;
    }

    public function setQuantity($quantity)
    {
        $this->quantity = $quantity;
        return $this;
    }

    public function getDeliveryDate()
    {
        return $this->deliveryDate;
    }

    public function setDeliveryDate($deliveryDate)
    {
        $this->deliveryDate = $deliveryDate;
        return $this;
    }

    public function isToBeShipped(): bool
    {
        return $this->toBeShipped;
    }

    public function setToBeShipped(bool $toBeShipped): self
    {
        $this->toBeShipped = $toBeShipped;
        return $this;
    }

    public function isDangerousProduct(): bool
    {
        return $this->dangerousProduct;
    }

    public function setDangerousProduct(bool $dangerousProduct): self
    {
        $this->dangerousProduct = $dangerousProduct;
        return $this;
    }

    public function isPartiallyDelivered(): bool
    {
        return $this->partiallyDelivered;
    }

    public function setPartiallyDelivered(bool $partiallyDelivered): self
    {
        $this->partiallyDelivered = $partiallyDelivered;
        return $this;
    }

    public function isFullyDelivered(): bool
    {
        return $this->fullyDelivered;
    }

    public function setFullyDelivered(bool $fullyDelivered): self
    {
        $this->fullyDelivered = $fullyDelivered;
        return $this;
    }

    public function getLastDeliveredDate(): ?\DateTimeImmutable
    {
        return $this->lastDeliveredDate;
    }

    public function setLastDeliveredDate(?\DateTimeImmutable $lastDeliveredDate): self
    {
        $this->lastDeliveredDate = $lastDeliveredDate;
        return $this;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(?string $country): self
    {
        $this->country = $country;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getOrderLine(): ?string
    {
        return $this->orderLine;
    }

    /**
     * @param string|null $orderLine
     * @return MerchantOrderItem
     */
    public function setOrderLine(?string $orderLine): self
    {
        $this->orderLine = $orderLine;
        return $this;
    }

    public function getCartItemComment(): ?string
    {
        return $this->cartItemComment;
    }

    public function setCartItemComment(?string $cartItemComment): self
    {
        $this->cartItemComment = $cartItemComment;
        return $this;
    }

    public function getCartItemSplitDelivery(): ?array
    {
        return $this->cartItemSplitDelivery;
    }

    public function setCartItemSplitDelivery(?array $cartItemSplitDelivery): self
    {
        $this->cartItemSplitDelivery = $cartItemSplitDelivery;
        return $this;
    }

    public function getRealStock(): ?string
    {
        return $this->realStock;
    }

    public function setRealStock(?string $realStock): self
    {
        $this->realStock = $realStock;
        return $this;
    }

    public function getDeliveryTimeDelayBeforeShipping(): ?int
    {
        return $this->deliveryTimeDelayBeforeShipping;
    }

    public function setDeliveryTimeDelayBeforeShipping(?int $deliveryTimeDelayBeforeShipping): self
    {
        $this->deliveryTimeDelayBeforeShipping = $deliveryTimeDelayBeforeShipping;
        return $this;
    }

    public function getDeliveryDateDelayBeforeShipping(): DateTimeImmutable
    {
        return $this->deliveryDateDelayBeforeShipping;
    }

    public function setDeliveryDateDelayBeforeShipping(DateTimeImmutable $deliveryDateDelayBeforeShipping): self
    {
        $this->deliveryDateDelayBeforeShipping = $deliveryDateDelayBeforeShipping;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getBuyerInternalReference(): ?string
    {
        return $this->buyerInternalReference;
    }

    /**
     * @param string|null $buyerInternalReference
     * @return MerchantOrderItem
     */
    public function setBuyerInternalReference(?string $buyerInternalReference): self
    {
        $this->buyerInternalReference = $buyerInternalReference;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getFrameContract(): ?string
    {
        return $this->frameContract;
    }

    /**
     * @param string|null $frameContract
     * @return MerchantOrderItem
     */
    public function setFrameContract(?string $frameContract): self
    {
        $this->frameContract = $frameContract;
        return $this;
    }


}
