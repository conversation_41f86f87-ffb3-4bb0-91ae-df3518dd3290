<?php

namespace AppBundle\Model\View\OrderDetail;

class ShipmentProduct
{
    /**
     * @var string|null
     */
    private $imageUrl;

    /**
     * @var string|null
     */
    private $name;

    /**
     * @var string|null
     */
    private $vendorRef;

    /**
     * @var int
     */
    private $quantity;

    /**
     * @return string|null
     */
    public function getImageUrl(): ?string
    {
        return $this->imageUrl;
    }

    /**
     * @param string|null $imageUrl
     * @return $this
     */
    public function setImageUrl(?string $imageUrl): self
    {
        $this->imageUrl = $imageUrl;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    /**
     * @param string|null $name
     * @return $this
     */
    public function setName(?string $name): self
    {
        $this->name = $name;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getVendorRef(): ?string
    {
        return $this->vendorRef;
    }

    /**
     * @param string|null $vendorRef
     * @return $this
     */
    public function setVendorRef(?string $vendorRef): self
    {
        $this->vendorRef = $vendorRef;
        return $this;
    }

    /**
     * @return int
     */
    public function getQuantity(): int
    {
        return $this->quantity;
    }

    /**
     * @param int $quantity
     * @return $this
     */
    public function setQuantity(int $quantity): self
    {
        $this->quantity = $quantity;
        return $this;
    }
}
