<?php

namespace AppBundle\Model\View\OrderDetail;

use AppBundle\Entity\Address;

class Order
{
    private $id;

    private $createdOn;

    private $idNumber;

    private $costCenter;

    private $addressName;

    private $paymentCode;

    private $reconciliationKey;

    private $site;

    private $validationNumber;

    private $ibanAccountName;

    private $iban;

    private $status;

    private $amount;

    private $amountVatIncluded;

    private $price;

    private $currency;

    private $shippingTotal;

    /**
     * Array of subtotal vat with ['vat' => 'total']
     * @var
     */
    private $subTotalVat;

    private $firstExpectedDate;

    private $hasInvoices;

    /**
     * @var array<MerchantOrder>
     */
    private $merchantOrders;

    /**
     * @var string|null
     */
    private $packagingRequest1;

    /**
     * @var string|null
     */
    private $packagingRequest2;

    /**
     * @var string|null
     */
    private $packagingRequest3;

    /**
     * @var string|null
     */
    private $documentationRequest1;

    /**
     * @var string|null
     */
    private $documentationRequest2;

    /**
     * @var string|null
     */
    private $documentationRequest3;

    /**
     * @var string|null
     */
    private $documentationRequest4;

    /**
     * @var string|null
     */
    private $documentationRequest5;

    /**
     * @var string|null
     */
    private $documentationRequest6;

    /**
     * @var string|null
     */
    private $documentationRequest7;

    /**
     * @var string|null
     */
    private $documentationRequest8;

    /**
     * @var string|null
     */
    private $documentationRequest9;

    /**
     * @var string|null
     */
    private $documentationRequest10;

    /**
     * @var Address|null
     */
    private ?Address $shippingAddress;

    /**
     * @var array|null
     */
    private ?array $documentFileUploaded;

    /**
     * @var array|null
     */
    private ?array $documentFileUploadedNames;


    public function getId()
    {
        return $this->id;
    }

    public function setId($id)
    {
        $this->id = $id;
        return $this;
    }

    public function getCreatedOn()
    {
        return $this->createdOn;
    }

    public function setCreatedOn($createdOn)
    {
        $this->createdOn = $createdOn;
        return $this;
    }

    public function getIdNumber()
    {
        return $this->idNumber;
    }

    public function setIdNumber($idNumber)
    {
        $this->idNumber = $idNumber;
        return $this;
    }

    public function getCostCenter()
    {
        return $this->costCenter;
    }

    public function setCostCenter($costCenter)
    {
        $this->costCenter = $costCenter;
        return $this;
    }

    public function getAddressName()
    {
        return $this->addressName;
    }

    public function setAddressName($addressName)
    {
        $this->addressName = $addressName;
        return $this;
    }

    public function getPaymentCode()
    {
        return $this->paymentCode;
    }

    public function setPaymentCode($paymentCode)
    {
        $this->paymentCode = $paymentCode;
        return $this;
    }

    public function getReconciliationKey()
    {
        return $this->reconciliationKey;
    }

    public function setReconciliationKey($reconciliationKey)
    {
        $this->reconciliationKey = $reconciliationKey;
        return $this;
    }

    public function getSite(): string
    {
        return $this->site;
    }

    public function setSite(string $site): Order
    {
        $this->site = $site;
        return $this;
    }

    public function getIbanAccountName()
    {
        return $this->ibanAccountName;
    }

    public function setIbanAccountName($ibanAccountName)
    {
        $this->ibanAccountName = $ibanAccountName;
        return $this;
    }

    public function getIban()
    {
        return $this->iban;
    }

    public function setIban($iban)
    {
        $this->iban = $iban;
        return $this;
    }

    public function getStatus()
    {
        return $this->status;
    }

    public function setStatus($status)
    {
        $this->status = $status;
        return $this;
    }

    public function getAmount()
    {
        return $this->amount;
    }

    public function setAmount($amount)
    {
        $this->amount = $amount;
        return $this;
    }

    public function getAmountVatIncluded()
    {
        return $this->amountVatIncluded;
    }

    public function setAmountVatIncluded($amountVatIncluded)
    {
        $this->amountVatIncluded = $amountVatIncluded;
        return $this;
    }

    public function getPrice()
    {
        return $this->price;
    }

    public function setPrice($price)
    {
        $this->price = $price;
        return $this;
    }

    public function getCurrency()
    {
        return $this->currency;
    }

    public function setCurrency($currency)
    {
        $this->currency = $currency;
        return $this;
    }

    public function getSubTotalVat()
    {
        return $this->subTotalVat;
    }

    public function setSubTotalVat($subTotalVat)
    {
        $this->subTotalVat = $subTotalVat;
        return $this;
    }

    public function getFirstExpectedDate()
    {
        return $this->firstExpectedDate;
    }

    public function setFirstExpectedDate($firstExpectedDate)
    {
        $this->firstExpectedDate = $firstExpectedDate;
        return $this;
    }

    public function getMerchantOrders(): array
    {
        return $this->merchantOrders;
    }

    public function setMerchantOrders(array $merchantOrders): self
    {
        $this->merchantOrders = $merchantOrders;
        return $this;
    }

    public function hasInvoices(): bool
    {
        return $this->hasInvoices;
    }

    public function setHasInvoices($hasInvoices)
    {
        $this->hasInvoices = $hasInvoices;
        return $this;
    }

    public function getShippingTotal()
    {
        return $this->shippingTotal;
    }

    public function setShippingTotal($shippingTotal)
    {
        $this->shippingTotal = $shippingTotal;
        return $this;
    }

    public function getValidationNumber(): ?string
    {
        return $this->validationNumber;
    }

    public function setValidationNumber(?string $validationNumber): self
    {
        $this->validationNumber = $validationNumber;
        return $this;
    }

    public function getPackagingRequest1(): ?string
    {
        return $this->packagingRequest1;
    }

    public function setPackagingRequest1(?string $packagingRequest1): Order
    {
        $this->packagingRequest1 = $packagingRequest1;
        return $this;
    }

    public function getPackagingRequest2(): ?string
    {
        return $this->packagingRequest2;
    }

    public function setPackagingRequest2(?string $packagingRequest2): Order
    {
        $this->packagingRequest2 = $packagingRequest2;
        return $this;
    }

    public function getPackagingRequest3(): ?string
    {
        return $this->packagingRequest3;
    }

    public function setPackagingRequest3(?string $packagingRequest3): Order
    {
        $this->packagingRequest3 = $packagingRequest3;
        return $this;
    }

    public function getDocumentationRequest1(): ?string
    {
        return $this->documentationRequest1;
    }

    public function setDocumentationRequest1(?string $documentationRequest1): Order
    {
        $this->documentationRequest1 = $documentationRequest1;
        return $this;
    }

    public function getDocumentationRequest2(): ?string
    {
        return $this->documentationRequest2;
    }

    public function setDocumentationRequest2(?string $documentationRequest2): Order
    {
        $this->documentationRequest2 = $documentationRequest2;
        return $this;
    }

    public function getDocumentationRequest3(): ?string
    {
        return $this->documentationRequest3;
    }

    public function setDocumentationRequest3(?string $documentationRequest3): Order
    {
        $this->documentationRequest3 = $documentationRequest3;
        return $this;
    }

    public function getDocumentationRequest4(): ?string
    {
        return $this->documentationRequest4;
    }

    public function setDocumentationRequest4(?string $documentationRequest4): Order
    {
        $this->documentationRequest4 = $documentationRequest4;
        return $this;
    }

    public function getDocumentationRequest5(): ?string
    {
        return $this->documentationRequest5;
    }

    public function setDocumentationRequest5(?string $documentationRequest5): Order
    {
        $this->documentationRequest5 = $documentationRequest5;
        return $this;
    }

    public function getDocumentationRequest6(): ?string
    {
        return $this->documentationRequest6;
    }

    public function setDocumentationRequest6(?string $documentationRequest6): Order
    {
        $this->documentationRequest6 = $documentationRequest6;
        return $this;
    }

    public function getDocumentationRequest7(): ?string
    {
        return $this->documentationRequest7;
    }

    public function setDocumentationRequest7(?string $documentationRequest7): Order
    {
        $this->documentationRequest7 = $documentationRequest7;
        return $this;
    }

    public function getDocumentationRequest8(): ?string
    {
        return $this->documentationRequest8;
    }

    public function setDocumentationRequest8(?string $documentationRequest8): Order
    {
        $this->documentationRequest8 = $documentationRequest8;
        return $this;
    }

    public function getDocumentationRequest9(): ?string
    {
        return $this->documentationRequest9;
    }

    public function setDocumentationRequest9(?string $documentationRequest9): Order
    {
        $this->documentationRequest9 = $documentationRequest9;
        return $this;
    }

    public function getDocumentationRequest10(): ?string
    {
        return $this->documentationRequest10;
    }

    public function setDocumentationRequest10(?string $documentationRequest10): Order
    {
        $this->documentationRequest10 = $documentationRequest10;
        return $this;
    }

    /**
     * @return Address|null
     */
    public function getShippingAddress(): ?Address
    {
        return $this->shippingAddress;
    }

    /**
     * @param Address|null $shippingAddress
     */
    public function setShippingAddress(?Address $shippingAddress): void
    {
        $this->shippingAddress = $shippingAddress;
    }

    /**
     * @return array|null
     */
    public function getDocumentFileUploaded(): ?array
    {
        return $this->documentFileUploaded;
    }

    /**
     * @param array|null $documentFileUploaded
     */
    public function setDocumentFileUploaded(?array $documentFileUploaded): void
    {
        $this->documentFileUploaded = $documentFileUploaded;
    }

    /**
     * @return array|null
     */
    public function getDocumentFileUploadedNames(): ?array
    {
        return $this->documentFileUploadedNames;
    }

    /**
     * @param array|null $documentFileUploadedNames
     */
    public function setDocumentFileUploadedNames(?array $documentFileUploadedNames): void
    {
        $this->documentFileUploadedNames = $documentFileUploadedNames;
    }
}
