<?php

namespace AppBundle\Model\View\OrderDetail;

use AppBundle\Entity\Address;
use AppBundle\Entity\Company;
use AppBundle\Entity\Site;
use AppBundle\Exception\UnexpectedValueException;
use AppBundle\Repository\AddressRepository;
use AppBundle\Services\AddressService;
use AppBundle\Services\CompanyCatalogService;
use AppBundle\Services\CountryService;
use AppBundle\Services\OfferService;
use AppBundle\Services\OrderService;
use AppBundle\Services\Shipping\TrackingService;
use AppBundle\Services\WebHelpIbanService;
use AppBundle\Util\DateUtil;
use DateTimeImmutable;
use Open\IzbergBundle\Api\MessageApi;
use Open\IzbergBundle\Api\OrderApi;
use Open\IzbergBundle\Model\OrderItem;
use Symfony\Contracts\Translation\TranslatorInterface;
use Upela\Api\Exception;
use Upela\Model\Product;

class OrderFactory
{

    private OrderService $orderService;
    private AddressService $addressService;
    private MessageApi $messageApi;
    private OfferService $offerService;
    private OrderApi $orderApi;
    private Company $company;
    private CompanyCatalogService $companyCatalogService;
    private WebHelpIbanService $webHelpIbanService;
    private TrackingService $trackingService;
    private TranslatorInterface $translator;
    private CountryService $countryService;
    private AddressRepository $addressRepository;

    public function __construct(
        OrderService $orderService,
        AddressService $addressService,
        MessageApi $messageApi,
        OfferService $offerService,
        OrderApi $orderApi,
        CompanyCatalogService $companyCatalogService,
        WebHelpIbanService $webHelpIbanService,
        TrackingService $trackingService,
        TranslatorInterface $translator,
        CountryService $countryService,
        AddressRepository $addressRepository
    )
    {
        $this->orderService = $orderService;
        $this->addressService = $addressService;
        $this->messageApi = $messageApi;
        $this->offerService = $offerService;
        $this->orderApi = $orderApi;
        $this->companyCatalogService = $companyCatalogService;
        $this->webHelpIbanService = $webHelpIbanService;
        $this->trackingService = $trackingService;
        $this->translator = $translator;
        $this->countryService = $countryService;
        $this->addressRepository = $addressRepository;
    }

    public function build(\Open\IzbergBundle\Model\Order $izbergOrder, Company $company, string $locale): Order
    {
        $this->company = $company;
        $order = (new Order())
            ->setId($izbergOrder->getId())
            ->setCreatedOn($izbergOrder->getCreatedOn())
            ->setIdNumber($izbergOrder->getIdNumber())
            ->setStatus($izbergOrder->getStatus())
            ->setAmount($izbergOrder->getAmount())
            ->setAmountVatIncluded($izbergOrder->getAmountVatIncluded())
            ->setPrice($izbergOrder->getPrice())
            ->setCurrency($izbergOrder->getCurrency());

        /** @var Address $address */
        $address = $this->addressRepository->findOneBy([
            'izbergAddressId' => $izbergOrder->getShippingAddress()->getId()
        ]);
        $order->setShippingAddress($address);

        $shippingPoint = $this->addressService->getCostCenterByIzbergAddressId($izbergOrder->getShippingAddress()->getId());
        $merchantOrder = $izbergOrder->getMerchantOrders()->first();

        if($shippingPoint != null){
            /** @var Site $shippingPointSite */
            $shippingPointSite = $shippingPoint->getSite();
            $order
                ->setCostCenter($shippingPointSite->getName())
                ->setAddressName($shippingPoint->getName())
            ;
        }

        if ($merchantOrder) {
            $merchantOrder = $this->orderApi->fetchMerchantOrderById($merchantOrder->getId());
            $attributes = $merchantOrder->getAttributes();
            $order
                ->setPackagingRequest1($attributes['packaging_info_1'] ?? null)
                ->setPackagingRequest2($attributes['packaging_info_2'] ?? null)
                ->setPackagingRequest3($attributes['packaging_info_3'] ?? null)
                ->setDocumentationRequest1($attributes['delivery_info_1'] ?? null)
                ->setDocumentationRequest2($attributes['delivery_info_2'] ?? null)
                ->setDocumentationRequest3($attributes['delivery_info_3'] ?? null)
                ->setDocumentationRequest4($attributes['delivery_info_4'] ?? null)
                ->setDocumentationRequest5($attributes['delivery_info_5'] ?? null)
                ->setDocumentationRequest6($attributes['delivery_info_6'] ?? null)
                ->setDocumentationRequest7($attributes['delivery_info_7'] ?? null)
                ->setDocumentationRequest8($attributes['delivery_info_8'] ?? null)
                ->setDocumentationRequest9($attributes['delivery_info_9'] ?? null)
                ->setDocumentationRequest10($attributes['delivery_info_10'] ?? null)
                ->setDocumentFileUploaded($this->orderService->buildDocumentFileUploaded($attributes))
            ;
        }

        // set $order->hasInvoices
        $hasInvoices = $this->computeHasInvoices($izbergOrder->getMerchantOrders()->toArray());
        $order->setHasInvoices($hasInvoices);

        // set $order->reconciliationKey
        $reconciliationKey = $this->fetchReconciliationKey($izbergOrder->getMerchantOrders()->toArray());
        $order->setReconciliationKey($reconciliationKey);

        // set $order->validationNumber
        $validationNumber = $this->fetchValidationNumber($izbergOrder->getMerchantOrders()->toArray());
        $order->setValidationNumber($validationNumber);

        // set $order->ibanAccountName
        // set $order->iban
        if ($order->getReconciliationKey()) {
            $iban = $this->webHelpIbanService->getIbanFromCurrency($order->getCurrency());
            $ibanAccountName = $this->webHelpIbanService->getIbanAccountNameFromCurrency($order->getCurrency());

            $order->setIban($iban);
            $order->setIbanAccountName($ibanAccountName);
        }

        // set $order->paymentCode
        if ($izbergOrder->getPayment() !== null && $izbergOrder->getPayment()->getPaymentMethodCode() !== null) {
            $paymentCode = $izbergOrder->getPayment()->getPaymentMethodCode();
            $order->setPaymentCode($paymentCode);
        }

        // set $order->merchantOrders
        $merchantOrders = $this->buildMerchantOrders($izbergOrder->getMerchantOrders()->toArray(), $izbergOrder->getCurrency());
        $order->setMerchantOrders($merchantOrders);

        $this->decorateWithShippingData($order, $locale);

        // set $order->subTotalVat
        $subTotalVat = array_reduce(
            $order->getMerchantOrders(),
            function(array $subTotalVat, MerchantOrder $merchantOrder) {
                $merchantOrderSubTotalVat = $merchantOrder->getSubTotalVat();
                foreach($merchantOrderSubTotalVat as $percent => $totalVat) {
                    if (!isset($subTotalVat[$percent])) {
                        $subTotalVat[$percent] = 0.0;
                    }

                    $subTotalVat[$percent] = $subTotalVat[$percent] + $totalVat;
                }

                return $subTotalVat;
            },
            []
        );
        krsort($subTotalVat);
        $order->setSubTotalVat($subTotalVat);

        $this->decorateWithShipmentTracking($order, $locale);

        return $order;
    }

    private function getOrderItemBuyerInternalReferenceValue(int $orderItemId): ?string
    {
        return $this->orderApi->getOrderItemBuyerInternalReferenceValue($orderItemId);
    }

    private function getOrderItemFrameContractValue(int $orderItemId): ?string
    {
        return $this->orderApi->getOrderItemFrameContractValue($orderItemId);
    }

    private function getOrderItemCartItemCommentValue(int $orderItemId): ?string
    {
        return $this->orderApi->getOrderItemCartItemCommentValue($orderItemId);
    }

    private function getOrderLineValue(int $orderItemId): ?string
    {
        return $this->orderApi->getOrderItemOrderLineValue($orderItemId);
    }

    /**
     * @param array<\Open\IzbergBundle\Model\MerchantOrder> $izbergMerchantOrders
     * @return array<MerchantOrder>
     * @throws \Exception
     */
    private function buildMerchantOrders(array $izbergMerchantOrders, string $currency): array
    {
        return array_map(
            function(\Open\IzbergBundle\Model\MerchantOrder $izbergMerchantOrder) use ($currency) : MerchantOrder {
                $izbergOrderMerchant = $this->orderService->fetchMerchantsOrderByOrderId($izbergMerchantOrder->getId());

                if (!$izbergOrderMerchant) {
                    throw new UnexpectedValueException(sprintf('Cannot find merchant order with id %s', $izbergMerchantOrder->getId()));
                }

                $attr = 'ZZC-Internal-Buyer-Order-ID';
                $merchantOrder = (new MerchantOrder())
                    ->setId($izbergOrderMerchant->getId())
                    ->setMerchantId($izbergOrderMerchant->getMerchant()->getId())
                    ->setMerchantName($izbergOrderMerchant->getMerchant()->getName())
                    ->setStatus($izbergOrderMerchant->getStatus())
                    ->setAmount($izbergOrderMerchant->getAmount())
                    ->setAmountVatIncluded($izbergOrderMerchant->getAmountVatIncluded())
                    ->setBuyerInternalOrderId($izbergOrderMerchant->getAttributes()[$attr] ?? null)
                    ->setCurrency($currency)
                ;

                // set $merchantOrder->hasDisputes
                $disputes = $this->messageApi->getDisputesByMerchantOrderId($izbergMerchantOrder->getId());
                $merchantOrder->setHasDisputes((count($disputes->objects) > 0));

                $orderItems = $izbergOrderMerchant->getItems()->toArray();

                // hide order item which represent shipping
                $orderItems = array_filter(
                    $orderItems,
                    function(OrderItem $orderItem) {
                        return !(preg_match('/^shipment-[0-9]+$/', $orderItem->getOfferExternalId()));
                    }
                );

                // set $merchantOrder->items
                $merchantOrder->setItems(
                    array_map(
                        function(OrderItem $orderItem) use ($izbergOrderMerchant): MerchantOrderItem{
                            $orderExtraInfo = $this->orderApi->getMerchantOrderItemExtraInfo($orderItem->getId());
                            $offer = $this->offerService->findOfferById($orderItem->getOfferId());

                            $cartItemSplitDelivery = $orderExtraInfo['Cart-item-split-delivery'] ?? null;
                            if (!empty($cartItemSplitDelivery) && !is_array($cartItemSplitDelivery)) {
                                $cartItemSplitDelivery = json_decode($cartItemSplitDelivery, true);
                            }

                            $merchantOrderItem = (new MerchantOrderItem())
                                ->setId($orderItem->getId())
                                ->setName($orderItem->getName())
                                ->setOfferId($orderItem->getOfferId())
                                ->setImageUrl($orderItem->getitem_image_url())
                                ->setStatus($orderItem->getStatus())
                                ->setPrice($orderItem->getPrice())
                                ->setCurrency($orderItem->getCurrency())
                                ->setQuantity($orderItem->getQuantity())
                                ->setBuyerInternalReference($this->getOrderItemBuyerInternalReferenceValue($orderItem->getId()))
                                ->setOrderLine($this->getOrderLineValue($orderItem->getId()))
                                ->setFrameContract($this->getOrderItemFrameContractValue($orderItem->getId()))
                                ->setCartItemComment($this->getOrderItemCartItemCommentValue($orderItem->getId()))
                                ->setCartItemSplitDelivery($cartItemSplitDelivery)
                                ->setRealStock($offer->getRealStock())
                                ->setDeliveryTimeDelayBeforeShipping($offer->getDeliveryTimeDelayBeforeShipping());
                            ;

                            // set $merchantOrderItem->setIncoterm
                            if ($offer) {
                                $merchantOrderItem->setIncoterm($offer->getIncoterm());
                            }

                            // set $merchantOrderItem->setDeliverydate
                            if ($offer) {
                                $deliveryDate = ($izbergOrderMerchant->getCreatedOn())
                                    ->add(
                                        (new \DateInterval(sprintf('P%dD', $offer->getDeliveryTime())))
                                    );

                                $deliveryDateDelayBeforeShipping = ($izbergOrderMerchant->getCreatedOn())
                                    ->add(
                                        (new \DateInterval(sprintf('P%dD', $offer->getDeliveryTimeDelayBeforeShipping())))
                                    );

                                if ($offer->getIncoterm() === 'FCA') {
                                    $deliveryDate = DateUtil::moveToNextWorkingDay($deliveryDate);
                                    $deliveryDateDelayBeforeShipping = DateUtil::moveToNextWorkingDay($deliveryDateDelayBeforeShipping);
                                }

                                $merchantOrderItem->setDeliverydate($deliveryDate);
                                $merchantOrderItem->setDeliveryDateDelayBeforeShipping($deliveryDateDelayBeforeShipping);
                            }

                            $shippingAddress = $izbergOrderMerchant->getShippingAddress();
                            if ($shippingAddress) {
                                $country = $shippingAddress->getCountry();

                                if ($country) {
                                    $countryEntity = $this->countryService->getCountryByIzbergId($country->getId());

                                    if ($countryEntity) {
                                        $merchantOrderItem->setCountry('country.' . strtolower($countryEntity->getCode()));
                                    }
                                }
                            }

                            $fullOrderItem = $this->orderApi->getOrderItemById($orderItem->getId());
                            if($fullOrderItem->delivery_dates != null && count($fullOrderItem->delivery_dates) > 0) {
                                $merchantOrderItem->setDeliverydate(($fullOrderItem->delivery_dates[0]->expected_delivery_date));
                            }

                            // set $merchantOrderItem->sellerRef
                            if ($offer) {
                                $merchantOrderItem->setSellerRef($offer->getSellerRef());
                            }

                            // overwrite $merchantOrderItem->name
                            if($offer && $offer->getOfferTitle() != null){
                                $merchantOrderItem->setName($offer->getOfferTitle());
                            }

                            // set $merchantOrderItem->dangerousProduct
                            if ($offer) {
                                $merchantOrderItem->setDangerousProduct($offer->isDangerousProduct());
                            }

                            return $merchantOrderItem;
                        },
                        $orderItems
                    )
                );

                // set $merchantOrder->subTotalVat
                $subTotalVat = array_reduce(
                    $orderItems,
                    function(array $subTotalVat, OrderItem $orderItem) use ($merchantOrder){

                        // do not calculate subtotal vat if orderitem is cancelled
                        if ($orderItem->getStatus() == 2000) {
                            return $subTotalVat;
                        }

                        $percent = ($orderItem->getVat()*100)/$orderItem->getPrice();

                        if (!$percent) {
                            return $subTotalVat;
                        }

                        if (!isset($subTotalVat[$percent])) {
                            $subTotalVat[$percent] = 0.0;
                        }

                        if($merchantOrder->getStatus() == 2000){
                            $subTotalVat[$percent] += 0;
                        }else{
                            $subTotalVat[$percent] += $orderItem->getVat() * $orderItem->getQuantity();
                        }

                        return $subTotalVat;
                    },
                    []
                );
                $merchantOrder->setSubTotalVat($subTotalVat);

                return $merchantOrder;
            },
            $izbergMerchantOrders
        );
    }

    /**
     * @param array<\Open\IzbergBundle\Model\MerchantOrder> $izbergMerchantOrders
     * @return bool
     */
    private function computeHasInvoices(array $izbergMerchantOrders): bool
    {
        $hasInvoices = false;

        foreach($izbergMerchantOrders as $izbergMerchantOrder) {
            $invoices = $this->orderService->getMerchantOrderInvoices($izbergMerchantOrder->getId());

            foreach ($invoices as $invoice){
                if($invoice->status == 'emitted'){
                    return true;
                }
            }
        }

        return $hasInvoices;
    }

    /**
     * @param array<\Open\IzbergBundle\Model\MerchantOrder> $izbergMerchantOrders
     * @return ?string
     */
    private function fetchReconciliationKey(array $izbergMerchantOrders): ?string
    {
        $reconciliationKey = null;

        foreach($izbergMerchantOrders as $izbergMerchantOrder) {
            $mOrder = $this->orderService->fetchMerchantsOrderByOrderId($izbergMerchantOrder->getId());

            if(!$mOrder) {
                throw new UnexpectedValueException(sprintf('Cannot find merchant order with id %s', $izbergMerchantOrder->getId()));
            }

            if ($mOrder->getAttributes() != null && array_key_exists("reconciliation_key", $mOrder->getAttributes())) {
                return $mOrder->getAttributes()["reconciliation_key"];
            }
        }

        return $reconciliationKey;
    }

    private function fetchValidationNumber(array $izbergMerchantOrders): ?string
    {
        $validationNumber = null;
        foreach($izbergMerchantOrders as $izbergMerchantOrder) {
            $mOrder = $this->orderService->fetchMerchantsOrderByOrderId($izbergMerchantOrder->getId());
            if (!$mOrder) {
                throw new UnexpectedValueException(sprintf('Cannot find merchant order with id %s', $izbergMerchantOrder->getId()));
            }
            if ($mOrder->getAttributes() != null && array_key_exists("ZZB-Internal-Buyer-Validation-ID", $mOrder->getAttributes())) {
                return $mOrder->getAttributes()["ZZB-Internal-Buyer-Validation-ID"];
            }
        }

        return $validationNumber;
    }

    /**
     * update the merchant order items delivery dates
     * update the merchant order shipping total
     * update the merchant order shipping name
     * update the merchant order shipping VAT
     * update the order shipping total
     * update the order shipping VAT
     *
     * @param Order $order
     * @throws \Exception
     */
    private function decorateWithShippingData(Order $order, string $locale)
    {
        $merchantOrders = array_map(
            function(MerchantOrder $merchantOrder) use ($order, $locale){
                $orderItemsDeliveryDates = [];
                $merchantOrderShippingTotal = null;
                $merchantOrderShippingName = null;
                $shippingSubTotalVat = [];
                $shippingOptions = [];

                if ($merchantOrder->getStatus() != 2000) {
                    $shippingOptions = $this->orderApi->fetchMerchantOrderShippingOptions($merchantOrder->getId());
                }

                $cancelledOrderItems = array_filter(array_map(
                    function(MerchantOrderItem $orderItem) {
                        return ($orderItem->getStatus() == 2000) ? $orderItem->getId() : null;
                    },
                    $merchantOrder->getItems()
                ));

                foreach($shippingOptions as $shippingOption) {

                    // si tous les shippingoption order items sont cancelled alors je break
                    $shippingCancelled = true;
                    foreach ($shippingOption->order_items as $orderItem) {
                        if (!in_array($orderItem->id, $cancelledOrderItems)) {
                            $shippingCancelled = false;
                            break;
                        }
                    }

                    if (!$shippingCancelled) {
                        $izbergShippingOffer = $shippingOption->options->offer ?? null;

                        if ($izbergShippingOffer) {
                            $vatRate = (int)$shippingOption->options->offer->vatRate;
                            $vatPrice = ($shippingOption->options->offer->priceTi - $shippingOption->options->offer->priceTe);

                            if (!array_key_exists($vatRate, $shippingSubTotalVat)) {
                                $shippingSubTotalVat[$vatRate] = 0;
                            }
                            $shippingSubTotalVat[$vatRate] += $vatPrice;

                            $orderItemsDeliveryDates = array_reduce(
                                $shippingOption->order_items,
                                function (array $orderItems, \stdClass $orderItem) use ($izbergShippingOffer) {
                                    $orderItems[$orderItem->id] = new DateTimeImmutable($izbergShippingOffer->deliveryDate);
                                    return $orderItems;
                                },
                                $orderItemsDeliveryDates
                            );

                            $merchantOrderShippingTotal = $merchantOrderShippingTotal + $shippingOption->options->offer->priceTe;
                        }

                        $merchantOrderShippingName = $shippingOption->name;
                    }
                }

                $merchantOrderItems = array_map(
                    function(MerchantOrderItem $merchantOrderItem) use ($orderItemsDeliveryDates) {
                        $deliveryDate  = $orderItemsDeliveryDates[$merchantOrderItem->getId()] ?? $merchantOrderItem->getDeliveryDate();
                        $merchantOrderItem->setDeliveryDate($deliveryDate);
                        $toBeShipped = array_key_exists($merchantOrderItem->getId(), $orderItemsDeliveryDates);
                        $merchantOrderItem->setToBeShipped($toBeShipped);

                        return $merchantOrderItem;
                    },
                    $merchantOrder->getItems()
                );

                // update the merchant order items delivery dates
                $merchantOrder->setItems($merchantOrderItems);

                // update the merchant order shipping total
                $merchantOrder->setShippingTotal($merchantOrderShippingTotal);

                // update the order shipping total
                $order->setShippingTotal($order->getShippingTotal() + $merchantOrderShippingTotal);

                // update the merchant order shipping name
                $toBeTranslated = ['cheapest', 'fastest'];
                if (in_array($merchantOrderShippingName, $toBeTranslated)){
                    $merchantOrderShippingName = $this->translator->trans(
                        'shipping.option.' . $merchantOrderShippingName,
                        [],
                        'AppBundle',
                        $locale
                    );
                }

                // translatable shipping name
                if (in_array($merchantOrderShippingName, ['cheapest', 'fastest'])) {
                    $merchantOrderShippingName = sprintf('shipping.option.%s', $merchantOrderShippingName);
                }
                $merchantOrder->setShippingName($merchantOrderShippingName);

                // update the merchant order subtotalVat with shipping VAT
                $merchantOrderSubTotalVat = $merchantOrder->getSubTotalVat();

                foreach($shippingSubTotalVat as $vatRate => $vatPrice) {
                    if (!array_key_exists($vatRate, $merchantOrderSubTotalVat)) {
                        $merchantOrderSubTotalVat[$vatRate] = 0;
                    }

                    $merchantOrderSubTotalVat[$vatRate] += $vatPrice;
                }
                krsort($merchantOrderSubTotalVat);

                $merchantOrder->setSubTotalVat($merchantOrderSubTotalVat);


                return $merchantOrder;
            },
            $order->getMerchantOrders()
        );

        $order->setMerchantOrders($merchantOrders);
    }

    private function decorateWithShipmentTracking(Order $order, string $locale)
    {
        $merchantOrders = array_reduce(
            $order->getMerchantOrders(),
            function(array $merchantOrders, MerchantOrder $merchantOrder) {

                // get all merchant order item elligible for upela shipping
                $itemsForShipping = array_filter(
                    array_map(
                        function(MerchantOrderItem $merchantOrderItem) {
                            if(!$merchantOrderItem->isToBeShipped()) {
                                return null;
                            }
                            return $merchantOrderItem;
                        },
                        $merchantOrder->getItems()
                    )
                );

                // array keys of those items
                $items = array_combine(
                    array_map(
                        function(MerchantOrderItem $merchantOrderItem) {
                            return $merchantOrderItem->getId();
                        },
                        $itemsForShipping
                    ),
                    $itemsForShipping
                );

                // fetch all products in upela for the given merchant order
                // retrieve all shipments object
                $products = [];
                /*
                if (!empty($items)) {
                    try {
                        $products = $this->trackingService->fetchProductByIds(array_keys($items));
                    } catch(Exception $exception) {
                        $merchantOrder->setTrackingError(true);
                    }
                } */


                // update merchant order item with shipping progression
                // go over all products to check if is fully delivered or not
                $productsDeliveryStatuses = array_reduce(
                    $products,
                    function(array $productsDeliveryStatuses, Product $upelaProduct) {

                        $lastDeliveryDate = null;
                        $quantity = $upelaProduct->getQuantity();

                        // total shipment delivered + fetch last delivery date
                        $deliveredShipments = array_filter(
                            $upelaProduct->getShipments(),
                            function(\Upela\Model\Shipment $upelaShipment) use (&$lastDeliveryDate) {
                                $delivered = ($upelaShipment->getStatus() === 'Delivered');

                                if($delivered) {
                                    $deliveryDate = $upelaShipment->getExpectedDeliveryDate();
                                    if ($deliveryDate) {
                                        $deliveryDate = new DateTimeImmutable($deliveryDate);

                                        if (!$lastDeliveryDate) {
                                            $lastDeliveryDate = $deliveryDate;
                                        }

                                        if ($deliveryDate > $lastDeliveryDate) {
                                            $lastDeliveryDate = $deliveryDate;
                                        }
                                    }
                                }

                                return $delivered;
                            }
                        );

                        $quantityDelivered = array_reduce(
                            $deliveredShipments,
                            function(int $quantityDelivered, \Upela\Model\Shipment $upelaShipment) {
                                $quantityDelivered = $quantityDelivered + $upelaShipment->getQuantityProduct();
                                return $quantityDelivered;
                            },
                            0
                        );

                        // compute partiallyDelivered and fullyDelivered
                        $partiallyDelivered = ($quantityDelivered > 0 && $quantityDelivered < $quantity);
                        $fullyDelivered = ($quantityDelivered > 0 && $quantityDelivered === $quantity);

                        $productDeliveryStatus = [
                            'partiallyDelivered' => $partiallyDelivered,
                            'fullyDelivered' => $fullyDelivered,
                            'lastDeliveryDate' => $lastDeliveryDate,
                        ];

                        $productsDeliveryStatuses[$upelaProduct->getProductId()] = $productDeliveryStatus;

                        return $productsDeliveryStatuses;
                    },
                    []
                );

                $merchantOrderItems = array_map(
                    function(MerchantOrderItem $merchantOrderItem) use ($productsDeliveryStatuses) {
                        $productStatus = $productsDeliveryStatuses[$merchantOrderItem->getId()] ?? null;

                        if ($productStatus) {
                            $merchantOrderItem
                                ->setPartiallyDelivered($productStatus['partiallyDelivered'])
                                ->setFullyDelivered($productStatus['fullyDelivered'])
                                ->setLastDeliveredDate($productStatus['lastDeliveryDate']);
                        }

                        return $merchantOrderItem;
                    },
                    $merchantOrder->getItems()
                );

                $merchantOrder->setItems($merchantOrderItems);

                // build shipments based on upela products
                $shipments = array_reduce(
                    $products,
                    function(array $shipments, Product $upelaProduct) use ($items) {

                        /** @var  MerchantOrderItem $item */
                        $item = $items[$upelaProduct->getProductId()] ?? null;

                        $upelaShipments = array_filter(
                            $upelaProduct->getShipments(),
                            function(\Upela\Model\Shipment $upelaShipment) {
                                return in_array(strtolower($upelaShipment->getStatus()), ['delivered', 'pickuped']);
                            }
                        );

                        /** @var \Upela\Model\Shipment $upelaShipment */
                        foreach($upelaShipments as $upelaShipment) {

                            $shipment = $shipments[$upelaShipment->getShipmentId()] ?? null;
                            if (!$shipment) {
                                $expectedDeliveryDate = new DateTimeImmutable($upelaShipment->getExpectedDeliveryDate());

                                $shipment = (new Shipment())
                                    ->setName($upelaShipment->getCarrierName())
                                    ->setProducts([])
                                    ->setDeliveryDate($expectedDeliveryDate)
                                    ->setStatus(strtolower($upelaShipment->getStatus()));
                            }

                            $existingShipmentProducts = $shipment->getProducts();

                            // build shipmentProduct object
                            $shipmentProduct = (new ShipmentProduct())
                                ->setQuantity($upelaShipment->getQuantityProduct());

                            if ($item) {
                                $shipmentProduct
                                    ->setName($item->getName())
                                    ->setImageUrl($item->getImageUrl())
                                    ->setVendorRef($item->getSellerRef());
                            }

                            $shipmentProducts = array_merge($existingShipmentProducts, [$shipmentProduct]);
                            $shipment->setProducts($shipmentProducts);

                            $shipments[$upelaShipment->getShipmentId()] = $shipment;
                        }

                        return $shipments;
                    },
                    []
                );

                $merchantOrder->setShipments($shipments);

                $merchantOrders[] = $merchantOrder;

                return $merchantOrders;
            },
            []
        );

        $order->setMerchantOrders($merchantOrders);
    }
}
