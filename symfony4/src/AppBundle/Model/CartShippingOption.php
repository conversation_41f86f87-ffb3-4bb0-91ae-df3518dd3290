<?php


namespace AppBundle\Model;

use J<PERSON>\Serializer\Annotation\Type;

class CartShippingOption
{
    /**
     * @var array
     */
    #[Type('array<AppBundle\Model\CartMerchantShippingOptions>')]
    private $cartMerchantShippingOptions;

    /**
     * @var int
     */
    #[Type('int')]
    private $cartId;

    /**
     * @var string
     */
    #[Type('string')]
    private $currency;

    public function getCartId(): int
    {
        return $this->cartId;
    }

    public function setCartId(int $cartId): void
    {
        $this->cartId = $cartId;
    }

    public function getCartMerchantShippingOptions(): array
    {
        return $this->cartMerchantShippingOptions;
    }

    public function setCartMerchantShippingOptions(array $cartMerchantShippingOptions): self
    {
        $this->cartMerchantShippingOptions = $cartMerchantShippingOptions;
        return $this;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function setCurrency(string $currency): self
    {
        $this->currency = $currency;
        return $this;
    }

    public function findMerchantShippingOption(int $merchantId, ?string $shippingOptionMode): ?ShippingOption
    {
        $merchantShippingOption = null;

        if (!$shippingOptionMode) {
            return null;
        }

        // valid $shippingOptionMode
        $regex = sprintf('/(%s)|(%s)|(%s_[0-9]+)/', CartMerchantShippingOptions::OPTION_FASTEST, CartMerchantShippingOptions::OPTION_CHEAPEST, CartMerchantShippingOptions::OPTION_MONOCARRIER);
        if (!preg_match($regex, $shippingOptionMode)) {
            throw new \InvalidArgumentException(sprintf('invalid shipping option given %s', $shippingOptionMode));
        }

        $merchantShippingOption = $this->findCartMerchantShippingOptions($merchantId);

        if (!$merchantShippingOption) {
            return null;
        }

        // return fastest
        if($shippingOptionMode === CartMerchantShippingOptions::OPTION_FASTEST) {
            return $merchantShippingOption->getFastestOption();
        }

        // return cheapest
        if($shippingOptionMode === CartMerchantShippingOptions::OPTION_CHEAPEST) {
            return $merchantShippingOption->getCheapestOption();
        }

        // return mono carrier shipping option
        $regex = sprintf('/%s_([0-9])+/', CartMerchantShippingOptions::OPTION_MONOCARRIER);
        if (preg_match($regex, $shippingOptionMode, $match)) {
            $index = $match[1];
            return $merchantShippingOption->getMonoCarrierOption()[$index] ?? null;
        }

        return null;
    }

    public function findCartMerchantShippingOptions(int $merchantId): ?CartMerchantShippingOptions
    {
        /** @var CartMerchantShippingOptions $merchantShippingOption */
        foreach($this->cartMerchantShippingOptions as $merchantShippingOption) {
            if ($merchantShippingOption->getMerchantId() === $merchantId) {
                return $merchantShippingOption;
            }
        }

        return null;
    }

    public function getMerchantItemDeliveryDates(int $merchantId, int $cartItemId)
    {
        $merchantShippingOption = null;
        /** @var CartMerchantShippingOptions $merchantShippingOption */
        foreach($this->cartMerchantShippingOptions as $merchantShippingOption) {
            if ($merchantShippingOption->getMerchantId() === $merchantId) {
                break;
            }
        }

        if (!$merchantShippingOption) {
            return null;
        }

        $itemDeliveryDates = [];

        $formatDeliveryDate = function(ShippingOption $shippingOption, int $cartItemId) {
            $date = $shippingOption->getCartItemDeliveryDate($cartItemId);
            return ($date) ? $date->format(DATE_ISO8601) : null;
        };

        $serviceName = function(ShippingOption $shippingOption, int $cartItemId) {
            return $shippingOption->getCartItemServiceName($cartItemId);
        };

        $getItemDeliveryDays = function(ShippingOption $shippingOption, int $cartItemId) {
            return $shippingOption->getCartItemDeliveryDays($cartItemId);
        };

        $getDeliveryDateTime = function(ShippingOption $shippingOption, int $cartItemId) {
            return $shippingOption->getCartItemDeliveryDateTime($cartItemId);
        };

        // return fastest delivery date
        $shippingOption = $merchantShippingOption->getFastestOption();
        if ($shippingOption !== null && $shippingOption->getPrice() !== null) {
            $dateFastest = call_user_func($formatDeliveryDate, $shippingOption, $cartItemId);
            if ($dateFastest !== null) {
                $itemDeliveryDates['fastest']['date'] = $dateFastest;
                $itemDeliveryDates['fastest']['serviceName'] = call_user_func($serviceName, $shippingOption, $cartItemId);
                $itemDeliveryDates['fastest']['itemDeliveryDays'] = call_user_func($getItemDeliveryDays, $shippingOption, $cartItemId);
                $itemDeliveryDates['fastest']['deliveryDateTime'] = call_user_func($getDeliveryDateTime, $shippingOption, $cartItemId);
            }
        }

        // return cheapest
        $shippingOption = $merchantShippingOption->getCheapestOption();
        if ($shippingOption !== null && $shippingOption->getPrice() !== null) {
            $dateCheapest = call_user_func($formatDeliveryDate, $shippingOption, $cartItemId);
            if ($dateCheapest !== null) {
                $itemDeliveryDates['cheapest']['date'] = $dateCheapest;
                $itemDeliveryDates['cheapest']['serviceName'] = call_user_func($serviceName, $shippingOption, $cartItemId);
                $itemDeliveryDates['cheapest']['itemDeliveryDays'] = call_user_func($getItemDeliveryDays, $shippingOption, $cartItemId);
                $itemDeliveryDates['cheapest']['deliveryDateTime'] = call_user_func($getDeliveryDateTime, $shippingOption, $cartItemId);
            }
        }

        // return mono carriers delivery dates
        $merchantShippingOptionMonos = $merchantShippingOption->getMonoCarrierOption();
        if ($merchantShippingOptionMonos !== null) {
            foreach ($merchantShippingOptionMonos as $index => $shippingOption) {
                if ($shippingOption !== null && $shippingOption->getPrice() !== null) {
                    $monoDate = call_user_func($formatDeliveryDate, $shippingOption, $cartItemId);
                    if ($monoDate !== null) {
                        $itemDeliveryDates['mono_' . $index]['date'] = $monoDate;
                        $itemDeliveryDates['mono_' . $index]['serviceName'] = call_user_func($serviceName, $shippingOption, $cartItemId);
                        $itemDeliveryDates['mono_' . $index]['itemDeliveryDays'] = call_user_func($getItemDeliveryDays, $shippingOption, $cartItemId);
                        $itemDeliveryDates['mono_' . $index]['deliveryDateTime'] = call_user_func($getDeliveryDateTime, $shippingOption, $cartItemId);
                    }
                }
            }
        }

        return json_encode($itemDeliveryDates);
    }
}
