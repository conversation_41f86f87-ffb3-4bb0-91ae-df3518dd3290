<?php

namespace AppBundle\Model;

class Payment
{
    public const PAYMENT_PREPAYMENT_BY_CREDIT_CARD = 0;
    public const PAYMENT_PREPAYMENT_BY_BANK_WIRE_TRANSFER = 1;
    public const PAYMENT_BY_TERM_PAYMENT = 2;

    /** @var int */
    private $type;

    /** @var int */
    private $izbergId;

    public function setType(int $type)
    {
        if (
            !in_array(
                $type,
                [
                    self::PAYMENT_PREPAYMENT_BY_CREDIT_CARD,
                    self::PAYMENT_PREPAYMENT_BY_BANK_WIRE_TRANSFER,
                    self::PAYMENT_BY_TERM_PAYMENT,
                ]
            )
        ) {
            throw new \InvalidArgumentException();
        }

        $this->type = $type;
    }

    public function isPrePaymentByCreditCard()
    {
        return ($this->type === self::PAYMENT_PREPAYMENT_BY_CREDIT_CARD);
    }

    public function isPrePaymentByBankWireTransfer()
    {
        return ($this->type === self::PAYMENT_PREPAYMENT_BY_BANK_WIRE_TRANSFER);
    }

    public function isTermPayment()
    {
        return ($this->type === self::PAYMENT_BY_TERM_PAYMENT);
    }

    public function getIzbergId(): int
    {
        return $this->izbergId;
    }

    public function setIzbergId(int $izbergId): void
    {
        $this->izbergId = $izbergId;
    }
}
