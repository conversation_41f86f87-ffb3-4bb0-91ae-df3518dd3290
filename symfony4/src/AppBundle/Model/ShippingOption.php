<?php


namespace AppBundle\Model;

use J<PERSON>\Serializer\Annotation\Type;

class ShippingOption
{
    #[Type('array<AppBundle\Model\CarrierOffer>')]
    private $carrierOffers;

    #[Type('float')]
    private $price;

    /**
     * @var int
     */
    #[Type('int')]
    private $vatRate;

    /**
     * @var float
     */
    #[Type('float')]
    private $vatPrice;

    public function getCarrierOffers(): ?array
    {
        return $this->carrierOffers;
    }

    public function setCarrierOffers(?array $carrierOffers): self
    {
        $this->carrierOffers = $carrierOffers;
        return $this;
    }

    public function getPrice(): ?float
    {
        return $this->price;
    }

    public function setPrice(?float $price): self
    {
        $this->price = $price;
        return $this;
    }

    public function getCartItemDeliveryDate($cartItemId): ?\DateTimeImmutable
    {
        /** @var CarrierOffer $carrierOffer */
        foreach ($this->getCarrierOffers() as $carrierOffer) {
            if ($carrierOffer->hasCartItem($cartItemId)) {
                return (new \DateTimeImmutable($carrierOffer->getDeliveryDate()));
            }
        }

        return null;
    }

    public function getFirstServiceName(): ?string
    {
        /** @var CarrierOffer $carrierOffer */
        foreach ($this->getCarrierOffers() as $carrierOffer) {
                return $carrierOffer->getServiceName();
        }

        return null;
    }

    public function getCartItemServiceName($cartItemId)
    {
        /** @var CarrierOffer $carrierOffer */
        foreach ($this->getCarrierOffers() as $carrierOffer) {
            if ($carrierOffer->hasCartItem($cartItemId)) {
                return $carrierOffer->getServiceName();
            }
        }

        return null;
    }

    public function getCartItemDeliveryDays($cartItemId)
    {
        /** @var CarrierOffer $carrierOffer */
        foreach ($this->getCarrierOffers() as $carrierOffer) {
            if ($carrierOffer->hasCartItem($cartItemId)) {
                // éviter le cas du 0 jour
                return $carrierOffer->getCartItem($cartItemId)->getDeliveryTime();
            }
        }

        return null;
    }

    public function getCartItemDeliveryDateTime($cartItemId)
    {
        /** @var CarrierOffer $carrierOffer */
        foreach ($this->getCarrierOffers() as $carrierOffer) {
            if ($carrierOffer->hasCartItem($cartItemId)) {
                return $carrierOffer->getDeliveryDateTime();
            }
        }

        return null;
    }

    public function getVatRate(): int
    {
        return $this->vatRate;
    }

    public function setVatRate(int $vatRate): self
    {
        $this->vatRate = $vatRate;
        return $this;
    }

    public function getVatPrice(): float
    {
        return $this->vatPrice;
    }

    public function setVatPrice(float $vatPrice): self
    {
        $this->vatPrice = $vatPrice;
        return $this;
    }
}
