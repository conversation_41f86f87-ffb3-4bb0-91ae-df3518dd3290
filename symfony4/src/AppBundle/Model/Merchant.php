<?php

namespace AppBundle\Model;
use AppBundle\Entity\Country;

class Merchant
{
    /**
     * @var int $id
     */
    private $id;

    /**
     * @var string logo
     */
    private $logo;

    /**
     * @var string $name
     */
    private $name;

    /**
     * @var string $shortDescription
     */
    private $shortDescription;

    /**
     * @var string $longDescription
     */
    private $longDescription;

    /**
     * @var string $tacLink
     */
    private $tacLink;

    /** @var Country $country  */
    private $country;

    /**
     * @var string $status
     */
    private $status;

    /**
     * @var string|null $mainContactFirstName
     */
    private $mainContactFirstName;

    /**
     * @var string|null $mainContactLastName
     */
    private $mainContactLastName;

    /**
     * @var string|null $mainContactEmail
     */
    private $mainContactEmail;

    /**
     * @var string | null
     */
    private $mainContactPhone;

    /** @var string */
    private $preferedLanguage;

    /**
     * @var bool
     */
    private $upelaActive;

    public function __construct()
    {
        $this->upelaActive = false;
    }

    /**
     * @return int
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getMainContactFirstName(): ?string
    {
      return $this->mainContactFirstName;
    }

    /**
     * @param string|null $mainContactFirstName
     */
    public function setMainContactFirstName(?string $mainContactFirstName): void
    {
      $this->mainContactFirstName = $mainContactFirstName;
    }

    /**
     * @return string|null
     */
    public function getMainContactLastName(): ?string
    {
      return $this->mainContactLastName;
    }

    /**
     * @param string|null $mainContactLastName
     */
    public function setMainContactLastName(?string $mainContactLastName): void
    {
      $this->mainContactLastName = $mainContactLastName;
    }

    public function getMainContactName(): ?string
    {
        return sprintf('%s %s', $this->mainContactFirstName, $this->mainContactLastName);
    }

    /**
     * @return string|null
     */
    public function getMainContactEmail(): ?string
    {
      return $this->mainContactEmail;
    }

    /**
     * @param string|null $mainContactEmail
     */
    public function setMainContactEmail(?string $mainContactEmail): void
    {
      $this->mainContactEmail = $mainContactEmail;
    }

    /**
     * @return string|null
     */
    public function getMainContactPhone(): ?string
    {
        return $this->mainContactPhone;
    }

    /**
     * @param string|null $mainContactPhone
     * @return $this
     */
    public function setMainContactPhone(?string $mainContactPhone): self
    {
        $this->mainContactPhone = $mainContactPhone;
        return $this;
    }

    /**
     * @return \AppBundle\Entity\Country
     */
    public function getCountry(): ?Country
    {
      return $this->country;
    }


  /***
   * @param \AppBundle\Entity\Country|null $country
   */
    public function setCountry(?Country $country): void
    {
      $this->country = $country;
    }



    /**
     * @return string
     */
    public function getLogo(): ?string
    {
        return $this->logo;
    }

    /**
     * @param string $logo
     */
    public function setLogo(?string $logo): void
    {
        $this->logo = $logo;
    }

    /**
     * @return string
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    /**
     * @param string $name
     */
    public function setName(?string $name): void
    {
        $this->name = $name;
    }

    /**
     * @return string
     */
    public function getShortDescription(): ?string
    {
        return $this->shortDescription;
    }

    /**
     * @param string $shortDescription
     */
    public function setShortDescription(?string $shortDescription): void
    {
        $this->shortDescription = $shortDescription;
    }

    /**
     * @return string
     */
    public function getLongDescription(): ?string
    {
        return $this->longDescription;
    }

    /**
     * @param string $longDescription
     */
    public function setLongDescription(?string $longDescription): void
    {
        $this->longDescription = $longDescription;
    }

    /**
     * @return string
     */
    public function getTacLink(): ?string
    {
        return $this->tacLink;
    }

    /**
     * @param string $tacLink
     */
    public function setTacLink(?string $tacLink): void
    {
        $this->tacLink = $tacLink;
    }

    /**
     * @return mixed
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @param mixed $status
     */
    public function setStatus($status)
    {
        $this->status = $status;
    }

    public function getPreferedLanguage(): ?string
    {
        return $this->preferedLanguage;
    }

    public function setPreferedLanguage(?string $preferedLanguage = null): self
    {
        $this->preferedLanguage = $preferedLanguage;

        return $this;
    }

    public function isUpelaActive(): bool
    {
        return $this->upelaActive;
    }

    public function setUpelaActive(bool $upelaActive): self
    {
        $this->upelaActive = $upelaActive;
        return $this;
    }
}
