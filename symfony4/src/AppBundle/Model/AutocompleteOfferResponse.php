<?php
/**
 * Created by PhpStorm.
 * User: LRO16285
 * Date: 30/04/2018
 * Time: 16:55
 */

namespace AppBundle\Model;
use JsonSerializable;

class AutocompleteOfferResponse implements \JsonSerializable
{

    private $mainOfferTitle;
    private $mainOfferCategory;
    private $mainOfferCategoryName;
    private $offerTitles;

  public function __construct (
    $mainOfferTitle,
    $mainOfferCategory,
    $mainOfferCategoryName,
    $offerTitles
    )
  {
    $this->setMainOfferCategory($mainOfferCategory);
    $this->setMainOfferTitle($mainOfferTitle);
    $this->setMainOfferCategoryName($mainOfferCategoryName);
    $this->setOfferTitles($offerTitles);
  }

  public function jsonSerialize(): array
    {
    return [
      'mainOfferTitle' =>$this->getMainOfferTitle(),
      "mainOfferCategory" => $this->getMainOfferCategory(),
      "mainOfferCategoryName" => $this->getMainOfferCategoryName(),
      "offerTitles" => $this->getOfferTitles(),
    ];
    }

  /**
   * @return mixed
   */
  public function getMainOfferTitle()
  {
    return $this->mainOfferTitle;
  }

  /**
   * @param mixed $mainOfferTitle
   */
  public function setMainOfferTitle($mainOfferTitle): void
  {
    $this->mainOfferTitle = $mainOfferTitle;
  }

  /**
   * @return mixed
   */
  public function getMainOfferCategoryName()
  {
    return $this->mainOfferCategoryName;
  }

  /**
   * @param mixed $mainOfferCategoryName
   */
  public function setMainOfferCategoryName($mainOfferCategoryName): void
  {
    $this->mainOfferCategoryName = $mainOfferCategoryName;
  }


  /**
   * @return mixed
   */
  public function getMainOfferCategory()
  {
    return $this->mainOfferCategory;
  }

  /**
   * @param mixed $mainOfferCategory
   */
  public function setMainOfferCategory($mainOfferCategory): void
  {
    $this->mainOfferCategory = $mainOfferCategory;
  }

  /**
   * @return mixed
   */
  public function getOfferTitles()
  {
    return $this->offerTitles;
  }

  /**
   * @param mixed $offerTitles
   */
  public function setOfferTitles($offerTitles): void
  {
    $this->offerTitles = $offerTitles;
  }


}
