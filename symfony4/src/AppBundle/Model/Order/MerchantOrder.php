<?php

namespace AppBundle\Model\Order;

class MerchantOrder
{
    /**
     * @var array
     */
    private $items;

    /**
     * @var int
     */
    private $id;

    /**
     * @var int
     */
    private $merchantId;

    /**
     * @var float
     */
    private $amountVatIncluded;

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getItems(): array
    {
        return $this->items;
    }

    public function setItems(array $items): self
    {
        $this->items = $items;

        return $this;
    }

    public function getAmountVatIncluded(): float
    {
        return $this->amountVatIncluded;
    }

    public function setAmountVatIncluded(float $amountVatIncluded): self
    {
        $this->amountVatIncluded = $amountVatIncluded;

        return $this;
    }

    public function getMerchantId(): int
    {
        return $this->merchantId;
    }

    public function setMerchantId(int $merchantId): self
    {
        $this->merchantId = $merchantId;

        return $this;
    }
}
