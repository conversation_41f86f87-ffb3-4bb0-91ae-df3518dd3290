<?php

namespace AppBundle\Model\Order;

use AppBundle\Util\CreatedByApiTrait;

class Order
{
    use CreatedByApiTrait;

    /** @var int */
    private $izbergId;

    /**
     * @var array
     */
    private $merchantOrders;

    /**
     * @var array
     */
    private $items;

    /**
     * @var string
     */
    private $currency;

    /** @var float */
    private $amountVatIncluded;

    /** @var string */
    private $idNumber;

    private ?string $accountingEmail = null;

    public function getIzbergId(): ?int
    {
        return $this->izbergId;
    }

    public function setIzbergId(?int $izbergId): self
    {
        $this->izbergId = $izbergId;

        return $this;
    }

    public function getItems(): array
    {
        return $this->items;
    }

    public function setItems(array $items): self
    {
        $this->items = $items;

        return $this;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function setCurrency(string $currency): self
    {
        $this->currency = $currency;

        return $this;
    }

    public function getMerchantOrders(): array
    {
        return $this->merchantOrders;
    }

    public function setMerchantOrders(array $merchantOrders): self
    {
        $this->merchantOrders = $merchantOrders;

        return $this;
    }

    public function getAmountVatIncluded(): float
    {
        return $this->amountVatIncluded;
    }

    public function setAmountVatIncluded(float $amountVatIncluded): self
    {
        $this->amountVatIncluded = $amountVatIncluded;

        return $this;
    }

    public function getIdNumber(): string
    {
        return $this->idNumber;
    }

    public function setIdNumber(string $idNumber): self
    {
        $this->idNumber = $idNumber;

        return $this;
    }

    public function getAccountingEmail(): ?string
    {
        return $this->accountingEmail;
    }

    public function setAccountingEmail(?string $accountingEmail): self
    {
        $this->accountingEmail = $accountingEmail;

        return $this;
    }
}
