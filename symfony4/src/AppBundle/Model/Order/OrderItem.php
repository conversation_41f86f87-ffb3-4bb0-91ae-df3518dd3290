<?php

namespace AppBundle\Model\Order;

use AppBundle\Model\Offer;

class OrderItem
{
    /**
     * @var int
     */
    private $id;

    /**
     * @var int
     */
    private $offerId;

    /**
     * @var string|null
     */
    private $offerExternalId;

    /**
     * @var int
     */
    private $quantity;

    /**
     * @var float
     */
    private $price;

    /**
     * @var Offer
     */
    private $offer;

    /**
     * @var int
     */
    private $taxRate;

    /**
     * @var string
     */
    private $currency;

    /**
     * @var int
     */
    private $deliveryTime;

    /**
     * @var int
     */
    private $trueDeliveryTime;

    /**
     * @var \DateTimeImmutable | null
     */
    private $expectedDeliveryDate;

    /**
     * @var \DateTimeImmutable | null
     */
    private $expectedShippingDate;

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): void
    {
        $this->id = $id;
    }

    public function getOfferId(): int
    {
        return $this->offerId;
    }

    public function setOfferId(int $offerId): self
    {
        $this->offerId = $offerId;

        return $this;
    }

    public function getQuantity(): int
    {
        return $this->quantity;
    }

    public function setQuantity(int $quantity): self
    {
        $this->quantity = $quantity;

        return $this;
    }

    public function getPrice(): float
    {
        return $this->price;
    }

    public function setPrice(float $price): self
    {
        $this->price = $price;

        return $this;
    }

    public function getOffer(): Offer
    {
        return $this->offer;
    }

    public function setOffer(Offer $offer): self
    {
        $this->offer = $offer;

        return $this;
    }

    public function getTaxRate(): int
    {
        return $this->taxRate;
    }

    public function setTaxRate(int $taxRate): self
    {
        $this->taxRate = $taxRate;

        return $this;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function setCurrency(string $currency): self
    {
        $this->currency = $currency;

        return $this;
    }

    public function getDeliveryTime(): ?int
    {
        return $this->deliveryTime;
    }

    public function setDeliveryTime(int $deliveryTime = null): void
    {
        $this->deliveryTime = $deliveryTime;
    }

    public function getTrueDeliveryTime(): ?int
    {
        return $this->trueDeliveryTime;
    }

    public function setTrueDeliveryTime(int $trueDeliveryTime): void
    {
        $this->trueDeliveryTime = $trueDeliveryTime;
    }

    public function getExpectedDeliveryDate(): ?\DateTimeImmutable
    {
        return $this->expectedDeliveryDate;
    }

    public function setExpectedDeliveryDate(?\DateTimeImmutable $expectedDeliveryDate): self
    {
        $this->expectedDeliveryDate = $expectedDeliveryDate;
        return $this;
    }

    public function getExpectedShippingDate(): ?\DateTimeImmutable
    {
        return $this->expectedShippingDate;
    }

    public function setExpectedShippingDate(?\DateTimeImmutable $expectedShippingDate): self
    {
        $this->expectedShippingDate = $expectedShippingDate;
        return $this;
    }

    public function getOfferExternalId(): ?string
    {
        return $this->offerExternalId;
    }

    public function setOfferExternalId(?string $offerExternalId): self
    {
        $this->offerExternalId = $offerExternalId;
        return $this;
    }
}
