<?php

namespace AppBundle\Model;

use AppBundle\Entity\Company;
use AppBundle\Entity\User;
use DateTimeImmutable;

class Offer
{
    private const STOCK_AVAILABILITY_ON_STOCK = 'on_stock';
    private const STOCk_AVAILABILITy_ON_DEMAND = 'on_demand';

    private ?int $batchSize = null;
    private ?array $offerPictures = null;
    private $izbergReference;
    private $sellerRef;
    private $buyerRef;
    private $manufacturerRef;
    private $manufacturerName;
    private $offerTitle;
    private $shortDescription;
    /**
     * @var bool
     */
    private bool $stockClearance = false;

    /**
     * @see CurrencyEnum
     */
    private string $currency;

    /**
     * @var array $prices
     * list of prices per currency. Example:
     * Price for seller currency: $prices[$currency]
     * Price in UDS: $prices[CurrencyEnum::USD]
     */
    private array $prices;

    /**
     * @var int $moq minimum quantity
     */
    private int $moq;

    /**
     * @var string $unitOfProduct one pack of 100 screws, or 100 meters of cable
     */
    private string $unitOfProduct = '';

    /**
     * @var string $incoterm product incoterm
     * @see IncotermEnum
     */
    private string $incoterm;

    /**
     * @var bool
     */
    private bool $incotermValid = true;

    /**
     * @var string $incotermCountry the country for the incoterm.
     */
    private ?string $incotermCountry = null;

    /**
     * @var int $quantity available quantity
     */
    private int $quantity;

    private Merchant $merchant;

    /**
     * threshold => price
     * @var array $thresholds
     */
    private array $thresholds;
    private $productId;
    private $status;
    private $deliveryTime;
    private $deliveryTimeDelayBeforeShipping;
    private ?string $dataSheet = null;
    private ?int $quantityPerSku = null;
    private ?string $skuUnit = null;
    private ?string $fcaAddress = null;
    private ?int $trueDeliveryTime = null;
    private ?string $stockAvailability = null;
    private bool $stockManagement = true;
    private ?string $taxGroup = null;
    private ?float $packageWidth = null;
    private ?float $packageHeight = null;
    private ?float $packageWeight = null;
    private ?float $packageLength = null;
    private ?float $packageWidthMoq = null;
    private ?float $packageHeightMoq = null;
    private ?float $packageWeightMoq = null;
    private ?float $packageLengthMoq = null;
    private bool $dangerousProduct = false;
    private ?string $fcaZipCode = null;
    private ?string $fcaZipTown = null;
    private ?string $transportType = null;
    private bool $stackability = false;
    private bool $isOfferRestricted = false;
    private array $restrictedProductBuyerIdent;
    private $customTariffCode;
    private ?User $user = null;
    private bool $noPrice = false;
    private ?string $frameContract = null;
    private ?DateTimeImmutable $priceValidityDate = null;
    private ?string $realStock = null;
    private ?string $warrantyPeriod = null;
    private ?bool $bafvAuthorized = null;
    private ?float $minimumOrderAmount = null;

    private string $madeIn = '';

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;
        return $this;
    }

    public function isOfferRestricted(): bool
    {
        return $this->isOfferRestricted;
    }

    public function setIsOfferRestricted(bool $isOfferRestricted): self
    {
        $this->isOfferRestricted = $isOfferRestricted;
        return $this;
    }

    public function getRestrictedProductBuyerIdent(): array
    {
        return $this->restrictedProductBuyerIdent;
    }

    public function setRestrictedProductBuyerIdent(array $restrictedProductBuyerIdent): self
    {
        $this->restrictedProductBuyerIdent = $restrictedProductBuyerIdent;
        return $this;
    }

    public function isAllowedForUser($user): bool {
        if ($user && $this->isOfferRestricted()) {
            $companyIdent = $user->getCompany()->getIdentification();
            return in_array($companyIdent, $this->getRestrictedProductBuyerIdent());
        }
        return !$this->isOfferRestricted();
    }

    public function setProductId($productId): self
    {
        $this->productId = $productId;
        return $this;
    }

    public function getProductId()
    {
        return $this->productId;
    }

    public function getOfferPictures(): ?array
    {
        return $this->offerPictures;
    }

    public function setOfferPictures(?array $offerPictures): self
    {
        $this->offerPictures = $offerPictures;
        return $this;
    }

    public function getIzbergReference(): ?string
    {
        return $this->izbergReference;
    }

    public function setIzbergReference(?string $izbergReference): self
    {
        $this->izbergReference = $izbergReference;
        return $this;
    }

    public function getSellerRef(): ?string
    {
        return $this->sellerRef;
    }

    public function setSellerRef(?string $sellerRef): self
    {
        $this->sellerRef = $sellerRef;
        return $this;
    }

    public function getBuyerRef(): ?string
    {
        return $this->buyerRef;
    }

    public function setBuyerRef(?string $buyerRef): self
    {
        $this->buyerRef = $buyerRef;
        return $this;
    }

    public function getOfferTitle(): ?string
    {
        return $this->offerTitle;
    }

    public function setOfferTitle(?string $offerTitle): self
    {
        $this->offerTitle = $offerTitle;
        return $this;
    }

    public function getShortDescription(): ?string
    {
        return $this->shortDescription;
    }

    public function setShortDescription(?string $shortDescription): self
    {
        $this->shortDescription = $shortDescription;
        return $this;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function setCurrency(?string $currency): self
    {
        $this->currency = $currency;
        return $this;
    }

    public function getPrices(): array
    {
        return $this->prices;
    }

    public function getPrice($currency)
    {
        return $this->prices[$currency];
    }

    public function setPrices(?array $prices): self
    {
        $this->prices = $prices;
        return $this;
    }

    public function getPriceForQuantity(int $quantity): float
    {
        $thresholds = $this->getThresholds();

        $price = $this->getPrice($this->getCurrency());

        foreach ($thresholds as $threshold => $thresholdPrice) {
            if ($quantity >= $threshold) {
                $price = $thresholdPrice;
            }
        }

        return $price;
    }

    public function setPrice($currency, $price): self
    {
        $this->prices[$currency] = $price;

        return $this;
    }

    public function hasNoPrice(): bool
    {
        return $this->noPrice;
    }

    public function setNoPrice(bool $noPrice): self
    {
        $this->noPrice = $noPrice;
        return $this;
    }

    public function getMoq(): ?int
    {
        return $this->moq;
    }

    public function setMoq(?int $moq): self
    {
        $this->moq = $moq;
        return $this;
    }

    public function getUnitOfProduct(): ?string
    {
        return $this->unitOfProduct;
    }

    public function setUnitOfProduct(?string $unitOfProduct): self
    {
        $this->unitOfProduct = $unitOfProduct;
        return $this;
    }

    public function getIncoterm(): ?string
    {
        return $this->incoterm;
    }

    public function setIncoterm(?string $incoterm): self
    {
        $this->incoterm = $incoterm;
        return $this;
    }

    public function getQuantity(): ?int
    {
        return $this->quantity;
    }

    public function setQuantity(?int $quantity): self
    {
        $this->quantity = $quantity;
        return $this;
    }

    public function isBafv(): bool
    {
        if (!$this->user) {
            return false;
        }

        /** @var Company $company */
        $company = $this->user->getCompany();

        if (!$company) {
            return false;
        }

        return $company->isVendor();
    }

    /**
     * isBafvAuthorized is set when a buyer is a BAFV and he is authorized to see the offer's merchant prices
     * it returns null if the property is not set
     * True if the property is set and the buyer is authorized to see the offer's merchant prices
     * False if the property is set and the buyer is not authorized to see the offer's merchant prices
     * @return bool|null
     */
    public function isBafvAuthorized(): ?bool
    {
        return $this->bafvAuthorized;
    }

    public function setBafvAuthorized(?bool $bafvAuthorized): self
    {
        $this->bafvAuthorized = $bafvAuthorized;
        return $this;
    }

    public function isLimited(): bool
    {
        // if offer is not active
        if(!$this->isActive()) {
            return true;
        }

        if($this->hasNoPrice()) {
            return true;
        }

        // if offer has a stock 0
        if(!$this->hasStock()) {
            return true;
        }

        // if incoterm DAP country different than buyer's country
        if(!$this->isIncotermValid()) {
            return true;
        }

        if (!$this->user) {
            return true;
        }

        /** @var Company $company */
        $company = $this->user->getCompany();

        if (!$company) {
            return true;
        }

        if ($company->hasValidSpecificPricesForOffer($this->sellerRef, $this->incoterm, $this->incotermCountry)) {
            return false;
        }
        return !$company->canSeePriceOfMerchant($this->merchant);
    }

    public function isAvailable(): bool
    {
        // if offer is not active
        if(!$this->isActive()) {
            return false;
        }

        // if offer has a stock 0
        if(!$this->hasStock()) {
            return false;
        }
        // if incoterm DAP country different than buyer's country
        return $this->isIncotermValid();
    }

    public function getIncotermCountry(): ?string
    {
        return $this->incotermCountry;
    }

    public function setIncotermCountry(?string $incotermCountry): self
    {
        $this->incotermCountry = $incotermCountry;
        return $this;
    }

    public function getManufacturerRef(): ?string
    {
        return $this->manufacturerRef;
    }

    public function setManufacturerRef(?string $manufacturerRef): self
    {
        $this->manufacturerRef = $manufacturerRef;
        return $this;
    }

    public function getMerchant(): ?Merchant
    {
        return $this->merchant;
    }

    public function setMerchant(?Merchant $merchant): self
    {
        $this->merchant = $merchant;
        return $this;
    }

    public function getManufacturerName(): ?string
    {
        return $this->manufacturerName;
    }

    public function setManufacturerName(?string $manufacturerName): self
    {
        $this->manufacturerName = $manufacturerName;
        return $this;
    }

    public function getThresholds(): array
    {
        return $this->thresholds;
    }

    public function setThresholds(array $thresholds): self
    {
        $this->thresholds = $thresholds;
        return $this;
    }

    public function addThreshold (int $threshold, float $price){
        $this->thresholds[$threshold] = $price;
    }

    public function removeThresholds()
    {
        $this->thresholds = [];
    }

    public function getBatchSize(): ?int
    {
        return $this->batchSize;
    }

    public function setBatchSize(?int $batchSize): self
    {
        $this->batchSize = $batchSize;
        return $this;
    }

    public function setDangerousProduct(bool $dangerousProduct): self
    {
        $this->dangerousProduct = $dangerousProduct;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }

    public function getDeliveryTime(): ?int
    {
        return $this->deliveryTime;
    }

    public function setDeliveryTime(?int $deliveryTime): self
    {
        $this->deliveryTime = $deliveryTime;
        return $this;
    }

    public function getDeliveryTimeDelayBeforeShipping(): ?int
    {
        return $this->deliveryTimeDelayBeforeShipping;
    }

    public function setDeliveryTimeDelayBeforeShipping(?int $deliveryTimeDelayBeforeShipping): self
    {
        $this->deliveryTimeDelayBeforeShipping = $deliveryTimeDelayBeforeShipping;
        return $this;
    }

    public function getDataSheet(): ?string
    {
        return $this->dataSheet;
    }

    public function setDataSheet(?string $dataSheet): self
    {
        $this->dataSheet = $dataSheet;
        return $this;
    }

    public function getQuantityPerSku(): ?int
    {
        return $this->quantityPerSku;
    }

    public function setQuantityPerSku(?int $quantityPerSku): self
    {
        $this->quantityPerSku = $quantityPerSku;
        return $this;
    }

    public function getSkuUnit(): ?string
    {
        return $this->skuUnit;
    }

    public function setSkuUnit(?string $skuUnit): self
    {
        $this->skuUnit = $skuUnit;
        return $this;
    }

    public function getFcaAddress()
    {
        return $this->fcaAddress;
    }

    public function setFcaAddress($fcaAddress): self
    {
        $this->fcaAddress = $fcaAddress;
        return $this;
    }

    public function getTrueDeliveryTime()
    {
        return $this->trueDeliveryTime;
    }

    public function setTrueDeliveryTime($trueDeliveryTime): self
    {
        $this->trueDeliveryTime = $trueDeliveryTime;
        return $this;
    }

    public function getStockAvailability(): ?string
    {
        return $this->stockAvailability;
    }

    public function setStockAvailability($stockAvailability): self
    {
        $this->stockAvailability = null;

        $stockAvailability = trim(strtolower(str_replace(' ', '_', $stockAvailability)));
        if (in_array($stockAvailability, [self::STOCk_AVAILABILITy_ON_DEMAND, self::STOCK_AVAILABILITY_ON_STOCK])) {
            $this->stockAvailability = trim($stockAvailability);
        }

        return $this;
    }

    public function getTaxGroup(): ?string
    {
        return $this->taxGroup;
    }

    public function setTaxGroup($taxGroup): self
    {
        $this->taxGroup = $taxGroup;
        return $this;
    }

    public function getPackageWidth(): ?float
    {
        return $this->packageWidth;
    }

    public function setPackageWidth(?float $packageWidth): self
    {
        $this->packageWidth = $packageWidth;
        return $this;
    }

    public function getPackageHeight(): ?float
    {
        return $this->packageHeight;
    }

    public function setPackageHeight(?float $packageHeight): self
    {
        $this->packageHeight = $packageHeight;
        return $this;
    }

    public function getPackageWeight(): ?float
    {
        return $this->packageWeight;
    }

    public function setPackageWeight(?float $packageWeight): self
    {
        $this->packageWeight = $packageWeight;
        return $this;
    }

    public function getPackageLength(): ?float
    {
        return $this->packageLength;
    }

    public function setPackageLength(?float $packageLength): self
    {
        $this->packageLength = $packageLength;
        return $this;
    }

    public function getPackageWidthMoq(): ?float
    {
        return $this->packageWidthMoq;
    }

    public function setPackageWidthMoq(?float $packageWidthMoq): self
    {
        $this->packageWidthMoq = $packageWidthMoq;
        return $this;
    }

    public function getPackageHeightMoq(): ?float
    {
        return $this->packageHeightMoq;
    }

    public function setPackageHeightMoq(?float $packageHeightMoq): self
    {
        $this->packageHeightMoq = $packageHeightMoq;
        return $this;
    }

    public function getPackageWeightMoq(): ?float
    {
        return $this->packageWeightMoq;
    }

    public function setPackageWeightMoq(?float $packageWeightMoq): self
    {
        $this->packageWeightMoq = $packageWeightMoq;
        return $this;
    }

    public function getPackageLengthMoq(): ?float
    {
        return $this->packageLengthMoq;
    }

    public function setPackageLengthMoq(?float $packageLengthMoq): self
    {
        $this->packageLengthMoq = $packageLengthMoq;
        return $this;
    }

    public function isDangerousProduct(): bool
    {
        return $this->dangerousProduct;
    }

    public function setIsDangerousProduct(?bool $dangerousProduct): self
    {
        $this->dangerousProduct = (bool)$dangerousProduct;
        return $this;
    }

    public function getFcaZipCode(): ?string
    {
        return $this->fcaZipCode;
    }

    public function setFcaZipCode(?string $fcaZipCode): self
    {
        $this->fcaZipCode = $fcaZipCode;
        return $this;
    }

    public function getFcaZipTown(): ?string
    {
        return $this->fcaZipTown;
    }

    public function setFcaZipTown(?string $fcaZipTown): self
    {
        $this->fcaZipTown = $fcaZipTown;
        return $this;
    }

    public function getTransportType(): ?string
    {
        return $this->transportType;
    }

    public function setTransportType(?string $transportType): self
    {
        $this->transportType = $transportType;
        return $this;
    }

    public function isStackableProduct(): bool
    {
        return $this->stackability;
    }

    public function setStackability(bool $stackability): self
    {
        $this->stackability = $stackability;
        return $this;
    }

    public function getCustomTariffCode()
    {
        return $this->customTariffCode;
    }

    public function setCustomTariffCode($customTariffCode): self
    {
        $this->customTariffCode = $customTariffCode;
        return $this;
    }

    public function isIncotermValid(): bool
    {
        return $this->incotermValid;
    }

    public function setIncotermValid(bool $incotermValid): self
    {
        $this->incotermValid = $incotermValid;
        return $this;
    }

    public function isActive(): bool
    {
        return ($this->status === 'active');
    }

    public function isBusinessEverywhere(): bool
    {
        if (!$this->user) {
            return false;
        }

        /** @var Company $company */
        $company = $this->user->getCompany();

        if (!$company) {
            return false;
        }

        return $company->canDoBusiness($this->merchant);
    }

    public function hasStock(): bool
    {
        return $this->quantity > 0;
    }

    public function isShippable(): bool
    {
        if ($this->currency != 'EUR') {
            return false;
        }

        // test incoterm produit FCA
        if ($this->incoterm != 'FCA') {
            return false;
        }

        // dangerous
        if ($this->isDangerousProduct()) {
            return false;
        }

        // upela active
        if (!$this->merchant->isUpelaActive()) {
            return false;
        }

        // transport type
        if (!$this->getTransportType()) {
            return false;
        }

        // complete fca address
        if (empty($this->getFcaAddress()) ||
            empty($this->getFcaZipCode()) ||
            empty($this->getFcaZipTown())) {
            return false;
        }

        // packages
        if (!empty($this->getPackageWeight()) && // packageWeight, Length, Height, Width are for batch size dimension
            !empty($this->getPackageLength()) &&
            !empty($this->getPackageHeight()) &&
            !empty($this->getPackageWidth()) &&
            !empty($this->getBatchSize())) {
            return true;
        } elseif (
            !empty($this->getPackageWeightMoq()) && // PackageWeightMoq, LengthMoq, HeightMoq, WidthMoq are for moq dimension
            !empty($this->getPackageLengthMoq()) &&
            !empty($this->getPackageHeightMoq()) &&
            !empty($this->getPackageWidthMoq()) &&
            !empty($this->getMoq())
        ) {
            return true;
        }

        return false;
    }

    public function isStockManagement(): bool
    {
        return $this->stockManagement;
    }

    public function setStockManagement(bool $stockManagement): self
    {
        $this->stockManagement = $stockManagement;
        return $this;
    }

    public function setFrameContract(?string $frameContract): self
    {
        $this->frameContract = $frameContract;
        return $this;
    }

    public function getFrameContract(): ?string
    {
        return $this->frameContract;
    }

    public function getPriceValidityDate(): ?DateTimeImmutable
    {
        return $this->priceValidityDate;
    }

    /**
     * @param DateTimeImmutable|null $priceValidityDate
     * @return Offer
     */
    public function setPriceValidityDate(?DateTimeImmutable $priceValidityDate): self
    {
        $this->priceValidityDate = $priceValidityDate;
        return $this;
    }

    public function getRealStock(): ?string
    {
        return $this->realStock;
    }

    /**
     * @param string|null $realStock
     * @return Offer
     */
    public function setRealStock(?string $realStock): self
    {
        $this->realStock = $realStock;

        return $this;
    }

    public function getWarrantyPeriod(): ?string
    {
        return $this->warrantyPeriod;
    }

    /**
     * @param string|null $warrantyPeriod
     * @return Offer
     */
    public function setWarrantyPeriod(?string $warrantyPeriod): self
    {
        $this->warrantyPeriod = $warrantyPeriod;
        return $this;
    }

    /**
     * @return bool
     */
    public function isStockClearance(): bool
    {
        return $this->stockClearance;
    }

    /**
     * @param bool $stockClearance
     */
    public function setStockClearance(bool $stockClearance): self
    {
        $this->stockClearance = $stockClearance;
        return $this;
    }

    public function getMinimumOrderAmount(): ?float
    {
        return $this->minimumOrderAmount;
    }

    public function setMinimumOrderAmount(?float $minimumOrderAmount): self
    {
        $this->minimumOrderAmount = $minimumOrderAmount;
        return $this;
    }

    public function getMadeIn(): string
    {
        return $this->madeIn;
    }

    public function setMadeIn(string $madeIn): self
    {
        $this->madeIn = $madeIn;
        return $this;
    }
}
