<?php

namespace AppBundle\Model\Cart;

use AppBundle\Model\Offer;
use DateTimeImmutable;
use <PERSON><PERSON>\Serializer\Annotation\Type;
use <PERSON><PERSON>\Serializer\Annotation\Exclude;

class CartItem
{
    #[Type('int')]
    private int $id;

    #[Type('string')]
    private string $name;

    #[Type('float')]
    private float $unitPrice;

    #[Type('float')]
    private float $unitVat;

    #[Type('float')]
    private float $unitPriceVatIncluded;

    #[Type('int')]
    private int $quantity;

    #[Type('int')]
    private int $moq;

    #[Type('int')]
    private int $batchSize;

    #[Type('int')]
    private int $stock;

    #[Type('int')]
    private int $offerId = 0;

    #[Type('string')]
    private string $imageUrl;

    #[Type('string')]
    private string $taxRate;

    #[Type('float')]
    private float $totalItem;

    #[Type('string')]
    private ?string $incoterm = null;

    #[Type('string')]
    private ?string $countryOfDelivery = null;

    #[Type('string')]
    private ?string $sellerRef = null;

    #[Type('string')]
    private ?string $buyerRef = null;

    #[Type('string')]
    private ?string $fcaAddress = null;

    #[Type('int')]
    private int $merchantId;

    #[Type('int')]
    private ?int $deliveryTime = null;

    #[Type('int')]
    private ?int $deliveryTimeDelayBeforeShipping = null;

    #[Type('int')]
    private ?int $trueDeliveryTime = null;

    #[Type('dateTimeImmutable')]
    private DateTimeImmutable $deliveryDate;

    #[Type('dateTimeImmutable')]
    private DateTimeImmutable $deliveryDateDelayBeforeShipping;


    #[Type('bool')]
    private bool $lostItem;

    #[Exclude]
    private ?Offer $offer = null;

    #[Type('string')]
    private ?string $currency = null;

    #[Type('bool')]
    private bool $dangerousProduct;

    #[Type('bool')]
    private bool $toBeShipped;

    #[Type('string')]
    private ?string $buyerInternalRef = null;
    private ?string $cartItemComment = null;
    private ?array $cartItemSplitDelivery = [];
    #[Type('string')]
    private ?string $purchaseRequestId = null;

    #[Exclude]
    private array $extraInfo;

    #[Type('bool')]
    private bool $valid;

    public function __construct()
    {
        $this->lostItem = false;
        $this->toBeShipped = false;
        $this->valid = true;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function isValid(): bool
    {
        return $this->valid;
    }

    public function setValid(bool $valid): self
    {
        $this->valid = $valid;
        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;
        return $this;
    }

    public function getUnitPrice(): float
    {
        return $this->unitPrice;
    }

    public function setUnitPrice(float $unitPrice): self
    {
        $this->unitPrice = $unitPrice;
        return $this;
    }

    public function getUnitVat(): float
    {
        return $this->unitVat;
    }

    public function setUnitVat(float $unitVat): self
    {
        $this->unitVat = $unitVat;
        return $this;
    }

    public function getUnitPriceVatIncluded(): float
    {
        return $this->unitPriceVatIncluded;
    }

    public function setUnitPriceVatIncluded(float $unitPriceVatIncluded): self
    {
        $this->unitPriceVatIncluded = $unitPriceVatIncluded;
        return $this;
    }

    public function getQuantity(): int
    {
        return $this->quantity;
    }

    public function setQuantity(int $quantity): self
    {
        $this->quantity = $quantity;
        return $this;
    }

    public function getMoq(): int
    {
        return $this->moq;
    }

    public function setMoq(int $moq): self
    {
        $this->moq = $moq;
        return $this;
    }

    public function getBatchSize(): int
    {
        return $this->batchSize;
    }

    public function setBatchSize(int $batchSize): self
    {
        $this->batchSize = $batchSize;
        return $this;
    }

    public function getStock(): int
    {
        return $this->stock;
    }

    public function setStock(int $stock): self
    {
        $this->stock = $stock;
        return $this;
    }

    public function getOfferId(): int
    {
        return $this->offerId;
    }

    public function setOfferId(int $offerId): self
    {
        $this->offerId = $offerId;
        return $this;
    }

    public function getImageUrl(): string
    {
        return $this->imageUrl;
    }

    public function setImageUrl(string $imageUrl): self
    {
        $this->imageUrl = $imageUrl;
        return $this;
    }

    public function getTaxRate(): string
    {
        return $this->taxRate;
    }

    public function setTaxRate(string $taxRate): self
    {
        $this->taxRate = $taxRate;
        return $this;
    }

    public function getTotalItem(): float
    {
        return $this->totalItem;
    }

    public function setTotalItem(float $totalItem): self
    {
        $this->totalItem = $totalItem;
        return $this;
    }

    public function getIncoterm(): ?string
    {
        return $this->incoterm;
    }

    public function setIncoterm(?string $incoterm): self
    {
        $this->incoterm = $incoterm;
        return $this;
    }

    public function getCountryOfDelivery(): ?string
    {
        return $this->countryOfDelivery;
    }

    public function setCountryOfDelivery(?string $countryOfDelivery): self
    {
        $this->countryOfDelivery = $countryOfDelivery;
        return $this;
    }

    public function getSellerRef(): ?string
    {
        return $this->sellerRef;
    }

    public function setSellerRef(?string $sellerRef): self
    {
        $this->sellerRef = $sellerRef;
        return $this;
    }

    public function getBuyerRef(): ?string
    {
        return $this->buyerRef;
    }

    public function setBuyerRef(?string $buyerRef): self
    {
        $this->buyerRef = $buyerRef;
        return $this;
    }

    public function getFcaAddress(): ?string
    {
        return $this->fcaAddress;
    }

    public function setFcaAddress(?string $fcaAddress): self
    {
        $this->fcaAddress = $fcaAddress;
        return $this;
    }

    public function getMerchantId(): int
    {
        return $this->merchantId;
    }

    public function setMerchantId(int $merchantId): self
    {
        $this->merchantId = $merchantId;
        return $this;
    }

    public function getDeliveryTime(): ?int
    {
        return $this->deliveryTime;
    }

    public function setDeliveryTime(?int $deliveryTime): self
    {
        $this->deliveryTime = $deliveryTime;
        return $this;
    }

    public function getDeliveryTimeDelayBeforeShipping(): ?int
    {
        return $this->deliveryTimeDelayBeforeShipping;
    }

    public function setDeliveryTimeDelayBeforeShipping(?int $deliveryTimeDelayBeforeShipping): self
    {
        $this->deliveryTimeDelayBeforeShipping = $deliveryTimeDelayBeforeShipping;
        return $this;
    }

    public function getTrueDeliveryTime(): ?int
    {
        return $this->trueDeliveryTime;
    }

    public function setTrueDeliveryTime(?int $trueDeliveryTime): self
    {
        $this->trueDeliveryTime = $trueDeliveryTime;
        return $this;
    }

    public function getDeliveryDate(): DateTimeImmutable
    {
        return $this->deliveryDate;
    }

    public function setDeliveryDate(DateTimeImmutable $deliveryDate): self
    {
        $this->deliveryDate = $deliveryDate;
        return $this;
    }

    public function getDeliveryDateDelayBeforeShipping(): DateTimeImmutable
    {
        return $this->deliveryDateDelayBeforeShipping;
    }

    public function setDeliveryDateDelayBeforeShipping(DateTimeImmutable $deliveryDateDelayBeforeShipping): self
    {
        $this->deliveryDateDelayBeforeShipping = $deliveryDateDelayBeforeShipping;
        return $this;
    }

    public function lost()
    {
        $this->lostItem = true;
    }

    public function isLost(): bool
    {
        return $this->lostItem;
    }

    public function isLostItem(): bool
    {
        return $this->lostItem;
    }

    public function setLostItem(bool $lostItem): self
    {
        $this->lostItem = $lostItem;
        return $this;
    }


    public function getOffer(): ?Offer
    {
        return $this->offer;
    }


    public function setOffer(?Offer $offer): self
    {
        $this->offer = $offer;
        return $this;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function setCurrency(?string $currency): self
    {
        $this->currency = $currency;
        return $this;
    }

    public function isFca(): bool
    {
        return ($this->getIncoterm() === "FCA");
    }

    public function isDangerousProduct(): ?bool
    {
        return $this->dangerousProduct;
    }

    public function getBuyerInternalRef(): ?string
    {
        return $this->buyerInternalRef;
    }

    public function setBuyerInternalRef(?string $buyerInternalRef): self
    {
        $this->buyerInternalRef = $buyerInternalRef;
        return $this;
    }

    public function getCartItemComment(): ?string
    {
        return $this->cartItemComment;
    }

    public function setCartItemComment(?string $cartItemComment): self
    {
        $this->cartItemComment = $cartItemComment;
        return $this;
    }

    public function getCartItemSplitDelivery(): ?array
    {
        return $this->cartItemSplitDelivery;
    }

    public function setCartItemSplitDelivery(?array $cartItemSplitDelivery): self
    {
        $this->cartItemSplitDelivery = $cartItemSplitDelivery;
        return $this;
    }

    public function setIsDangerousProduct(?bool $dangerousProduct): self
    {
        $this->dangerousProduct = (bool)$dangerousProduct;
        return $this;
    }

    public function isToBeShipped(): bool
    {
        return $this->toBeShipped;
    }

    public function setToBeShipped(bool $toBeShipped): self
    {
        $this->toBeShipped = $toBeShipped;
        return $this;
    }

    public function getPurchaseRequestId(): ?string
    {
        return $this->purchaseRequestId;
    }

    public function setPurchaseRequestId(?string $purchaseRequestId): self
    {
        $this->purchaseRequestId = $purchaseRequestId;
        return $this;
    }

    public function getExtraInfo(): array
    {
        return $this->extraInfo;
    }

    public function setExtraInfo(array $extraInfo): self
    {
        $this->extraInfo = $extraInfo;
        return $this;
    }
}
