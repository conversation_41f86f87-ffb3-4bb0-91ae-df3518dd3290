<?php

namespace AppBundle\Model\Cart;

class CartNotification
{
    public const TYPE_PRODUCT_DELETED = 'product_deleted';
    public const TYPE_PRODUCT_DEACTIVATED = 'product_deactivated';
    public const TYPE_OFFER_DELETED = 'offer_deleted';
    public const TYPE_OFFER_DEACTIVATED = 'offer_deactivated';
    public const TYPE_OFFER_PRICE_CHANGED = 'offer_price_changed';
    public const TYPE_OFFER_NO_MORE_STOCK = 'offer_no_more_stock';
    public const TYPE_MERCHANT_PAUSED = 'merchant_paused';
    public const TYPE_MERCHANT_STOPPED = 'merchant_stopped';
    public const TYPE_MERCHANT_DELETED = 'merchant_deleted';

    private ?int $id = null;
    private string $type;
    private int $cartItemId;
    private string $message;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function setMessage(string $message): self
    {
        $this->message = $message;

        return $this;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getCartItemId(): int
    {
        return $this->cartItemId;
    }

    public function setCartItemId(int $cartItemId): self
    {
        $this->cartItemId = $cartItemId;

        return $this;
    }

    public function cartItemNotAvailable(): bool
    {
        $notAvailableTypes = [
            self::TYPE_PRODUCT_DELETED,
            self::TYPE_PRODUCT_DEACTIVATED,
            self::TYPE_OFFER_DELETED,
            self::TYPE_OFFER_DEACTIVATED,
            self::TYPE_OFFER_NO_MORE_STOCK,
            self::TYPE_MERCHANT_PAUSED,
            self::TYPE_MERCHANT_STOPPED,
            self::TYPE_MERCHANT_DELETED,
        ];

        return (in_array($this->type, $notAvailableTypes));
    }
}
