<?php

namespace AppBundle\Model\Cart;

use AppBundle\Entity\Address;
use AppBundle\Entity\CreationSource\CreationSourceTrait;
use AppBundle\Entity\ShippingPoint;
use AppBundle\Entity\Site;
use AppBundle\Entity\User;
use AppBundle\Model\Order\Order;
use AppBundle\Util\AsAccountingEmail;

class Cart
{
    use CreationSourceTrait;
    use AsAccountingEmail;

    public const STATUS_CREATE = 'CREATE';
    public const STATUS_ASSIGN = 'ASSIGN';
    public const STATUS_ORDER = 'ORDER';

    private int $id;
    private int $itemsCount;

    /**
     * @var array<array-key, CartItem> $items
     */
    private array $items;
    private array $merchants;
    private ?string $status = null;
    private string $currency;
    private float $subTotalWithoutVat;
    private array $subTotalVat;
    private float $vat;
    private float $total;
    private ?float $totalShipping = null;
    private ?Address $address = null;
    private ?Address $billingAddress = null;
    private ?ShippingPoint $shippingPoint = null;
    private ?Site $assignedSite = null;
    private ?string $paymentMode = null;
    private ?string $paymentType = null;
    private ?string $paymentMethod = null;
    private ?string $paymentTerm = null;
    private ?string $paymentTermIzbergId = null;
    private ?string $validationNumber = null ;
    private ?Order $order = null ;
    private array $notifications;
    private ?User $creator = null ;
    private ?User $assignedUser = null;
    private array $shippingOption;
    private ?string $buyerOrderId = null;
    private array $documentsRequest = [];
    private bool $valid;

    public function __construct()
    {
        $this->shippingOption = [];
        $this->documentsRequest = [];
        $this->notifications = [];
        $this->subTotalVat = [];
        $this->merchants = [];
        $this->items = [];
        $this->valid = true;
        $this->vat = 0.0;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function isValid(): bool
    {
        return $this->valid;
    }

    public function setValid(bool $valid): self
    {
        $this->valid = $valid;
        return $this;
    }

    public function getItemsCount(): int
    {
        return $this->itemsCount;
    }

    public function setItemsCount(int $itemsCount): self
    {
        $this->itemsCount = $itemsCount;
        return $this;
    }

    /**
     * @return array<array-key, CartItem>
     */
    public function getItems(): array
    {
        return $this->items;
    }

    /**
     * @param array<array-key, CartItem> $items
     */
    public function setItems(array $items): self
    {
        $this->items = $items;
        return $this;
    }

    public function addItem(CartItem $itemToAdd): self
    {
        $this->items[] = $itemToAdd;
        return $this;
    }

    public function removeItem(CartItem $itemToRemove): self
    {
        /**
         * @var  int $index
         * @var  CartItem $item
         */
        foreach($this->items as $index => $item) {
            if ($item->getId() === $itemToRemove->getId()) {
                unset($this->items[$index]);
            }
        }

        return $this;
    }

    public function getMerchants(): array
    {
        return $this->merchants;
    }

    public function setMerchants(array $merchants): self
    {
        $this->merchants = $merchants;
        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): self
    {
        $this->status = $status;
        return $this;
    }

    public function isAssigned(): bool
    {
        return ($this->status === self::STATUS_ASSIGN);
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function setCurrency(string $currency): self
    {
        $this->currency = $currency;
        return $this;
    }

    public function getSubTotalWithoutVat(): float
    {
        return $this->subTotalWithoutVat;
    }

    public function setSubTotalWithoutVat(float $subTotalWithoutVat): self
    {
        $this->subTotalWithoutVat = $subTotalWithoutVat;
        return $this;
    }

    public function addSubTotalWithoutVat(?float $total): self
    {
        $this->subTotalWithoutVat += $total;
        return $this;
    }

    public function getSubTotalVat(): array
    {
        return $this->subTotalVat;
    }

    public function setSubTotalVat(array $subTotalVat): self
    {
        $this->subTotalVat = $subTotalVat;
        return $this;
    }

    public function addSubTotalVat(string $taxRate, float $vat): self
    {
        if (!array_key_exists($taxRate, $this->subTotalVat)) {
            $this->subTotalVat[$taxRate] = 0;
        }

        $this->subTotalVat[$taxRate] += $vat;

        return $this;
    }

    public function getVat(): float
    {
        return $this->vat;
    }

    public function setVat(float $vat): self
    {
        $this->vat = $vat;
        return $this;
    }

    public function addVat(float $vat): self
    {
        $this->vat += $vat;
        return $this;
    }

    public function getTotal(): float
    {
        return $this->total;
    }

    public function setTotal(float $total): self
    {
        $this->total = $total;
        return $this;
    }

    public function addTotal(?float $total): self
    {
        $this->total += $total;
        return $this;
    }

    public function getTotalShipping(): ?float
    {
        return $this->totalShipping;
    }

    public function setTotalShipping(?float $totalShipping): self
    {
        $this->totalShipping = $totalShipping;
        return $this;
    }

    public function addTotalShipping(?float $totalShipping): self
    {
        $this->totalShipping += $totalShipping;
        return $this;
    }

    public function getAddress(): ?Address
    {
        return $this->address;
    }

    public function setAddress(Address $address): self
    {
        $this->address = $address;
        return $this;
    }

    public function getBillingAddress(): ?Address
    {
        return $this->billingAddress;
    }

    public function setBillingAddress(?Address $billingAddress): self
    {
        $this->billingAddress = $billingAddress;
        return $this;
    }

    public function getShippingPoint(): ?ShippingPoint
    {
        return $this->shippingPoint;
    }

    public function setShippingPoint(?ShippingPoint $shippingPoint): self
    {
        $this->shippingPoint = $shippingPoint;
        return $this;
    }

    public function getAssignedSite(): ?Site
    {
        return $this->assignedSite;
    }

    public function setAssignedSite(?Site $assignedSite): self
    {
        $this->assignedSite = $assignedSite;
        return $this;
    }

    public function fetchLostCartItems(): array
    {
        return array_filter(
            $this->getItems(),
            function(CartItem $cartItem) {
                return $cartItem->isLost();
            }
        );
    }

    public function hasOfferId(int $offerId): bool
    {
        return (
            count(
                array_filter(
                    $this->getItems(),
                    function(CartItem $cartItem) use ($offerId) {
                        return ($cartItem->getOfferId() == $offerId);
                    }
                )
            ) >= 1
        );
    }

    public function getPaymentMode(): ?string
    {
        return $this->paymentMode;
    }

    public function setPaymentMode(?string $paymentMode): self
    {
        $this->paymentMode = $paymentMode;
        return $this;
    }

    public function getPaymentType(): ?string
    {
        return $this->paymentType;
    }

    public function setPaymentType(?string $paymentType): self
    {
        $this->paymentType = $paymentType;
        return $this;
    }

    public function getPaymentMethod(): ?string
    {
        return $this->paymentMethod;
    }

    public function setPaymentMethod(?string $paymentMethod): self
    {
        $this->paymentMethod = $paymentMethod;
        return $this;
    }

    public function getPaymentTerm(): ?string
    {
        return $this->paymentTerm;
    }

    public function setPaymentTerm(?string $paymentTerm): self
    {
        $this->paymentTerm = $paymentTerm;
        return $this;
    }

    public function getPaymentTermIzbergId(): ?string
    {
        return $this->paymentTermIzbergId;
    }

    public function setPaymentTermIzbergId(?string $paymentTermIzbergId): self
    {
        $this->paymentTermIzbergId = $paymentTermIzbergId;
        return $this;
    }

    public function getValidationNumber(): ?string
    {
        return $this->validationNumber;
    }

    public function setValidationNumber(?string $validationNumber)
    {
        $this->validationNumber = $validationNumber;
    }

    public function getBuyerOrderId(): ?string
    {
        return $this->buyerOrderId;
    }

    public function setBuyerOrderId(?string $buyerOrderId): self
    {
        $this->buyerOrderId = $buyerOrderId;
        return $this;
    }

    public function getOrder(): ?Order
    {
        return $this->order;
    }

    public function setOrder(?Order $order): self
    {
        $this->order = $order;
        return $this;
    }

    public function getTotalVatIncluded(): float
    {
        return $this->total + $this->vat;
    }


    public function getTotalWithoutVatWithShipping(): float
    {
        return $this->subTotalWithoutVat + $this->totalShipping;
    }

    public function hasOrder(): bool
    {
        return ($this->getOrder() && $this->getOrder()->getIzbergId());
    }

    public function addNotification(CartNotification $cartNotification): self
    {
        $this->notifications[] = $cartNotification;
        return $this;
    }

    public function hasNotifications(): bool
    {
        if(!$this->notifications) {
            return false;
        }

        return (count($this->notifications) >= 1);
    }

    public function getNotifications(): ?array
    {
        return $this->notifications;
    }

    public function getCreator(): ?User
    {
        return $this->creator;
    }

    public function setCreator(?User $creator): self
    {
        $this->creator = $creator;
        return $this;
    }

    public function isCartCreator(User $user): bool
    {
        if ($this->getCreator()) {
            return ($this->getCreator()->getId() === $user->getId());
        }

        return false;
    }

    public function getAssignedUser(): ?User
    {
        return $this->assignedUser;
    }

    public function setAssignedUser(?User $assignedUser): void
    {
        $this->assignedUser = $assignedUser;
    }

    public function isCartAssignedTo(User $user): bool
    {
        if($this->getAssignedUser()) {
            return ($this->getAssignedUser()->getId() === $user->getId());
        }

        return false;
    }

    public function getShippingOption(): array
    {
        return $this->shippingOption;
    }

    public function setShippingOption(array $shippingOption): self
    {
        $this->shippingOption = $shippingOption;
        return $this;
    }

    public function getDocumentsRequests(): array
    {
        return $this->documentsRequest;
    }

    public function setDocumentsRequests(array $documentsRequest): Cart
    {
        $this->documentsRequest = $documentsRequest;
        return $this;
    }

    public function isEmpty(): bool
    {
        return \count($this->items) === 0;
    }

    public function hasShippableItems(): bool
    {
        return boolval(
            array_filter(
                $this->getMerchants(),
                function(CartMerchant $cartMerchant) {
                    $ItemsShippable = array_filter(
                        $this->getItems(),
                        function(CartItem $cartItem) use ($cartMerchant) {
                            if ($cartItem->getMerchantId() === $cartMerchant->getId()) {
                                // it's possible that cart item has no offer like the cart item created for the shipping
                                $offer = $cartItem->getOffer();
                                if ($offer) {
                                    return $offer->isShippable();
                                }
                            }
                            return false;
                        });

                    return !empty($ItemsShippable);
                }
            )
        );
    }
}
