<?php

namespace AppBundle\Model\Cart;

use AppBundle\Entity\Country;
use AppBundle\Model\CustomsInfo;

class CartMerchant
{
    /**
     * @var int
     */
    private $id;

    /**
     * @var string
     */
    private $name;

    /**
     * @var string
     */
    private $description;

    /**
     * @var string
     */
    private $longDescription;

    /**
     * @var string
     */
    private $logoImage;

    /**
     * @var Country
     */
    private $country;

    /**
     * @var string
     */
    private $conditions;

    /**
     * @var array
     */
    private $subTotalVat;

    /**
     * @var float
     */
    private $subTotalWithoutVat;

    /**
     * @var float
     */
    private $vat;

    /**
     * @var float
     */
    private $total;

    /**
     * @var array
     */
    private $items;

    /**
     * @var CustomsInfo
     */
    private $vatInformation;

    /**
     * @var float
     */
    private $minimumOrderAmount;

    /**
     * @var bool
     */
    private $upelaActive;

    /**
     * @var bool
     */
    private $shippable;

    /**
     * @var float|null
     */
    private $shippingTotal;

    /**
     * @var float|null
     */
    private $shippingTotalVatIncluded;

    /**
     * @var string|null
     */
    private $shippingName;

    public function __construct()
    {
        $this->upelaActive = false;
        $this->shippable = false;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): void
    {
        $this->id = $id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name = null): void
    {
        $this->name = $name;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(string $description = null): void
    {
        $this->description = $description;
    }

    public function getLongDescription(): ?string
    {
        return $this->longDescription;
    }

    public function setLongDescription(string $longDescription = null): void
    {
        $this->longDescription = $longDescription;
    }

    public function getLogoImage(): ?string
    {
        return $this->logoImage;
    }

    public function setLogoImage(string $logoImage = null): void
    {
        $this->logoImage = $logoImage;
    }

    public function getCountry(): Country
    {
        return $this->country;
    }

    public function setCountry(Country $country): void
    {
        $this->country = $country;
    }

    public function getConditions(): ?string
    {
        return $this->conditions;
    }

    public function setConditions(string $conditions = null): void
    {
        $this->conditions = $conditions;
    }

    public function getSubTotalVat(): array
    {
        return $this->subTotalVat;
    }

    public function setSubTotalVat(array $subTotalVat): void
    {
        $this->subTotalVat = $subTotalVat;
    }

    public function addSubTotalVat(string $taxRate, float $vat)
    {
        if (!array_key_exists($taxRate, $this->subTotalVat)) {
            $this->subTotalVat[$taxRate] = 0;
        }

        $this->subTotalVat[$taxRate] += $vat;
    }

    public function getSubTotalWithoutVat(): float
    {
        return $this->subTotalWithoutVat;
    }

    public function setSubTotalWithoutVat(float $subTotalWithoutVat): void
    {
        $this->subTotalWithoutVat = $subTotalWithoutVat;
    }

    public function addSubTotalWithoutVat(float $subTotalWithoutVat)
    {
        $this->subTotalWithoutVat += $subTotalWithoutVat;
    }

    public function getVat(): float
    {
        return $this->vat;
    }

    public function setVat(float $vat): void
    {
        $this->vat = $vat;
    }

    public function addVat(float $vat)
    {
        $this->vat += $vat;
    }

    public function getTotal(): float
    {
        return $this->total;
    }

    public function setTotal(?float $total): void
    {
        $this->total = $total;
    }

    public function addTotal(?float $total)
    {
        $this->total += $total;
    }

    public function getItems(): array
    {
        return $this->items;
    }

    public function setItems(array $items): void
    {
        $this->items = $items;
    }

    public function getVatInformation(): CustomsInfo
    {
        return $this->vatInformation;
    }

    public function setVatInformation(CustomsInfo $vatInformation): void
    {
        $this->vatInformation = $vatInformation;
    }

    public function getMinimumOrderAmount(): ?float
    {
        return $this->minimumOrderAmount;
    }

    public function setMinimumOrderAmount(?float $minimumOrderAmount): CartMerchant
    {
        $this->minimumOrderAmount = $minimumOrderAmount;
        return $this;
    }

    public function isUpelaActive(): bool
    {
        return $this->upelaActive;
    }

    public function setUpelaActive(bool $upelaActive): self
    {
        $this->upelaActive = $upelaActive;
        return $this;
    }

    public function isShippable(): bool
    {
        return $this->shippable;
    }

    public function setShippable(bool $shippable): self
    {
        $this->shippable = $shippable;
        return $this;
    }

    /**
     * @return float|null
     */
    public function getShippingTotal(): ?float
    {
        return $this->shippingTotal;
    }

    /**
     * @param float|null $shippingTotal
     * @return $this
     */
    public function setShippingTotal(?float $shippingTotal): self
    {
        $this->shippingTotal = $shippingTotal;
        return $this;
    }

    /**
     * @return float|null
     */
    public function getShippingTotalVatIncluded(): ?float
    {
        return $this->shippingTotalVatIncluded;
    }

    /**
     * @param float|null $shippingTotalVatIncluded
     * @return $this
     */
    public function setShippingTotalVatIncluded(?float $shippingTotalVatIncluded): self
    {
        $this->shippingTotalVatIncluded = $shippingTotalVatIncluded;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getShippingName(): ?string
    {
        return $this->shippingName;
    }

    /**
     * @param string|null $shippingName
     * @return $this
     */
    public function setShippingName(?string $shippingName): self
    {
        $this->shippingName = $shippingName;
        return $this;
    }
}
