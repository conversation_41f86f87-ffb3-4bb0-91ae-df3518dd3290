<?php

namespace AppBundle\Model;

use AppBundle\Model\Cart\CartItem;
use <PERSON><PERSON>\Serializer\Annotation\Type;

class CarrierOffer
{
    #[Type('int')]
    private $id;

    /**
     * @var int
     */
    #[Type('int')]
    private $shipmentId;

    #[Type('string')]
    private $carrierCode;

    #[Type('string')]
    private $carrierName;

    #[Type('string')]
    private $serviceName;

    #[Type('string')]
    private $serviceCode;

    #[Type('string')]
    private $isExpress;

    #[Type('int')]
    private $allowPickup;

    #[Type('int')]
    private $allowDropoff;

    #[Type('int')]
    private $isSaas;

    #[Type('string')]
    private $vatRate;

    #[Type('float')]
    private $priceTe;

    #[Type('float')]
    private $priceTi;

    /**
     * @var float
     */
    #[Type('float')]
    private $upelaPriceTi;

    /**
     * @var float
     */
    #[Type('float')]
    private $upelaPriceTe;

    #[Type('float')]
    private $price;

    #[Type('string')]
    private $logo;

    #[Type('string')]
    private $currency;

    #[Type('string')]
    private $carrierIdentifier;

    #[Type('string')]
    private $deliveryDate;

    #[Type('string')]
    private $shipmentDate;

    /**
     * @var string
     */
    #[Type('string')]
    private $deliveryDateTime;

    /**
     * @var array
     */
    #[Type('array<AppBundle\Model\Cart\CartItem>')]
    private $cartItems;

    /**
     * @var array
     */
    #[Type('int')]
    private $itemsQte;

    /**
     * @var GroupOfPackages
     */
    #[Type('AppBundle\Model\GroupOfPackages')]
    private $groupOfPackages;

    /**
     * @var float
     */
    #[Type('float')]
    private $vatPrice;

    /**
     * @var float
     */
    #[Type('float')]
    private $upelaVatPrice;

    public function getId()
    {
        return $this->id;
    }

    public function setId($id)
    {
        $this->id = $id;
        return $this;
    }

    public function getCarrierCode()
    {
        return $this->carrierCode;
    }

    public function setCarrierCode($carrierCode)
    {
        $this->carrierCode = $carrierCode;
        return $this;
    }

    public function getCarrierName()
    {
        return $this->carrierName;
    }

    public function setCarrierName($carrierName)
    {
        $this->carrierName = $carrierName;
        return $this;
    }

    public function getServiceName()
    {
        return $this->serviceName;
    }

    public function setServiceName($serviceName):self
    {
        $this->serviceName = $serviceName;
        return $this;
    }

    public function getPrice()
    {
        return $this->price;
    }

    public function setPrice($price)
    {
        $this->price = $price;
        return $this;
    }

    public function getLogo()
    {
        return $this->logo;
    }

    public function setLogo($logo)
    {
        $this->logo = $logo;
        return $this;
    }

    public function getCarrierIdentifier(): ?string
    {
        return $this->carrierIdentifier;
    }

    public function setCarrierIdentifier(?string $carrierIdentifier): self
    {
        $this->carrierIdentifier = $carrierIdentifier;
        return $this;
    }

    public function getDeliveryDate()
    {
        return $this->deliveryDate;
    }

    public function setDeliveryDate($deliveryDate)
    {
        $this->deliveryDate = $deliveryDate;
        return $this;
    }

    /**
     * @return array<CartItem>
     */
    public function getCartItems(): array
    {
        return $this->cartItems;
    }

    /**
     * @param array<CartItem> $cartItems
     * @return $this
     */
    public function setCartItems(array $cartItems): self
    {
        $this->cartItems = $cartItems;
        return $this;
    }

    public function getShipmentDate(): ?string
    {
        return $this->shipmentDate;
    }

    public function setShipmentDate(?string $shipmentDate): self
    {
        $this->shipmentDate = $shipmentDate;
        return $this;
    }

    public function getGroupOfPackages(): GroupOfPackages
    {
        return $this->groupOfPackages;
    }

    public function setGroupOfPackages(GroupOfPackages $groupOfPackages): void
    {
        $this->groupOfPackages = $groupOfPackages;
    }

    public function hasCartItem(int $cartId)
    {
        $cartIds = array_map(
            function(CartItem $cartItem) {
                return $cartItem->getId();
            },
            $this->cartItems
        );

        return in_array($cartId, $cartIds);
    }

    public function getCartItem(int $cartId)
    {
        if ($this->hasCartItem($cartId)) {
            $cartItems = $this->getCartItems();
            foreach ($cartItems as $cartItem) {
                if ($cartItem->getId() === $cartId) {
                    return $cartItem;
                }
            }
        }

        return null;
    }

    public function getServiceCode()
    {
        return $this->serviceCode;
    }

    public function setServiceCode($serviceCode): self
    {
        $this->serviceCode = $serviceCode;
        return $this;
    }

    public function getIsExpress()
    {
        return $this->isExpress;
    }

    public function setIsExpress($isExpress): self
    {
        $this->isExpress = $isExpress;
        return $this;
    }

    public function getAllowPickup()
    {
        return $this->allowPickup;
    }

    public function setAllowPickup($allowPickup): self
    {
        $this->allowPickup = $allowPickup;
        return $this;
    }

    public function getAllowDropoff()
    {
        return $this->allowDropoff;
    }

    public function setAllowDropoff($allowDropoff): self
    {
        $this->allowDropoff = $allowDropoff;
        return $this;
    }

    public function getIsSaas()
    {
        return $this->isSaas;
    }

    public function setIsSaas($isSaas): self
    {
        $this->isSaas = $isSaas;
        return $this;
    }

    public function getVatRate()
    {
        return $this->vatRate;
    }

    public function setVatRate($vatRate): self
    {
        $this->vatRate = $vatRate;
        return $this;
    }

    public function getVatPrice(): float
    {
        return $this->vatPrice;
    }

    public function setVatPrice(float $vatPrice): self
    {
        $this->vatPrice = $vatPrice;
        return $this;
    }

    public function getPriceTe()
    {
        return $this->priceTe;
    }

    public function setPriceTe($priceTe): self
    {
        $this->priceTe = $priceTe;
        return $this;
    }

    public function getPriceTi()
    {
        return $this->priceTi;
    }

    public function setPriceTi($priceTi): self
    {
        $this->priceTi = $priceTi;
        return $this;
    }

    public function getUpelaPriceTi(): float
    {
        return $this->upelaPriceTi;
    }

    public function setUpelaPriceTi(float $upelaPriceTi): self
    {
        $this->upelaPriceTi = $upelaPriceTi;
        return $this;
    }

    public function getUpelaPriceTe(): float
    {
        return $this->upelaPriceTe;
    }

    public function setUpelaPriceTe(float $upelaPriceTe): self
    {
        $this->upelaPriceTe = $upelaPriceTe;
        return $this;
    }

    public function getUpelaVatPrice(): float
    {
        return $this->upelaVatPrice;
    }

    public function setUpelaVatPrice(float $upelaVatPrice): self
    {
        $this->upelaVatPrice = $upelaVatPrice;
        return $this;
    }

    public function getCurrency()
    {
        return $this->currency;
    }

    public function setCurrency($currency): self
    {
        $this->currency = $currency;
        return $this;
    }

    public function getDeliveryDateTime(): string
    {
        return $this->deliveryDateTime;
    }

    public function setDeliveryDateTime(string $deliveryDateTime): self
    {
        $this->deliveryDateTime = $deliveryDateTime;
        return $this;
    }

    public function getShipmentId(): int
    {
        return $this->shipmentId;
    }

    public function setShipmentId(int $shipmentId): self
    {
        $this->shipmentId = $shipmentId;
        return $this;
    }

    public function getItemsQte(): array
    {
        return $this->itemsQte;
    }

    public function setItemsQte(array $itemsQte): self
    {
        $this->itemsQte = $itemsQte;
        return $this;
    }

    public function toArray()
    {
        return [
            'carrier_code' => $this->carrierCode,
            'carrier_name' => $this->carrierName,
            'service_code' => $this->serviceCode,
            'service_name' => $this->serviceName,
            'is_express' => $this->isExpress,
            'allow_pickup' => $this->allowPickup,
            'allow_dropoff' => $this->allowDropoff,
            'is_saas' => $this->isSaas,
            'vat_rate' => $this->vatRate,
            'price_te' => $this->priceTe,
            'price_ti' => $this->priceTi,
            'currency' => $this->currency,
        ];

    }
}
