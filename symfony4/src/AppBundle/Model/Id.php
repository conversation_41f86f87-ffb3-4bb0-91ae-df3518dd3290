<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 28/01/2019
 * Time: 11:10
 */

namespace AppBundle\Model;

/**
 * Just an Id...
 * Class Id
 * @package AppBundle\Model
 */
class Id
{
    private $id;

    /**
     * Id constructor.
     * @param $id
     */
    public function __construct($id)
    {
        $this->id = $id;
    }


    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id): void
    {
        $this->id = $id;
    }



}
