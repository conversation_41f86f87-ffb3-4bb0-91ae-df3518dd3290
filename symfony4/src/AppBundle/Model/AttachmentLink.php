<?php

namespace AppBundle\Model;


class AttachmentLink
{

    /**
     * @var string $link
     */
    private $link;

    /**
     * @var string filename
     */
    private $filename;

    /**
     * @return string
     */
    public function getLink(): string
    {
        return $this->link;
    }

    /**
     * @param string $link
     */
    public function setLink(string $link): void
    {
        $this->link = $link;
    }

    /**
     * @return string
     */
    public function getFilename(): string
    {
        return $this->filename;
    }

    /**
     * @param string $filename
     */
    public function setFilename(string $filename): void
    {
        $this->filename = $filename;
    }



}
