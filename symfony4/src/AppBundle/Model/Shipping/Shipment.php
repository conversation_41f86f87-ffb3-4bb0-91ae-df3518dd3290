<?php

namespace AppBundle\Model\Shipping;

final class Shipment
{
    /**
     * @var int
     */
    private $shipmentId;

    /**
     * @var int
     */
    private $quantity;

    /**
     * @var int
     */
    private $orderItemId;

    /**
     * @var int
     */
    private $merchantOrderId;

    public function getShipmentId(): int
    {
        return $this->shipmentId;
    }

    public function setShipmentId(int $shipmentId): self
    {
        $this->shipmentId = $shipmentId;
        return $this;
    }

    public function getQuantity(): int
    {
        return $this->quantity;
    }

    public function setQuantity(int $quantity): self
    {
        $this->quantity = $quantity;
        return $this;
    }

    public function getOrderItemId(): int
    {
        return $this->orderItemId;
    }

    public function setOrderItemId(int $orderItemId): self
    {
        $this->orderItemId = $orderItemId;
        return $this;
    }

    public function getMerchantOrderId(): int
    {
        return $this->merchantOrderId;
    }

    public function setMerchantOrderId(int $merchantOrderId): self
    {
        $this->merchantOrderId = $merchantOrderId;
        return $this;
    }
}
