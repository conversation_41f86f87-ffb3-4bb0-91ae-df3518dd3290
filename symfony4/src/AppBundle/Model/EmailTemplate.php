<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 14/11/2018
 * Time: 13:18
 */

namespace AppBundle\Model;


class EmailTemplate
{
    /**
     * @var string $templateName name of the template
     */
    private $templateName;

    /**
     * @var string $content the default content of this template
     */
    private $content;

    /**
     * @var string subject of the template (only used to validate an email)
     */
    private $subject;

    /**
     * @var array $variables the list of variables for this template ["name" => "defaultValue]
     */
    private $variables;

    /**
     * @return string
     */
    public function getTemplateName(): string
    {
        return $this->templateName;
    }

    /**
     * @param string $templateName
     */
    public function setTemplateName(string $templateName): void
    {
        $this->templateName = $templateName;
    }

    /**
     * @return string
     */
    public function getContent(): string
    {
        return $this->content;
    }

    /**
     * @param string $content
     */
    public function setContent(string $content): void
    {
        $this->content = $content;
    }

    /**
     * @return array
     */
    public function getVariables(): array
    {
        return $this->variables;
    }

    /**
     * @param array $variables
     */
    public function setVariables(array $variables): void
    {
        $this->variables = $variables;
    }

    /**
     * @return string
     */
    public function getSubject(): string
    {
        return $this->subject;
    }

    /**
     * @param string $subject
     */
    public function setSubject(string $subject): void
    {
        $this->subject = $subject;
    }





}
