<?php


namespace AppBundle\Model;

use AppBundle\Entity\Address;
use AppBundle\Model\Cart\CartItem;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use <PERSON><PERSON>\Serializer\Annotation\Type;
use <PERSON><PERSON>\Serializer\Annotation\Exclude;
use AppBundle\Entity\User;
use AppBundle\Model\Cart\CartMerchant;

class GroupOfPackages
{
    /**
     * @var array
     */
    #[Type('array<AppBundle\Model\Cart\CartItem>')]
    private $cartItems;
    /**
     * @var array
     */
    #[Type('array')]
    private $packages;
    /**
     * @var string
     */
    #[Type('string')]
    private $shipmentDate;

    /**
     * @var int|null
     */
    #[Type('integer')]
    private $merchantId;

    /**
     * @var string
     */
    #[Type('string')]
    private $merchantName;
    /**
     * @var string
     */
    #[Type('string')]
    private $companyName;
    /**
     * @var string
     */
    #[Type('string')]
    private $countryCode;
    /**
     * @var string
     */
    #[Type('string')]
    private $street;
    /**
     * @var string
     */
    #[Type('string')]
    private $zipCode;
    /**
     * @var string
     */
    #[Type('string')]
    private $town;
    /**
     * @var Address
     */
    #[Type('AppBundle\Entity\Address')]
    private $shippingAddress;
    /**
     * @var User
     */
    #[exclude]
    private $user;
    /** @var CartMerchant
     */
    #[exclude]
    private $merchant;

    /**
     * @var int
     */
    #[Type('int')]
    private $shipmentType;

    /**
     * @var array
     */
    #[Type('int')]
    private $itemsQte;

    public function __construct()
    {
        $this->cartItems = [];
        $this->packages = [];
        $this->itemsQte = [];
    }

    /**
     * @return array<CartItem>|null
     */
    public function getCartItems(): ?array
    {
        return $this->cartItems;
    }

    /**
     * @param array<CartItem>|null $cartItems
     * @return $this
     */
    public function setCartItems(?array $cartItems): self
    {
        $this->cartItems = $cartItems;
        return $this;
    }


    public function getPackages(): ?array
    {
        return $this->packages;
    }

    public function setPackages(?array $packages): self
    {
        $this->packages = $packages;
        return $this;
    }

    public function getShipmentDate(): ?string
    {
        return $this->shipmentDate;
    }

    public function setShipmentDate(?string $shipmentDate): self
    {
        $this->shipmentDate = $shipmentDate;
        return $this;
    }

    public function getMerchantName(): ?string
    {
        return $this->merchantName;
    }

    public function setMerchantName(?string $merchantName): self
    {
        $this->merchantName = $merchantName;
        return $this;
    }

    public function getMerchantId(): ?int
    {
        return $this->merchantId;
    }

    public function setMerchantId(?int $merchantId): self
    {
        $this->merchantId = $merchantId;
        return $this;
    }

    public function getCountryCode(): ?string
    {
        return $this->countryCode;
    }

    public function setCountryCode(?string $countryCode): self
    {
        $this->countryCode = $countryCode;
        return $this;
    }

    public function getStreet(): ?string
    {
        return $this->street;
    }

    public function setStreet(?string $street): self
    {
        $this->street = $street;
        return $this;
    }

    public function getZipCode(): ?string
    {
        return $this->zipCode;
    }

    public function setZipCode(?string $zipCode): self
    {
        $this->zipCode = $zipCode;
        return $this;
    }

    public function getTown(): ?string
    {
        return $this->town;
    }

    public function setTown(?string $town): self
    {
        $this->town = $town;
        return $this;
    }

    public function getCompanyName(): ?string
    {
        return $this->companyName;
    }

    public function setCompanyName(?string $companyName): self
    {
        $this->companyName = $companyName;
        return $this;
    }

    public function getShippingAddress(): ?Address
    {
        return $this->shippingAddress;
    }

    public function setShippingAddress(?Address $shippingAddress): self
    {
        $this->shippingAddress = $shippingAddress;
        return $this;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function setUser(User $user): self
    {
        $this->user = $user;
        return $this;
    }

    public function getMerchant(): CartMerchant
    {
        return $this->merchant;
    }

    public function setMerchant(CartMerchant $merchant): self
    {
        $this->merchant = $merchant;
        return $this;
    }

    public function getShipmentType(): int
    {
        return $this->shipmentType;
    }

    public function setShipmentType(int $shipmentType): self
    {
        $this->shipmentType = $shipmentType;
        return $this;
    }

    public function getItemsQte(): array
    {
        return $this->itemsQte;
    }

    public function setItemsQte(array $itemsQte): self
    {
        $this->itemsQte = $itemsQte;
        return $this;
    }

}
