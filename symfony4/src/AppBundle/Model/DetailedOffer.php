<?php

namespace AppBundle\Model;

class DetailedOffer
{
    /**
     * the basic offer
     */
    private Offer $offer;

    /**
     * list of attributes to be displayed in "Technical details" tab
     */
    private array $technicalAttributes;

    /**
     * list of attributes to be displayed in "Logistic Information" tab
     */
    private array $logisticAttributes;

    private array $associatedProducts;

    private array $associatedServices;

    public function __construct()
    {
        $this->associatedProducts = [];
        $this->associatedServices = [];
        $this->technicalAttributes = [];
        $this->logisticAttributes = [];
    }

    public function getOffer(): Offer
    {
        return $this->offer;
    }

    public function setOffer(Offer $offer): self
    {
        $this->offer = $offer;
        return $this;
    }

    public function getTechnicalAttributes(): array
    {
        return $this->technicalAttributes;
    }

    public function setTechnicalAttributes(array $technicalAttributes): self
    {
        $this->technicalAttributes = $technicalAttributes;
        return $this;
    }

    public function getLogisticAttributes(): array
    {
        return $this->logisticAttributes;
    }

    public function setLogisticAttributes(array $logisticAttributes): self
    {
        $this->logisticAttributes = $logisticAttributes;
        return $this;
    }

    public function getAssociatedProducts(): array
    {
        return $this->associatedProducts;
    }

    public function setAssociatedProducts(array $associatedProducts): self
    {
        $this->associatedProducts = $associatedProducts;
        return $this;
    }

    public function getAssociatedServices(): array
    {
        return $this->associatedServices;
    }

    public function setAssociatedServices(array $associatedServices): self
    {
        $this->associatedServices = $associatedServices;
        return $this;
    }
}
