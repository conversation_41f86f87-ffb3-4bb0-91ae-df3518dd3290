<?php

namespace AppBundle\Model;

class Transaction
{
    private $izbergPaymentId;

    private $wpsTransactionCode;

    private $izbergOrderId;

    /** @var string */
    private $status;

    /** @var string */
    private $redirectionCardUrl;

    /** @var string */
    private $reason;

    /** @var string */
    private $reconciliationkey;

    public const STATUS_REFUSED = 'REFUSED';

    public function getIzbergPaymentId()
    {
        return $this->izbergPaymentId;
    }

    public function setIzbergPaymentId($izbergPaymentId): self
    {
        $this->izbergPaymentId = $izbergPaymentId;

        return $this;
    }

    public function getWpsTransactionCode()
    {
        return $this->wpsTransactionCode;
    }

    public function setWpsTransactionCode($wpsTransactionCode): self
    {
        $this->wpsTransactionCode = $wpsTransactionCode;

        return $this;
    }

    public function getIzbergOrderId()
    {
        return $this->izbergOrderId;
    }

    public function setIzbergOrderId($izbergOrderId): self
    {
        $this->izbergOrderId = $izbergOrderId;

        return $this;
    }

    public function isRefused()
    {
        return ($this->status === self::STATUS_REFUSED);
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getReason(): ?string
    {
        return $this->reason;
    }

    public function setReason(?string $reason): self
    {
        $this->reason = $reason;

        return $this;
    }

    public function getRedirectionCardUrl(): ?string
    {
        return $this->redirectionCardUrl;
    }

    public function setRedirectionCardUrl(?string $redirectionCardUrl): self
    {
        $this->redirectionCardUrl = $redirectionCardUrl;

        return $this;
    }

    public function getReconciliationkey(): ?string
    {
        return $this->reconciliationkey;
    }

    public function setReconciliationkey(?string $reconciliationkey): self
    {
        $this->reconciliationkey = $reconciliationkey;

        return $this;
    }
}
