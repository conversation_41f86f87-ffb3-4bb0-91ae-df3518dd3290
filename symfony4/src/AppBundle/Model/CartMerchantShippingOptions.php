<?php

namespace AppBundle\Model;

use J<PERSON>\Serializer\Annotation\Type;

class CartMerchantShippingOptions
{
    const OPTION_FASTEST = 'fastest';
    const OPTION_CHEAPEST = 'cheapest';
    const OPTION_MONOCARRIER = 'mono';

    #[Type('int')]
    private $merchantId;

    #[Type('int')]
    private $cartId;

    #[Type('AppBundle\Model\ShippingOption')]
    private $fastestOption;

    #[Type('AppBundle\Model\ShippingOption')]
    private $cheapestOption;

    #[Type('array<AppBundle\Model\ShippingOption>')]
    private $monoCarrierOption;

    /**
     * @var int
     */
    #[Type('int')]
    private $vatRate;

    public function getMerchantId(): int
    {
        return $this->merchantId;
    }

    public function setMerchantId(int $merchantId): self
    {
        $this->merchantId = $merchantId;
        return $this;
    }

    public function getCartId(): int
    {
        return $this->cartId;
    }

    public function setCartId(int $cartId): self
    {
        $this->cartId = $cartId;
        return $this;
    }

    public function getFastestOption(): ?ShippingOption
    {
        return $this->fastestOption;
    }

    public function setFastestOption(?ShippingOption $fastestOption): self
    {
        $this->fastestOption = $fastestOption;
        return $this;
    }

    public function getCheapestOption(): ?ShippingOption
    {
        return $this->cheapestOption;
    }

    public function setCheapestOption(?ShippingOption $cheapestOption): self
    {
        $this->cheapestOption = $cheapestOption;
        return $this;
    }

    public function getMonoCarrierOption(): ?array
    {
        return $this->monoCarrierOption;
    }

    public function setMonoCarrierOption(?array $monoCarrierOption): self
    {
        $this->monoCarrierOption = $monoCarrierOption;
        return $this;
    }

    public function getVatRate(): int
    {
        return $this->vatRate;
    }

    public function setVatRate(int $vatRate): self
    {
        $this->vatRate = $vatRate;
        return $this;
    }
}
