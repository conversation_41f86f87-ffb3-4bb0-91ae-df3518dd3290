<?php

namespace AppBundle\Message;

use Open\IzbergBundle\Model\FetchInvoicesResponse;

class SyncInvoice
{
    protected FetchInvoicesResponse $invoiceResponse;

    public function __construct(FetchInvoicesResponse $invoiceResponse)
    {
        $this->invoiceResponse = $invoiceResponse;
    }

    public function getInvoiceResponse(): FetchInvoicesResponse
    {
        return $this->invoiceResponse;
    }
}
