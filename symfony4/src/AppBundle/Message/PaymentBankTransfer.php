<?php


namespace AppBundle\Message;


class PaymentBankTransfer
{
    private int $cartId;
    private int $userId;
    private ?string $validationNumber = null;
    private ?string $accountingEmail = null;

    /**
     * PaymentBankTransfer constructor.
     *
     * @param int $cartId
     * @param int $userId
     * @param string|null $validationNumber
     */
    public function __construct(int $cartId, int $userId, ?string $validationNumber, ?string $accountingEmail)
    {
        $this->cartId = $cartId;
        $this->userId = $userId;
        $this->validationNumber = $validationNumber;
        $this->accountingEmail = $accountingEmail;
    }

    public function getCartId(): int
    {
        return $this->cartId;
    }

    public function getUserId(): int
    {
        return $this->userId;
    }

    public function getValidationNumber(): ?string
    {
        return $this->validationNumber;
    }

    public function getAccountingEmail(): ?string
    {
        return $this->accountingEmail;
    }
}
