<?php

namespace AppBundle\Message;

class SendEmailMessage
{
    // High priority email types (time-sensitive, user-facing)
    public const HIGH_PRIORITY_TEMPLATES = [
        'USER_RESET_MDP',
        'BUYER_ACCOUNT_NEW_USER_TOUSER',
        'B<PERSON><PERSON><PERSON>_ACCOUNT_CREATION_TO_BUYER',
        'VENDOR_ACCOUNT_CREATION_TO_VENDOR',
        'ORDER_CONFIRMATION_TO_BUYER',
        'ORDER_CONFIRMED_BY_VENDOR_TO_BUYER',
        'ORDER_PROCESSED_BY_VENDOR_TO_BUYER',
        'PAYMENT_PREPAYMENT_BANK_TRANSFER_INSCTRUCTIONS_TO_BUYER',
        'PAYMENT_TERMPAYMENT_BANK_TRANSFER_INSCTRUCTIONS_TO_BUYER',
        'PAYMENT_FULL_RECEIVED_TO_BUYER',
        'PAYMENT_PARTIAL_RECEIVED_TO_BUYER',
        'TICKET_THREAD_NEW_FROM_BUYER_TO_OPERATOR',
        'TICKET_THREAD_NEW_FROM_OPERATOR_TO_BUYER',
        'TICKET_THREAD_UPDATED_TO_BUYER',
        'TICKET_THREAD_UPDATED_TO_OPERATOR',
        'BUYER_ACCOUNT_VALIDATED_TO_BUYER',
        'BUYER_ACCOUNT_REJECTED_TO_BUYER',
        'VENDOR_ACCOUNT_VALIDATED_TO_VENDOR',
        'VENDOR_ACCOUNT_REJECTED_TO_VENDOR',
    ];

    private string $emailIdentifier;
    private string $lang;
    private $toUsers; // string or array
    private array $data;
    private ?string $fromEmailAddress;
    private ?string $fromName;
    private array $attachFiles;
    private string $priority;

    public function __construct(
        string $emailIdentifier,
        string $lang,
        $toUsers,
        array $data,
        ?string $fromEmailAddress = null,
        ?string $fromName = null,
        array $attachFiles = []
    ) {
        $this->emailIdentifier = $emailIdentifier;
        $this->lang = $lang;
        $this->toUsers = $toUsers;
        $this->data = $data;
        $this->fromEmailAddress = $fromEmailAddress;
        $this->fromName = $fromName;
        $this->attachFiles = $attachFiles;
        $this->priority = $this->determinePriority($emailIdentifier);
    }

    public function getEmailIdentifier(): string
    {
        return $this->emailIdentifier;
    }

    public function getLang(): string
    {
        return $this->lang;
    }

    public function getToUsers()
    {
        return $this->toUsers;
    }

    public function getData(): array
    {
        return $this->data;
    }

    public function getFromEmailAddress(): ?string
    {
        return $this->fromEmailAddress;
    }

    public function getFromName(): ?string
    {
        return $this->fromName;
    }

    public function getAttachFiles(): array
    {
        return $this->attachFiles;
    }

    public function getPriority(): string
    {
        return $this->priority;
    }

    public function isHighPriority(): bool
    {
        return $this->priority === 'high';
    }

    private function determinePriority(string $emailIdentifier): string
    {
        // Check if this email type should be high priority
        if (in_array($emailIdentifier, self::HIGH_PRIORITY_TEMPLATES)) {
            return 'high';
        }

        // Default to low priority for bulk emails, reports, etc.
        return 'low';
    }
}
