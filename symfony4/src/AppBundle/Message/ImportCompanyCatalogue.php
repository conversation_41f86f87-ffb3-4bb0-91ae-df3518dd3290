<?php


namespace AppBundle\Message;


class ImportCompanyCatalogue
{
    private array $dataSet;
    private int $companyId;
    private string $country;

    public function __construct(array $dataSet, int $companyId, string $country)
    {
        $this->dataSet = $dataSet;
        $this->companyId = $companyId;
        $this->country = $country;
    }

    public function getDataSet(): array
    {
        return $this->dataSet;
    }

    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    public function getCountry(): string
    {
        return $this->country;
    }
}
