<?php

namespace AppBundle\MessageHandler;

use AppBundle\Message\SendEmailMessage;
use AppBundle\Services\MailService;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
class SendEmailMessageHandler
{
    private MailService $mailService;
    private LoggerInterface $logger;

    public function __construct(MailService $mailService, LoggerInterface $messengerLogger)
    {
        $this->mailService = $mailService;
        $this->logger = $messengerLogger;
    }

    public function __invoke(SendEmailMessage $sendEmailMessage): bool
    {
        $emailIdentifier = $sendEmailMessage->getEmailIdentifier();
        $priority = $sendEmailMessage->getPriority();
        
        $logContext = [
            LogUtil::EVENT_NAME => EventNameEnum::EMAIL_SENT,
            'email_identifier' => $emailIdentifier,
            'priority' => $priority,
            'to_users' => $sendEmailMessage->getToUsers(),
            'lang' => $sendEmailMessage->getLang()
        ];

        $this->logger->info(
            sprintf('Processing async email message: %s (priority: %s)', $emailIdentifier, $priority),
            LogUtil::buildContext($logContext)
        );

        try {
            // Call the original sendEmailMessage method synchronously within the handler
            $result = $this->mailService->sendEmailMessageSync(
                $sendEmailMessage->getEmailIdentifier(),
                $sendEmailMessage->getLang(),
                $sendEmailMessage->getToUsers(),
                $sendEmailMessage->getData(),
                $sendEmailMessage->getFromEmailAddress(),
                $sendEmailMessage->getFromName(),
                $sendEmailMessage->getAttachFiles()
            );

            if ($result) {
                $this->logger->info(
                    sprintf('Successfully processed async email: %s', $emailIdentifier),
                    LogUtil::buildContext($logContext)
                );
            } else {
                $this->logger->warning(
                    sprintf('Email processing returned false: %s', $emailIdentifier),
                    LogUtil::buildContext(array_merge($logContext, [
                        LogUtil::EVENT_NAME => EventNameEnum::EMAIL_ERROR
                    ]))
                );
            }

            return $result;

        } catch (\Throwable $e) {
            $this->logger->error(
                sprintf('Error processing async email %s: %s', $emailIdentifier, $e->getMessage()),
                LogUtil::buildContext(array_merge($logContext, [
                    LogUtil::EVENT_NAME => EventNameEnum::EMAIL_ERROR,
                    'exception' => $e->getTraceAsString(),
                    'error_message' => $e->getMessage()
                ]))
            );

            // Re-throw to trigger retry mechanism if configured
            throw $e;
        }
    }
}
