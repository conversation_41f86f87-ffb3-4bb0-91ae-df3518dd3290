<?php

namespace AppBundle\MessageHandler;

use AppBundle\Message\ImportCompanyCatalogue;
use AppBundle\Services\CompanyCatalogService;
use AppBundle\Services\MessengerProgressionService;
use Exception;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LoggerTrait;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Exception\UnrecoverableMessageHandlingException;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
class ImportCompanyCatalogueHandler
{
    use LoggerTrait;

    protected CompanyCatalogService $companyCatalogService;
    private MessengerProgressionService $messengerProgressionService;

    public function __construct(CompanyCatalogService $companyCatalogService, LoggerInterface $messengerLogger, MessengerProgressionService $messengerProgressionService)
    {
        $this->companyCatalogService = $companyCatalogService;
        $this->logger = $messengerLogger;
        $this->companyCatalogService->setLogger($this->logger);
        $this->messengerProgressionService = $messengerProgressionService;
    }

    public function __invoke(ImportCompanyCatalogue $importCompanyCatalogue)
    {
        $logMessage = sprintf('Import company catalog group of data');
        $context = LogUtil::buildContext([
            LogUtil::EVENT_NAME => EventNameEnum::COMPANY_CATALOGUE_IMPORT
        ]);

        $this->startLogInfo($logMessage, $context);

        $dataSet = $importCompanyCatalogue->getDataSet();
        $country = $importCompanyCatalogue->getCountry();

        try {
            $this->companyCatalogService->importCompanyCatalogDataRowset($dataSet, $country);
            $this->messengerProgressionService->incrementProgression(MessengerProgressionService::CATALOG_MESSENGER, $importCompanyCatalogue->getCompanyId(),count($dataSet));

        } catch(Exception $exception) {
            throw new UnrecoverableMessageHandlingException($exception->getMessage());
        }

        $this->endLogInfo($logMessage, $context);
    }

}
