<?php

namespace AppBundle\MessageHandler;

use AppBundle\Message\AutoCancelOrder;
use AppBundle\Services\MerchantOrderService;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LoggerTrait;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
class AutoCancelOrderHandler
{
    use LoggerTrait;

    private MerchantOrderService $merchantOrderService;

    public function __construct(MerchantOrderService $merchantOrderService, LoggerInterface $messengerLogger)
    {
        $this->merchantOrderService = $merchantOrderService;
        $this->logger = $messengerLogger;

        $this->merchantOrderService->setLogger($this->logger);
    }

    public function __invoke(AutoCancelOrder $autoCancelOrder)
    {
        $logMessage = sprintf('autocancel merchant orders with ids : [%s]', implode(', ', $autoCancelOrder->getMerchantOrderIds()));
        $context = LogUtil::buildContext([
            LogUtil::EVENT_NAME => EventNameEnum::MERCHANT_ORDER_CANCEL
        ]);

        $this->startLogInfo($logMessage, $context);
        $this->merchantOrderService->autoCancel($autoCancelOrder->getMerchantOrderIds());
        $this->endLogInfo($logMessage, $context);
    }
}
