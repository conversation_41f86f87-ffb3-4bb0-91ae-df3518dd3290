<?php

namespace AppBundle\MessageHandler;

use AppBundle\Message\SyncInvoice;
use AppBundle\Services\InvoiceService;
use Open\IzbergBundle\Model\Invoice;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LoggerTrait;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
class SyncInvoiceHandler
{
    use LoggerTrait;

    private InvoiceService $invoiceService;

    public function __construct(InvoiceService $invoiceService, LoggerInterface $messengerLogger)
    {
        $this->invoiceService = $invoiceService;

        $this->logger = $messengerLogger;
        $this->invoiceService->setLogger($this->logger);
    }

    public function __invoke(SyncInvoice $syncInvoice)
    {
        /** @var array<array-key, mixed> $invoices */
        $invoices = $syncInvoice->getInvoiceResponse()->getObjects()->toArray();
        /** @var array<array-key, mixed> $invoiceIds */
        $invoiceIds = array_map(
            fn(Invoice $invoice) => $invoice->getId(),
            $invoices
        );

        $logMessage = 'sync group of invoice: [' . implode(',', $invoiceIds) . ']';
        $context = LogUtil::buildContext([
            LogUtil::EVENT_NAME => EventNameEnum::INVOICE
        ]);

        $this->startLogInfo($logMessage, $context);
        $this->invoiceService->sync($syncInvoice->getInvoiceResponse());
        $this->endLogInfo($logMessage, $context);
    }
}
