<?php

namespace AppBundle\MessageHandler;

use AppBundle\Message\PaymentBankTransfer;
use AppBundle\Model\Cart\Cart;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
class PaymentBankTransferHandler extends PaymentHandler
{
    public function __invoke(PaymentBankTransfer $paymentBankTransfer): int
    {
        $user = $this->userRepository->find($paymentBankTransfer->getUserId());
        $cart = $this->cartService->findCart($user, $paymentBankTransfer->getCartId());
        $validationNumber = $paymentBankTransfer->getValidationNumber();
        $accountingEmail = $paymentBankTransfer->getAccountingEmail();

        $logMessage = 'Prepayment with wire transfer';
        $cartId = $paymentBankTransfer->getCartId();
        $izbergUserId = $user->getCompany()->getIzbergUserId();

        $logContext = [
            LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_PROCESS,
            LogUtil::USER_NAME => $user->getUsername(),
            'cartId' => $cartId,
            'izbergUserId' => $izbergUserId,
            'validationNumber' => $validationNumber,
        ];

        $this->startLogInfo($logMessage, $logContext);

        if(!$cart instanceof Cart){
            $errorMessage = 'Cannot find cart in prepayment with wire transfer process';
            $this->logger->error($errorMessage, $logContext);
            return 1;
        }

        $this->useUserConnection($user);
        $this->paymentService->prePaymentWithWireTransfer($cart, $user, $validationNumber, $accountingEmail);

        $this->endLogInfo($logMessage, $logContext);
        return 0;
    }
}
