<?php

namespace AppBundle\MessageHandler;

use AppBundle\Message\SyncOrder;
use AppBundle\Services\OrderService;
use Open\IzbergBundle\Model\Order;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LoggerTrait;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
class SyncOrderHandler
{
    use LoggerTrait;

    private OrderService $orderService;

    public function __construct(OrderService $orderService, LoggerInterface $messengerLogger)
    {
        $this->orderService = $orderService;
        $this->logger = $messengerLogger;

        $this->orderService->setLogger($messengerLogger);
    }

    public function __invoke(SyncOrder $syncOrder)
    {
        $orders = $syncOrder->getOrdersResponse()->getObjects()->toArray();
        $orderIds = array_map(fn(Order $order) => $order->getId(), $orders);

        $logMessage = 'sync group of orders: [' . implode(',', $orderIds) . ']';
        $context = LogUtil::buildContext([
            LogUtil::EVENT_NAME => EventNameEnum::ORDER_SYNC
        ]);

        $this->startLogInfo($logMessage, $context);
        $this->orderService->sync($syncOrder->getOrdersResponse());
        $this->endLogInfo($logMessage, $context);
    }
}
