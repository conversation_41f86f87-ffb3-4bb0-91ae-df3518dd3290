<?php

namespace AppBundle\MessageHandler;

use AppBundle\Entity\Company;
use AppBundle\Entity\User;
use AppBundle\Repository\UserRepository;
use AppBundle\Services\CartService;
use AppBundle\Services\PaymentService;
use Open\IzbergBundle\Api\ApiClientManager;
use Open\IzbergBundle\Api\ApiConfigurator;
use Open\IzbergBundle\Api\AuthenticationApi;
use Open\LogBundle\Utils\LoggerTrait;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
abstract class PaymentHandler
{
    use LoggerTrait;

    protected UserRepository $userRepository;
    protected CartService $cartService;
    protected PaymentService $paymentService;
    private ApiClientManager $apiClientManager;
    private ApiConfigurator $apiConfigurator;
    private AuthenticationApi $authenticationApi;

    public function __construct(
        UserRepository $userRepository,
        CartService $cartService,
        PaymentService $paymentService,
        AuthenticationApi $authentication,
        ApiClientManager $clientManager,
        ApiConfigurator $configurator,
        LoggerInterface $messengerLogger
    )
    {
        $this->userRepository = $userRepository;
        $this->cartService = $cartService;
        $this->paymentService = $paymentService;
        $this->authenticationApi = $authentication;
        $this->apiClientManager = $clientManager;
        $this->apiConfigurator = $configurator;

        $this->logger = $messengerLogger;
        $this->paymentService->setLogger($messengerLogger);
    }

    protected function useUserConnection(User $user):void
    {
        $company = $user->getCompany();
        if(!$company instanceof Company){
            return;
        }
        $this->apiConfigurator->generateAsyncUserConfiguration($company, $this->authenticationApi);

        $this->apiClientManager->useConnection(ApiConfigurator::CONNECTION_ASYNC_USER_ACTION);
        $this->apiConfigurator->configure($this->apiClientManager);
    }
}
