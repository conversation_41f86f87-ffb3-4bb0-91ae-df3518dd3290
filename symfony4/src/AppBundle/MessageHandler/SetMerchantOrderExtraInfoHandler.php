<?php

namespace AppBundle\MessageHandler;

use AppBundle\Message\SetMerchantOrderExtraInfo;
use AppBundle\Services\OrderService;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LoggerTrait;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
class SetMerchantOrderExtraInfoHandler
{
    use LoggerTrait;

    private OrderService $orderService;

    /**
     * SetMerchantOrderExtraInfoHandler constructor.
     * @param OrderService $orderService
     * @param LoggerInterface $messengerLogger
     */
    public function __construct(OrderService $orderService, LoggerInterface $messengerLogger)
    {
        $this->orderService = $orderService;

        $this->logger = $messengerLogger;
        $this->orderService->setLogger($this->logger);
    }

    public function __invoke(SetMerchantOrderExtraInfo $setMerchantOrderExtraInfo)
    {
        $logMessage = sprintf('Set extra infos for merchant orders [%s]', implode(',', $setMerchantOrderExtraInfo->getMerchantOrderIds()));
        $context = LogUtil::buildContext([
            LogUtil::EVENT_NAME => EventNameEnum::MERCHANT_ORDER_EXTRA_INFO
        ]);

        $this->startLogInfo($logMessage, $context);

        $this->orderService->updateMerchantOrdersExtraInfos(
            $setMerchantOrderExtraInfo->getMerchantOrderIds(),
            $setMerchantOrderExtraInfo->getAttributes()
        );

        $this->endLogInfo($logMessage, $context);
    }
}
