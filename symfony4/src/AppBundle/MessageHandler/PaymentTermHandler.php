<?php

namespace AppBundle\MessageHandler;

use AppBundle\Message\PaymentTerm;
use AppBundle\Model\Cart\Cart;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
class PaymentTermHandler extends PaymentHandler
{
    public function __invoke(PaymentTerm $paymentTerm): int
    {
        $user = $this->userRepository->find($paymentTerm->getUserId());
        $cart = $this->cartService->findCart($user, $paymentTerm->getCartId());
        $validationNumber = $paymentTerm->getValidationNumber();
        $accountingEmail = $paymentTerm->getAccountingEmail();

        $logMessage = 'Term payment';
        $cartId = $paymentTerm->getCartId();
        $izbergUserId = $user->getCompany()->getIzbergUserId();

        $logContext = [
            LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_PROCESS,
            LogUtil::USER_NAME => $user->getUsername(),
            'cartId' => $cartId,
            'izbergUserId' => $izbergUserId,
            'validationNumber' => $validationNumber,
        ];

        $this->startLogInfo($logMessage, $logContext);

        if(!$cart instanceof Cart){
            return 1;
        }

        $this->useUserConnection($user);
        $this->paymentService->termPayment($cart, $user, $validationNumber, $accountingEmail);
        $this->endLogInfo($logMessage, $logContext);

        return 0;
    }
}
