<?php

namespace AppBundle\MessageHandler;

use AppBundle\Message\SyncCreditNote;
use AppBundle\Services\CreditNoteService;
use Open\IzbergBundle\Model\CreditNote;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LoggerTrait;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
class SyncCreditNoteHandler
{
    use LoggerTrait;

    private CreditNoteService $creditNoteService;

    /**
     * SyncCreditNoteHandler constructor.
     * @param CreditNoteService $creditNoteService
     * @param LoggerInterface $messengerLogger
     */
    public function __construct(CreditNoteService $creditNoteService, LoggerInterface $messengerLogger)
    {
        $this->creditNoteService = $creditNoteService;

        $this->logger = $messengerLogger;
        $this->creditNoteService->setLogger($this->logger);
    }

    public function __invoke(SyncCreditNote $syncCreditNote)
    {
        $creditNotes = $syncCreditNote->getCreditNotesResponse()->getObjects()->toArray();
        $creditNoteIds = array_map(fn(CreditNote $creditNote) => $creditNote->getId(), $creditNotes);
        $logMessage = 'sync group of credit note: [' . implode(',', $creditNoteIds) . ']';

        $context = LogUtil::buildContext([
            LogUtil::EVENT_NAME => EventNameEnum::CREDIT_NOTE
        ]);

        $this->startLogInfo($logMessage, $context);
        $this->creditNoteService->sync($syncCreditNote->getCreditNotesResponse());
        $this->endLogInfo($logMessage, $context);
    }
}
