<?php

namespace AppBundle\Entity;

use AppBundle\Repository\OrderItemRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: OrderItemRepository::class)]
class OrderItem
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private $productName;

    #[ORM\Column(type: 'float', nullable: true)]
    private $unitPrice;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $quantity;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private $currency;

    #[ORM\Column(type: 'string', nullable: true)]
    private $orderLine;
    private $cartItemComment;
    private $cartItemSplitDelivery;

    #[ORM\Column(type: 'datetime_immutable', nullable: true)]
    private $expectedDeliveryDate;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private $vendorReference;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private $buyerReference;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private $frameContractNumber;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $izbergId;

    /**
     *
     * @var MerchantOrder
     */
    #[ORM\JoinColumn(name: 'merchantOrder_id', referencedColumnName: 'id', nullable: false)]
    #[ORM\ManyToOne(targetEntity: \MerchantOrder::class, inversedBy: 'OrderItems', cascade: ['persist'])]
    private MerchantOrder $merchantOrder;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getProductName(): ?string
    {
        return $this->productName;
    }

    public function setProductName(?string $productName): self
    {
        $this->productName = $productName;

        return $this;
    }

    public function getUnitPrice(): ?float
    {
        return $this->unitPrice;
    }

    public function setUnitPrice(?float $unitPrice): self
    {
        $this->unitPrice = $unitPrice;

        return $this;
    }

    public function getQuantity(): ?int
    {
        return $this->quantity;
    }

    public function setQuantity(?int $quantity): self
    {
        $this->quantity = $quantity;

        return $this;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function setCurrency(?string $currency): self
    {
        $this->currency = $currency;

        return $this;
    }

    public function getOrderLine(): ?string
    {
        return $this->orderLine;
    }

    public function setOrderLine(?string $orderLine): self
    {
        $this->orderLine = $orderLine;

        return $this;
    }

    public function getCartItemComment(): ?string
    {
        return $this->cartItemComment;
    }

    public function setCartItemComment(?string $cartItemComment): self
    {
        $this->cartItemComment = $cartItemComment;
        return $this;
    }

    public function getCartItemSplitDelivery(): ?array
    {
        return $this->cartItemSplitDelivery;
    }

    public function setCartItemSplitDelivery(?array $cartItemSplitDelivery): self
    {
        $this->cartItemSplitDelivery = $cartItemSplitDelivery;
        return $this;
    }

    public function getExpectedDeliveryDate(): ?\DateTimeImmutable
    {
        return $this->expectedDeliveryDate;
    }

    public function setExpectedDeliveryDate(?\DateTimeImmutable $expectedDeliveryDate): self
    {
        $this->expectedDeliveryDate = $expectedDeliveryDate;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getVendorReference():?string
    {
        return $this->vendorReference;
    }

    /**
     * @param string|null $vendorReference
     * @return $this
     */
    public function setVendorReference(?string $vendorReference): self
    {
        $this->vendorReference = $vendorReference;
        return $this;
    }

    public function getBuyerReference()
    {
        return $this->buyerReference;
    }

    public function setBuyerReference($buyerReference)
    {
        $this->buyerReference = $buyerReference;
        return $this;
    }

    /**
     * @return MerchantOrder
     */
    public function getMerchantOrder(): MerchantOrder
    {
        return $this->merchantOrder;
    }

    /**
     * @param MerchantOrder $merchantOrder
     * @return $this
     */
    public function setMerchantOrder(MerchantOrder $merchantOrder): self
    {
        $this->merchantOrder = $merchantOrder;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getIzbergId():?int
    {
        return $this->izbergId;
    }

    /**
     * @param int|null $izbergId
     * @return $this
     */
    public function setIzbergId(?int $izbergId): self
    {
        $this->izbergId = $izbergId;
        return $this;
    }

    public function getFrameContractNumber()
    {
        return $this->frameContractNumber;
    }

    public function setFrameContractNumber($frameContractNumber)
    {
        $this->frameContractNumber = $frameContractNumber;
        return $this;
    }
}
