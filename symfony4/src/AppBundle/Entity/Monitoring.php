<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Monitoring
 */
#[ORM\Table(name: 'monitoring')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\MonitoringRepository::class)]
class Monitoring
{
    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

    /**
     * @var bool|null
     */
    #[ORM\Column(name: 'redisStatus', type: 'boolean', nullable: true)]
    private $redisStatus;

    /**
     * @var bool|null
     */
    #[ORM\Column(name: 'algoliaStatus', type: 'boolean', nullable: true)]
    private $algoliaStatus;

    /**
     * @var bool|null
     */
    #[ORM\Column(name: 'izbAdminAccountStatus', type: 'boolean', nullable: true)]
    private $izbAdminAccountStatus;

    /**
     * @var bool|null
     */
    #[ORM\Column(name: 'izbConsoleAccountStatus', type: 'boolean', nullable: true)]
    private $izbConsoleAccountStatus;

    /**
     * @var bool|null
     */
    #[ORM\Column(name: 'izbAsyncStatus', type: 'boolean', nullable: true)]
    private $izbAsyncStatus;

    /**
     * @var int
     */
    #[ORM\Column(name: 'failedJobs', type: 'integer')]
    private $failedJobs;

    /**
     * @var int
     */
    #[ORM\Column(name: 'successJobs', type: 'integer')]
    private $successJobs;

    /**
     * @var \DateTime
     */
    #[ORM\Column(name: 'checkDate', type: 'datetime', unique: true)]
    private $checkDate;


    /**
     * Get id.
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set redisStatus.
     *
     * @param bool|null $redisStatus
     *
     * @return Monitoring
     */
    public function setRedisStatus($redisStatus = null) :self
    {
        $this->redisStatus = $redisStatus;

        return $this;
    }

    /**
     * Get redisStatus.
     *
     * @return bool|null
     */
    public function getRedisStatus()
    {
        return $this->redisStatus;
    }

    /**
     * Set algoliaStatus.
     *
     * @param bool|null $algoliaStatus
     *
     * @return Monitoring
     */
    public function setAlgoliaStatus($algoliaStatus = null) :self
    {
        $this->algoliaStatus = $algoliaStatus;

        return $this;
    }

    /**
     * Get algoliaStatus.
     *
     * @return bool|null
     */
    public function getAlgoliaStatus()
    {
        return $this->algoliaStatus;
    }

    /**
     * Set izbAdminAccountStatus.
     *
     * @param bool|null $izbAdminAccountStatus
     *
     * @return Monitoring
     */
    public function setIzbAdminAccountStatus($izbAdminAccountStatus = null) :self
    {
        $this->izbAdminAccountStatus = $izbAdminAccountStatus;

        return $this;
    }

    /**
     * Get izbAdminAccountStatus.
     *
     * @return bool|null
     */
    public function getIzbAdminAccountStatus()
    {
        return $this->izbAdminAccountStatus;
    }

    /**
     * Set izbConsoleAccountStatus.
     *
     * @param bool|null $izbConsoleAccountStatus
     *
     * @return Monitoring
     */
    public function setIzbConsoleAccountStatus($izbConsoleAccountStatus = null) :self
    {
        $this->izbConsoleAccountStatus = $izbConsoleAccountStatus;

        return $this;
    }

    /**
     * Get izbConsoleAccountStatus.
     *
     * @return bool|null
     */
    public function getIzbConsoleAccountStatus()
    {
        return $this->izbConsoleAccountStatus;
    }

    /**
     * Set izbAsyncStatus.
     *
     * @param bool|null $izbAsyncStatus
     *
     * @return Monitoring
     */
    public function setIzbAsyncStatus($izbAsyncStatus = null) :self
    {
        $this->izbAsyncStatus = $izbAsyncStatus;

        return $this;
    }

    /**
     * Get izbAsyncStatus.
     *
     * @return bool|null
     */
    public function getIzbAsyncStatus()
    {
        return $this->izbAsyncStatus;
    }

    /**
     * Set checkDate.
     *
     * @param \DateTime $checkDate
     *
     * @return Monitoring
     */
    public function setCheckDate($checkDate) :self
    {
        $this->checkDate = $checkDate;

        return $this;
    }

    /**
     * @return int
     */
    public function getFailedJobs(): int
    {
        return $this->failedJobs;
    }

    /**
     * @param int $failedJobs
     * @return Monitoring
     */
    public function setFailedJobs(int $failedJobs): Monitoring
    {
        $this->failedJobs = $failedJobs;
        return $this;
    }

    /**
     * @return int
     */
    public function getSuccessJobs(): int
    {
        return $this->successJobs;
    }

    /**
     * @param int $successJobs
     * @return Monitoring
     */
    public function setSuccessJobs(int $successJobs): Monitoring
    {
        $this->successJobs = $successJobs;
        return $this;
    }


    /**
     * Get checkDate.
     *
     * @return \DateTime
     */
    public function getCheckDate()
    {
        return $this->checkDate;
    }
}
