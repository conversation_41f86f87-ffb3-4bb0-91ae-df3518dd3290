<?php

namespace AppBundle\Entity;

use DateTime;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\Index;

/**
 * SpecificPrice
 *
 *
 */
#[ORM\Table(name: 'specific_prices')]
#[Index(name: 'companyIdentification_idx', columns: ['company_ident'])]
#[Index(name: 'IZB_merchant_id_idx', columns: ['IZB_merchant_id'])]
#[Index(name: 'vendor_reference_idx', columns: ['vendor_reference'])]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\SpecificPriceRepository::class)]
#[ORM\HasLifecycleCallbacks]
class SpecificPrice
{
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private int $id;

    #[ORM\Column(name: 'company_ident', type: 'string', length: 255, nullable: false)]
    private string $companyIdentification;

    #[ORM\Column(name: 'threshold1', type: 'integer', nullable: true)]
    private ?int $threshold1 = null;

    #[ORM\Column(name: 'price1', type: 'float', nullable: true)]
    private ?float $price1 = null;

    #[ORM\Column(name: 'threshold2', type: 'integer', nullable: true)]
    private ?int $threshold2 = null;

    #[ORM\Column(name: 'price2', type: 'float', nullable: true)]
    private ?float $price2 = null;

    #[ORM\Column(name: 'threshold3', type: 'integer', nullable: true)]
    private ?int $threshold3 = null;

    #[ORM\Column(name: 'price3', type: 'float', nullable: true)]
    private ?float $price3 = null;

    #[ORM\Column(name: 'threshold4', type: 'integer', nullable: true)]
    private ?int $threshold4 = null;

    #[ORM\Column(name: 'price4', type: 'float', nullable: true)]
    private ?float $price4 = null;

    #[ORM\Column(name: 'IZB_merchant_id', type: 'integer', nullable: false)]
    private int $IZBmerchantId;

    #[ORM\Column(name: 'moq', type: 'integer', nullable: true)]
    private ?int $moq = null;

    #[ORM\Column(name: 'basic_price', type: 'float', nullable: true)]
    private ?float $basicPrice = null;

    #[ORM\Column(name: 'vendor_reference', type: 'string', length: 255)]
    private string $vendorReference;

    #[ORM\Column(name: 'incoterm', type: 'string', length: 255)]
    private string $incoterm;

    #[ORM\Column(name: 'country', type: 'string', length: 255, nullable: false)]
    private string $country;

    #[ORM\Column(name: 'created_at', type: 'datetime', nullable: false)]
    private DateTime $createdAt;

    #[ORM\Column(name: 'validity_date', type: 'datetime', nullable: true)]
    private ?DateTime $validityDate = null;

    #[ORM\Column(name: 'delay_of_delivery', type: 'integer', nullable: true)]
    private ?int $delayOfDelivery = null;

    #[ORM\Column(name: 'frame_contract', type: 'string', length: 255, nullable: true)]
    private ?string $frameContract = null;

    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\Company::class, inversedBy: 'specificPrices')]
    private ?Company $company;

    public function __construct()
    {
        $this->createdAt = new DateTime();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function hasThreshold(): bool
    {
        return (count($this->getThresholdsAndPrices()) >= 1);
    }

    /**
     * return an array of threshold and price associated
     * example: [
     *      50 => 150.00,
     *      60 => 140.50,
     *      80 => 160.70,
     * ]
     * This method filter only the couple threshold/price not null
     *
     * @return array
     */
    public function getThresholdsAndPrices(): array
    {
        $thresholds = [$this->getThreshold1(), $this->getThreshold2(), $this->getThreshold3(), $this->getThreshold4(),];
        $prices = [$this->getPrice1(), $this->getPrice2(), $this->getPrice3(), $this->getPrice4(),];

        return array_filter(
            array_map(
                fn($threshold, $price): ?array => ($threshold && $price) ? [$threshold, $price] : null,
                $thresholds,
                $prices
            )
        );
    }

    public function getCompanyIdentification(): string
    {
        return $this->companyIdentification;
    }

    public function setCompanyIdentification(string $companyIdentification): self
    {
        $this->companyIdentification = $companyIdentification;
        return $this;
    }

    public function getThreshold1(): ?int
    {
        return $this->threshold1;
    }

    public function setThreshold1(?int $threshold1): self
    {
        $this->threshold1 = $threshold1;
        return $this;
    }

    public function getPrice1(): ?float
    {
        return $this->price1;
    }

    public function setPrice1(?float $price1): self
    {
        $this->price1 = $price1;
        return $this;
    }

    public function getThreshold2(): ?int
    {
        return $this->threshold2;
    }

    public function setThreshold2(?int $threshold2): self
    {
        $this->threshold2 = $threshold2;
        return $this;
    }

    public function getPrice2(): ?float
    {
        return $this->price2;
    }

    public function setPrice2(?float $price2): self
    {
        $this->price2 = $price2;
        return $this;
    }

    public function getThreshold3(): ?int
    {
        return $this->threshold3;
    }

    public function setThreshold3(?int $threshold3): self
    {
        $this->threshold3 = $threshold3;
        return $this;
    }

    public function getPrice3(): ?float
    {
        return $this->price3;
    }

    public function setPrice3(?float $price3): self
    {
        $this->price3 = $price3;
        return $this;
    }

    public function getThreshold4(): ?int
    {
        return $this->threshold4;
    }

    public function setThreshold4(?int $threshold4): self
    {
        $this->threshold4 = $threshold4;
        return $this;
    }

    public function getPrice4(): ?float
    {
        return $this->price4;
    }

    public function setPrice4(?float $price4): self
    {
        $this->price4 = $price4;
        return $this;
    }

    public function getIZBmerchantId(): int
    {
        return $this->IZBmerchantId;
    }

    public function setIZBmerchantId(int $IZBmerchantId): self
    {
        $this->IZBmerchantId = $IZBmerchantId;
        return $this;
    }

    public function getMoq(): ?int
    {
        return $this->moq;
    }

    public function setMoq(?int $moq): self
    {
        $this->moq = $moq;
        return $this;
    }

    public function getBasicPrice(): ?float
    {
        return $this->basicPrice;
    }

    public function setBasicPrice(?float $basicPrice): self
    {
        $this->basicPrice = $basicPrice;
        return $this;
    }

    public function getVendorReference(): string
    {
        return $this->vendorReference;
    }

    public function setVendorReference(string $vendorReference): self
    {
        $this->vendorReference = $vendorReference;
        return $this;
    }

    public function getIncoterm(): string
    {
        return $this->incoterm;
    }

    public function setIncoterm(string $incoterm): self
    {
        $this->incoterm = $incoterm;
        return $this;
    }

    public function getCountry(): string
    {
        return $this->country;
    }

    public function setCountry(string $country): self
    {
        $this->country = $country;
        return $this;
    }

    public function getCreatedAt(): DateTime
    {
        return $this->createdAt;
    }

    public function setCreatedAt(DateTime $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getValidityDate(): ?DateTime
    {
        return $this->validityDate;
    }

    public function setValidityDate(?DateTime $validityDate): self
    {
        $this->validityDate = $validityDate;
        return $this;
    }

    public function getDelayOfDelivery(): ?int
    {
        return $this->delayOfDelivery;
    }

    public function setDelayOfDelivery(?int $delayOfDelivery): self
    {
        $this->delayOfDelivery = $delayOfDelivery;
        return $this;
    }

    public function getFrameContract(): ?string
    {
        return $this->frameContract;
    }

    public function setFrameContract(?string $frameContract): self
    {
        $this->frameContract = $frameContract;
        return $this;
    }

    public function getCompany(): ?Company
    {
        return $this->company;
    }

    public function setCompany(?Company $company): self
    {
        $this->company = $company;
        return $this;
    }

    public function getPriceByQuantity(int $expectedQuantity): ?float
    {
        if (!empty($this->getThreshold1()) && $expectedQuantity >= $this->getThreshold1()) {
            if (!empty($this->getThreshold2()) && $expectedQuantity >= $this->getThreshold2()) {
                if (!empty($this->getThreshold3()) && $expectedQuantity >= $this->getThreshold3()) {
                    if (!empty($this->getThreshold4()) && $expectedQuantity >= $this->getThreshold4()) {
                        return $this->getPrice4();
                    }
                    return $this->getPrice3();
                }
                return $this->getPrice2();
            }
            return $this->getPrice1();
        }
        return $this->getBasicPrice();
    }
}
