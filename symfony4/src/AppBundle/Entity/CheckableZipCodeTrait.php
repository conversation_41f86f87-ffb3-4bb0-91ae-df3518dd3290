<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 05/04/2018
 * Time: 16:56
 */

namespace AppBundle\Entity;


use AppBundle\Controller\MkoController;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\Form\SubmitButton;

trait CheckableZipCodeTrait
{

    /**
     * @param FormInterface|null $form
     * @param $address
     * @param $country
     * @return bool
     */
    protected function checkZipCode(?FormInterface $form, $address, $country){
        $thereIsError = false;
        if($country == 'france' && isset($form)) {


            if ($form->has('submit')) {
                $submit_field = $form->get('submit');
            } elseif ($form->has('save')) {
                $submit_field = $form->get('save');
            }
            /** @var SubmitButton $submit_field */
            if ($submit_field->isClicked() && $form->get($address)->get('zipCode')) {
                if (!preg_match('/^[0-9]{5}$/', $form->get($address)->get('zipCode')->getData())) {
                    $error = new FormError($this->get(self::TRANSLATOR)->trans('error.zipCode', array(), MkoController::TRANSLATION_DOMAIN));
                    $form->get($address)->get('zipCode')->addError($error);
                    $thereIsError = true;
                }
            }
        }
        return $thereIsError;

    }

}
