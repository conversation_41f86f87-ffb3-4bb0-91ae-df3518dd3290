<?php

namespace AppBundle\Entity;

use AppBundle\Repository\MerchantOrderRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: MerchantOrderRepository::class)]
class MerchantOrder
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $izbergId;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private $vendorName;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $vendorId;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $merchantOrderStatus;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private $buyerInternalOrderId;

    /**
     *
     * @var Order
     */
    #[ORM\JoinColumn(name: 'order_id', referencedColumnName: 'id', nullable: false)]
    #[ORM\ManyToOne(targetEntity: \Order::class, inversedBy: 'merchantOrders', cascade: ['persist'])]
    private Order $order;

    /**
     * @var Collection
     */
    #[ORM\OneToMany(targetEntity: \OrderItem::class, mappedBy: 'merchantOrder', cascade: ['persist', 'remove'])]
    private Collection $orderItems;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $packagingRequest1 = null;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $packagingRequest2 = null;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $packagingRequest3 = null;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $documentationRequest1 = null;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $documentationRequest2 = null;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $documentationRequest3 = null;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $documentationRequest4 = null;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $documentationRequest5 = null;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $documentationRequest6 = null;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $documentationRequest7 = null;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $documentationRequest8 = null;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $documentationRequest9 = null;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $documentationRequest10 = null;

    /**
     * Merchant Order constructor.
     */
    public function __construct()
    {
        $this->orderItems = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getIzbergId(): ?int
    {
        return $this->izbergId;
    }

    public function setIzbergId(?int $izbergId): self
    {
        $this->izbergId = $izbergId;

        return $this;
    }

    public function getMerchantOrderStatus(): ?int
    {
        return $this->merchantOrderStatus;
    }

    public function setMerchantOrderStatus(?int $merchantOrderStatus): self
    {
        $this->merchantOrderStatus = $merchantOrderStatus;

        return $this;
    }

    public function getVendorName():?string
    {
        return $this->vendorName;
    }

    public function setVendorName(?string $vendorName): self
    {
        $this->vendorName = $vendorName;
        return $this;
    }

    public function getVendorId()
    {
        return $this->vendorId;
    }

    public function setVendorId($vendorId)
    {
        $this->vendorId = $vendorId;
        return $this;
    }

    /**
     * @return Order
     */
    public function getOrder(): Order
    {
        return $this->order;
    }

    /**
     * @param Order $order
     */
    public function setOrder(Order $order): void
    {
        $this->order = $order;
    }

    /**
     * @return Collection
     */
    public function getOrderItems()
    {
        return $this->orderItems;
    }

    /**
     * @param Collection $orderItems
     */
    public function setOrderItems($orderItems): void
    {
        $this->orderItems = $orderItems;
    }

    public function getBuyerInternalOrderId()
    {
        return $this->buyerInternalOrderId;
    }

    public function setBuyerInternalOrderId($buyerInternalOrderId)
    {
        $this->buyerInternalOrderId = $buyerInternalOrderId;
        return $this;
    }

    public function getPackagingRequest1(): ?string
    {
        return $this->packagingRequest1;
    }

    public function setPackagingRequest1(?string $packagingRequest1): self
    {
        $this->packagingRequest1 = $packagingRequest1;
        return $this;
    }

    public function getPackagingRequest2(): ?string
    {
        return $this->packagingRequest2;
    }

    public function setPackagingRequest2(?string $packagingRequest2): self
    {
        $this->packagingRequest2 = $packagingRequest2;
        return $this;
    }

    public function getPackagingRequest3(): ?string
    {
        return $this->packagingRequest3;
    }

    public function setPackagingRequest3(?string $packagingRequest3): self
    {
        $this->packagingRequest3 = $packagingRequest3;
        return $this;
    }

    public function getDocumentationRequest1(): ?string
    {
        return $this->documentationRequest1;
    }

    public function setDocumentationRequest1(?string $documentationRequest1): self
    {
        $this->documentationRequest1 = $documentationRequest1;
        return $this;
    }

    public function getDocumentationRequest2(): ?string
    {
        return $this->documentationRequest2;
    }

    public function setDocumentationRequest2(?string $documentationRequest2): self
    {
        $this->documentationRequest2 = $documentationRequest2;
        return $this;
    }

    public function getDocumentationRequest3(): ?string
    {
        return $this->documentationRequest3;
    }

    public function setDocumentationRequest3(?string $documentationRequest3): self
    {
        $this->documentationRequest3 = $documentationRequest3;
        return $this;
    }

    public function getDocumentationRequest4(): ?string
    {
        return $this->documentationRequest4;
    }

    public function setDocumentationRequest4(?string $documentationRequest4): self
    {
        $this->documentationRequest4 = $documentationRequest4;
        return $this;
    }

    public function getDocumentationRequest5(): ?string
    {
        return $this->documentationRequest5;
    }

    public function setDocumentationRequest5(?string $documentationRequest5): self
    {
        $this->documentationRequest5 = $documentationRequest5;
        return $this;
    }

    public function getDocumentationRequest6(): ?string
    {
        return $this->documentationRequest6;
    }

    public function setDocumentationRequest6(?string $documentationRequest6): self
    {
        $this->documentationRequest6 = $documentationRequest6;
        return $this;
    }

    public function getDocumentationRequest7(): ?string
    {
        return $this->documentationRequest7;
    }

    public function setDocumentationRequest7(?string $documentationRequest7): self
    {
        $this->documentationRequest7 = $documentationRequest7;
        return $this;
    }

    public function getDocumentationRequest8(): ?string
    {
        return $this->documentationRequest8;
    }

    public function setDocumentationRequest8(?string $documentationRequest8): self
    {
        $this->documentationRequest8 = $documentationRequest8;
        return $this;
    }

    public function getDocumentationRequest9(): ?string
    {
        return $this->documentationRequest9;
    }

    public function setDocumentationRequest9(?string $documentationRequest9): self
    {
        $this->documentationRequest9 = $documentationRequest9;
        return $this;
    }

    public function getDocumentationRequest10(): ?string
    {
        return $this->documentationRequest10;
    }

    public function setDocumentationRequest10(?string $documentationRequest10): self
    {
        $this->documentationRequest10 = $documentationRequest10;
        return $this;
    }
}
