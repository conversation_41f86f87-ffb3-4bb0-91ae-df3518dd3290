<?php
/**
 * Created by PhpStorm.
 * User: CMA15450
 * Date: 18/01/2018
 * Time: 10:50
 */

namespace AppBundle\Entity;


use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;


#[ORM\Table(name: '`node_content`')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\NodeContentRepository::class)]
#[ORM\HasLifecycleCallbacks]
class NodeContent implements \JsonSerializable
{

    use TechnicalIdentifierTrait;

	#[ORM\Id]
    #[ORM\Column(type: 'integer')]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

	/**
     * @var Node $node
     */
    #[ORM\JoinColumn(name: 'node_id', referencedColumnName: 'id', nullable: false)]
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\Node::class, inversedBy: 'content')]
    private $node;


	#[ORM\Column(name: 'lang', type: 'string', length: 2)]
    private $lang;


	#[ORM\Column(name: 'title', type: 'string', length: 255, nullable: true)]
    private $title;

	#[ORM\Column(name: 'body', type: 'text', nullable: true)]
    private $body;

	#[ORM\Column(name: 'link', type: 'text', nullable: true)]
    private $link;

    #[ORM\Column(name: 'link_external', type: 'text', nullable: true)]
    private $linkExternal;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'external_link_type', type: 'boolean', nullable: false)]
    private $externalLinkType;

	#[ORM\Column(name: 'linkText', type: 'string', length: 100, nullable: true)]
    private $linkText;

	public function __construct()
    {
        $this->externalLinkType = false;
    }

    public function getId()
	{
		return $this->id;
	}

	public function setId($id)
	{
		$this->id = $id;
	}



	/**
	 * @return Node
	 */
	public function getNode(): ?Node
	{
		return $this->node;
	}

	/**
	 * @param $node
	 */
	public function setNode($node): void
	{
		$this->node = $node;
	}

	/**
	 * @return mixed
	 */
	public function getLang()
	{
		return $this->lang;
	}

	/**
	 * @param mixed $lang
	 */
	public function setLang($lang): void
	{
		$this->lang = $lang;
	}

	/**
	 * @return mixed
	 */
	public function getTitle()
	{
		return $this->title;
	}

	/**
	 * @param mixed $title
	 */
	public function setTitle($title): void
	{
		$this->title = $title;
	}

	/**
	 * @return string
	 */
	public function getBody()
	{
		return $this->body;
	}

	/**
	 * @param mixed $content
	 */
	public function setBody($content): void
	{
		$this->body = $content;
	}

	/**
	 * @return string
	 */
	public function getLink()
	{
		return $this->link;
	}

	/**
	 * @param mixed $link
	 */
	public function setLink($link): void
	{
		$this->link = $link;
	}

    /**
     * @return string
     */
    public function getLinkExternal()
    {
        return $this->linkExternal;
    }

    public function setLinkExternal($linkExternal): void
    {
        $this->linkExternal = $linkExternal;
    }

	/**
	 * @return string
	 */
	public function getLinkText()
	{
		return $this->linkText;
	}

	/**
	 * @param mixed $content
	 */
	public function setLinkText($linkText): void
	{
		$this->linkText = $linkText;
	}

    /**
     * @return bool
     */
    public function isExternalLinkType(): bool
    {
        return $this->externalLinkType;
    }

    public function setExternalLinkType($externalLinkType): void
    {
        $this->externalLinkType = $externalLinkType;
    }

	public function __toString()
	{
	    return $this->body;
	}

    public function jsonSerialize(): array
    {
        return [
            'lang' => $this->lang,
            'title' => $this->title,
            'body' => $this->body
        ];
    }
}
