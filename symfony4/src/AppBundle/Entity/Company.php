<?php

namespace AppBundle\Entity;

use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Open\TicketBundle\Entity\Ticket;
use Symfony\Component\Validator\Constraints as Assert;

// Les lignes suivantes sont nécessaires pour les test unitaires
/**
 * Company
 *
 *
 */
#[ORM\Table(name: 'company')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\CompanyRepository::class)]
#[ORM\HasLifecycleCallbacks]
class Company implements \JsonSerializable
{
    use TimestampedTrait;
    use ExportableEntityTrait;
    use TechnicalIdentifierTrait;

    const STATUS_INITIAL = 'initial';
    const STATUS_DRAFT = 'draft';
    const STATUS_PENDING = 'pending';
    const STATUS_VALID = 'valid';
    const STATUS_DISABLED = 'disabled';
    const STATUS_ACCEPTABLE = 'acceptable';
    const STATUS_REJECTED = 'rejected';

    const TYPE_BUYER = 'buyer';
    const TYPE_MERCHANT = 'merchant';

    const CREDIT_CARD = 'payment_mode.Prepayment_creditcard';
    const MONEY_TRANSFERT = 'payment_mode.Prepayment_moneytransfert';
    const TERM_PAYMENT = 'payment_mode.Termpayment_moneytransfert';

    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

    /**
     * One company has Many users.
     */
    #[ORM\OneToMany(targetEntity: \User::class, mappedBy: 'company')]
    private $users;

    /**
     * @var string
     */
    #[Assert\Length(max: 50)]
    #[Assert\NotBlank(groups: ['company'])]
    #[ORM\Column(name: 'name', type: 'string', length: 50, nullable: true)]
    private $name;

    /**
     * @var string
     */
    #[Assert\NotBlank(groups: ['company'])]
    #[ORM\Column(name: 'ident', type: 'string', length: 100, nullable: false)]
    private $identification;

    /**
     * @var string
     *
     *
     */
    #[Assert\Length(max: 50)]
    #[ORM\Column(name: 'comment', type: 'string', length: 50, nullable: true)]
    private $comment;

    /**
     * One company has Many sites.
     */
    #[ORM\OneToMany(targetEntity: \Site::class, mappedBy: 'company', cascade: ['persist'])]
    private $sites;

    /**
     * One company has one main contact.
     */
    #[Assert\Type(type: 'AppBundle\Entity\Contact')]
    #[Assert\Valid]
    #[ORM\JoinColumn(name: 'contact_main_id', referencedColumnName: 'id', nullable: true)]
    #[ORM\OneToOne(targetEntity: \Contact::class, cascade: ['persist'], orphanRemoval: true)]
    private $mainContact;

    /**
     * One company has one main contact.
     */
    #[Assert\Type(type: 'AppBundle\Entity\Contact')]
    #[Assert\Valid]
    #[ORM\JoinColumn(name: 'contact_billing_id', referencedColumnName: 'id', nullable: true)]
    #[ORM\OneToOne(targetEntity: \Contact::class, cascade: ['persist'], orphanRemoval: true)]
    private $billingContact;

    /**
     * One company has one main contact.
     */
    #[Assert\Type(type: 'AppBundle\Entity\Contact')]
    #[Assert\Valid]
    #[ORM\JoinColumn(name: 'contact_adv_id', referencedColumnName: 'id', nullable: true)]
    #[ORM\OneToOne(targetEntity: \Contact::class, cascade: ['persist'], orphanRemoval: true)]
    private $advContact;

    /**
     * One company has one main contact.
     */
    #[Assert\Type(type: 'AppBundle\Entity\Contact')]
    #[Assert\Valid]
    #[ORM\JoinColumn(name: 'contact_logistic_id', referencedColumnName: 'id', nullable: true)]
    #[ORM\OneToOne(targetEntity: \Contact::class, cascade: ['persist'])]
    private $logisticContact;

    /**
     * One company has one main address.
     */
    #[Assert\Type(type: 'AppBundle\Entity\Address')]
    #[Assert\Valid]
    #[ORM\JoinColumn(name: 'address_main_id', referencedColumnName: 'id', nullable: true)]
    #[ORM\OneToOne(targetEntity: \Address::class, cascade: ['persist'])]
    private $mainAddress;

    /**
     * One company has one biling address.
     *
     */
    #[Assert\Type(type: 'AppBundle\Entity\Address')]
    #[Assert\Valid]
    #[ORM\JoinColumn(name: 'address_billing_id', referencedColumnName: 'id', nullable: true)]
    #[ORM\OneToOne(targetEntity: \Address::class, cascade: ['persist'])]
    private $billingAddress;

    /**
     * @var float|null $totalSales
     */
    #[ORM\Column(name: 'total_sales', type: 'decimal', nullable: true)]
    private $totalSales;


    /**
     * @var DateTime
     *
     */
    #[ORM\Column(name: 'last_connexion', type: 'datetime', nullable: true)]
    private $lastConnexion;

    /**
     * list of attach documents
     */
    #[ORM\OneToMany(targetEntity: \AppBundle\Entity\Document::class, mappedBy: 'company', cascade: ['persist'])]
    private $documents;

    /**
     * @var ArrayCollection
     * One company has many tickets
     */
    #[ORM\OneToMany(targetEntity: \Open\TicketBundle\Entity\Ticket::class, mappedBy: 'company', cascade: ['persist'], orphanRemoval: true)]
    private $tickets;

    /**
     * @var Category
     */
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\Category::class)]
    private $category;

    /**
     * @var bool
     *
     */
    #[ORM\Column(name: 'enabled', type: 'boolean', nullable: false)]
    protected $enabled;

    /**
     * @var bool
     *
     */
    #[ORM\Column(name: 'cgu', type: 'boolean', nullable: false)]
    private $cgu;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'full_auto', type: 'boolean', options: ['default' => 0])]
    private $fullAuto = false;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'e_catalog', type: 'boolean', options: ['default' => 0])]
    private $eCatalog = false;

    /**
     * @var string
     *
     *
     */
    #[Assert\Length(max: 255)]
    #[Assert\NotBlank(groups: ['company'])]
    #[ORM\Column(name: 'billing_service', type: 'string', length: 255, nullable: true)]
    private $billingService;

    /**
     * @var string $izbergEmail
     */
    #[ORM\Column(name: 'izberg_email', type: 'string', length: 180)]
    private $izbergEmail;

    /**
     * @var string $izbergUsername
     */
    #[ORM\Column(name: 'izberg_username', type: 'string', length: 180, nullable: true)]
    private $izbergUsername;

    /**
     * @var string $izbergUserId
     */
    #[ORM\Column(name: 'izberg_user_id', type: 'string', nullable: true)]
    private $izbergUserId;

    #[ORM\Column(name: 'prepayment_creditcard_enabled', type: 'boolean', options: ['default' => 1])]
    private $prepaymentCreditcardEnabled;

    #[ORM\Column(name: 'prepayment_moneytransfert_enabled', type: 'boolean', options: ['default' => 0])]
    private $prepaymentMoneyTransfertEnabled;

    #[ORM\Column(name: 'termpayment_moneytransfert_enabled', type: 'boolean', options: ['default' => 0])]
    private $termpaymentMoneyTransfertEnabled;

    #[ORM\Column(name: 'termpayment_moneytransfert_pending', type: 'boolean', options: ['default' => 0])]
    private $termpaymentMoneyTransfertPending;

    #[ORM\Column(name: 'termpayment_moneytransfert_request_date', type: 'datetime', nullable: true)]
    private $termpaymentMoneyTransfertRequestDate;

    #[ORM\Column(name: 'termpayment_moneytransfert_accept_date', type: 'datetime', nullable: true)]
    private $termpaymentMoneyTransfertAcceptDate;

    #[ORM\Column(name: 'termpayment_money_transfert_deny_date', type: 'datetime', nullable: true)]
    private $termpaymentMoneyTransfertDenyDate;

    #[ORM\Column(name: 'termpayment_money_transfert_deny_reason', type: 'string', nullable: true)]
    private $termpaymentMoneyTransfertDenyReason;

    #[ORM\Column(name: 'termpayment_moneytransfert_izb_id', type: 'string', nullable: true)]
    private $termpaymentMoneyTransfertIzbergId;

    /**
     * @var ArrayCollection $ticketsAsRecipient the list of tickets for which user is recipient
     */
    #[ORM\OneToMany(targetEntity: \Open\TicketBundle\Entity\Ticket::class, mappedBy: 'recipient')]
    private $ticketsAsRecipient;

    /**
     * @var ArrayCollection $ticketsAsAuthor the list of tickets for which user is author
     */
    #[ORM\OneToMany(targetEntity: \Open\TicketBundle\Entity\Ticket::class, mappedBy: 'author')]
    private $ticketsAsAuthor;

    #[ORM\OneToMany(targetEntity: \AppBundle\Entity\CompanyCatalog::class, mappedBy: 'company')]
    private $companyCatalog;

    #[ORM\Column(name: 'id_customer_wps', type: 'string', nullable: true, options: ['default' => 'null'])]
    private $idCustomerWPS;

    #[ORM\Column(name: 'wps_code', type: 'string', nullable: true, options: ['default' => 'null'])]
    private $wpsCode;

    /**
     * @var bool
     *
     */
    #[ORM\Column(name: 'rejected', type: 'boolean', nullable: false)]
    private $rejected;

    #[ORM\Column(name: 'rejected_reason', type: 'string', length: 512, nullable: true)]
    private $rejectedReason;

    #[ORM\Column(name: 'deactivation_reason', type: 'string', length: 512, nullable: true)]
    private $deactivationReason;

    /**
     * @var bool
     *
     */
    #[ORM\Column(name: 'custom_attributes_exist', type: 'boolean', nullable: false)]
    private $customAttributesExist = false;

    /**
     * @var ArrayCollection
     */
    #[ORM\OneToMany(targetEntity: \AppBundle\Entity\UserBafvMerchantList::class, mappedBy: 'company')]
    private $merchantAuthorizations;

    #[ORM\OneToMany(targetEntity: \AppBundle\Entity\SpecificPrice::class, mappedBy: 'company')]
    private Collection $specificPrices;

    private $shippingAddress;

    #[ORM\Column(nullable: true)]
    private ?string $endpointUrl = null;

    /**
     * @var bool
     */
    private $billingUseMain;

    /**
     * Company constructor.
     */
    public function __construct()
    {
        $this->sites = new ArrayCollection();
        $this->users = new ArrayCollection();
        $this->documents = new ArrayCollection();
        $this->companyCatalog = new ArrayCollection();
        $this->tickets = new ArrayCollection();
        $this->ticketsAsRecipient = new ArrayCollection();
        $this->ticketsAsAuthor = new ArrayCollection();
        $this->merchantAuthorizations = new ArrayCollection();
        $this->specificPrices = new ArrayCollection();

        $this->billingUseMain = true;
        $this->billingService = '';
        $this->prepaymentCreditcardEnabled = true;
        $this->prepaymentMoneyTransfertEnabled = true;
        $this->termpaymentMoneyTransfertEnabled = false;
        $this->termpaymentMoneyTransfertPending = false;
        $this->termpaymentMoneyTransfertRequestDate = null;
        $this->termpaymentMoneyTransfertAcceptDate = null;
        $this->cgu = false;
        $this->termpaymentMoneyTransfertIzbergId = null;
        $this->idCustomerWPS = null;
        $this->rejected = false;
    }

    /**
     * @param int $id
     */
    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getIdCustomerWPS()
    {
        return $this->idCustomerWPS;
    }

    /**
     * @param mixed $idCustomerWPS
     */
    public function setIdCustomerWPS($idCustomerWPS): void
    {
        $this->idCustomerWPS = $idCustomerWPS;
    }

    /**
     * @return mixed
     */
    public function getTermpaymentMoneyTransfertIzbergId()
    {
        return $this->termpaymentMoneyTransfertIzbergId;
    }

    /**
     * @param mixed $termpaymentMoneyTransfertIzbergId
     */
    public function setTermpaymentMoneyTransfertIzbergId($termpaymentMoneyTransfertIzbergId)
    {
        $this->termpaymentMoneyTransfertIzbergId = $termpaymentMoneyTransfertIzbergId;
    }

    /**
     * @return mixed
     */
    public function getPrepaymentCreditcardEnabled()
    {
        return $this->prepaymentCreditcardEnabled;
    }

    /**
     * @param mixed $prepaymentCreditcardEnabled
     */
    public function setPrepaymentCreditcardEnabled($prepaymentCreditcardEnabled
    ): void
    {
        $this->prepaymentCreditcardEnabled = $prepaymentCreditcardEnabled;
    }

    /**
     * @return mixed
     */
    public function getPrepaymentMoneyTransfertEnabled()
    {
        return $this->prepaymentMoneyTransfertEnabled;
    }

    /**
     * @param mixed $prepaymentMoneyTransfertEnabled
     */
    public function setPrepaymentMoneyTransfertEnabled(
        $prepaymentMoneyTransfertEnabled
    ): void
    {
        $this->prepaymentMoneyTransfertEnabled = $prepaymentMoneyTransfertEnabled;
    }

    /**
     * @return mixed
     */
    public function getTermpaymentMoneyTransfertEnabled()
    {
        return $this->termpaymentMoneyTransfertEnabled;
    }

    /**
     * @param mixed $termpaymentMoneyTransfertEnabled
     */
    public function setTermpaymentMoneyTransfertEnabled(
        $termpaymentMoneyTransfertEnabled
    ): void
    {
        $this->termpaymentMoneyTransfertEnabled = $termpaymentMoneyTransfertEnabled;
    }

    /**
     * @return mixed
     */
    public function getTermpaymentMoneyTransfertPending()
    {
        return $this->termpaymentMoneyTransfertPending;
    }

    /**
     * @param mixed $termpaymentMoneyTransfertPending
     */
    public function setTermpaymentMoneyTransfertPending(
        $termpaymentMoneyTransfertPending
    ): void
    {
        $this->termpaymentMoneyTransfertPending = $termpaymentMoneyTransfertPending;
    }

    /**
     * @return mixed
     */
    public function getTermpaymentMoneyTransfertRequestDate()
    {
        return $this->termpaymentMoneyTransfertRequestDate;
    }

    /**
     * @param mixed $termpaymentMoneyTransfertRequestDate
     */
    public function setTermpaymentMoneyTransfertRequestDate($termpaymentMoneyTransfertRequestDate): void
    {
        $this->termpaymentMoneyTransfertRequestDate = $termpaymentMoneyTransfertRequestDate;
    }

    /**
     * @return mixed
     */
    public function getTermpaymentMoneyTransfertAcceptDate()
    {
        return $this->termpaymentMoneyTransfertAcceptDate;
    }

    /**
     * @param mixed $termpaymentMoneyTransfertRequestDate
     */
    public function setTermpaymentMoneyTransfertAcceptDate($tertermpaymentMoneyTransfertAcceptDate): void
    {
        $this->termpaymentMoneyTransfertAcceptDate = $tertermpaymentMoneyTransfertAcceptDate;
    }


    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @return mixed
     */
    public function getUsers()
    {
        return $this->users;
    }

    /**
     * @return mixed
     */
    public function getenabled_users()
    {
        $ret = [];
        foreach ($this->users as $user) {
            if ($user->isEnabled()) {
                $ret[] = $user;
            }
        }

        return $ret;
    }


    /**
     * @param mixed $users
     */
    public function setUsers($users)
    {
        $this->users = $users;
    }

    public function addUser(User $user)
    {
        $user->setCompany($this);
        $this->users->add($user);

        return $this;
    }


    public function removeUser(User $user)
    {
        $this->sites->removeElement($user);
    }


    /**
     * Set name
     *
     * @param string $name
     *
     * @return Company
     */
    public function setName($name)
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Get name
     *
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }


    /**
     * @return string
     */
    public function getComment()
    {
        return $this->comment;
    }

    /**
     * @param string $comment
     */
    public function setComment($comment)
    {
        $this->comment = $comment;
    }


    /**
     * @return mixed
     */
    public function getSites()
    {
        return $this->sites;
    }

    /**
     * @param mixed $sites
     */
    public function setSites($sites)
    {
        $this->sites = $sites;
    }

    public function addSite(Site $site)
    {
        $site->setCompany($this);
        $this->sites->add($site);

        return $this;
    }

    public function removeSite(Site $site)
    {
        $this->sites->removeElement($site);
    }

    public function getMainAddress(): ?Address
    {
        return $this->mainAddress;
    }

    /**
     * @param mixed $mainAddress
     */
    public function setMainAddress($mainAddress)
    {
        $this->mainAddress = $mainAddress;
    }

    public function getBillingAddress(): ?Address
    {
        return $this->billingAddress;
    }

    public function setBillingAddress($billingAddress)
    {
        $this->billingAddress = $billingAddress;
    }

    /**
     * @return string
     */
    public function getStatus()
    {
        if ($this->isRejected()) {
            return self::STATUS_REJECTED;
        }

        switch ($this->getStep()) {
            case 2:
                $ret = self::STATUS_PENDING;
                break;
            case 3:
                $ret = self::STATUS_VALID;
                break;
            default:
                $ret = self::STATUS_INITIAL;
                break;
        }
        return $ret;
    }

    public function __toString()
    {
        if ($this->name) {
            return $this->name;
        } else {
            return 'ND';
        }
    }


    /**
     * @return mixed
     */
    public function getLastConnexion()
    {
        return $this->lastConnexion;
    }

    /**
     * @param mixed $lastConnexion
     */
    public function setLastConnexion($lastConnexion)
    {
        $this->lastConnexion = $lastConnexion;
    }

    /**
     * @return string
     */
    public function getIdentification()
    {
        return $this->identification;
    }

    /**
     * @param string $ident
     */
    public function setIdentification($ident)
    {
        $this->identification = $ident;
    }


    /**
     * @return Category
     */
    public function getCategory()
    {
        return $this->category;
    }

    /**
     * @param $category
     */
    public function setCategory($category): void
    {
        $this->category = $category;
    }

    public function getMainContact(): ?Contact
    {
        return $this->mainContact;
    }

    /**
     * @param mixed $mainContact
     */
    public function setMainContact($mainContact)
    {
        $this->mainContact = $mainContact;
    }

    /**
     * @return mixed
     */
    public function getBillingContact()
    {
        return $this->billingContact;
    }

    /**
     * @param mixed $billingContact
     */
    public function setBillingContact($billingContact)
    {
        $this->billingContact = $billingContact;
    }

    /**
     * @return mixed
     */
    public function getAdvContact()
    {
        return $this->advContact;
    }

    /**
     * @param mixed $advContact
     */
    public function setAdvContact($advContact)
    {
        $this->advContact = $advContact;
    }

    /**
     * @return mixed
     */
    public function getLogisticContact()
    {
        return $this->logisticContact;
    }

    /**
     * @param mixed $logisticContact
     */
    public function setLogisticContact($logisticContact)
    {
        $this->logisticContact = $logisticContact;
    }


    /**
     * @return bool whether the company is enabled
     */
    public function isEnabled()
    {
        return $this->enabled;
    }

    /**
     * @param bool $enabled
     */
    public function setEnabled($enabled)
    {
        $this->enabled = $enabled;
    }

    /**
     * @return mixed
     */
    public function getCgu()
    {
        return $this->cgu;
    }

    /**
     * @param mixed $cgu
     */
    public function setCgu($cgu)
    {
        $this->cgu = $cgu;
    }

    public function getFullAuto(): ?bool
    {
        return $this->fullAuto;
    }

    public function setFullAuto(bool $fullAuto)
    {
        $this->fullAuto = $fullAuto;
    }

    public function getECatalog(): ?bool
    {
        return $this->eCatalog;
    }

    public function setECatalog(bool $eCatalog)
    {
        $this->eCatalog = $eCatalog;
    }

    /**
     * @return mixed
     */
    public function getbillingService()
    {
        return $this->billingService;
    }

    /**
     * @param mixed $billingService
     */
    public function setbillingService($billingService)
    {
        $this->billingService = $billingService;
    }

    /**
     * @return mixed
     */
    public function getStep()
    {
        $hasMainContact = !empty($this->getMainContact());
        $cguAccepted = ($this->getCgu() != 0);
        $hasMainAddress = !empty($this->getMainAddress());
        $companyActive = $this->getEnabled();

        $step = 0;
        if ($hasMainContact) {
            $step = 1;

            if ($companyActive && $cguAccepted && $hasMainAddress) {
                $step = 3;
            } else if ($cguAccepted && $hasMainAddress) {
                $step = 2;
            }
        }
        return $step;
    }

    public function getLocale()
    {
        if ($this->getMainAddress() != null) {
            return $this->getMainAddress()->getCountry()->getLocale();
        } else {
            return null;
        }
    }

    /**
     * @return string|null
     */
    public function getIzbergEmail(): ?string
    {
        return $this->izbergEmail;
    }

    /**
     * @param string $izbergEmail
     */
    public function setIzbergEmail(?string $izbergEmail): void
    {
        $this->izbergEmail = $izbergEmail;
    }

    /**
     * @return string
     */
    public function getIzbergUsername(): ?string
    {
        return $this->izbergUsername;
    }

    /**
     * @param string $izbergUsername
     */
    public function setIzbergUsername(string $izbergUsername): void
    {
        $this->izbergUsername = $izbergUsername;
    }

    /**
     * @return string
     */
    public function getIzbergUserId(): ?string
    {
        return $this->izbergUserId;
    }

    /**
     * @param string $izbergUserId
     */
    public function setIzbergUserId(string $izbergUserId): void
    {
        $this->izbergUserId = $izbergUserId;
    }


    /**
     * generate a uniq
     *
     * @param $domain
     */
    public function generateIzbergEmail(string $domain)
    {
        $this->setIzbergEmail(uniqid() . $domain);
    }

    /**
     * @return mixed
     */
    public function getTickets()
    {
        return $this->tickets;
    }

    /**
     * @param mixed $tickets
     */
    public function setTickets($tickets): void
    {
        $this->tickets = $tickets;
    }

    /**
     * assign a new ticket to this company
     *
     * @param Ticket $ticket
     */
    public function addTicket(Ticket $ticket): void
    {
        //add the ticket to the list
        $this->tickets->add($ticket);
        $ticket->setCompany($this);
    }

    /***
     * @return bool
     */
    public function canAccessSites()
    {
        return ($this->getStep() === 3);
    }

    /***
     * @return bool
     */
    public function canAccessUsers()
    {
        return $this->canAccessSites();
    }

    /***
     * @return bool
     */
    public function canAccessPaymentModes()
    {
        return $this->canAccessSites();
    }

    public function jsonSerialize(): array
    {
        return [
            'name' => $this->name,
            'identification' => $this->identification,
            'comment' => $this->comment,
            'status' => $this->getStatus(),
            'step' => $this->getStep(),
            'enabled' => $this->enabled,
        ];
    }

    public function getEnabled()
    {
        return $this->enabled;
    }

    public function getDocuments()
    {
        return $this->documents;
    }

    public function setDocuments($documents): void
    {
        $this->documents = $documents;
    }

    public function addDocument(Document $document): self
    {
        $this->documents[] = $document;

        return $this;
    }

    public function removeDocument(Document $document)
    {
        return $this->documents->removeElement($document);
    }

    public function removeTicket(Ticket $ticket): bool
    {
        return $this->tickets->removeElement($ticket);
    }

    public function addTicketsAsRecipient(Ticket $ticketsAsRecipient): self
    {
        $this->ticketsAsRecipient->add($ticketsAsRecipient);

        return $this;
    }

    public function removeTicketsAsRecipient(Ticket $ticketsAsRecipient): bool
    {
        return $this->ticketsAsRecipient->removeElement($ticketsAsRecipient);
    }

    public function addTicketsAsAuthor(Ticket $ticketsAsAuthor): self
    {
        $this->ticketsAsAuthor->add($ticketsAsAuthor);

        return $this;
    }

    public function removeTicketsAsAuthor(Ticket $ticketsAsAuthor): bool
    {
        return $this->ticketsAsAuthor->removeElement($ticketsAsAuthor);
    }

    public function getTicketsAsRecipient(): ArrayCollection
    {
        return $this->ticketsAsRecipient;
    }

    public function setTicketsAsRecipient($ticketsAsRecipient): void
    {
        $this->ticketsAsRecipient = $ticketsAsRecipient;
    }

    public function getTicketsAsAuthor(): ArrayCollection
    {
        return $this->ticketsAsAuthor;
    }

    public function setTicketsAsAuthor($ticketsAsAuthor): void
    {
        $this->ticketsAsAuthor = $ticketsAsAuthor;
    }

    public function getCompanyCatalog(): ArrayCollection
    {
        return $this->companyCatalog;
    }

    public function setCompanyCatalog(array $companyCatalog): void
    {
        $this->companyCatalog = $companyCatalog;
    }

    public function getWpsCode()
    {
        return $this->wpsCode;
    }

    public function setWpsCode($wpsCode): void
    {
        $this->wpsCode = $wpsCode;
    }


    /**
     * @return Country|null
     */
    public function getFiscalCountry()
    {
        /** @var Address $mainAddress */
        $mainAddress = $this->getMainAddress();
        if ($mainAddress) {
            return $mainAddress->getCountry();
        }

        return null;

    }

    /**
     * @return mixed
     */
    public function getTermpaymentMoneyTransfertDenyReason()
    {
        return $this->termpaymentMoneyTransfertDenyReason;
    }

    /**
     * @param mixed $termpaymentMoneyTransfertDenyReason
     */
    public function setTermpaymentMoneyTransfertDenyReason($termpaymentMoneyTransfertDenyReason)
    {
        $this->termpaymentMoneyTransfertDenyReason = $termpaymentMoneyTransfertDenyReason;
    }

    /**
     * @return mixed
     */
    public function getTermpaymentMoneyTransfertDenyDate()
    {
        return $this->termpaymentMoneyTransfertDenyDate;
    }

    /**
     * @param mixed $termpaymentMoneyTransfertDenyDate
     */
    public function setTermpaymentMoneyTransfertDenyDate($termpaymentMoneyTransfertDenyDate)
    {
        $this->termpaymentMoneyTransfertDenyDate = $termpaymentMoneyTransfertDenyDate;
    }

    /**
     * @return bool
     */
    public function isRejected(): bool
    {
        return $this->rejected;
    }

    /**
     * @param bool $rejected
     */
    public function setRejected(bool $rejected)
    {
        $this->rejected = $rejected;
    }

    /**
     * @return mixed
     */
    public function getRejectedReason()
    {
        return $this->rejectedReason;
    }

    /**
     * @param mixed $rejectedReason
     */
    public function setRejectedReason($rejectedReason)
    {
        $this->rejectedReason = $rejectedReason;
    }

    /**
     * @return mixed
     */
    public function getDeactivationReason()
    {
        return $this->deactivationReason;
    }

    /**
     * @param mixed $deactivationReason
     */
    public function setDeactivationReason($deactivationReason)
    {
        $this->deactivationReason = $deactivationReason;
    }

    /**
     * @return bool
     */
    public function isCustomAttributesExist(): bool
    {
        return $this->customAttributesExist;
    }

    /**
     * @param bool $customAttributesExist
     */
    public function setCustomAttributesExist(bool $customAttributesExist)
    {
        $this->customAttributesExist = $customAttributesExist;
    }

    public function getSpecificPrices(): Collection
    {
        return $this->specificPrices;
    }

    public function setSpecificPrices(Collection $specificPrices): self
    {
        $this->specificPrices = $specificPrices;
        return $this;
    }

    public function addSpecificPrice(SpecificPrice $price): self
    {
        if (!$this->specificPrices->contains($price)) {
            $this->specificPrices->add($price);
        }

        return $this;
    }

    /**
     * @return bool
     */
    public function isVendor(): bool
    {
        return !(in_array($this->getCategory()->getLabel(), [Category::CATEGORY_NEW]));
    }

    /**
     * A company can see the price of a merchant if it can do business with the merchant and is authorized to access the catalog
     *
     * @param \AppBundle\Model\Merchant $merchant
     *
     * @return bool
     */
    public function canSeePriceOfMerchant(\AppBundle\Model\Merchant $merchant): bool
    {
        $canDoBusiness = $this->canDoBusiness($merchant);

        // if BAFV
        if ($this->isVendor()) {
            /** @var UserBafvMerchantList $merchantAuthorization */
            foreach ($this->merchantAuthorizations as $merchantAuthorization) {
                // check if authorized
                if ($merchantAuthorization->getMerchantId() === $merchant->getId()) {
                    return $canDoBusiness;
                }
            }

            return false;
        }

        return $canDoBusiness;
    }

    public function hasValidSpecificPricesForOffer(string $vendorOfferReference, string $offerIncoterm, string $incotermCountry): bool
    {
        return !$this->specificPrices->filter(function (SpecificPrice $price) use ($vendorOfferReference, $offerIncoterm, $incotermCountry) {
            return (
                strtolower($price->getVendorReference()) === strtolower($vendorOfferReference) &&
                strtolower($price->getIncoterm()) === strtolower($offerIncoterm) &&
                strtolower($price->getCountry()) === strtolower($incotermCountry) &&
                ($price->getValidityDate() === null || ($price->getValidityDate() >= (new DateTime())))
            );
        })->isEmpty();
    }

    public function canDoBusiness(\AppBundle\Model\Merchant $merchant): bool
    {
        $companyBuyEverywhere = $this->getFiscalCountry()?->isBusinessEverywhere();
        $merchantSellEverywhere = $merchant?->getCountry()?->isBusinessEverywhere();
        return ($companyBuyEverywhere || $merchantSellEverywhere);
    }

    public function getShippingAddress()
    {
        return $this->shippingAddress;
    }

    public function setShippingAddress($shippingAddress): Company
    {
        $this->shippingAddress = $shippingAddress;
        return $this;
    }

    public function getEndpointUrl(): ?string
    {
        return $this->endpointUrl;
    }

    public function setEndpointUrl(?string $endpointUrl): self
    {
        $this->endpointUrl = $endpointUrl;

        return $this;
    }

    /**
     * Return true if the company has at least one user API and enabled
     */
    public function hasUserApi(): bool
    {
        return $this->users
            ->filter(fn (User $user) => $user->isBuyerApi())
            ->filter(fn (User $user) => $user->isEnabled())
            ->count() > 0;
    }

    public function getBillingOrMainAddress(): ?Address
    {
        return $this->billingAddress ?? $this->mainAddress;
    }
}
