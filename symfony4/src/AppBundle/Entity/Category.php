<?php
/**
 * Created by PhpStorm.
 * User: CMA15450
 * Date: 10/01/2018
 * Time: 16:48
 */

namespace AppBundle\Entity;

use AppBundle\Enum\CompanyTypeEnum;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * Company
 */
#[UniqueEntity(fields: 'id')]
#[ORM\Table(name: 'category')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\CategoryRepository::class)]
#[ORM\HasLifecycleCallbacks]
class Category implements \JsonSerializable
{

	use TimestampedTrait;
    use TechnicalIdentifierTrait;

    public const CATEGORY_BAFV = 'category_bafv';
    public const CATEGORY_NEW = 'category_new';

	/**
     * @var int
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    private $id;

	/**
     * @var String
     */
    #[ORM\Column(name: 'label', type: 'string', length: 50)]
    private $label;


	public function setId($id)
	{
		$this->id = $id;

	}

	/**
	 * Get id
	 *
	 * @return int
	 */
	public function getId()
	{
		return $this->id;
	}

	/**
	 * @return String
	 */
	public function getLabel(): String
	{
		return $this->label;
	}

	/**
	 * @param String $label
	 */
	public function setLabel(String $label): void
	{
		$this->label = $label;
	}

	public function __toString()
	{
		return 'category.' . $this->label;
	}
    public function jsonSerialize(): array
    {
        return [
            "label" =>$this->label
        ];
    }

}
