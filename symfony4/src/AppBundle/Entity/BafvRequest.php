<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * BafvRequest
 */
#[ORM\Table(name: 'bafv_request')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\BafvRequestRepository::class)]
class BafvRequest
{
    public const STATUS_REJECT = 0;
    public const STATUS_PENDING = 1;
    public const STATUS_ACCEPTED = 2;

    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

    #[ORM\Column(name: 'status', type: 'smallint')]
    private $status = self::STATUS_PENDING;

    #[ORM\JoinColumn(name: 'company_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\Company::class)]
    private $company;

    #[ORM\JoinColumn(name: 'user_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\User::class)]
    private $user;

    #[ORM\Column(name: 'merchant_id', type: 'integer')]
    private $merchantId;

    #[ORM\Column(name: 'merchant_name', type: 'string')]
    private $merchantName;

    #[ORM\Column(name: 'created_at', type: 'datetime', nullable: true)]
    private $createdAt;

    #[ORM\Column(name: 'rejection_reason', type: 'text', nullable: true)]
    private $rejectionReason;

    public function getId(): int
    {
        return $this->id;
    }

    public function setStatus(int $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getStatus(): int
    {
        return $this->status;
    }

    /**
     * Convert status to string to be used in translation keys
     * example: back.merchant.list.status.rejected
     *
     * @return string
     */
    public function getStatusText(): string
    {
        $mapper = [
            self::STATUS_REJECT => 'rejected',
            self::STATUS_PENDING => 'pending',
            self::STATUS_ACCEPTED => 'accepted',
        ];

        return $mapper[$this->status] ?? '';
    }

    public function getCompany(): Company
    {
        return $this->company;
    }

    public function setCompany(Company $company): self
    {
        $this->company = $company;
        return $this;
    }

    public function getUser():User
    {
        return $this->user;
    }

    public function setUser(User $user):self
    {
        $this->user = $user;
        return $this;
    }

    public function getMerchantId(): int
    {
        return $this->merchantId;
    }

    public function setMerchantId($merchantId): self
    {
        $this->merchantId = $merchantId;
        return $this;
    }

    public function getMerchantName()
    {
        return $this->merchantName;
    }

    public function setMerchantName($merchantName)
    {
        $this->merchantName = $merchantName;
        return $this;
    }

    public function getCreatedAt(): \DateTime
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTime $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getRejectionReason(): ?string
    {
        return $this->rejectionReason;
    }

    public function setRejectionReason(?string $rejectionReason): self
    {
        $this->rejectionReason = $rejectionReason;
        return $this;
    }
}
