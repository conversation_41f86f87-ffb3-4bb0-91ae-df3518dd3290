<?php


namespace AppBundle\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Table(name: 'purchase_request')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\PurchaseRequestRepository::class)]
class PurchaseRequest
{
    public const STATUS_INITIALISED = 'initialised';

    public const STATUS_ONGOING = 'ongoing';

    public const STATUS_ARCHIVED = 'archived';

    public const CSV_COLUMN_PER_ROW = 12;

    /**
     * @var int
     */
    #[ORM\Id]
    #[ORM\Column(type: 'integer')]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

    /**
     * @var User
     */
    #[ORM\JoinColumn(name: 'user_id', referencedColumnName: 'id', nullable: false)]
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\User::class)]
    private $user;

    /**
     * @var Collection
     */
    #[ORM\OneToMany(targetEntity: \AppBundle\Entity\PurchaseRequestItem::class, mappedBy: 'purchaseRequest', cascade: ['persist'])]
    private $items;

    /**
     * @var string
     */
    #[Assert\NotBlank]
    #[ORM\Column(name: 'status', type: 'string', length: 50, nullable: false)]
    private $status;

    /**
     * @var BddFile
     */
    #[ORM\JoinColumn(name: 'import_file_id', referencedColumnName: 'id', nullable: true)]
    #[ORM\OneToOne(targetEntity: \AppBundle\Entity\BddFile::class, cascade: ['persist'])]
    protected $importFile;

    /**
     * @var BddFile
     */
    #[ORM\JoinColumn(name: 'not_found_items_file_id', referencedColumnName: 'id', nullable: true)]
    #[ORM\OneToOne(targetEntity: \AppBundle\Entity\BddFile::class, cascade: ['persist'])]
    protected $notFoundItemsFile;

    #[ORM\Column(name: 'created_at', type: 'datetime', nullable: true)]
    private $createdAt;

    public function __construct()
    {
        $this->items = new ArrayCollection();
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     * @return PurchaseRequest
     */
    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getCreatedAt(): \DateTime
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTime $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    /**
     * @return BddFile|null
     */
    public function getImportFile() :?BddFile
    {
        return $this->importFile;
    }

    /**
     * @param BddFile $importFile
     * @return $this
     */
    public function setImportFile(BddFile $importFile): self
    {
        $this->importFile = $importFile;
        return $this;
    }

    /**
     * @return BddFile|null
     */
    public function getNotFoundItemsFile():?BddFile
    {
        return $this->notFoundItemsFile;
    }

    /**
     * @param BddFile $notFoundItemsFile
     * @return $this
     */
    public function setNotFoundItemsFile(BddFile $notFoundItemsFile): self
    {
        $this->notFoundItemsFile = $notFoundItemsFile;
        return $this;
    }

    /**
     * @param PurchaseRequestItem $item
     * @return $this
     */
    public function addItem(PurchaseRequestItem $item): self
    {
        $this->items[] = $item;
        return $this;
    }

    public function getItems(): array
    {
        return $this->items->toArray();
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function setUser(User $user): self
    {
        $this->user = $user;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }
}
