<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Table(name: 'merchant')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\MerchantRepository::class)]
#[ORM\HasLifecycleCallbacks]
class Merchant
{

    const STATUS_ACCEPTED = 'accepted';
    const STATUS_REJECTED = 'rejected';
    const STATUS_PENDING = 'pending';

    #[ORM\Id]
    #[ORM\Column(type: 'integer')]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

    #[Assert\NotBlank]
    #[Assert\Length(max: 50)]
    #[ORM\Column(name: 'firstname', type: 'string', length: 50)]
    private $firstname;

    #[Assert\NotBlank]
    #[Assert\Length(max: 50)]
    #[ORM\Column(name: 'lastname', type: 'string', length: 50)]
    private $lastname;

    #[Assert\Length(max: 50)]
    #[Assert\Regex(pattern: '/^[\+]?[0-9]+$/', match: true, message: 'form.contact.phone_regex')]
    #[ORM\Column(name: 'phone_number', type: 'string', length: 50, nullable: true)]
    private $phoneNumber;

    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\Country::class)]
    private $country;

    #[Assert\NotBlank]
    #[Assert\Length(max: 50)]
    #[ORM\Column(name: 'currency', type: 'string', length: 50)]
    private $currency;

    #[Assert\Length(max: 50)]
    #[Assert\NotBlank]
    #[ORM\Column(name: 'name', type: 'string', length: 50, nullable: true)]
    private $name;

    #[Assert\NotBlank]
    #[ORM\Column(name: 'ident', type: 'string', length: 100, nullable: false)]
    private $identification;

    #[Assert\NotBlank]
    #[ORM\Column(name: 'email', type: 'string', length: 100, nullable: false)]
    private $email;

    #[Assert\NotBlank]
    #[ORM\Column(name: 'password', type: 'string', nullable: false)]
    private $password;

    #[Assert\NotBlank]
    #[ORM\Column(name: 'status', type: 'string', length: 50, nullable: false)]
    private $status;

    #[ORM\Column(name: 'registration_date', type: 'datetime', nullable: true)]
    private $registrationDate;

    /**
     * One cart have ont user that created it
     */
    #[ORM\JoinColumn(name: 'action_by_id', referencedColumnName: 'id', nullable: true)]
    #[ORM\ManyToOne(targetEntity: \User::class)]
    private $actionBy;

    #[ORM\Column(name: 'action_at', type: 'datetime', nullable: true)]
    private $updatedAt;

    /**
     * @var int
     */
    #[ORM\Column(name: 'izberg_id', type: 'integer', nullable: true)]
    private $izbergId;


    #[ORM\Column(name: 'rejected_reason', type: 'string', length: 512, nullable: true)]
    private $rejectedReason;

    /**
     * @var bool
     *
     */
    #[ORM\Column(name: 'tva_checked', type: 'boolean', options: ['default' => 1])]
    private $tvaChecked;

    #[ORM\Column(name: 'izb_status', type: 'string', length: 50, nullable: true)]
    private $izbStatus;

    #[ORM\Column(name: 'izb_merchant_name', type: 'string', length: 255, nullable: true)]
    private $izbMerchantName;

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param $id
     * @return $this
     */
    public function setId($id): self
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getFirstname()
    {
        return $this->firstname;
    }

    /**
     * @param mixed $firstname
     */
    public function setFirstname($firstname)
    {
        $this->firstname = $firstname;
    }

    /**
     * @return mixed
     */
    public function getLastname()
    {
        return $this->lastname;
    }

    /**
     * @param mixed $lastname
     */
    public function setLastname($lastname)
    {
        $this->lastname = $lastname;
    }

    /**
     * @return mixed
     */
    public function getPhoneNumber()
    {
        return $this->phoneNumber;
    }

    /**
     * @param mixed $phoneNumber
     */
    public function setPhoneNumber($phoneNumber)
    {
        $this->phoneNumber = $phoneNumber;
    }

    public function getCountry():? Country
    {
        return $this->country;
    }

    /**
     * @param mixed $country
     */
    public function setCountry($country)
    {
        $this->country = $country;
    }

    /**
     * @return mixed
     */
    public function getCurrency()
    {
        return $this->currency;
    }

    /**
     * @param mixed $currency
     */
    public function setCurrency($currency)
    {
        $this->currency = $currency;
    }

    /**
     * @return mixed
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * @param mixed $name
     */
    public function setName($name)
    {
        $this->name = $name;
    }

    /**
     * @return mixed
     */
    public function getIdentification()
    {
        return $this->identification;
    }

    /**
     * @param mixed $identification
     */
    public function setIdentification($identification)
    {
        $this->identification = $identification;
    }

    /**
     * @return mixed
     */
    public function getEmail()
    {
        return $this->email;
    }

    /**
     * @param mixed $email
     */
    public function setEmail($email)
    {
        $this->email = $email;
    }

    /**
     * @return mixed
     */
    public function getPassword()
    {
        return $this->password;
    }

    /**
     * @param mixed $password
     */
    public function setPassword($password)
    {
        $this->password = $password;
    }

    /**
     * @return mixed
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @param mixed $status
     */
    public function setStatus($status)
    {
        $this->status = $status;
    }

    /**
     * @return mixed
     */
    public function getActionBy()
    {
        return $this->actionBy;
    }

    /**
     * @param mixed $actionBy
     */
    public function setActionBy($actionBy)
    {
        $this->actionBy = $actionBy;
    }

    /**
     * @return mixed
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }

    /**
     * @param mixed $updatedAt
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;
    }

    public function getIzbergId(): ?int
    {
        return $this->izbergId;
    }

    /**
     * @param int $izbergId
     * @return $this
     */
    public function setIzbergId(int $izbergId): self
    {
        $this->izbergId = $izbergId;
        return $this;
    }

    public function isPending(): bool
    {
        return ($this->getStatus() === self::STATUS_PENDING);
    }

    public function isRejected(): bool
    {
        return ($this->getStatus() === self::STATUS_REJECTED);
    }

    public function toArray()
    {
        return [
            'id' => $this->getId(),
            'firstname' => $this->getFirstname(),
            'lastname' => $this->getLastname(),
            'phoneNumber' => $this->getPhoneNumber(),
            'country' => ($this->getCountry()) ? $this->getCountry()->getCode() : null,
            'currency' => $this->getCurrency(),
            'name' => $this->getName(),
            'identification' => $this->getIdentification(),
            'email' => $this->getEmail(),
            'status' => $this->getStatus(),
        ];
    }

    /**
     * @return mixed
     */
    public function getRegistrationDate()
    {
        return $this->registrationDate;
    }

    /**
     * @param mixed $registrationDate
     */
    public function setRegistrationDate($registrationDate)
    {
        $this->registrationDate = $registrationDate;
    }

    /**
     * @return mixed
     */
    public function getRejectedReason()
    {
        return $this->rejectedReason;
    }

    /**
     * @param mixed $rejectedReason
     */
    public function setRejectedReason($rejectedReason)
    {
        $this->rejectedReason = $rejectedReason;
    }

    /**
     * @return bool
     */
    public function isTvaChecked(): bool
    {
        return $this->tvaChecked;
    }

    /**
     * @param bool $tvaChecked
     */
    public function setTvaChecked(bool $tvaChecked)
    {
        $this->tvaChecked = $tvaChecked;
    }

    /**
     * @return mixed
     */
    public function getIzbStatus()
    {
        return $this->izbStatus;
    }

    /**
     * @param mixed $izbStatus
     */
    public function setIzbStatus($izbStatus): void
    {
        $this->izbStatus = $izbStatus;
    }

    /**
     * @return mixed
     */
    public function getIzbMerchantName()
    {
        return $this->izbMerchantName;
    }

    /**
     * @param mixed $izbMerchantName
     */
    public function setIzbMerchantName($izbMerchantName): void
    {
        $this->izbMerchantName = $izbMerchantName;
    }
}
