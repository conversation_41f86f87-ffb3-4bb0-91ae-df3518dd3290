<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * HealthCheck
 */
#[ORM\Table(name: 'health_check')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\HealthCheckRepository::class)]
class HealthCheck
{
    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

    /**
     * @var string
     */
    #[ORM\Column(name: 'type', type: 'string', length: 255)]
    private $type;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'status', type: 'boolean')]
    private $status;

    /**
     * @var \DateTime
     */
    #[ORM\Column(name: 'check_date', type: 'datetime')]
    private $checkDate;

    /**
     * @var string|null
     */
    #[ORM\Column(name: 'extra_info', type: 'string', length: 255, nullable: true)]
    private $extraInfo;


    /**
     * Get id.
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set type.
     *
     * @param string $type
     *
     * @return HealthCheck
     */
    public function setType($type)
    {
        $this->type = $type;

        return $this;
    }

    /**
     * Get type.
     *
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Set status.
     *
     * @param bool $status
     *
     * @return HealthCheck
     */
    public function setStatus($status)
    {
        $this->status = $status;

        return $this;
    }

    /**
     * Get status.
     *
     * @return bool
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * Set checkDate.
     *
     * @param \DateTime $checkDate
     *
     * @return HealthCheck
     */
    public function setCheckDate($checkDate)
    {
        $this->checkDate = $checkDate;

        return $this;
    }

    /**
     * Get checkDate.
     *
     * @return \DateTime
     */
    public function getCheckDate()
    {
        return $this->checkDate;
    }

    /**
     * Set extraInfo.
     *
     * @param string|array|null $extraInfo
     *
     * @return HealthCheck
     */
    public function setExtraInfo($extraInfo = null)
    {
        $this->extraInfo = $extraInfo;

        return $this;
    }

    /**
     * Get extraInfo.
     *
     * @return string|null
     */
    public function getExtraInfo()
    {
        return $this->extraInfo;
    }
}
