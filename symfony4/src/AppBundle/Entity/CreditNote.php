<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'credit_notes')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\CreditNoteRepository::class)]
class CreditNote
{
    public const CURRENCY_EUR = 'EUR';
    public const CURRENCY_USD = 'USD';

    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

    /**
     * @var int
     */
    #[ORM\Column(name: 'izberg_id', type: 'integer', nullable: false)]
    private $izbergId;

    /**
     * @var string
     */
    #[ORM\Column(name: 'number_id', type: 'string', nullable: true)]
    private $numberId;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'pdf_file', type: 'boolean', nullable: false)]
    private $pdfFile;

    /**
     * @var string
     */
    #[ORM\Column(name: 'issuer_name', type: 'string', nullable: true)]
    private $issuerName;

    /**
     * @var \DateTimeImmutable
     */
    #[ORM\Column(name: 'created_on', type: 'datetime_immutable', nullable: false)]
    private $createdOn;

    /**
     * @var int
     */
    #[ORM\Column(name: 'order_id', type: 'integer', nullable: true)]
    private $orderId;

    /**
     * @var string
     */
    #[ORM\Column(name: 'num_order', type: 'string', nullable: true)]
    private $numOrder;

    /**
     * @var double
     */
    #[ORM\Column(name: 'total_amount_with_taxes', type: 'float', nullable: true)]
    private $totalAmountWithTaxes;

    /**
     * @var string
     */
    #[ORM\Column(name: 'currency', type: 'string', nullable: true)]
    private $currency;

    /**
     * @var ?Company
     */
    #[ORM\JoinColumn(name: 'company_id', referencedColumnName: 'id', nullable: true)]
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\Company::class, inversedBy: 'invoices', cascade: ['persist'])]
    private $company;

    /**
     * @var string
     */
    #[ORM\Column(name: 'payment_status', type: 'string', nullable: true)]
    private $paymentStatus;

    /**
     * @var string
     */
    #[ORM\Column(name: 'status', type: 'string', nullable: true)]
    private $status;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId(int $id)
    {
        $this->id = $id;
    }

    /**
     * @return int
     */
    public function getIzbergId(): int
    {
        return $this->izbergId;
    }

    /**
     * @param int $izbergId
     */
    public function setIzbergId(int $izbergId)
    {
        $this->izbergId = $izbergId;
    }

    /**
     * @return bool
     */
    public function isPdfFile(): bool
    {
        return $this->pdfFile;
    }

    /**
     * @param bool $pdfFile
     */
    public function setPdfFile(bool $pdfFile)
    {
        $this->pdfFile = $pdfFile;
    }

    /**
     * @return string
     */
    public function getIssuerName(): string
    {
        return $this->issuerName;
    }

    /**
     * @param string $issuerName
     */
    public function setIssuerName(string $issuerName)
    {
        $this->issuerName = $issuerName;
    }

    /**
     * @return \DateTimeImmutable
     */
    public function getCreatedOn(): \DateTimeImmutable
    {
        return $this->createdOn;
    }

    /**
     * @param \DateTimeImmutable $createdOn
     */
    public function setCreatedOn(\DateTimeImmutable $createdOn)
    {
        $this->createdOn = $createdOn;
    }

    /**
     * @return int
     */
    public function getOrderId(): int
    {
        return $this->orderId;
    }

    /**
     * @param int $orderId
     */
    public function setOrderId(int $orderId)
    {
        $this->orderId = $orderId;
    }

    /**
     * @return string
     */
    public function getNumOrder(): string
    {
        return $this->numOrder;
    }

    /**
     * @param string $numOrder
     */
    public function setNumOrder(string $numOrder)
    {
        $this->numOrder = $numOrder;
    }

    /**
     * @return float
     */
    public function getTotalAmountWithTaxes(): float
    {
        return $this->totalAmountWithTaxes;
    }

    /**
     * @param float $totalAmountWithTaxes
     */
    public function setTotalAmountWithTaxes(float $totalAmountWithTaxes)
    {
        $this->totalAmountWithTaxes = $totalAmountWithTaxes;
    }

    /**
     * @return string
     */
    public function getCurrency(): string
    {
        return $this->currency;
    }

    /**
     * @param string $currency
     */
    public function setCurrency(string $currency)
    {
        $this->currency = $currency;
    }

    /**
     * @return mixed
     */
    public function getCompany()
    {
        return $this->company;
    }

    /**
     * @param mixed $company
     */
    public function setCompany($company)
    {
        $this->company = $company;
    }

    /**
     * @return string
     */
    public function getPaymentStatus(): string
    {
        return $this->paymentStatus;
    }

    /**
     * @param string $paymentStatus
     */
    public function setPaymentStatus(string $paymentStatus)
    {
        $this->paymentStatus = $paymentStatus;
    }

    /**
     * @return string
     */
    public function getStatus(): string
    {
        return $this->status;
    }

    /**
     * @param string $status
     */
    public function setStatus(string $status)
    {
        $this->status = $status;
    }

    /**
     * @return string
     */
    public function getNumberId()
    {
        return $this->numberId;
    }

    /**
     * @param string $numberId
     */
    public function setNumberId($numberId)
    {
        $this->numberId = $numberId;
    }

    public function isInvoiceType ()
    {
        return false;
    }

    public function isCreditNoteType()
    {
        return true;
    }
}
