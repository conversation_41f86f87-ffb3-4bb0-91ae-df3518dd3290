<?php
/**
 * Created by PhpStorm.
 * User: CMA15450
 * Date: 28/04/2017
 * Time: 09:48
 */

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * Document
 */
#[ORM\Table(name: 'menu_item')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\MenuItemRepository::class)]
class MenuItem
{

    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;


    /**
     * @var int
     */
    #[ORM\JoinColumn(name: 'menu_id', referencedColumnName: 'id', nullable: false)]
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\Menu::class, inversedBy: 'items')]
    private $menu;


    #[Assert\NotBlank]
    #[Assert\NotNull]
    #[ORM\Column(name: 'label', type: 'string', length: 255, nullable: false)]
    private $label;


    #[Assert\NotBlank]
    #[Assert\NotNull]
    #[ORM\Column(name: 'link', type: 'string', length: 255, nullable: false)]
    private $link = "";

    #[Assert\NotNull]
    #[ORM\Column(name: 'open_in_new_window', type: 'boolean', nullable: false, options: ['default' => false])]
    private $newWindow = false;

    #[ORM\Column(name: 'weight', type: 'integer', options: ['default' => 0])]
    private $weight=0;


    #[ORM\Column(name: 'active', type: 'boolean', options: ['default' => 1])]
    private $active = true;


    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return int
     */
    public function getMenu()
    {
        return $this->menu;
    }

    /**
     * @param int $menu
     */
    public function setMenu($menu)
    {
        $this->menu = $menu;
    }

    /**
     * @return mixed
     */
    public function getLabel()
    {
        return $this->label;
    }

    /**
     * @param mixed $label
     */
    public function setLabel($label)
    {
        $this->label = $label;
    }

    /**
     * @return mixed
     */
    public function getLink()
    {
        return $this->link;
    }

    /**
     * @param mixed $link
     */
    public function setLink($link)
    {
        $this->link = $link;
    }

    /**
     * @return mixed
     */
    public function getNewWindow()
    {
        return $this->newWindow;
    }

    /**
     * @param mixed $newWindow
     */
    public function setNewWindow($newWindow)
    {
        $this->newWindow = $newWindow;
    }

    /**
     * @return mixed
     */
    public function getWeight()
    {
        return $this->weight;
    }

    /**
     * @param mixed $weight
     */
    public function setWeight($weight)
    {
        $this->weight = $weight;
    }

    /**
     * @return mixed
     */
    public function getActive()
    {
        return $this->active;
    }

    /**
     * @param mixed $active
     */
    public function setActive($active)
    {
        $this->active = $active;
    }


    public function __toString()
    {
        if ($this->label) {
            return $this->label;
        } else {
            return "Element de menu sans nom";
        }
    }
}
