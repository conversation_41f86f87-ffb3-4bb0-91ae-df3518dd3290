<?php

namespace AppBundle\Entity;

use Api\Core\Entity\UserOwnedInterface;
use AppBundle\Services\MailService;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use FOS\UserBundle\Model\User as BaseUser;
use FOS\UserBundle\Model\UserInterface;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

#[UniqueEntity('email')]
#[ORM\Table(name: '`users`')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\UserRepository::class)]
#[ORM\HasLifecycleCallbacks]
class User extends BaseUser implements UserInterface, \JsonSerializable, UserOwnedInterface
{
    use TimestampedTrait;
    use ExportableEntityTrait;
    use TechnicalIdentifierTrait;

    public const ROLE_BUYER_BUYER = 'ROLE_BUYER_BUYER';
    public const ROLE_BUYER_PAYER = 'ROLE_BUYER_PAYER';
    public const ROLE_BUYER_ADMIN = 'ROLE_BUYER_ADMIN';
    public const ROLE_API = 'ROLE_API';

    #[ORM\Id]
    #[ORM\Column(type: 'integer')]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    protected $id;

    #[Assert\NotBlank]
    #[Assert\Length(max: 50)]
    #[ORM\Column(name: 'firstname', type: 'string', length: 50)]
    protected $firstname;


    #[Assert\NotBlank]
    #[Assert\Length(max: 50)]
    #[ORM\Column(name: 'lastname', type: 'string', length: 50)]
    protected $lastname;

    /**
     * Many user have One company.
     */
    #[ORM\JoinColumn(name: 'company_id', referencedColumnName: 'id', nullable: true)]
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\Company::class, inversedBy: 'users')]
    private $company;

    #[ORM\JoinTable(name: 'users_sites')]
    #[ORM\JoinColumn(nullable: true)]
    #[ORM\ManyToMany(targetEntity: \Site::class, inversedBy: 'users')]
    private $sites;

    #[Assert\Length(max: 50)]
    #[Assert\Regex(pattern: '/^[\+]?[0-9]+$/', match: true, message: 'form.contact.phone_regex')]
    #[ORM\Column(name: 'optional_phone_number', type: 'string', length: 50, nullable: true)]
    private $optionalPhoneNumber;


    #[Assert\Length(max: 50)]
    #[Assert\Regex(pattern: '/^[\+]?[0-9]+$/', match: true, message: 'form.contact.phone_regex')]
    #[ORM\Column(name: 'main_phone_number', type: 'string', length: 50, nullable: true)]
    private $mainPhoneNumber;

    /**
     * a string with the user role. Used for screen conception. Not persisted
     * @var string
     */
    private $role;

    /**
     * @var ArrayCollection $nodes list of nodes
     */
    #[ORM\OneToMany(targetEntity: \AppBundle\Entity\Node::class, mappedBy: 'author', cascade: ['persist'])]
    private $nodes;


    #[ORM\Column(name: 'email_confirmed', type: 'boolean')]
    private $emailConfirmed = false;

    /**
     * @var int
     */
    #[ORM\Column(name: 'login_attempt', type: 'integer', length: 11, nullable: false)]
    private $loginAttempt = 0;


    /**
     * @var string
     */
    #[ORM\Column(name: 'locale', type: 'string', length: 2, nullable: false)]
    private $locale = "fr";


    #[ORM\Column(name: 'last_failed_login', type: 'datetime', nullable: true)]
    private $lastFailedLogin;


    /**
     * @var string
     */
    #[ORM\Column(name: 'civ', type: 'string', length: 2, nullable: false)]
    private $civ = 'mr';

    /**
     * @var Collection $connections list of connections
     */
    #[ORM\OneToMany(targetEntity: \AppBundle\Entity\Connection::class, mappedBy: 'user', cascade: ['persist'])]
    private $connections;


    /**
     * @var \DateTime date when the user was disabled
     */
    #[ORM\Column(name: 'disabled_at', type: 'datetime', nullable: true)]
    private $disabledAt;

    /**
     * @var string $function User function or job
     */
    #[Assert\Length(max: 50)]
    #[ORM\Column(name: 'function_', type: 'string', length: 50, nullable: true)]
    private $function;

    /**
     * @var string $comparisonSheetId User IZB comparison sheet id
     */
    #[ORM\Column(name: 'comparison_sheet_eur_id', type: 'string', length: 50, nullable: true)]
    private $comparisonSheetEURId;

    /**
     * @var string $comparisonSheetId User IZB comparison sheet id
     */
    #[ORM\Column(name: 'comparison_sheet_usd_id', type: 'string', length: 50, nullable: true)]
    private $comparisonSheetUSDId;


    /**
     * @var int
     */
    #[ORM\Column(name: 'item_in_comparison_sheet', type: 'integer', length: 2, options: ['default' => 0], nullable: true)]
    private $itemInComparisonSheet;

    /**
     * @var string|null
     */
    #[ORM\Column(name: 'cart_eur_id', type: 'string', length: 50, nullable: true)]
    private $cartEURId;

    /**
     * @var string|null
     */
    #[ORM\Column(name: 'cart_usd_id', type: 'string', length: 50, nullable: true)]
    private $cartUSDId;

    /**
     * @var int
     */
    #[ORM\Column(name: 'item_in_cart_eur', type: 'integer', length: 50, options: ['default' => 0], nullable: true)]
    private $itemInCartEUR;

    /**
     * @var int
     */
    #[ORM\Column(name: 'item_in_cart_usd', type: 'integer', length: 50, options: ['default' => 0], nullable: true)]
    private $itemInCartUSD;

    private $currency;

    public function __construct()
    {
        parent::__construct();

        $this->nodes = new ArrayCollection();
        $this->connections = new ArrayCollection();
    }

    /**
     * @return mixed
     */
    public function getCurrency()
    {
        return $this->currency;
    }

    /**
     * @param mixed $currency
     */
    public function setCurrency($currency)
    {
        $this->currency = $currency;
    }

    public function setId($id): void
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getFirstname()
    {
        return $this->firstname;
    }

    /**
     * @param mixed $firstname
     */
    public function setFirstname($firstname)
    {
        $this->firstname = $firstname;
    }

    /**
     * @return mixed
     */
    public function getLastname()
    {
        return $this->lastname;
    }

    /**
     * @param mixed $lastname
     */
    public function setLastname($lastname)
    {
        $this->lastname = $lastname;
    }

    /**
     * @return null|Company
     */
    public function getCompany()
    {
        return $this->company;
    }

    /**
     * @param mixed $company
     */
    public function setCompany($company)
    {
        $this->company = $company;
    }

    /**
     * @return string|null
     */
    public function getRole(): ?string
    {
        if ($this->roles && !empty($this->roles)) {
            return $this->roles[0];
        } else {
            return '';
        }
    }

    /**
     * @param string $role
     */
    public function setRole(?string $role)
    {
        $this->roles[0] = $role;
    }

    /**
     * @return mixed
     */
    public function getSites()
    {
        return $this->sites;
    }

    /**
     * @param mixed $sites
     */
    public function setSites($sites)
    {
        $this->sites = $sites;
    }

    /**
     * @return mixed
     */
    public function getOptionalPhoneNumber()
    {
        return $this->optionalPhoneNumber;
    }

    /**
     * @param mixed $phoneNumber
     */
    public function setOptionalPhoneNumber($phoneNumber)
    {
        $this->optionalPhoneNumber = $phoneNumber;
    }

    /**
     * @return mixed
     */
    public function getMainPhoneNumber()
    {
        return $this->mainPhoneNumber;
    }

    /**
     * @param mixed $phoneNumber
     */
    public function setMainPhoneNumber($phoneNumber)
    {
        $this->mainPhoneNumber = $phoneNumber;
    }

    /**
     * @return mixed
     */
    public function getEmailConfirmed()
    {
        return $this->emailConfirmed;
    }

    /**
     * @param mixed $emailConfirmed
     */
    public function setEmailConfirmed($emailConfirmed)
    {
        $this->emailConfirmed = $emailConfirmed;
    }

    public function getLocale(): string
    {
        if ($this->locale !== null) {
            return $this->locale;
        } else {
            return MailService::DEFAULT_LOCALE;
        }
    }

    /**
     * @param string $locale
     */
    public function setLocale($locale)
    {
        $this->locale = $locale;
    }

    /**
     * Gest user gender
     * @return string
     */
    public function getCiv()
    {
        return $this->civ;
    }

    /**
     * Set user gender
     * @param $civ string
     */
    public function setCiv($civ)
    {
        if ($civ == 'mr' || $civ == 'ms') {
            $this->civ = $civ;
        }
    }

    /**
     * @return \DateTime
     */
    public function getDisabledAt()
    {
        return $this->disabledAt;
    }

    /**
     * @param \DateTime|null $disabledAt
     */
    public function setDisabledAt(?\DateTime $disabledAt)
    {
        $this->disabledAt = $disabledAt;
    }

    public function __toString()
    {
        return $this->firstname . ' ' . $this->lastname . ' (' . $this->username . ')';
    }

    public function getConnections(): Collection
    {
        return $this->connections;
    }

    /**
     * @param ArrayCollection $connections
     */
    public function setConnections(ArrayCollection $connections): void
    {
        $this->connections = $connections;
    }

    /**
     * add a new connection to the user history
     * @param Connection $connection the new connection to add to the history
     */
    public function addConnection(Connection $connection)
    {
        $this->connections [] = $connection;
    }

    public function jsonSerialize(): array
    {
        return [
            'id' => $this->id,
            'username' => $this->username,
            'enabled' => $this->enabled,
            'email' => $this->emailCanonical,
            'firstname' => $this->firstname,
            'lastname' => $this->lastname,
        ];
    }

    /**
     * Return number of login attempt for the user
     * @return integer
     */
    public function getLoginAttempt()
    {
        return intval($this->loginAttempt);
    }

    /**
     * Set number of login attempt
     * @param integer $cnt
     * @return integer
     */
    public function setLoginAttempt($cnt)
    {
        return $this->loginAttempt = intval($cnt);
    }


    /**
     * @return mixed
     */
    public function getLastFailedLogin()
    {
        return $this->lastFailedLogin;
    }

    /**
     * @param mixed $last_failed_login
     */
    public function setLastFailedLogin($last_failed_login)
    {
        $this->lastFailedLogin = $last_failed_login;
    }

    /**
     * @return string
     */
    public function getFunction(): ?string
    {
        return $this->function;
    }

    /**
     * @param string $function
     */
    public function setFunction(?string $function): void
    {
        $this->function = $function;
    }


    /**
     * Add site.
     *
     * @param \AppBundle\Entity\Site $site
     *
     * @return User
     */
    public function addSite(\AppBundle\Entity\Site $site)
    {
        $this->sites[] = $site;

        return $this;
    }

    /**
     * Remove site.
     *
     * @param \AppBundle\Entity\Site $site
     *
     * @return boolean TRUE if this collection contained the specified element, FALSE otherwise.
     */
    public function removeSite(\AppBundle\Entity\Site $site)
    {
        return $this->sites->removeElement($site);
    }

    /**
     * Add node.
     *
     * @param \AppBundle\Entity\Node $node
     *
     * @return User
     */
    public function addNode(\AppBundle\Entity\Node $node)
    {
        $this->nodes[] = $node;

        return $this;
    }

    /**
     * Remove node.
     *
     * @param \AppBundle\Entity\Node $node
     *
     * @return boolean TRUE if this collection contained the specified element, FALSE otherwise.
     */
    public function removeNode(\AppBundle\Entity\Node $node)
    {
        return $this->nodes->removeElement($node);
    }

    /**
     * Get nodes.
     *
     * @return ArrayCollection
     */
    public function getNodes()
    {
        return $this->nodes;
    }

    /**
     * Remove connection.
     *
     * @param \AppBundle\Entity\Connection $connection
     *
     * @return boolean TRUE if this collection contained the specified element, FALSE otherwise.
     */
    public function removeConnection(\AppBundle\Entity\Connection $connection)
    {
        return $this->connections->removeElement($connection);
    }

    /**
     * @return string
     */
    public function getComparisonSheetEURId()
    {
        return $this->comparisonSheetEURId;
    }

    /**
     * @param string $comparisonSheetEURId
     */
    public function setComparisonSheetEURId($comparisonSheetEURId)
    {
        $this->comparisonSheetEURId = $comparisonSheetEURId;
    }

    /**
     * @return string
     */
    public function getComparisonSheetUSDId()
    {
        return $this->comparisonSheetUSDId;
    }

    /**
     * @param string $comparisonSheetUSDId
     */
    public function setComparisonSheetUSDId($comparisonSheetUSDId)
    {
        $this->comparisonSheetUSDId = $comparisonSheetUSDId;
    }

    /**
     * @return int
     */
    public function getItemInComparisonSheet()
    {
        return $this->itemInComparisonSheet;
    }

    /**
     * @param int $itemInComparisonSheet
     */
    public function setItemInComparisonSheet($itemInComparisonSheet)
    {
        $this->itemInComparisonSheet = $itemInComparisonSheet;
    }

    public function getCartEURId(): ?int
    {
        return (int)$this->cartEURId;
    }

    /**
     * @param string|null $cartEURId
     */
    public function setCartEURId(?string $cartEURId)
    {
        $this->cartEURId = $cartEURId;
    }

    public function getCartUSDId(): ?int
    {
        return (int)$this->cartUSDId;
    }

    /**
     * @param string|null $cartUSDId
     */
    public function setCartUSDId(?string $cartUSDId)
    {
        $this->cartUSDId = $cartUSDId;
    }

    /**
     * @return int
     */
    public function getItemInCartEUR()
    {
        return $this->itemInCartEUR;
    }

    /**
     * @param int $itemInCartEUR
     */
    public function setItemInCartEUR($itemInCartEUR)
    {
        $this->itemInCartEUR = $itemInCartEUR;
    }

    /**
     * @return int
     */
    public function getItemInCartUSD()
    {
        return $this->itemInCartUSD;
    }

    /**
     * @param int $itemInCartUSD
     */
    public function setItemInCartUSD($itemInCartUSD)
    {
        $this->itemInCartUSD = $itemInCartUSD;
    }

    #[Assert\Callback(groups: ['user_sites'])]
    public function validate(ExecutionContextInterface $context, $payload): void
    {
        if (!$this->isBuyerApi() && empty($this->function)) {
            $context->buildViolation('form.user.function.mandatory')
                ->atPath('function')
                ->addViolation();
        }

        $hasAtLeastOneSite = ($this->getSites() && count($this->getSites()));
        if (
            $this->isBuyerApi() === false
            && !$this->isBuyerAdmin()
            && !in_array('ROLE_SUPER_ADMIN', $this->getRoles())
            && !in_array('ROLE_OPERATOR', $this->getRoles())
            && !$hasAtLeastOneSite
        ) {
            $context->buildViolation('form.user.sites.mandatory')
                ->atPath('sites')
                ->addViolation();
        }
    }

    public function isBuyerAdmin(): bool
    {
        return in_array(self::ROLE_BUYER_ADMIN, $this->getRoles());
    }

    public function isBuyerBuyer(): bool
    {
        return in_array(self::ROLE_BUYER_BUYER, $this->getRoles());
    }

    public function isBuyerApi(): bool
    {
        return in_array(self::ROLE_API, $this->getRoles());
    }

    public function canSplitDeliveryDate(): bool
    {
        return !static::isSuperAdmin() && !static::isBuyerApi();
    }

    /**
     * Display the user info : "firstname lastname (email)"
     */
    public function display(): string
    {
        return sprintf(
            '%s %s (%s)',
            $this->getFirstname(),
            $this->getLastname(),
            $this->getEmail()
        );
    }

    public function isDefaultForSite(): bool
    {
        if (!($this->getCompany() instanceof Company)) {
            return false;
        }

        $isDefaultUserForSite = false;

        $this->getCompany()->getSites()->map(function (Site $site) use (&$isDefaultUserForSite) {
            if ($site->getDefaultUser() && $site->getDefaultUser()->getId() === $this->getId()) {
                $isDefaultUserForSite = true;
            }
        });

        return $isDefaultUserForSite;
    }
}
