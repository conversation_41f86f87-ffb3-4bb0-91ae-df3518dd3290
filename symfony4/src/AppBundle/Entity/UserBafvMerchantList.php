<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * UserRestriction
 */
#[ORM\Table(name: 'user_bafv_merchant_list')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\UserBafvMerchantListRepository::class)]
class UserBafvMerchantList
{
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\Company::class)]
    private $company;

    #[ORM\Column(name: 'merchant_id', type: 'integer')]
    private $merchantId;


    public function getId(): int
    {
        return $this->id;
    }

    public function getCompany(): Company
    {
        return $this->company;
    }

    public function setCompany(Company $company): self
    {
        $this->company = $company;
        return $this;
    }

    public function getMerchantId(): ?int
    {
        return $this->merchantId;
    }

    public function setMerchantId(?int $merchantId): self
    {
        $this->merchantId = $merchantId;
        return $this;
    }

}
