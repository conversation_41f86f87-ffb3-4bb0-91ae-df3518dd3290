<?php
/**
 * Created by PhpStorm.
 * User: AQU04740
 * Date: 24/03/2017
 * Time: 17:15
 */

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * Zipcode
 */
#[ORM\Table(name: 'zipcode')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\CityRepository::class)]
#[ORM\HasLifecycleCallbacks]
class ZipCode implements \JsonSerializable
{

    use TimestampedTrait;
    use TechnicalIdentifierTrait;

    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

    /**
     * One country has Many sites.
     */
    #[ORM\ManyToOne(targetEntity: \Country::class, inversedBy: 'zipcode')]
    private $country;

    /**
     * @var string
     */
    #[Assert\NotBlank]
    #[ORM\Column(name: 'zipcode', type: 'string', length: 7)]
    private $zipcode;

    /**
     * @var string
     */
    #[Assert\NotBlank]
    #[ORM\Column(name: 'city', type: 'string', length: 50)]
    private $city;

    /**
     * @var string
     */
    #[ORM\Column(name: 'insee_code', type: 'string', length: 7, nullable: true)]
    private $inseeCode;

    /**
     * @var string
     */
    #[ORM\Column(name: 'gps', type: 'string', length: 35, nullable: true)]
    private $gps;

    /**
     * @var string
     */
    #[ORM\Column(name: 'label', type: 'string', length: 70, nullable: false)]
    private $label;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }




    /**
     * @return string
     */
    public function getZipcode()
    {
        return $this->zipcode;
    }

    /**
     * @param string $zipcode
     */
    public function setZipcode($zipcode)
    {
        $this->zipcode = $zipcode;
    }

    /**
     * @return string
     */
    public function getCity()
    {
        return $this->city;
    }

    /**
     * @param string $city
     */
    public function setCity($city)
    {
        $this->city = $city;
    }

    /**
     * @return string
     */
    public function getInseeCode()
    {
        return $this->inseeCode;
    }

    /**
     * @param string $inseeCode
     */
    public function setInseeCode($inseeCode)
    {
        $this->inseeCode = $inseeCode;
    }

    /**
     * @return string
     */
    public function getGps()
    {
        return $this->gps;
    }

    /**
     * @param string $gps
     */
    public function setGps($gps)
    {
        $this->gps = $gps;
    }

    /**
     * @return string
     */
    public function getLabel()
    {
        return $this->label;
    }

    /**
     * @param string $label
     */
    public function setLabel($label)
    {
        $this->label = $label;
    }

    /**
     * @return Country|null
     */
    public function getCountry():?Country
    {
        return $this->country;
    }

    /**
     * @param  Country|null $country
     */
    public function setCountry(?Country $country)
    {
        $this->country = $country;
    }

    public function __toString()
    {
        $result = "(";
        foreach ($this->jsonSerialize() as $key => $value){
            $result .= $key."=".$value.",";
        }

        return $result . ")";
    }

    public function jsonSerialize(): array {
        return [
            'zipcode' => $this->zipcode,
            'city' => $this->city,
            'gps' => $this->gps,
            'inseeCode' => $this->inseeCode,
            'label' => $this->label
        ];
    }




}
