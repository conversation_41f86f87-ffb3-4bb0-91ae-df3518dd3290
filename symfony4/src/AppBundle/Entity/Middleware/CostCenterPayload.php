<?php

declare(strict_types=1);

namespace AppBundle\Entity\Middleware;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
class CostCenterPayload extends AbstractPayload
{
    public const ACTION_CREATE = 'COST_CENTER_CREATE';
    public const ACTION_UPDATE = 'COST_CENTER_UPDATE';
    public const ACTION_DELETE = 'COST_CENTER_DELETE';


    public function type(): string
    {
        return 'COST_CENTER_PAYLOAD';
    }
}
