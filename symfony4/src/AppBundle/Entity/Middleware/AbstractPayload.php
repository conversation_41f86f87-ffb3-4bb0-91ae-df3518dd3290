<?php

namespace AppBundle\Entity\Middleware;

use DateTime;
use Doctrine\ORM\Mapping as ORM;
use JsonSerializable;
use Ramsey\Uuid\Uuid;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use AppBundle\Repository\Payload\PayloadRepository;

#[UniqueEntity(fields: 'identifier')]
#[ORM\Table(name: 'middleware_payloads')]
#[ORM\Entity(repositoryClass: PayloadRepository::class)]
#[ORM\InheritanceType(value: 'SINGLE_TABLE')]
#[ORM\DiscriminatorColumn(name: 'payload_type', type: 'string')]
#[ORM\DiscriminatorMap(['PurchaseRequestItemPayload' => PurchaseRequestItemPayload::class, 'OrderPayload' => OrderPayload::class, 'InvoicePayload' => InvoicePayload::class, 'CostCenterPayload' => CostCenterPayload::class])]
abstract class AbstractPayload implements JsonSerializable
{
    public const STATUS_CREATED = 'STATUS_CREATED';
    public const STATUS_SUCCESS = 'STATUS_SUCCESS';
    public const STATUS_FAILED = 'STATUS_FAILED';

    #[ORM\Id]
    #[ORM\Column(type: 'integer')]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    protected ?int $id;

    #[ORM\Column]
    protected string $identifier;

    #[ORM\Column(type: 'datetime')]
    protected DateTime $createdAt;

    #[ORM\Column]
    protected string $status;

    #[ORM\Column]
    protected string $entity;

    #[ORM\Column(nullable: true)]
    protected ?string $userType = null;

    #[ORM\Column(type: 'integer')]
    protected int $entityId;

    #[ORM\Column]
    protected string $action;

    #[ORM\Column(type: 'array')]
    protected array $data;

    #[ORM\Column(type: 'text')]
    protected string $sentContent;

    public function __construct()
    {
        $this->status = self::STATUS_CREATED;
        $this->createdAt = new DateTime();
        $this->identifier = Uuid::uuid4()->toString();
    }

    public static function createFromData(array $data, string $action, int $entityId): AbstractPayload
    {
        $payload = ((new static())
            ->setData($data)
            ->setAction($action)
            ->setEntity(static::class)
            ->setEntityId($entityId)
        );

        return $payload->setSentContent(json_encode($payload,  JSON_UNESCAPED_SLASHES));
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getIdentifier(): string
    {
        return $this->identifier;
    }

    public function setIdentifier(string $identifier): self
    {
        $this->identifier = $identifier;
        return $this;
    }

    public function getCreatedAt(): DateTime
    {
        return $this->createdAt;
    }

    public function setCreatedAt(DateTime $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }

    public function getEntity(): string
    {
        return $this->entity;
    }

    public function setEntity(string $entity): self
    {
        $this->entity = $entity;
        return $this;
    }

    public function getAction(): string
    {
        return $this->action;
    }

    public function setAction(string $action): self
    {
        $this->action = $action;
        return $this;
    }

    public function getData(): array
    {
        return $this->data;
    }

    public function setData(array $data): self
    {
        $this->data = $data;
        return $this;
    }

    public function getSentContent(): string
    {
        return $this->sentContent;
    }

    public function setSentContent(string $sentContent): self
    {
        $this->sentContent = $sentContent;
        return $this;
    }

    public function getEntityId(): int
    {
        return $this->entityId;
    }

    public function setEntityId(int $entityId): self
    {
        $this->entityId = $entityId;

        return $this;
    }

    public function getUserType(): ?string
    {
        return $this->userType;
    }

    public function setUserType(string $userType): self
    {
        $this->userType = $userType;

        return $this;
    }

    abstract public function type(): string;

    final public function label(): string
    {
        return 'back.payloads.types.' . strtolower($this->type());
    }

    public function jsonSerialize(): array
    {
        return [
            'id' => $this->getIdentifier(),
            'type' => $this->type(),
            'created' => $this->getCreatedAt()->format('Y-m-d H:i:s'),
            'payload' => [
                'id' => $this->getEntityId(),
                'action' => $this->getAction(),
                'entity' => $this->getEntity(),
                'data' => $this->getData(),
            ]
        ];
    }

    public function failed(): void
    {
        $this->status = AbstractPayload::STATUS_FAILED;
    }

    public function success(): void
    {
        $this->status = AbstractPayload::STATUS_SUCCESS;
    }
}
