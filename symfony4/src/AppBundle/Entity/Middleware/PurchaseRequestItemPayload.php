<?php

namespace AppBundle\Entity\Middleware;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
class PurchaseRequestItemPayload extends AbstractPayload
{
    private const PAYLOAD_TYPE = 'PURCHASE_REQUEST_ITEM_PAYLOAD';
    private const CREATE_PAYLOAD_ACTION = 'purchase.request.item.payload.creation';

    private ?int $purchaseRequestId = null;
    private ?int $current = null;
    private ?int $total = null;

    public function getPurchaseRequestId(): ?int
    {
        return $this->purchaseRequestId;
    }

    public function setPurchaseRequestId(int $purchaseRequestId): self
    {
        $this->purchaseRequestId = $purchaseRequestId;
        return $this;
    }

    public function getCurrent(): ?int
    {
        return $this->current;
    }

    public function setCurrent(int $current): self
    {
        $this->current = $current;
        return $this;
    }

    public function getTotal(): ?int
    {
        return $this->total;
    }

    public function setTotal(int $total): self
    {
        $this->total = $total;
        return $this;
    }

    public static function create(
        int   $purchaseRequestId,
        int   $current,
        int   $total,
        array $data,
        string $userType
    ): self {
        /** @var self $payload */
        $payload = self::createFromData($data, self::CREATE_PAYLOAD_ACTION, $purchaseRequestId);

        $payload->setPurchaseRequestId($purchaseRequestId)
            ->setCurrent($current)
            ->setTotal($total)
            ->setData($data)
            ->setEntity(self::class)
            ->setUserType($userType)
            ->setAction(self::CREATE_PAYLOAD_ACTION)
        ;

        return $payload->setSentContent(json_encode($payload, JSON_UNESCAPED_UNICODE));
    }

    public function type(): string
    {
        return self::PAYLOAD_TYPE;
    }

    public function jsonSerialize(): array
    {
        $data = parent::jsonSerialize();
        $data['payload']['purchaseRequestId'] = $this->getPurchaseRequestId();
        $data['payload']['current'] = $this->getCurrent();
        $data['payload']['total'] = $this->getTotal();

        return $data;
    }

    public function action(): string
    {
        return self::CREATE_PAYLOAD_ACTION;
    }
}
