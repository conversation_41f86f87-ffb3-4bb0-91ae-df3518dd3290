<?php
/**
 * Created by PhpStorm.
 * User: CMA15450
 * Date: 08/03/2017
 * Time: 14:30
 */

namespace AppBundle\Entity;

use Doctrine\Common\Collections\ArrayCollection;

/**
 * Class needed to embed multiple Setting entities in one form
 * Class SettingsCollection
 * @package AppBundle\Entity
 */
class SettingsCollection
{
    private $settings;

    public function __construct()
    {
        $this->settings = new ArrayCollection();
    }

    public function getSettings() {
        return $this->settings;
    }

    public function setSettings($settings) {
        $this->settings = $settings;
    }
}
