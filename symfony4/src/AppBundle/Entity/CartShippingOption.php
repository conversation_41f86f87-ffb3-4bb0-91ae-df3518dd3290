<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>\Serializer\Annotation as JSM;

#[ORM\Table(name: 'cart_shipping_option')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\CartShippingOptionRepository::class)]
class CartShippingOption
{
    public const FLAG_CHEAPEST = 0;
    public const FLAG_FASTEST = 1;
    public const FLAG_MONO_CARRIER = 2;

    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

    #[ORM\JoinColumn(name: 'cart_id', referencedColumnName: 'id', nullable: false)]
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\Cart::class, inversedBy: 'cartShippingOption')]
    private $cart;

    #[ORM\Column(name: 'merchant_id', type: 'integer')]
    private $merchantId;

    #[ORM\Column(name: 'flag', type: 'smallint')]
    private $flag;

    #[ORM\Column(name: 'offers', type: 'json')]
    private $offers;

    #[ORM\Column(name: 'price', type: 'float', nullable: true)]
    private $price;


    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getCart(): Cart
    {
        return $this->cart;
    }

    public function setCart(Cart $cart): self
    {
        $this->cart = $cart;
        return $this;
    }

    public function getFlag(): int
    {
        return $this->flag;
    }

    public function setFlag(int $flag): self
    {
        $this->flag = $flag;
        return $this;
    }

    public function getOffers()
    {
        return $this->offers;
    }

    public function setOffers($offers): self
    {
        $this->offers = $offers;
        return $this;
    }

    public function setPrice(?float $price): self
    {
        $this->price = $price;

        return $this;
    }

    public function getPrice(): ?float
    {
        return $this->price;
    }

    public function getMerchantId():?int
    {
        return $this->merchantId;
    }

    public function setMerchantId(?int $merchantId):self
    {
        $this->merchantId = $merchantId;
        return $this;
    }
}
