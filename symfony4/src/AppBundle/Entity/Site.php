<?php

namespace AppBundle\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

/**
 * Site
 * */
#[UniqueEntity(fields: 'id')]
#[ORM\Table(name: 'site')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\SiteRepository::class)]
#[ORM\HasLifecycleCallbacks]
class Site implements \JsonSerializable
{

    use TimestampedTrait;
    use ExportableEntityTrait;
    use TechnicalIdentifierTrait;

    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    #[Assert\NotBlank]
    #[Assert\Length(max: 50)]
    #[ORM\Column(name: 'name', type: 'string', length: 50, nullable: true)]
    private ?string $name = null;

    /**
     * Many site have One company.
     */
    #[ORM\JoinColumn(name: 'company_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\Company::class, inversedBy: 'sites')]
    private ?Company $company = null;

    /**
     * One site has Many shipping points.
     */
    #[ORM\OneToMany(targetEntity: \AppBundle\Entity\ShippingPoint::class, mappedBy: 'site', cascade: ['persist'])]
    private Collection $shippingPoints;


    /**
     * @var bool
     */
    #[ORM\Column(name: 'enabled', type: 'boolean', nullable: false)]
    protected bool $enabled;

    /**
     * Many Site have users.
     */
    #[ORM\ManyToMany(targetEntity: \User::class, mappedBy: 'sites')]
    private Collection $users;

    #[ORM\JoinColumn(nullable: true)]
    #[ORM\ManyToOne(targetEntity: \User::class)]
    private ?User $defaultUser = null;

    /***
     * MethanationSite constructor.
     */
    public function __construct()
    {
        $this->shippingPoints = new ArrayCollection();
        $this->users = new ArrayCollection();
        $this->enabled = true;
    }

    public function getShippingPoints()
    {
        return $this->shippingPoints;
    }

    public function setShippingPoints($shippingPoints)
    {
        $this->shippingPoints = $shippingPoints;
    }

    public function addShippingPoint(ShippingPoint $shippingPoint)
    {
        $shippingPoint->setSite($this);
        $this->shippingPoints->add($shippingPoint);

        return $this;
    }


    public function removeShippingPoint(ShippingPoint $shippingPoint)
    {
        $this->shippingPoints->removeElement($shippingPoint);
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id)
    {
        $this->id = $id;
    }

    /**
     * @return string|null
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * @param string|null $name
     */
    public function setName($name)
    {
        $this->name = $name;
    }

    public function getCompany(): ?Company
    {
        return $this->company;
    }

    public function setCompany(Company $company)
    {
        $this->company = $company;
    }

    /**
     * @return mixed
     */
    public function getUsers()
    {
        return $this->users;
    }

    /**
     * @param mixed $users
     */
    public function setUsers($users)
    {
        if (is_array($users)) {
            $this->users = new ArrayCollection($users);
        } else {
            $this->users = $users;
        }
    }

    public function __toString()
    {
        if ($this->name) {
            return $this->name;
        } else {
            return '';
        }
    }

    public function addUser(User $user)
    {
        $this->users[] = $user;

        return $this;
    }

    /**
     * Remove user
     * @param \AppBundle\Entity\User $user
     */
    public function removeUser(User $user)
    {
        $this->users->removeElement($user);
    }

    /**
     * @return bool whether the site is enabled
     */
    public function isEnabled()
    {
        return $this->enabled;
    }

    /**
     * @param bool $enabled
     */
    public function setEnabled($enabled)
    {
        $this->enabled = $enabled;
    }

    public function jsonSerialize(): array
    {
        return [];
    }

    /**
     * Get enabled.
     *
     * @return bool
     */
    public function getEnabled()
    {
        return $this->enabled;
    }

    public function getDefaultUser(): ?User
    {
        return $this->defaultUser;
    }

    public function setDefaultUser(?User $defaultUser): self
    {
        $this->defaultUser = $defaultUser;

        return $this;
    }
}
