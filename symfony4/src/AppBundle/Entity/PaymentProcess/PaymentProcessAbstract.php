<?php

namespace AppBundle\Entity\PaymentProcess;

abstract class PaymentProcessAbstract
{
    protected const PAYMENT_PROCESS_TYPE_CREDIT_NOTE = 'credit_note';
    protected const PAYMENT_PROCESS_TYPE_INVOICE = 'invoice';
    protected const PAYMENT_PROCESS_TYPE_PAYMENT = 'payment';
    protected const PAYMENT_PROCESS_TYPE_REFUND = 'refund';

    /**
     * @var string
     */
    protected $type;

    /**
     * @var ?string
     */
    protected $id;

    /**
     * @var \DateTimeImmutable
     */
    protected $createdOn;

    public function getType(): string
    {
        return $this->type;
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getCreatedOn(): \DateTimeImmutable
    {
        return $this->createdOn;
    }

    public function isCreditNoteType(): bool
    {
        return ($this->getType() === self::PAYMENT_PROCESS_TYPE_CREDIT_NOTE);
    }

    public function isInvoiceType(): bool
    {
        return ($this->getType() === self::PAYMENT_PROCESS_TYPE_INVOICE);
    }

    public function isPaymentType(): bool
    {
        return ($this->getType() === self::PAYMENT_PROCESS_TYPE_PAYMENT);
    }

    public function isRefundType(): bool
    {
        return ($this->getType() === self::PAYMENT_PROCESS_TYPE_REFUND);
    }

    abstract public function hasAmount(): bool;

    abstract public function hasPayment(): bool;
}
