<?php

namespace AppBundle\Entity\PaymentProcess;

use DateTimeImmutable;

class Invoice extends PaymentProcessAbstract
{
    use ProcessAmountTrait;

    /**
     * @var array
     */
    private $paymentProcesses;

    /**
     * @var float
     */
    private $totalAmount;

    /**
     * @var float
     */
    private $totalPayment;

    /**
     * @var DateTimeImmutable
     */
    private $dueOn;

    /**
     * @var string
     */
    private $pdfUrl;

    /**
     * @var string
     */
    private $izbId;

    public function __construct(
        ?string $id,
        DateTimeImmutable $createdOn,
        float $amount,
        ?DateTimeImmutable $dueOn = null,
        ?string $pdfUrl = null,
        float $totalPayment = 0.0,
        ?string $izbId = null
    )
    {
        $this->paymentProcesses = [];
        $this->type = self::PAYMENT_PROCESS_TYPE_INVOICE;
        $this->id = $id;
        $this->createdOn = $createdOn;
        $this->dueOn = $dueOn;
        $this->amount = $amount;
        $this->pdfUrl = $pdfUrl;
        $this->izbId = $izbId;

        $this->totalAmount = $amount;
        $this->totalPayment = $totalPayment;
    }

    public function getPaymentProcesses(): array
    {
        usort(
            $this->paymentProcesses,
            function(PaymentProcessAbstract $onePaymentProcess, PaymentProcessAbstract $anotherPaymentProcess) {
                return ($onePaymentProcess->getCreatedOn() < $anotherPaymentProcess->getCreatedOn()) ? -1 : 1;
            }
        );

        return $this->paymentProcesses;
    }

    public function addCreditNote(CreditNote $creditNote)
    {
        $this->paymentProcesses[] = $creditNote;
        $this->totalAmount = $this->totalAmount - $creditNote->getAmount();
    }

    public function addPayment(Payment $payment)
    {
        $this->paymentProcesses[] = $payment;
        $this->totalPayment = $this->totalPayment + $payment->getPayment();
    }

    public function addRefund(Refund $refund)
    {
        $this->paymentProcesses[] = $refund;
        $this->totalPayment = $this->totalPayment + $refund->getPayment();
    }

    public function getDueOn(): ?DateTimeImmutable
    {
        return $this->dueOn;
    }

    public function setDueOn(DateTimeImmutable $dueOn): void
    {
        $this->dueOn = $dueOn;
    }

    public function getPdfUrl(): ?string
    {
        return $this->pdfUrl;
    }

    public function setPdfUrl(string $pdfUrl): void
    {
        $this->pdfUrl = $pdfUrl;
    }

    public function hasPdfUrl(): bool
    {
        return ($this->pdfUrl !== null);
    }

    public function getTotalAmount(): float
    {
        return $this->totalAmount;
    }

    public function getTotalPayment(): float
    {
        return $this->totalPayment;
    }

    public function getTotalRemainingAmountToPay(): float
    {
        $remainingAmount = $this->totalAmount - $this->totalPayment;

        return ($remainingAmount > 0) ? $remainingAmount : 0.0;
    }

    public function getIzbId(): string
    {
        return $this->izbId;
    }

    public function setIzbId(string $izbId)
    {
        $this->izbId = $izbId;
    }
}
