<?php

namespace AppBundle\Entity\PaymentProcess;

class InvoiceTotal
{
    /**
     * @var float
     */
    private $amount;

    /**
     * @var float
     */
    private $payment;

    /**
     * @var float
     */
    private $remainingAmountToPay;

    public function __construct(float $amount, float $payment, float $remainingAmountToPay)
    {
        $this->amount = $amount;
        $this->payment = $payment;
        $this->remainingAmountToPay = $remainingAmountToPay;
    }

    public function getAmount(): float
    {
        return $this->amount;
    }

    public function getPayment(): float
    {
        return $this->payment;
    }

    public function getRemainingAmountToPay(): float
    {
        return $this->remainingAmountToPay;
    }
}
