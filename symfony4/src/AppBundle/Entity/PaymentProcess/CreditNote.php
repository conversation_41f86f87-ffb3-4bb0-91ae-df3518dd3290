<?php

namespace AppBundle\Entity\PaymentProcess;

class CreditNote extends PaymentProcessAbstract
{
    use ProcessAmountTrait;

    /**
     * @var string
     */
    private $pdfUrl;

    /**
     * @var string
     */
    private $izbId;

    public function __construct(string $id, \DateTimeImmutable $createdOn, float $amount, ?string $pdfUrl = null, ?string $izbId = null)
    {
        $this->type = self::PAYMENT_PROCESS_TYPE_CREDIT_NOTE;
        $this->id = $id;
        $this->createdOn = $createdOn;
        $this->amount = $amount;
        $this->pdfUrl = $pdfUrl;
        $this->izbId = $izbId;
    }

    public function getPdfUrl(): string
    {
        return $this->pdfUrl;
    }

    public function setPdfUrl(string $pdfUrl)
    {
        $this->pdfUrl = $pdfUrl;
    }

    public function getIzbId(): string
    {
        return $this->izbId;
    }

    public function setIzbId(string $izbId)
    {
        $this->izbId = $izbId;
    }
}
