<?php

namespace AppBundle\Entity;

use <PERSON>trine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Table(name: 'contact')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\ContactRepository::class)]
#[ORM\HasLifecycleCallbacks]
class Contact implements \JsonSerializable
{

    use TimestampedTrait;
    use TechnicalIdentifierTrait;


    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

    /**
     * @var string
     */
    #[Assert\Length(max: 50, groups: ['contact'])]
    #[ORM\Column(name: 'first_name', type: 'string', length: 50, nullable: true)]
    private $firstName;

    /**
     * @var string
     */
    #[Assert\Length(max: 50, groups: ['contact'])]
    #[ORM\Column(name: 'last_name', type: 'string', length: 50, nullable: true)]
    private $lastName;


    /**
     * @var string
     */
    #[Assert\Length(max: 20, groups: ['contact'])]
    #[Assert\Regex(pattern: '/^[\+]?[0-9]+$/', match: true, message: 'form.contact.phone_regex', groups: ['contact'])]
    #[ORM\Column(name: 'phone1', type: 'string', length: 20, nullable: true)]
    private $phone1;

    /**
     * @var string
     */
    #[Assert\Regex(pattern: '/^[0-9,\+]+$/', match: true, message: 'form.contact.phone_regex', groups: ['contact'])]
    #[ORM\Column(name: 'phone2', type: 'string', length: 20, nullable: true)]
    private $phone2;

    /**
     * @var string
     */
    #[Assert\Email(groups: ['contact'])]
    #[Assert\Length(max: 50, groups: ['contact'])]
    #[ORM\Column(name: 'email', type: 'string', length: 50, nullable: true)]
    private $email;

    /**
     * @var string
     */
    #[Assert\Length(max: 50, groups: ['contact'])]
    #[ORM\Column(name: 'role', type: 'string', length: 50, nullable: true)]
    private $function;

    /**
     * One site has Many shipping points.
     */
    #[ORM\OneToMany(targetEntity: \ShippingPoint::class, mappedBy: 'contact', cascade: ['persist'])]
    private $shippingPoints;

    private $check;

    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @return string
     */
    public function getPhone1()
    {
        return $this->phone1;
    }

    /**
     * @param string $phone1
     */
    public function setPhone1($phone1)
    {
        $this->phone1 = $phone1;
    }

    /**
     * @return string
     */
    public function getPhone2()
    {
        return $this->phone2;
    }

    /**
     * @param string $phone2
     */
    public function setPhone2($phone2)
    {
        $this->phone2 = $phone2;
    }


    /**
     * @return string
     */
    public function getEmail()
    {
        return $this->email;
    }

    /**
     * @param string $email
     */
    public function setEmail($email)
    {
        $this->email = $email;
    }



    public function getContactLabel()
    {
        return $this->__toString() . ", " . $this->phone1;
    }


    public function __toString()
    {
        $fullName = "Contact sans nom";
        if ($this->firstName != null) {
            $fullName = $this->firstName;
        }
        if($this->lastName != null){
            $fullName = $fullName." ".$this->lastName;
        }
        return $fullName;
    }

    /**
     * @return mixed
     */
    public function getCheck()
    {
        return $this->check;
    }

    /**
     * @param mixed $check
     */
    public function setCheck($check)
    {
        $this->check = $check;
    }

    /**
     * @return string
     */
    public function getFirstName()
    {
        return $this->firstName;
    }

    /**
     * @param string $firstName
     */
    public function setFirstName(string $firstName)
    {
        $this->firstName = $firstName;
    }

    /**
     * @return string
     */
    public function getLastName()
    {
        return $this->lastName;
    }

    /**
     * @param string $lastName
     */
    public function setLastName(string $lastName)
    {
        $this->lastName = $lastName;
    }

    /**
     * @return string
     */
    public function getFunction()
    {
        return $this->function;
    }

    /**
     * @param string $function
     */
    public function setFunction(string $function)
    {
        $this->function = $function;
    }



    public function jsonSerialize(): array {
        return [
            'check' => $this->check,
            'email' => $this->email,
            'firstName' => $this->firstName,
            'function' => $this->function,
            'lastName' => $this->lastName,
            'phone1' => $this->phone1,
            'phone2' => $this->phone2
        ];
    }

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->shippingPoints = new \Doctrine\Common\Collections\ArrayCollection();
    }

    /**
     * Add shippingPoint.
     *
     * @param \AppBundle\Entity\ShippingPoint $shippingPoint
     *
     * @return Contact
     */
    public function addShippingPoint(\AppBundle\Entity\ShippingPoint $shippingPoint)
    {
        $this->shippingPoints[] = $shippingPoint;

        return $this;
    }

    /**
     * Remove shippingPoint.
     *
     * @param \AppBundle\Entity\ShippingPoint $shippingPoint
     *
     * @return boolean TRUE if this collection contained the specified element, FALSE otherwise.
     */
    public function removeShippingPoint(\AppBundle\Entity\ShippingPoint $shippingPoint)
    {
        return $this->shippingPoints->removeElement($shippingPoint);
    }

    /**
     * Get shippingPoints.
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getShippingPoints()
    {
        return $this->shippingPoints;
    }
}
