<?php
/**
 * Created by PhpStorm.
 * User: AQU04740
 * Date: 29/11/2017
 * Time: 14:09
 */

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>\Serializer\Annotation\Exclude;
use <PERSON>ymfony\Component\Validator\Constraints as Assert;
use <PERSON>MS\Serializer\Annotation\Type;

#[ORM\Table(name: 'address')]
#[ORM\Entity(repositoryClass: 'AppBundle\Repository\AddressRepository')]
#[ORM\HasLifecycleCallbacks]
class Address implements \JsonSerializable
{

    use TimestampedTrait;
    use TechnicalIdentifierTrait;
    #[ORM\Id]
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    #[Type("int")]
    private $id;

    #[ORM\Column(name: 'address', type: 'string', length: 100, nullable: true)]
    #[Type("string")]
    #[Assert\Length(max: 100)]
    #[Assert\NotBlank(groups: ['contact'])]
    private $address;

    #[ORM\Column(name: 'address2', type: 'string', length: 100, nullable: true)]
    #[Type("string")]
    #[Assert\Length(max: 100)]
    private $address2;

    #[ORM\Column(name: 'city', type: 'string', length: 50, nullable: true)]
    #[Type("string")]
    #[Assert\Length(max: 50)]
    #[Assert\NotBlank(groups: ['contact'])]
    private $city;

    #[ORM\Column(name: 'zip_code', type: 'string', length: 10, nullable: true)]
    #[Type("string")]
    #[Assert\Length(max: 10)]
    #[Assert\NotBlank(groups: ['contact'])]
    private $zipCode;

    #[ORM\Column(name: 'longitude', type: 'string', length: 20, nullable: true)]
    #[Type("string")]
    private $longitude;

    #[ORM\Column(name: 'latitude', type: 'string', length: 20, nullable: true)]
    #[Type("string")]
    private $latitude;

    #[ORM\ManyToOne(targetEntity: 'AppBundle\Entity\Country')]
    #[Exclude]
    private $country;

    #[ORM\ManyToOne(targetEntity: 'AppBundle\Entity\Region')]
    #[Exclude]
    private $region;

    #[ORM\Column(name: 'region_text', type: 'string', length: 50, nullable: true)]
    #[Type("array")]
    #[Assert\Length(max: 50)]
    private $regionText;

    #[ORM\OneToMany(targetEntity: 'AppBundle\Entity\ShippingPoint', mappedBy: 'address', cascade: ['persist'])]
    #[Exclude]
    private $shippingPoints;

    private $check;

    #[ORM\Column(name: 'izberg_address_id', type: 'string', nullable: true)]
    #[Type("string")]
    private $izbergAddressId;


    public function getAddressLabel()
    {
        return $this->address . " " . $this->zipCode . ", " . $this->city;
    }

    /**
     * @return int
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId(?int $id)
    {
        $this->id = $id;
    }

    /**
     * @return string
     */
    public function getAddress(): ?string
    {
        return $this->address;
    }

    /**
     * @param string $address
     */
    public function setAddress( $address)
    {
        $this->address = $address;
    }

    /**
     * @return string
     */
    public function getAddress2(): ?string
    {
        return $this->address2;
    }

    /**
     * @param $address2
     */
    public function setAddress2(?string $address2)
    {
        $this->address2 = $address2;
    }

    /**
     * @return string
     */
    public function getCity(): ?string
    {
        return $this->city;
    }

    /**
     * @param string $city
     */
    public function setCity( $city)
    {
        $this->city = $city;
    }

    /**
     * @return string
     */
    public function getZipCode(): ?string
    {
        return $this->zipCode;
    }

    /**
     * @param string $zipCode
     */
    public function setZipCode($zipCode)
    {
        $this->zipCode = $zipCode;
    }



    /**
     * @return string
     */
    public function getLongitude(): ?string
    {
        return $this->longitude;
    }

    /**
     * @param string $longitude
     */
    public function setLongitude(string $longitude)
    {
        $this->longitude = $longitude;
    }

    /**
     * @return string
     */
    public function getLatitude(): ?string
    {
        return $this->latitude;
    }

    /**
     * @param string $latitude
     */
    public function setLatitude(string $latitude)
    {
        $this->latitude = $latitude;
    }

    /**
     * @return Country
     */
    public function getCountry()
    {
        return $this->country;
    }

    /**
     * @param Country $country
     */
    public function setCountry($country): void
    {
        $this->country = $country;
    }

    /**
     * @return mixed
     */
    public function getCheck()
    {
        return $this->check;
    }

    /**
     * @param mixed $check
     */
    public function setCheck($check)
    {
        $this->check = $check;
    }

    /**
     * @return mixed
     */
    public function getRegion()
    {
        return $this->region;
    }

    /**
     * @param mixed $region
     */
    public function setRegion($region)
    {
        $this->region = $region;
    }

	/**
	 * @return mixed
	 */
	public function getRegionText()
	{
		return $this->regionText;
	}

	/**
	 * @param mixed $regionText
	 */
	public function setRegionText($regionText)
	{
		$this->regionText = $regionText;
	}



    public function __toString()
    {
        $result = "(";
        foreach ($this->jsonSerialize() as $key => $value){
            $result .= $key."=".$value.",";
        }

        return $result . ")";
    }

    public function jsonSerialize(): array {
        return [
            'city' =>$this->city,
            "address" => $this->address,
            "address2" => $this->address2,
            "latitude" => $this->latitude,
            "longitude" => $this->longitude,
            "zipCode" => $this->zipCode
        ];
    }


    /**
     * Constructor
     */
    public function __construct()
    {
        $this->shippingPoints = new \Doctrine\Common\Collections\ArrayCollection();
    }

    /**
     * Add shippingPoint.
     *
     * @param \AppBundle\Entity\ShippingPoint $shippingPoint
     *
     * @return Address
     */
    public function addShippingPoint(\AppBundle\Entity\ShippingPoint $shippingPoint)
    {
        $this->shippingPoints[] = $shippingPoint;

        return $this;
    }

    /**
     * Remove shippingPoint.
     *
     * @param \AppBundle\Entity\ShippingPoint $shippingPoint
     *
     * @return boolean TRUE if this collection contained the specified element, FALSE otherwise.
     */
    public function removeShippingPoint(\AppBundle\Entity\ShippingPoint $shippingPoint)
    {
        return $this->shippingPoints->removeElement($shippingPoint);
    }

    /**
     * Get shippingPoints.
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getShippingPoints()
    {
        return $this->shippingPoints;
    }


    /**
     * @return string
     */
    public function getIzbergAddressId()
    {
        return $this->izbergAddressId;
    }

    /**
     * @param $izbergAddressId
     */
    public function setIzbergAddressId($izbergAddressId)
    {
        $this->izbergAddressId = $izbergAddressId;
    }

    public function isEqual(Address $address)
    {
        return
            $this->address === $address->getAddress() &&
            $this->address2 === $address->getAddress2() &&
            $this->zipCode === $address->getZipCode() &&
            $this->city === $address->getCity() &&
            $this->regionText === $address->getRegionText();
    }

    public function createFromAddress(Address $address): void
    {
        $this->address = $address->getAddress();
        $this->address2 = $address->getAddress2();
        $this->zipCode = $address->getZipCode();
        $this->city = $address->getCity();
        $this->regionText = $address->getRegionText();
        $this->country = $address->getCountry();
    }
}
