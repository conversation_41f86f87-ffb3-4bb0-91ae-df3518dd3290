<?php
/**
 * Created by PhpStorm.
 * User: CMA15450
 * Date: 28/04/2017
 * Time: 09:50
 */

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Component\Validator\Constraints as Assert;


/**
 * Document
 */
#[UniqueEntity('name')]
#[ORM\Table(name: 'menu')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\MenuRepository::class)]
class Menu
{
    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

    #[Assert\NotBlank]
    #[ORM\Column(name: 'name', type: 'string', length: 20, unique: true)]
    private $name;

    #[Assert\NotBlank]
    #[ORM\Column(name: 'label', type: 'string', length: 50)]
    private $label;

    #[ORM\Column(name: 'description', type: 'string', length: 255)]
    private $description;


    /**
     * One company has Many sites.
     */
    #[ORM\OneToMany(targetEntity: \MenuItem::class, mappedBy: 'menu', cascade: ['persist'], orphanRemoval: true)]
    #[ORM\OrderBy(['weight' => 'ASC'])]
    private $items;


    public function __construct()
    {
        $this->items = new ArrayCollection();
    }


    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * @param mixed $name
     */
    public function setName($name)
    {
        $this->name = $name;
    }

    /**
     * @return mixed
     */
    public function getLabel()
    {
        return $this->label;
    }

    /**
     * @param mixed $label
     */
    public function setLabel($label)
    {
        $this->label = $label;
    }

    /**
     * @return mixed
     */
    public function getDescription()
    {
        return $this->description;
    }

    /**
     * @param mixed $description
     */
    public function setDescription($description)
    {
        $this->description = $description;
    }

    /**
     * @return mixed
     */
    public function getItems()
    {
        return $this->items;
    }

    /**
     * @param mixed $items
     */
    public function setItems($items)
    {
        $this->items = $items;
    }

    /**
     * The medthod is called when adding a menu from the 'add' button
     * @return MenuItem
     */
    public function addItem()
    {
        $item = new MenuItem();

        $item->setMenu($this->id); // link the menu item to this menu

        $this->items->add($item);

        return $item;
    }

    public function __toString()
    {
        if ($this->label) {
            return $this->label;
        } else {
            return "Menu sans nom";
        }
    }
}
