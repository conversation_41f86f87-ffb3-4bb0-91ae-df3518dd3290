<?php

declare(strict_types=1);

namespace AppBundle\Entity\CreationSource;

trait CreationSourceTrait
{
    private string $creationSource = AbstractCreationSource::SOURCE_FRONT;

    public function getCreationSource(): string
    {
        return $this->creationSource;
    }

    public function setCreationSource(string $creationSource): self
    {
        $this->creationSource = $creationSource;

        return $this;
    }

    public function isApiSource(): bool
    {
        return $this->creationSource === AbstractCreationSource::SOURCE_API;
    }
}
