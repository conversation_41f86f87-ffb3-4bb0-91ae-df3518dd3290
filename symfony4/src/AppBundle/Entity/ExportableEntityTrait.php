<?php

namespace AppBundle\Entity;

use JMS\Serializer\Annotation\Exclude;

trait ExportableEntityTrait
{
    /**
     * @var array
     */
    #[Exclude]
    private $customProperties = [];

    /**
     * @return mixed
     */
    public function getCustomProperties()
    {
        return $this->customProperties;
    }

    /**
     * @param mixed $customProperties
     */
    public function setCustomProperties($customProperties): void
    {
        $this->customProperties = $customProperties;
    }

    /**
     * @param string $name name of the property
     * @param mixed $value value of the property
     */
    public function addProperty (string $name, $value = ""){
        $this->customProperties[$name] = $value;
    }

    public function getPropertyValue (string $name){
        try{
            return $this->customProperties[$name];
        }catch (\Exception $e){
            return "";
        }
    }

}
