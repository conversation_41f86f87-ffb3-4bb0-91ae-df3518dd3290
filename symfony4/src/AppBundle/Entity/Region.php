<?php
/**
 * Created by PhpStorm.
 * User: AQU04740
 * Date: 16/01/2018
 * Time: 14:47
 */

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

/**
 * Company
 */
#[UniqueEntity(fields: 'id')]
#[ORM\Table(name: 'region')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\RegionRepository::class)]
#[ORM\HasLifecycleCallbacks]
class Region implements \JsonSerializable
{

    use TechnicalIdentifierTrait;

    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    private $id;

    /**
     * @var String
     */
    #[ORM\Column(name: 'code', type: 'string', length: 20)]
    private $code;

    /**
     * Many regions have One Country.
     */
    #[ORM\JoinColumn(name: 'country_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\Country::class, inversedBy: 'regions')]
    private $country;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId(int $id)
    {
        $this->id = $id;
    }

    /**
     * @return String
     */
    public function getCode(): String
    {
        return $this->code;
    }

    /**
     * @param String $code
     */
    public function setCode(String $code)
    {
        $this->code = $code;
    }

    /**
     * @return mixed
     */
    public function getCountry()
    {
        return $this->country;
    }

    /**
     * @param mixed $country
     */
    public function setCountry($country)
    {
        $this->country = $country;
    }

    public function __toString()
    {
        return 'country.region.' . $this->code;
    }

    public function jsonSerialize(): array {
        return [
            'code' => $this->code
        ];
    }





}
