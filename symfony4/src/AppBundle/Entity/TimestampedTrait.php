<?php
namespace AppBundle\Entity;

use <PERSON>trine\ORM\Mapping as ORM;
use <PERSON><PERSON>\Serializer\Annotation\Type;
use <PERSON><PERSON>\Serializer\Annotation\Exclude;

trait TimestampedTrait
{
    /**
     * created Time/Date
     *
     * @var \DateTimeImmutable
     */
    #[ORM\Column(name: 'created_at', type: 'datetime', nullable: true)]
    #[exclude]
    protected $createdAt;

    /**
     * updated Time/Date
     *
     * @var \DateTimeImmutable
     */
    #[ORM\Column(name: 'updated_at', type: 'datetime', nullable: true)]
    #[exclude]
    protected $updatedAt;

    /**
     * Set createdAt
     *
     * @throws \Exception
     */
    #[ORM\PrePersist]
    public function setCreatedAt()
    {
        $this->createdAt = new \DateTimeImmutable();
        $this->updatedAt = new \DateTimeImmutable();
    }

    public function forceCreatedAt($createdAt){
        $this->createdAt = $createdAt;
    }

    /**
     * Get createdAt
     *
     * @return \DateTimeImmutable
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set updatedAt
     *
     * @throws \Exception
     */
    #[ORM\PreUpdate]
    public function setUpdatedAt()
    {
        $this->updatedAt = new \DateTimeImmutable();
    }

    public function forceUpdatedAt($updatedAt){
        $this->updatedAt = $updatedAt;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTimeImmutable
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }
}
