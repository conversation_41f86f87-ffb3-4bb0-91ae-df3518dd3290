<?php

namespace AppBundle\Entity;

use AppBundle\Repository\ThreadParentMessageRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'thread_messages')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\ThreadMessageRepository::class)]
#[ORM\HasLifecycleCallbacks]
class ThreadMessage
{
    use TimestampedTrait;

    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $izbergId;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $izbergUserId;

    #[ORM\Column(type: 'string', length: 220, nullable: true)]
    private $subject;

    #[ORM\Column(type: 'string', length: 100, nullable: true)]
    private $senderResourceUri;

    #[ORM\Column(type: 'string', length: 50, nullable: true)]
    private $fromDisplayName;

    #[ORM\Column(type: 'string', length: 50, nullable: true)]
    private $toDisplayName;

    #[ORM\Column(type: 'string', nullable: true)]
    private $originalCreatedDate;

    #[ORM\Column(type: 'string', nullable: true)]
    private $lastModifiedDate;

    #[ORM\Column(type: 'boolean')]
    private $hasUnreadMessages;


    public function getIzbergUserId(): ?int
    {
        return $this->izbergUserId;
    }

    public function setIzbergUserId(?int $izbergUserId): void
    {
        $this->izbergUserId = $izbergUserId;
    }

    public function getIzbergId(): ?int
    {
        return $this->izbergId;
    }

    public function setIzbergId(?int $izbergId): void
    {
        $this->izbergId = $izbergId;
    }

    public function getSubject(): ?string
    {
        return $this->subject;
    }

    public function setSubject(?string $subject): void
    {
        $this->subject = $subject;
    }

    public function getSenderResourceUri(): ?string
    {
        return $this->senderResourceUri;
    }

    public function setSenderResourceUri(?string $senderResourceUri): void
    {
        $this->senderResourceUri = $senderResourceUri;
    }

    public function getFromDisplayName(): ?string
    {
        return $this->fromDisplayName;
    }

    public function setFromDisplayName(?string $fromDisplayName): void
    {
        $this->fromDisplayName = $fromDisplayName;
    }

    public function getToDisplayName(): ?string
    {
        return $this->toDisplayName;
    }

    public function setToDisplayName(?string $toDisplayName): void
    {
        $this->toDisplayName = $toDisplayName;
    }

    public function getOriginalCreatedDate()
    {
        return $this->originalCreatedDate;
    }

    public function setOriginalCreatedDate($originalCreatedDate): void
    {
        $this->originalCreatedDate = $originalCreatedDate;
    }

    public function getLastModifiedDate()
    {
        return $this->lastModifiedDate;
    }

    public function setLastModifiedDate($lastModifiedDate): void
    {
        $this->lastModifiedDate = $lastModifiedDate;
    }

    public function getHasUnreadMessages(): ?bool
    {
        return $this->hasUnreadMessages;
    }

    public function setHasUnreadMessages(bool $hasUnreadMessages): void
    {
        $this->hasUnreadMessages = $hasUnreadMessages;
    }

}
