<?php

namespace AppBundle\Entity;

use <PERSON>trine\ORM\Mapping as ORM;

#[ORM\Table(name: 'search_historization')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\SearchHistorizationRepository::class)]
class SearchHistorization implements \JsonSerializable
{
    use ExportableEntityTrait;

    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

    /**
     * created Time/Date
     *
     * @var \DateTime
     */
    #[ORM\Column(name: 'created_at', type: 'datetime', nullable: true)]
    private $createdAt;

    #[ORM\Column(name: 'is_anonymous', type: 'boolean', nullable: true, options: ['default' => true])]
    private $is_anonymous;

    #[ORM\Column(name: 'company_name', type: 'string', length: 255, nullable: true)]
    private $company_name;

    #[ORM\Column(name: 'user_full_name', type: 'string', length: 255, nullable: true)]
    private $user_full_name;

    #[ORM\Column(name: 'search_term', type: 'string', length: 1024, nullable: true)]
    private $search_term;

    #[ORM\Column(name: 'filter', type: 'string', length: 4096, nullable: true)]
    private $filter;

    #[ORM\Column(name: 'nb_hits', type: 'integer', nullable: true)]
    private $nb_hits;

    /**
     * SearchHistorization constructor.
     * @param $is_anonymous
     * @param $company_name
     * @param $user_full_mame
     * @param $search_term
     * @param $filter
     * @param $nb_hits
     */
    public function __construct(
        $is_anonymous,
        $company_name,
        $user_full_mame,
        $search_term,
        $filter,
        $nb_hits
    )
    {
        $this->setCreatedAt();
        $this->is_anonymous = $is_anonymous;
        $this->company_name = $company_name;
        $this->user_full_name = $user_full_mame;
        $this->search_term = $search_term;
        $this->filter = $filter;
        $this->nb_hits = $nb_hits;
    }

  /**
   * @return int
   */
  public function getId(): int
  {
    return $this->id;
  }

  /**
   * @param int $id
   */
  public function setId(int $id): void
  {
    $this->id = $id;
  }

  /**
   * @return \DateTime
   */
  public function getCreatedAt(): \DateTime
  {
    return $this->createdAt;
  }

    /**
     * @param \DateTime|null $createdAt
     */
  public function setCreatedAt(\DateTime $createdAt = null): void
  {
    if (empty($createdAt)){
      $createdAt = new \DateTime();
    }
    $this->createdAt = $createdAt;
  }

    /**
     * @return bool
     */
    public function getIsAnonymous(): bool
    {
        return $this->is_anonymous;
    }

    /**
     * @param mixed $is_anonymous
     */
    public function setIsAnonymous(bool $is_anonymous): void
    {
        $this->is_anonymous = $is_anonymous;
    }

    /**
     * @return string|null
     */
    public function getCompanyName(): ?string
    {
        return $this->company_name;
    }

    /**
     * @param string|null $company_name
     * @return $this
     */
    public function setCompanyName(?string $company_name): self
    {
        $this->company_name = $company_name;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getUserFullName(): ?string
    {
        return $this->user_full_name;
    }

    /**
     * @param string|null $user_full_name
     * @return $this
     */
    public function setUserFullName(?string $user_full_name): self
    {
        $this->user_full_name = $user_full_name;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getSearchTerm()
    {
        return $this->search_term;
    }

    /**
     * @param mixed $search_term
     */
    public function setSearchTerm($search_term): void
    {
        $this->search_term = $search_term;
    }

  /**
   * @return mixed
   */
  public function getFilter()
  {
    return $this->filter;
  }

  /**
   * @param mixed $filter
   */
  public function setFilter($filter): void
  {
    $this->filter = $filter;
  }

  /**
   * @return mixed
   */
  public function getNbHits()
  {
    return $this->nb_hits;
  }

  /**
   * @param mixed $nb_hits
   */
  public function setNbHits($nb_hits): void
  {
    $this->nb_hits = $nb_hits;
  }

    /**
     * @return array|mixed
     */
    public function jsonSerialize(): array
    {
        return [
            'id' => $this->id,
            'created_at' => $this->createdAt,
            'is_anonymous' => $this->is_anonymous,
            'user_full_name' => $this->user_full_name,
            'company_name' => $this->company_name,
            'search_term' => $this->search_term,
            'filter' => $this->filter,
            'nb_hits' => $this->nb_hits,
        ];
    }

}
