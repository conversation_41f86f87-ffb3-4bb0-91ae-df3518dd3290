<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'tva_rate')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\TvaRateRepository::class)]
#[ORM\HasLifecycleCallbacks]
class TvaRate
{

    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\TvaGroup::class)]
    private $group;

    /**
     *
     * @var \DateTime
     */
    #[ORM\Column(name: 'from_date', type: 'datetime', nullable: true)]
    private $fromDate;


    #[ORM\Column(name: 'rate', type: 'integer')]
    private $rate;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId(int $id)
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getGroup()
    {
        return $this->group;
    }

    /**
     * @param mixed $group
     */
    public function setGroup($group)
    {
        $this->group = $group;
    }

    /**
     * @return \DateTime
     */
    public function getFromDate(): \DateTime
    {
        return $this->fromDate;
    }

    /**
     * @param \DateTime $from
     */
    public function setFromDate(\DateTime $fromDate)
    {
        $this->fromDate = $fromDate;
    }

    /**
     * @return mixed
     */
    public function getRate()
    {
        return $this->rate;
    }

    /**
     * @param mixed $rate
     */
    public function setRate($rate)
    {
        $this->rate = $rate;
    }
}
