<?php

namespace AppBundle\Entity;

use <PERSON>trine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Table(name: 'file')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\BddFileRepository::class)]
class BddFile
{
    /**
     * @var int
     */
    #[ORM\Id]
    #[ORM\Column(type: 'integer')]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

    /**
     * @var string
     */
    #[Assert\NotBlank]
    #[ORM\Column(name: 'name', type: 'string', length: 255, nullable: false)]
    private $name;

    /**
     * @var string
     */
    #[Assert\NotBlank]
    #[ORM\Column(name: 'type', type: 'string', length: 50, nullable: false)]
    private $type;

    #[ORM\Column(name: 'created_at', type: 'datetime', nullable: true)]
    private $createdAt;

    #[ORM\Column(name: 'data', type: 'blob')]
    private $data;

    #[ORM\Column(name: 'size', type: 'string', length: 255, nullable: false)]
    private $size;

    #[ORM\Column(name: 'token', type: 'string', length: 255, nullable: false)]
    private $token;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     * @return BddFile
     */
    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @param string $name
     * @return BddFile
     */
    public function setName(string $name): self
    {
        $this->setToken(uniqid(sha1($name)));
        $this->name = $name;
        return $this;
    }

    /**
     * @return string
     */
    public function getType(): string
    {
        return $this->type;
    }

    /**
     * @param string $type
     * @return BddFile
     */
    public function setType(string $type): self
    {
        $this->type = $type;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * @param mixed $createdAt
     * @return BddFile
     */
    public function setCreatedAt($createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getData()
    {
        return $this->data;
    }

    /**
     * @param mixed $data
     * @return BddFile
     */
    public function setData($data): self
    {
        $this->data = $data;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getSize()
    {
        return $this->size;
    }

    /**
     * @param mixed $size
     * @return BddFile
     */
    public function setSize($size): self
    {
        $this->size = $size;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getToken()
    {
        return $this->token;
    }

    /**
     * @param mixed $token
     */
    private function setToken($token): self
    {
        $this->token = $token;
        return $this;
    }
}
