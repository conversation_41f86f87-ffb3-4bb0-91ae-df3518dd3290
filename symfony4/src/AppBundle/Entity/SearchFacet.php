<?php

namespace AppBundle\Entity;

class SearchFacet
{
    /**
     * @var string
     */
    private $name;

    /**
     * @var string
     */
    private $label;

    /**
     * @var array
     */
    private $values = [];

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getLabel(): string
    {
        return $this->label;
    }

    public function setLabel(string $label): self
    {
        $this->label = $label;

        return $this;
    }

    public function getValues(): array
    {
        return $this->values;
    }

    public function addValue(SearchFacetValue $facetValue): self
    {
        $this->values[] = $facetValue;

        return $this;
    }

    public function hasFacetValue(SearchFacetValue $searchedFacetValue): bool
    {
        return (
            count(
                array_filter(
                    $this->getValues(),
                    function(SearchFacetValue $facetValue) use ($searchedFacetValue) {
                        return ($facetValue->getValue() === $searchedFacetValue->getValue());
                    }
                )
            ) >= 1
        );
    }

    public function toArray(): array
    {
        return array_map(
            fn(SearchFacetValue $facetValue): string => sprintf('%s:%s', $this->name, $facetValue->getValue()),
            $this->values
        );
    }
}
