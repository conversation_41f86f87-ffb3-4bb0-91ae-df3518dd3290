<?php

namespace AppBundle\Entity;

use \Open\IzbergBundle\Model\Category;

class SearchResult
{
    /**
     * @var Category
     */
    private $category;

    /**
     * @var string
     */
    private $parentCategory;

    /**
     * @var int
     */
    private $totalHits;

    /**
     * @var int
     */
    private $numberOfPages;

    /**
     * @var int
     */
    private $currentPage;

    /**
     * @var array
     */
    private $offers;

    /**
     * @var array
     */
    private $commonFacets;

    /**
     * @var array
     */
    private $specificFacets;

    /**
     * @var array
     */
    private $filters;

    /**
     * @var array
     */
    private $selectedFacets;

    public function __construct()
    {
        $this->offers = [];
        $this->commonFacets = [];
        $this->specificFacets = [];
        $this->filters = [];
        $this->selectedFacets = [];

        $this->totalHits = 0;
        $this->numberOfPages = 1;
        $this->currentPage = 1;
    }

    public function getCategory():?Category
    {
        return $this->category;
    }

    public function setCategory(?Category $category): void
    {
        $this->category = $category;
    }

    public function hasOffers(): bool
    {
        return (count($this->getOffers()) >= 1);
    }

    public function getOffers(): array
    {
        return $this->offers;
    }

    public function setOffers(array $offers): void
    {
        $this->offers = $offers;
    }

    public function setTotalHits(int $totalHits): void
    {
        $this->totalHits = $totalHits;
    }

    public function getTotalHits(): int
    {
        return $this->totalHits;
    }

    public function getNumberOfPages(): int
    {
        return $this->numberOfPages;
    }

    public function getCurrentPage(): int
    {
        return $this->currentPage;
    }

    public function setNumberOfPages(int $numberOfPages)
    {
        $this->numberOfPages = $numberOfPages;
    }

    public function total(): int
    {
        return $this->totalHits;
    }

    public function hasFacets(): bool
    {
        return (count($this->getCommonFacets()) + count($this->getSpecificFacets()) >= 1);
    }

    public function pages(): array
    {
        return ($this->numberOfPages) ? range(0, $this->numberOfPages - 1) : [];
    }

    public function currentPage(): int
    {
        return $this->currentPage;
    }

    public function setCurrentPage(int $currentPage): void
    {
        $this->currentPage = $currentPage;
    }

    public function getParentCategory():? string
    {
        return $this->parentCategory;
    }

    public function setParentCategory(?string $parentCategory): void
    {
        $this->parentCategory = $parentCategory;
    }

    /**
     * @return array
     */
    public function getCommonFacets(): array
    {
        return $this->commonFacets;
    }

    /**
     * @param array $commonFacets
     */
    public function setCommonFacets(array $commonFacets): void
    {
        $this->commonFacets = $commonFacets;
    }

    public function getSpecificFacets(): array
    {
        return $this->specificFacets;
    }

    public function setSpecificFacets(array $specificFacets): void
    {
        $this->specificFacets = $specificFacets;
    }

    public function getFilters(): array
    {
        return $this->filters;
    }

    public function setFilters(array $filters): void
    {
        $this->filters = $filters;
    }

    public function getSelectedFacets(): array
    {
        return $this->selectedFacets;
    }

    public function setSelectedFacets(array $selectedFacets): void
    {
        $this->selectedFacets = $selectedFacets;
    }
}
