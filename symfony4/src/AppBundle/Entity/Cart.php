<?php

namespace AppBundle\Entity;

use AppBundle\Entity\CreationSource\AbstractCreationSource;
use AppBundle\Entity\CreationSource\CreationSourceTrait;
use AppBundle\Util\AsAccountingEmail;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * Cart
 *
 *
 */
#[ORM\Table(name: 'cart')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\CartRepository::class)]
#[ORM\HasLifecycleCallbacks]
class Cart
{
    use AsAccountingEmail;

    //the cart has been assigned to an other user
    public const STATUS_ASSIGN = "ASSIGN";

    //the cart has been created: It is not assigned to any user
    public const STATUS_CREATE = "CREATE";

    //the cart has been rejected: it has been reassigned to the cart creator
    public const STATUS_REJECTED = "REJECTED";

    //The cart has been accepted and ordered by a buyer
    public const STATUS_ORDER = "ORDER";

    //All the process for this cart is DONE => this cart can't be updated
    public const STATUS_DONE = "DONE";

    //the cart should not appear anymore in the application
    public const STATUS_REMOVED = "REMOVED";

    use TimestampedTrait;
    use TechnicalIdentifierTrait;
    use CreationSourceTrait;

    /**
     * @var int
     */
    #[ORM\Id]
    #[ORM\Column(name: 'id', type: 'integer')]
    private $id;

    /**
     * One cart have ont user that created it
     *
     * @var User
     */
    #[ORM\JoinColumn(name: 'created_user_id', referencedColumnName: 'id', nullable: true)]
    #[ORM\ManyToOne(targetEntity: \User::class)]
    private $createdUser;

    /**
     * One cart have a current user that is assign to.
     *
     * @var User
     */
    #[ORM\JoinColumn(name: 'current_user_id', referencedColumnName: 'id', nullable: true)]
    #[ORM\ManyToOne(targetEntity: \User::class)]
    private $currentUser;

    /**
     * One cart have a site that is assign to.
     */
    #[ORM\JoinColumn(name: 'site_id', referencedColumnName: 'id', nullable: true)]
    #[ORM\ManyToOne(targetEntity: \Site::class)]
    private $site;

    #[Assert\Length(max: 20)]
    #[ORM\Column(name: 'status', type: 'string', length: 20, nullable: false)]
    private $status;

    #[ORM\Column(name: 'payment_mode', type: 'string', length: 50, nullable: true)]
    private $paymentMode;

    #[ORM\Column(name: 'payment_term', type: 'string', length: 50, nullable: true)]
    private $paymentTerm;

    #[ORM\Column(name: 'payment_method', type: 'string', length: 50, nullable: true)]
    private $paymentMethod;

    #[ORM\Column(name: 'payment_type', type: 'string', length: 50, nullable: true)]
    private $paymentType;

    #[ORM\Column(name: 'payment_term_izberg_id', type: 'string', length: 50, nullable: true)]
    private $paymentTermIzbergId;

    /**
     * One cart have a shipping address.
     */
    #[ORM\JoinColumn(name: 'address_id', referencedColumnName: 'id', nullable: true)]
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\Address::class)]
    private $address;

    #[ORM\JoinColumn(name: 'billing_address_id', referencedColumnName: 'id', nullable: true)]
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\Address::class, cascade: ['persist'])]
    private $billingAddress;

    /**
     * One company has Many users.
     */
    #[ORM\OneToMany(targetEntity: \CartHistoric::class, mappedBy: 'cart')]
    private $cartHistoric;


    #[ORM\OneToMany(targetEntity: \AppBundle\Entity\CartShippingOption::class, mappedBy: 'cart')]
    private $cartShippingOption;

    #[ORM\Column(name: 'order_id', type: 'integer', nullable: true)]
    private $orderId;


    #[ORM\Column(name: 'wps_transaction_id', type: 'string', length: 50, nullable: true)]
    private $wpsTransactionId;

    #[ORM\Column(name: 'wps_reconciliation_key', type: 'string', nullable: true)]
    private $wpsReconciliationKey;

    #[ORM\Column(name: 'validation_number', type: 'string', nullable: true)]
    private $validationNumber;

    /**
     * @var string|null
     */
    #[ORM\Column(name: 'buyer_order_id', type: 'string', nullable: true)]
    private $buyerOrderId;

    /**
     * @var array
     */
    #[ORM\Column(name: 'documents_request', type: 'array')]
    private $documentsRequest;

    #[ORM\Column]
    private string $creationSource = AbstractCreationSource::SOURCE_FRONT;

    public function __construct()
    {
        $this->cartHistoric = new ArrayCollection();
        $this->cartShippingOption = new ArrayCollection();
        $this->documentsRequest = [];
    }

    public function getCartId(): int
    {
        return $this->id;
    }

    public function setCartId(int $id): void
    {
        $this->id = $id;
    }

    public function getCreatedUser(): ?User
    {
        return $this->createdUser;
    }

    public function setCreatedUser(User $createdUser): void
    {
        $this->createdUser = $createdUser;
    }

    public function getCurrentUser(): ?User
    {
        return $this->currentUser;
    }

    public function setCurrentUser(User $currentUser): void
    {
        $this->currentUser = $currentUser;
    }

    public function getStatus()
    {
        return $this->status;
    }

    public function setStatus($status): void
    {
        $this->status = $status;
    }

    public function getCartHistoric()
    {
        return $this->cartHistoric;
    }

    public function setCartHistoric(ArrayCollection $cartHistoric)
    {
        $this->cartHistoric = $cartHistoric;
    }

    public function getSite(): ?Site
    {
        return $this->site;
    }

    public function setSite(?Site $site): void
    {
        $this->site = $site;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): void
    {
        $this->id = $id;
    }

    public function getAddress()
    {
        return $this->address;
    }

    public function setAddress($address): void
    {
        $this->address = $address;
    }

    public function getBillingAddress()
    {
        return $this->billingAddress;
    }

    public function setBillingAddress($billingAddress): void
    {
        $this->billingAddress = $billingAddress;
    }

    public function getOrderId()
    {
        return $this->orderId;
    }

    public function setOrderId($orderId): void
    {
        $this->orderId = $orderId;
    }

    public function getWpsTransactionId()
    {
        return $this->wpsTransactionId;
    }

    public function setWpsTransactionId($wpsTransactionId): void
    {
        $this->wpsTransactionId = $wpsTransactionId;
    }

    public function getPaymentMode()
    {
        return $this->paymentMode;
    }

    public function setPaymentMode($paymentMode): void
    {
        $this->paymentMode = $paymentMode;
    }

    public function getPaymentTerm()
    {
        return $this->paymentTerm;
    }

    public function setPaymentTerm($paymentTerm): void
    {
        $this->paymentTerm = $paymentTerm;
    }

    public function getPaymentMethod()
    {
        return $this->paymentMethod;
    }

    public function setPaymentMethod($paymentMethod): void
    {
        $this->paymentMethod = $paymentMethod;
    }

    public function getPaymentType()
    {
        return $this->paymentType;
    }

    public function setPaymentType($paymentType): void
    {
        $this->paymentType = $paymentType;
    }

    public function getPaymentTermIzbergId()
    {
        return $this->paymentTermIzbergId;
    }

    public function setPaymentTermIzbergId($paymentTermIzbergId): void
    {
        $this->paymentTermIzbergId = $paymentTermIzbergId;
    }

    /**
     * @return mixed
     */
    public function getWpsReconciliationKey()
    {
        return $this->wpsReconciliationKey;
    }

    /**
     * @param mixed $wpsReconciliationKey
     */
    public function setWpsReconciliationKey($wpsReconciliationKey): void
    {
        $this->wpsReconciliationKey = $wpsReconciliationKey;
    }

    /**
     * @return mixed
     */
    public function getValidationNumber()
    {
        return $this->validationNumber;
    }

    /**
     * @param mixed $validationNumber
     */
    public function setValidationNumber($validationNumber)
    {
        $this->validationNumber = $validationNumber;
    }

    /**
     * @return string|null
     */
    public function getBuyerOrderId(): ?string
    {
        return $this->buyerOrderId;
    }

    /**
     * @param string|null $buyerOrderId
     * @return $this
     */
    public function setBuyerOrderId(?string $buyerOrderId): self
    {
        $this->buyerOrderId = $buyerOrderId;
        return $this;
    }

    public function addCartShippingOption(CartShippingOption $cartShippingOption):self
    {
        $this->cartShippingOption[] = $cartShippingOption;

        return $this;
    }

    public function removeCartShippingOption(CartShippingOption $cartShippingOption):bool
    {
        return $this->cartShippingOption->removeElement($cartShippingOption);
    }

    public function getCartShippingOption()
    {
        return $this->cartShippingOption;
    }

    public function getDocumentsRequest(): array
    {
        return $this->documentsRequest;
    }

    public function setDocumentsRequest(array $documentsRequest): Cart
    {
        $this->documentsRequest = $documentsRequest;
        return $this;
    }
}
