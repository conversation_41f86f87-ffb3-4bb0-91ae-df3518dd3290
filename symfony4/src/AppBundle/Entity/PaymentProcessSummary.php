<?php

namespace AppBundle\Entity;

use AppBundle\Entity\PaymentProcess\Payment;
use AppBundle\Entity\PaymentProcess\PaymentProcessAbstract;
use AppBundle\Entity\PaymentProcess\Refund;
use AppBundle\Entity\PaymentProcess\Invoice;
use Open\IzbergBundle\Model\OrderMerchant;
use Open\IzbergBundle\Model\Order;

class PaymentProcessSummary
{
    private const TYPE_TERM_PAYMENT = 'term_payment';

    /**
     * @var float
     */
    private $alreadyInvoiced;

    /**
     * @var float
     */
    private $totalRemainingAmountToPay;

    /**
     * @var float
     */
    private $totalToPay;

    /**
     * @var array
     */
    private $paymentProcesses;

    /**
     * @var string
     */
    private $currency;

    /**
     * @var Order
     */
    private $order;

    /**
     * @var OrderMerchant
     */
    private $merchantOrder;

    public function __construct(Order $order, OrderMerchant $merchantOrder)
    {
        $this->paymentProcesses = [];
        $this->currency = $order->getCurrency();
        $this->order = $order;
        $this->merchantOrder = $merchantOrder;

        $this->totalToPay = $merchantOrder->getAmountVatIncluded();
        $this->totalRemainingAmountToPay = 0.0;
        $this->alreadyInvoiced = 0.0;
    }

    public function getAlreadyInvoiced(): float
    {
        return $this->alreadyInvoiced;
    }

    public function getNotYetInvoiced(): float
    {
        return $this->totalToPay - $this->alreadyInvoiced;
    }

    public function getTotalRemainingAmountToPay(): float
    {
        return $this->totalRemainingAmountToPay;
    }

    public function getTotalPayed(): float
    {
        return $this->totalToPay - $this->totalRemainingAmountToPay;
    }

    public function getTotalToPay(): float
    {
        return $this->totalToPay;
    }

    public function setTotalToPay(float $totalToPay): void
    {
        $this->totalToPay = $totalToPay;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function setCurrency(string $currency): void
    {
        $this->currency = $currency;
    }

    public function getOrder(): Order
    {
        return $this->order;
    }

    public function setOrder(Order $order): void
    {
        $this->order = $order;
    }

    public function getMerchantOrder(): OrderMerchant
    {
        return $this->merchantOrder;
    }

    public function setMerchantOrder(OrderMerchant $merchantOrder): void
    {
        $this->merchantOrder = $merchantOrder;
    }

    public function getPaymentProcesses(): array
    {
        usort(
            $this->paymentProcesses,
            function(PaymentProcessAbstract $onePaymentProcess, PaymentProcessAbstract $anotherPaymentProcess) {
                return ($onePaymentProcess->getCreatedOn() < $anotherPaymentProcess->getCreatedOn()) ? -1 : 1;
            }
        );

        return $this->paymentProcesses;
    }

    public function isPrePaymentType(): bool
    {
        return (!$this->isTermPaymentType());
    }

    public function isTermPaymentType(): bool
    {
        return ($this->merchantOrder->getPaymentType() === self::TYPE_TERM_PAYMENT);
    }

    public function addPayment(Payment $payment)
    {
        $this->paymentProcesses[] = $payment;
        $this->totalRemainingAmountToPay = ($this->alreadyInvoiced) - $this->totalRemainingAmountToPay - $payment->getPayment();
    }

    public function addRefund(Refund $refund)
    {
        $this->paymentProcesses[] = $refund;
    }

    public function addInvoice(Invoice $invoice)
    {
        $this->paymentProcesses[] = $invoice;
        $this->alreadyInvoiced = $this->alreadyInvoiced + $invoice->getAmount();
        $this->totalRemainingAmountToPay = $this->alreadyInvoiced - $this->totalRemainingAmountToPay;
    }

    public function hasRefund(): bool
    {
        return (
            count(
                array_filter($this->paymentProcesses, function(PaymentProcessAbstract $paymentProcess) {
                    return ($paymentProcess instanceof Refund);
                })
            ) >= 1
        );
    }

    public function hasPayment(): bool
    {
        return (
            count(
                array_filter($this->paymentProcesses, function(PaymentProcessAbstract $paymentProcess) {
                    return ($paymentProcess instanceof Payment);
                })
            ) >= 1
        );

    }

    public function hasInvoice(): bool
    {
        return (
            count(
                array_filter($this->paymentProcesses, function(PaymentProcessAbstract $paymentProcess) {
                    return ($paymentProcess instanceof Invoice);
                })
            ) >= 1
        );
    }

    public function isPaid(): bool
    {
        return (
            $this->isPrePaymentType()
            || (
                $this->alreadyInvoiced === $this->totalToPay && $this->totalRemainingAmountToPay == 0
            )
        );
    }

    public function isCancelled(): bool
    {
        return ($this->order->getStatus() === 2000);
    }
}
