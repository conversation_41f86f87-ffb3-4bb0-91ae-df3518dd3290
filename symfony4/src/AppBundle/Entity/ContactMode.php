<?php
/**
 * Created by PhpStorm.
 * User: QAR14123
 * Date: 24/01/2018
 * Time: 09:20
 */

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

/**
 * ContactMode
 */
#[UniqueEntity(fields: 'id')]
#[ORM\Table(name: 'contact_mode')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\ContactModeRepository::class)]
#[ORM\HasLifecycleCallbacks]
class ContactMode implements \JsonSerializable
{

    use TechnicalIdentifierTrait;

    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    private $id;

    /**
     * @var String
     */
    #[ORM\Column(name: 'name', type: 'string')]
    private $name;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId(int $id)
    {
        $this->id = $id;
    }

    /**
     * @return String
     */
    public function getName(): String
    {
        return $this->name;
    }

    /**
     * @param String $name
     */
    public function setName(String $name)
    {
        $this->name = $name;
    }

    public function __toString()
    {
        return 'contactMode.' . $this->name;
    }


    public function jsonSerialize(): array {
        return [
            "name" =>$this->name
        ];
    }
}
