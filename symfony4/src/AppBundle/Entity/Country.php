<?php

namespace AppBundle\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\ManyToMany;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

/**
 * Country
 */
#[UniqueEntity(fields: 'code')]
#[ORM\Table(name: 'country')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\CountryRepository::class)]
#[ORM\HasLifecycleCallbacks]
class Country implements \JsonSerializable
{
    use TimestampedTrait;
    use TechnicalIdentifierTrait;

    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    private $id;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'enabled', type: 'boolean', nullable: false, options: ['default' => true])]
    private $enabled;

    /**
     * @var string
     */
    #[ORM\Column(name: 'code', type: 'string', length: 30, nullable: false, unique: true)]
    private $code;

    /**
     * @var string
     */
    #[ORM\Column(name: 'izb_fca_country', type: 'string', length: 30)]
    private $izbFcaCountry;

    /**
     * @var string
     */
    #[ORM\Column(name: 'company_ident_regex', type: 'string', length: 50, nullable: false)]
    private $companyIdentRegex;

    /**
     * @var string
     */
    #[ORM\Column(name: 'site_ident_regex', type: 'string', length: 50, nullable: true)]
    private $siteIdentRegex;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'buyer', type: 'boolean', nullable: false)]
    private $buyer;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'vendor', type: 'boolean', nullable: false)]
    private $vendor;

    /**
     * @var string
     */
    #[ORM\Column(name: 'locale', type: 'string', length: 5, nullable: false)]
    private $locale;

    /**
     * One Country have Many Regions.
     */
    #[ORM\OneToMany(targetEntity: \AppBundle\Entity\Region::class, mappedBy: 'country', cascade: ['persist'])]
    private $regions;

    /**
     * One Country have Many Zipcode.
     */
    #[ORM\OneToMany(targetEntity: \AppBundle\Entity\ZipCode::class, mappedBy: 'country', cascade: ['persist'])]
    private $zipcode;

    /**
     * @var string
     */
    #[ORM\Column(name: 'izberg_code', type: 'string', length: 3, nullable: false)]
    private $izbergCode;

    /**
     * @var string
     */
    #[ORM\Column(name: 'izberg_id', type: 'integer', nullable: false)]
    private $izbergId;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'eu', type: 'boolean', nullable: false)]
    private $inEU;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'business_everywhere', type: 'boolean', nullable: false)]
    private $businessEverywhere;

    /**
     * @var string
     */
    #[ORM\Column(name: 'legal_product_export_EU', type: 'string', length: 1024, nullable: true)]
    private $legalNoticeProductExportEU;

    /**
     * @var string
     */
    #[ORM\Column(name: 'legal_product_export_NONEU', type: 'string', length: 1024, nullable: true)]
    private $legalNoticeProductExportNonEU;

    /**
     * @var string
     */
    #[ORM\Column(name: 'legal_service_export_EU', type: 'string', length: 1024, nullable: true)]
    private $legalNoticeServiceExportEU;

    /**
     * @var string
     */
    #[ORM\Column(name: 'legal_service_export_NONEU', type: 'string', length: 1024, nullable: true)]
    private $legalNoticeServiceExportNonEU;

    public function __construct()
    {
        $this->regions = new ArrayCollection();
        $this->zipcode = new ArrayCollection();
        $this->enabled = true;
        $this->businessEverywhere = false;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function isEnabled(): bool
    {
        return $this->enabled;
    }

    public function setEnabled(bool $enabled): self
    {
        $this->enabled = $enabled;

        return $this;
    }

    public function getLegalNoticeProductExportEU(): ?string
    {
        return $this->legalNoticeProductExportEU;
    }

    public function setLegalNoticeProductExportEU(?string $legalNoticeProductExportEU): self
    {
        $this->legalNoticeProductExportEU = $legalNoticeProductExportEU;

        return $this;
    }

    public function getLegalNoticeProductExportNonEU(): ?string
    {
        return $this->legalNoticeProductExportNonEU;
    }

    public function setLegalNoticeProductExportNonEU(string $legalNoticeProductExportNonEU): self
    {
        $this->legalNoticeProductExportNonEU = $legalNoticeProductExportNonEU;

        return $this;
    }

    public function getLegalNoticeServiceExportEU(): ?string
    {
        return $this->legalNoticeServiceExportEU;
    }

    public function setLegalNoticeServiceExportEU(string $legalNoticeServiceExportEU): self
    {
        $this->legalNoticeServiceExportEU = $legalNoticeServiceExportEU;

        return $this;
    }

    public function getLegalNoticeServiceExportNonEU(): ?string
    {
        return $this->legalNoticeServiceExportNonEU;
    }

    public function setLegalNoticeServiceExportNonEU(string $legalNoticeServiceExportNonEU): self
    {
        $this->legalNoticeServiceExportNonEU = $legalNoticeServiceExportNonEU;

        return $this;
    }

    public function isInEU(): bool
    {
        return $this->inEU;
    }

    public function setInEU(bool $inEU): self
    {
        $this->inEU = $inEU;

        return $this;
    }

    public function isBusinessEverywhere(): bool
    {
        return $this->businessEverywhere;
    }

    public function setBusinessEverywhere(bool $businessEverywhere): self
    {
        $this->businessEverywhere = $businessEverywhere;
        return $this;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function setCode(string $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function getCompanyIdentRegex(): string
    {
        return $this->companyIdentRegex;
    }

    public function setCompanyIdentRegex(string $companyIdentRegex): self
    {
        $this->companyIdentRegex = $companyIdentRegex;

        return $this;
    }

    public function getSiteIdentRegex(): ?string
    {
        return $this->siteIdentRegex;
    }

    public function setSiteIdentRegex(?string $siteIdentRegex): self
    {
        $this->siteIdentRegex = $siteIdentRegex;

        return $this;
    }

    public function isBuyer(): bool
    {
        return $this->buyer;
    }

    public function setBuyer(bool $buyer): self
    {
        $this->buyer = $buyer;

        return $this;
    }

    public function isVendor(): bool
    {
        return $this->vendor;
    }

    public function setVendor(bool $vendor): self
    {
        $this->vendor = $vendor;

        return $this;
    }

    public function getLocale(): string
    {
        return $this->locale;
    }

    public function setLocale(string $locale): self
    {
        $this->locale = $locale;

        return $this;
    }

    public function getRegions(): Collection
    {
        return $this->regions;
    }

    public function addRegion(Region $region): self
    {
        if (!$this->zipcode->regions($region)) {
            $this->regions[] = $region;
            $region->setCountry($this);
        }
        $this->regions[] = $region;

        return $this;
    }

    public function removeRegion(Region $region): self
    {
        if ($this->regions->contains($region)) {
            $this->regions->removeElement($region);
            if ($region->getCountry() === $this) {
                $region->setCountry(null);
            }
        }
        return $this;
    }

    public function getZipcode(): Collection
    {
        return $this->zipcode;
    }

    public function addZipcode(ZipCode $zipcode): self
    {
        if (!$this->zipcode->contains($zipcode)) {
            $this->zipcode[] = $zipcode;
            $zipcode->setCountry($this);
        }

        return $this;
    }

    public function removeZipcode(ZipCode $zipcode): self
    {
        if ($this->zipcode->contains($zipcode)) {
            $this->zipcode->removeElement($zipcode);
            if ($zipcode->getCountry() === $this) {
                $zipcode->setCountry(null);
            }
        }
        return $this;
    }

    public function getIzbFcaCountry(): string
    {
        return $this->izbFcaCountry;
    }

    public function setIzbFcaCountry(string $izbFcaCountry): self
    {
        $this->izbFcaCountry = $izbFcaCountry;
        return $this;
    }

    public function getIzbergCode(): string
    {
        return $this->izbergCode;
    }

    public function setIzbergCode(string $izbergCode): self
    {
        $this->izbergCode = $izbergCode;
        return $this;
    }

    public function getIzbergId(): string
    {
        return $this->izbergId;
    }

    public function setIzbergId(string $izbergId): self
    {
        $this->izbergId = $izbergId;
        return $this;
    }

    public function __toString()
    {
        return 'country.' . $this->code;
    }

    public function jsonSerialize(): array
    {
        return [
            "code" => $this->code,
            "izbFcaCode" => $this->izbFcaCountry,
            "buyer" => $this->buyer,
            "companyIdentRegex" => $this->companyIdentRegex,
            "locale" => $this->locale,
            "regions" => $this->regions,
            "siteIdentRegex" => $this->siteIdentRegex,
            "vendor" => $this->vendor,
            "zipcode" => $this->zipcode
        ];
    }
}
