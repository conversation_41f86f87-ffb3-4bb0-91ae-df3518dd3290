<?php
/**
 * Created by PhpStorm.
 * User: AQU04740
 * Date: 24/01/2018
 * Time: 16:47
 */

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Table(name: 'opening_time')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\OpeningRepository::class)]
#[ORM\HasLifecycleCallbacks]
class OpeningTime implements \JsonSerializable
{

    use TechnicalIdentifierTrait;

    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

    /**
     * @var string
     */
    #[ORM\Column(name: 'morning_start', type: 'string', length: 15, nullable: true)]
    private $morningStart;

    /**
     * @var string
     */
    #[ORM\Column(name: 'morning_end', type: 'string', length: 15, nullable: true)]
    private $morningEnd;

    /**
     * @var string
     */
    #[ORM\Column(name: 'afternoon_start', type: 'string', length: 15, nullable: true)]
    private $AfternoonStart;

    /**
     * @var string
     */
    #[ORM\Column(name: 'afternoon_end', type: 'string', length: 15, nullable: true)]
    private $AfternoonEnd;


    /**
     * @var int
     */
    #[ORM\Column(name: 'day', type: 'integer', nullable: true)]
    private $day;

    function __construct($day)
    {
        $this->$day = $day;
    }

    /**
     * @return int
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId(?int $id)
    {
        $this->id = $id;
    }

    /**
     * @return string
     */
    public function getMorningStart(): ?string
    {
        return $this->morningStart;
    }

    /**
     * @param string $morningStart
     */
    public function setMorningStart(?string $morningStart)
    {
        $this->morningStart = $morningStart;
    }

    /**
     * @return string
     */
    public function getMorningEnd(): ?string
    {
        return $this->morningEnd;
    }

    /**
     * @param string $morningEnd
     */
    public function setMorningEnd(?string $morningEnd)
    {
        $this->morningEnd = $morningEnd;
    }

    /**
     * @return string
     */
    public function getAfternoonStart(): ?string
    {
        return $this->AfternoonStart;
    }

    /**
     * @param string $AfternoonStart
     */
    public function setAfternoonStart(?string $AfternoonStart)
    {
        $this->AfternoonStart = $AfternoonStart;
    }

    /**
     * @return string
     */
    public function getAfternoonEnd(): ?string
    {
        return $this->AfternoonEnd;
    }

    /**
     * @param string $AfternoonEnd
     */
    public function setAfternoonEnd(?string $AfternoonEnd)
    {
        $this->AfternoonEnd = $AfternoonEnd;
    }

    /**
     * @return int
     */
    public function getDay(): ?int
    {
        return $this->day;
    }

    /**
     * @param int $day
     */
    public function setDay(?int $day)
    {
        $this->day = $day;
    }

    public function __toString()
    {
        $result = "(";
        foreach ($this->jsonSerialize() as $key => $value){
            $result .= $key."=".$value.",";
        }

        return $result . ")";
    }

    public function jsonSerialize(): array {
        return [
            "AfternoonEnd" =>$this->AfternoonEnd,
            "day" => $this->day,
            "AfternoonStart" => $this->AfternoonStart,
            "morningEnd" => $this->morningEnd,
            "morningStart" => $this->morningStart
        ];
    }




}
