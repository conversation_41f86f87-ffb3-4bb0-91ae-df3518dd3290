<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'purchase_request_item')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\PurchaseRequestItemRepository::class)]
class PurchaseRequestItem
{
    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

    /**
     * @var PurchaseRequest
     */
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\PurchaseRequest::class, inversedBy: 'items')]
    private $purchaseRequest;

    /**
     * @var string|null
     */
    #[ORM\Column(name: 'buyer_reference', type: 'string', length: 250, nullable: true)]
    private $buyerReference;

    /**
     * @var string|null
     */
    #[ORM\Column(name: 'manufacturer_reference', type: 'string', length: 250, nullable: true)]
    private $manufacturerReference;

    /**
     * @var int|null
     */
    #[ORM\Column(name: 'offer_id', type: 'integer', nullable: true)]
    private $offerId;

    /**
     * @var int|null
     */
    #[ORM\Column(name: 'quantity', type: 'integer', nullable: true)]
    private $quantity;

    /**
     * @var int|null
     */
    #[ORM\Column(name: 'cart_id', type: 'integer', nullable: true)]
    private $cartId;

    /**
     * @var string|null
     */
    #[ORM\Column(name: 'manufacturer_name', type: 'string', length: 250, nullable: true)]
    private $manufacturerName;

    /**
     * @var string|null
     */
    #[ORM\Column(name: 'product_name', type: 'string', length: 250, nullable: true)]
    private $productName;

    /**
     * @var int|null
     */
    #[ORM\Column(name: 'quantity_expected', type: 'string', length: 250, nullable: true)]
    private $quantityExpected;

    /**
     * @var string|null
     */
    #[ORM\Column(name: 'unit_price_of_reference', type: 'string', length: 250, nullable: true)]
    private $unitPriceOfReference;

    /**
     * @var string|null
     */
    #[ORM\Column(name: 'currency_of_reference', type: 'string', length: 250, nullable: true)]
    private $currencyOfReference;

    /**
     * @var string|null
     */
    #[ORM\Column(name: 'expected_delivery_date', type: 'string', length: 250, nullable: true)]
    private $expectedDeliveryDate;

    /**
     * @var string|null
     */
    #[ORM\Column(name: 'cost_center', type: 'string', length: 250, nullable: true)]
    private $costCenter;

    /**
     * @var string|null
     */
    #[ORM\Column(name: 'purchase_request_number', type: 'string', length: 250, nullable: true)]
    private $purchaseRequestNumber;

    /**
     * @var string|null
     */
    #[ORM\Column(name: 'buyer_order_number', type: 'string', length: 250, nullable: true)]
    private $buyerOrderNumber;

    /**
     * @var string|null
     */
    #[ORM\Column(name: 'order_line', type: 'string', length: 250, nullable: true)]
    private $orderLine;

    public function getId(): int
    {
        return $this->id;
    }

    public function getBuyerReference(): ?string
    {
        return $this->buyerReference;
    }

    public function setBuyerReference(?string $buyerReference): self
    {
        $this->buyerReference = $buyerReference;
        return $this;
    }

    public function getManufacturerReference(): ?string
    {
        return $this->manufacturerReference;
    }

    public function setManufacturerReference(?string $manufacturerReference): self
    {
        $this->manufacturerReference = $manufacturerReference;
        return $this;
    }

    public function getOfferId(): ?int
    {
        return $this->offerId;
    }

    public function setOfferId(?int $offerId): self
    {
        $this->offerId = $offerId;
        return $this;
    }

    public function setPurchaseRequest(PurchaseRequest $purchaseRequest): self
    {
        $this->purchaseRequest = $purchaseRequest;
        return $this;
    }

    public function getPurchaseRequest(): PurchaseRequest
    {
        return $this->purchaseRequest;
    }

    public function getQuantity(): ?int
    {
        return $this->quantity;
    }

    public function setQuantity(?int $quantity): self
    {
        $this->quantity = $quantity;
        return $this;
    }

    public function getCartId(): ?int
    {
        return $this->cartId;
    }

    public function setCartId(?int $cartId): self
    {
        $this->cartId = $cartId;
        return $this;
    }

    public function getManufacturerName(): ?string
    {
        return $this->manufacturerName;
    }

    public function setManufacturerName(?string $manufacturerName): self
    {
        $this->manufacturerName = $manufacturerName;
        return $this;
    }

    public function getProductName(): ?string
    {
        return $this->productName;
    }

    public function setProductName(?string $productName): self
    {
        $this->productName = $productName;
        return $this;
    }

    public function getQuantityExpected(): ?int
    {
        return $this->quantityExpected;
    }

    public function setQuantityExpected(?int $quantityExpected): self
    {
        $this->quantityExpected = $quantityExpected;
        return $this;
    }

    public function getUnitPriceOfReference(): ?string
    {
        return $this->unitPriceOfReference;
    }

    public function setUnitPriceOfReference(?string $unitPriceOfReference): self
    {
        $this->unitPriceOfReference = $unitPriceOfReference;
        return $this;
    }

    public function getCurrencyOfReference(): ?string
    {
        return $this->currencyOfReference;
    }

    public function setCurrencyOfReference(?string $currencyOfReference): self
    {
        $this->currencyOfReference = $currencyOfReference;
        return $this;
    }

    public function getExpectedDeliveryDate(): ?string
    {
        return $this->expectedDeliveryDate;
    }

    public function setExpectedDeliveryDate(?string $expectedDeliveryDate): self
    {
        $this->expectedDeliveryDate = $expectedDeliveryDate;
        return $this;
    }

    public function getCostCenter(): ?string
    {
        return $this->costCenter;
    }

    public function setCostCenter(?string $costCenter): self
    {
        $this->costCenter = $costCenter;
        return $this;
    }

    public function getPurchaseRequestNumber(): ?string
    {
        return $this->purchaseRequestNumber;
    }

    public function setPurchaseRequestNumber(?string $purchaseRequestNumber): self
    {
        $this->purchaseRequestNumber = $purchaseRequestNumber;
        return $this;
    }

    public function getBuyerOrderNumber(): ?string
    {
        return $this->buyerOrderNumber;
    }

    public function setBuyerOrderNumber(?string $buyerOrderNumber): self
    {
        $this->buyerOrderNumber = $buyerOrderNumber;
        return $this;
    }

    public function getOrderLine(): ?string
    {
        return $this->orderLine;
    }

    public function setOrderLine(?string $orderLine): self
    {
        $this->orderLine = $orderLine;
        return $this;
    }
}
