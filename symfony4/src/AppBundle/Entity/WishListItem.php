<?php

namespace AppBundle\Entity;

use AppBundle\Model\Offer;
use Doctrine\ORM\Mapping as ORM;

/**
 * WishListItem
 *
 *
 */
#[ORM\Table(name: 'wishListItem')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\WishListItemRepository::class)]
#[ORM\HasLifecycleCallbacks]
class WishListItem
{
    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

    /**
     * @var string
     */
    #[ORM\Column(name: 'offer_id', type: 'integer')]
    private $offerId;

    /**
     * @var int
     */
    #[ORM\Column(name: 'quantity', type: 'integer')]
    private $quantity;

    /**
     * Many site have One company.
     */
    #[ORM\JoinColumn(name: 'wishList_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\WishList::class, inversedBy: 'items')]
    private $wishList;

    /**
     * @var Offer
     */
    private $offer;

    /**
     * @var float
     */
    private $price;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId(int $id)
    {
        $this->id = $id;
    }

    /**
     * @return string
     */
    public function getOfferId(): string
    {
        return $this->offerId;
    }

    /**
     * @param string $offerId
     */
    public function setOfferId(string $offerId)
    {
        $this->offerId = $offerId;
    }

    /**
     * @return int
     */
    public function getQuantity(): int
    {
        return $this->quantity;
    }

    /**
     * @param int $quantity
     */
    public function setQuantity(int $quantity)
    {
        $this->quantity = $quantity;
    }

    /**
     * @return mixed
     */
    public function getWishList()
    {
        return $this->wishList;
    }

    public function setWishList($wishList)
    {
        $this->wishList = $wishList;
    }

    public function getOffer(): Offer
    {
        return $this->offer;
    }

    public function setOffer(Offer $offer): void
    {
        $this->offer = $offer;
    }

    public function getPrice(): float
    {
        return $this->price;
    }

    public function setPrice(float $price): void
    {
        $this->price = $price;
    }
}
