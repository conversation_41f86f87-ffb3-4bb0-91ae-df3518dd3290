<?php

namespace AppBundle\Entity;

use AppBundle\Repository\ThreadParentMessageRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'thread_parent_message')]
#[ORM\Entity(repositoryClass: ThreadParentMessageRepository::class)]
class ThreadParentMessage
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $idThreadMessage;

    /**
     * @var int
     */
    #[ORM\Column(name: 'izberg_id', type: 'integer', nullable: true)]
    private ?int $izbergId;

    #[ORM\Column(name: 'from_email', type: 'string', length: 255, nullable: true)]
    private ?string $fromEmail;


    public function getIzbergId(): ?int
    {
        return $this->izbergId;
    }

    public function setIzbergId(?int $izbergId): void
    {
        $this->izbergId = $izbergId;
    }

    public function getFromEmail(): ?string
    {
        return $this->fromEmail;
    }

    public function setFromEmail(?string $fromEmail): void
    {
        $this->fromEmail = $fromEmail;
    }

}
