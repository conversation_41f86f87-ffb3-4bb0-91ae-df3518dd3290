<?php
namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use AppBundle\Validator\Constraints\SlugExist as AssertSlugDontExist;
use App<PERSON><PERSON>le\Validator\Constraints\SlugHttp as AssertSlugNoHTTP;
use AppBundle\Validator\Constraints\SlugFormat as AssertSlugFormat;
use AppBundle\Validator\Constraints\RedirectDestinationFormat as AssertRedirectDestinationFormat;


#[ORM\Table(name: '`redirects`')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\RedirectRepository::class)]
class Redirect implements \JsonSerializable
{
	use TimestampedTrait;
    use TechnicalIdentifierTrait;

    #[ORM\Id]
    #[ORM\Column(type: 'integer')]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

    /**
     * @AssertSlugDontExist(message = "form.redirect.slug.invalid_exist", groups={"redirect"})
     * @AssertSlugNoHTTP(message = "form.redirect.slug.invalid_http", groups={"redirect"})
     * @AssertSlugFormat(message = "form.redirect.slug.invalid_format", groups={"redirect"})
     */
    #[Assert\NotBlank(groups: ['redirect'])]
    #[ORM\Column(name: 'origin', type: 'string', length: 255)]
    private $origin;

    /**
     * @AssertRedirectDestinationFormat(message = "form.redirect.slug.invalid_format", groups={"redirect"})
     */
    #[Assert\NotBlank]
    #[ORM\Column(name: 'destination', type: 'string', length: 255)]
    private $destination;

    #[ORM\Column(name: 'type', type: 'integer')]
    private $type = 302;



    public function getId() {
        return intval($this->id);
    }

    public function setId($id) {
        return $this->id = $id;
    }

    public function getOrigin() {
        return $this->origin;
    }

    public function setOrigin($origin) {
		return $this->origin = rtrim($origin,'/');
    }

    public function getDestination() {
        return $this->destination;
    }

    public function setDestination($destination) {
		return $this->destination = rtrim($destination,'/');
    }

    public function getType() {
        return $this->type;
    }

    public function setType($type = 302) {

        // only 301 and 302 are allowed
        if ($type != 301 && $type != 302) {
            $type = 302;
        }

        return $this->type = $type;
    }

    public function __toString()
    {
        return $this->origin . ' > ' . $this->destination;
    }

    public function jsonSerialize(): array {
        return [
            'origin' => $this->origin,
            'destination' => $this->destination,
            'type' => $this->type

        ];
    }
}
