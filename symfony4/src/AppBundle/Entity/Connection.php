<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 25/01/2018
 * Time: 15:51
 */

namespace AppBundle\Entity;
use Doctrine\ORM\Mapping as ORM;


/**
 * Connection
 */
#[ORM\Table(name: 'connection')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\ConnectionRepository::class)]
#[ORM\HasLifecycleCallbacks]
class Connection
{

    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    #[ORM\Id]
    private $id;

    /**
     * @var string
     */
    #[ORM\Column(name: 'ip', type: 'string', length: 46, nullable: true, unique: false)]
    private $ip;

    /**
     * @var \DateTime $connectedAt
     */
    #[ORM\Column(name: 'connected_at', type: 'datetime', nullable: true)]
    private $connectedAt;


    /**
     * @var string
     */
    #[ORM\Column(name: 'ua', type: 'string', length: 200, nullable: true, unique: false)]
    private $userAgent;

    /**
     * @var string
     */
    #[ORM\Column(name: 'resolution', type: 'string', length: 50, nullable: true, unique: false)]
    private $resolution;

    /**
     * @var string
     */
    #[ORM\Column(name: 'browser', type: 'string', length: 200, nullable: true, unique: false)]
    private $browser;

    /**
     * @var string
     */
    #[ORM\Column(name: 'platform', type: 'string', length: 200, nullable: true, unique: false)]
    private $platform;


    /**
     * @var string
     */
    #[ORM\Column(name: 'browser_version', type: 'string', length: 200, nullable: true, unique: false)]
    private $browserVersion;

    /**
     * @var string
     */
    #[ORM\Column(name: 'device_type', type: 'string', length: 200, nullable: true, unique: false)]
    private $deviceType;

    #[ORM\JoinColumn(name: 'user_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\User::class, inversedBy: 'connections')]
    private $user;

    /**
     * @return int
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId(?int $id): void
    {
        $this->id = $id;
    }

    /**
     * @return string
     */
    public function getIp(): ?string
    {
        return $this->ip;
    }

    /**
     * @param string $ip
     */
    public function setIp(?string $ip): void
    {
        $this->ip = $ip;
    }

    /**
     * @return \DateTime
     */
    public function getConnectedAt(): ?\DateTime
    {
        return $this->connectedAt;
    }

    /**
     * @param \DateTime $connectedAt
     */
    public function setConnectedAt(?\DateTime $connectedAt): void
    {
        $this->connectedAt = $connectedAt;
    }

    /**
     * @return string
     */
    public function getUserAgent(): ?string
    {
        return $this->userAgent;
    }

    /**
     * @param string $userAgent
     */
    public function setUserAgent(?string $userAgent): void
    {
        $this->userAgent = $userAgent;
    }

    public function getResolution(): ?string
    {
        return $this->resolution;
    }

    /**
     * @param string $resolution
     */
    public function setResolution(?string $resolution): void
    {
        $this->resolution = $resolution;
    }

    /**
     * @return mixed
     */
    public function getUser()
    {
        return $this->user;
    }

    /**
     * @param mixed $user
     */
    public function setUser($user): void
    {
        $this->user = $user;
    }

    /**
     * @return string
     */
    public function getBrowser(): ?string
    {
        return $this->browser;
    }

    /**
     * @param string $browser
     */
    public function setBrowser(?string $browser): void
    {
        $this->browser = $browser;
    }

    /**
     * @return string
     */
    public function getPlatform(): ?string
    {
        return $this->platform;
    }

    /**
     * @param string $platform
     */
    public function setPlatform(?string $platform): void
    {
        $this->platform = $platform;
    }

    /**
     * @return string
     */
    public function getBrowserVersion(): ?string
    {
        return $this->browserVersion;
    }

    /**
     * @param string $browserVersion
     */
    public function setBrowserVersion(?string $browserVersion): void
    {
        $this->browserVersion = $browserVersion;
    }

    /**
     * @return string
     */
    public function getDeviceType(): ?string
    {
        return $this->deviceType;
    }

    /**
     * @param string $deviceType
     */
    public function setDeviceType(?string $deviceType): void
    {
        $this->deviceType = $deviceType;
    }








}
