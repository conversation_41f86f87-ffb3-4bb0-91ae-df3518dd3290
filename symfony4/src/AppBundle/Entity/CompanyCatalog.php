<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use Doctrine\ORM\Mapping\Index;

#[ORM\Table(name: 'company_catalog')]
#[Index(name: 'ref_idx', columns: ['ref'])]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\CompanyCatalogRepository::class)]
#[ORM\HasLifecycleCallbacks]
class CompanyCatalog implements \JsonSerializable
{
    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: 'integer', options: ['unsigned' => true])]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

    /**
     * Many catalog entries have One company.
     */
    #[ORM\JoinColumn(name: 'company_id', referencedColumnName: 'id', nullable: true)]
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\Company::class, inversedBy: 'companyCatalog')]
    private $company;

    /**
     * @var string
     */
    #[Assert\Length(max: 255)]
    #[ORM\Column(name: 'ref', type: 'string', length: 255, nullable: true)]
    private $ref;

    /**
     * @var string
     */
    #[Assert\Length(max: 255)]
    #[ORM\Column(name: 'external_ref', type: 'string', length: 255, nullable: true)]
    private $externalRef;

    /**
     * @var string
     */
    #[Assert\Length(max: 255)]
    #[ORM\Column(name: 'manufacturer_name', type: 'string', length: 255, nullable: true)]
    private $manufacturerName;

    /**
     * @var string
     */
    #[Assert\Length(max: 255)]
    #[ORM\Column(name: 'designation_reference', type: 'string', length: 255, nullable: true)]
    private $designationReference;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'valid', type: 'boolean', options: ['default' => 0])]
    private $valid;

    public function __toString()
    {
        return '[' . $this->ref.'] ==> [' . $this->externalRef . ']';
    }

    public function jsonSerialize(): array
    {
        return [
          'ref' => $this->ref,
          'externalRef' => $this->externalRef,
        ];
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): void
    {
        $this->id = $id;
    }

    public function getCompany()
    {
        return $this->company;
    }

    public function setCompany($company): void
    {
        $this->company = $company;
    }

    public function getRef(): string
    {
        return $this->ref;
    }

    public function setRef(string $ref): void
    {
        $this->ref = $ref;
    }

    public function getExternalRef(): string
    {
        return $this->externalRef;
    }

    public function setExternalRef(string $externalRef): void
    {
        $this->externalRef = $externalRef;
    }

    public function isValid(): bool
    {
        return $this->valid;
    }

    public function setValid(bool $valid): void
    {
        $this->valid = $valid;
    }

    public function getManufacturerName(): string
    {
        return $this->manufacturerName;
    }

    public function setManufacturerName(string $manufacturerName): void
    {
        $this->manufacturerName = $manufacturerName;
    }

    public function getDesignationReference(): string
    {
        return $this->designationReference;
    }

    public function setDesignationReference(string $designationReference): void
    {
        $this->designationReference = $designationReference;
    }
}
