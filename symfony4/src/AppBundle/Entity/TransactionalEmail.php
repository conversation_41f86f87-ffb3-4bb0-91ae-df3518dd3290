<?php

namespace AppBundle\Entity;

use AppBundle\Repository\TransactionalEmailRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'transactional_email')]
#[ORM\Entity(repositoryClass: TransactionalEmailRepository::class)]
class TransactionalEmail
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $idTransactionalEmail;

    #[ORM\Column(type: 'string', length: 255)]
    private ?string $emailIdentifier;

    #[ORM\Column(type: 'boolean')]
    private ?bool $active;

    /**
     * @param string|null $emailIdentifier
     */
    public function __construct(?string $emailIdentifier)
    {
        $this->emailIdentifier = $emailIdentifier;
    }

    public function getIdTransactionalEmail(): ?int
    {
        return $this->idTransactionalEmail;
    }

    /**
     * @param int $idTransactionalEmail
     * @return TransactionalEmail
     */
    public function setIdTransactionalEmail(int $idTransactionalEmail): self
    {
        $this->idTransactionalEmail = $idTransactionalEmail;

        return $this;
    }

    public function getEmailIdentifier(): ?string
    {
        return $this->emailIdentifier;
    }

    public function setEmailIdentifier(string $emailIdentifier): self
    {
        $this->emailIdentifier = $emailIdentifier;

        return $this;
    }

    public function isActive(): ?bool
    {
        return $this->active;
    }

    public function isNotActive(): ?bool
    {
        return !$this->active;
    }

    public function setActive(bool $active): self
    {
        $this->active = $active;

        return $this;
    }
}
