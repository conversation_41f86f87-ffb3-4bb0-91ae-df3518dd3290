<?php

namespace AppBundle\Entity;

use J<PERSON>\Serializer\Annotation\Type;
use Doctrine\ORM\Mapping as ORM;

trait TechnicalIdentifierTrait
{
    #[ORM\Column(name: "technical_id", type: "string", length: 50, nullable: true)]
    #[Type("string")]
    private $technicalId;

    /**
     * Set createdAt
     *
     * @ORM\PrePersist
     */
    public function initTechnicalId(){
        if ($this->technicalId === null){
            $this->technicalId = md5(uniqid());
        }
    }

    /**
     * @return string
     */
    public function getTechnicalId(): ?string
    {
        return $this->technicalId;
    }

    /**
     * @param string $technicalId
     */
    public function setTechnicalId(?string $technicalId): void
    {
        $this->technicalId = $technicalId;
    }
}
