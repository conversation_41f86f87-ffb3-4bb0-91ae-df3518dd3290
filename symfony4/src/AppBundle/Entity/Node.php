<?php
namespace AppBundle\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use AppBundle\Validator\Constraints\SlugExist as AssertSlugDontExist;
use AppBundle\Validator\Constraints\SlugHttp as AssertSlugNoHTTP;
use AppBundle\Validator\Constraints\SlugFormat as AssertSlugFormat;


#[ORM\Table(name: '`node`')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\NodeRepository::class)]
#[ORM\HasLifecycleCallbacks]
class Node implements \JsonSerializable
{
	use TimestampedTrait;
    use TechnicalIdentifierTrait;

	const STATUS_DRAFT = "draft";
	const STATUS_PUBLISHED = "published";

	const TYPE_TESTIMONIAL = "testimonial";
	const TYPE_PAGE = "page";
    const TYPE_EMAIL = "email";

	#[ORM\Id]
    #[ORM\Column(type: 'integer')]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

	#[ORM\Column(name: 'type', type: 'string', length: 20)]
    private $type = "page";

	#[ORM\JoinColumn(name: 'author', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\User::class, inversedBy: 'nodes')]
    private $author;


	#[ORM\OneToMany(targetEntity: \NodeContent::class, mappedBy: 'node', cascade: ['persist'], orphanRemoval: true)]
    private $content;

	/**
     * @var string|null
     */
    #[ORM\Column(name: 'template', type: 'string', nullable: true, length: 40)]
    private $template = "default";

	/**
  * @AssertSlugDontExist(message = "form.node.slug.invalid_exist", groups={"page"})
  * @AssertSlugNoHTTP(message = "form.node.slug.invalid_http", groups={"page"})
  * @AssertSlugFormat(message = "form.node.slug.invalid_format", groups={"page"})
  */
 #[Assert\NotBlank]
 #[ORM\Column(name: 'slug', type: 'string', length: 255)]
 private $slug;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'title_visible', type: 'boolean', nullable: false)]
    private $titleVisible;

    #[ORM\Column(name: 'link_external', type: 'string', nullable: true)]
    private $linkExternal;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'external_link_type', type: 'boolean', nullable: false)]
    private $externalLinkType;

	#[Assert\NotBlank]
 #[ORM\Column(name: 'status', type: 'string', length: 20)]
 private $status = self::STATUS_DRAFT;


	#[Assert\File(maxSize: '5M')]
 #[ORM\JoinColumn(name: 'image_id', referencedColumnName: 'id', nullable: true)]
 #[ORM\OneToOne(targetEntity: \Image::class, cascade: ['persist'], orphanRemoval: true)]
 private $backgroundImage;

    #[ORM\Column(name: 'order_node', type: 'integer', length: 20, nullable: true)]
    private $orderNode;

    /**
     * published Time/Date
     *
     * @var \DateTime
     */
    #[ORM\Column(name: 'published_at', type: 'datetime', nullable: true)]
    private $publishedAt;

    /**
     * ended Time/Date
     *
     * @var \DateTime
     */
    #[ORM\Column(name: 'ended_at', type: 'datetime', nullable: true)]
    private $endedAt;

    public function __construct()
	{
		$this->content = new ArrayCollection();
		$this->titleVisible = true;
		$this->externalLinkType = false;
		$this->template = 'default';
	}

	public function addContent($content)
	{
		$this->content[] = $content;

		$content->setNode($this);

		return $this;
	}

	public function removeContent($content)
	{
		$this->content->removeElement($content);
	}


    /**
     * Return node Id
     * @return integer
     */
    public function getId()
    {
        return intval($this->id);
    }

    public function setId($id)
	{
		$this->id = intval($id);
	}

    /**
     * Return node type
     * @return string
     */
    public function getType() {
        return $this->type;
    }

    /**
     * Set node type
     * @param $type string
     * @return string
     */
    public function setType($type = "page") {
        return $this->type = $type;
    }


    /**
     * Return node content
     * @param null|string $lang
     * @return mixed
     */
    public function getContent($lang = null)
    {
    	if ($lang != null) {
    		/** @var NodeContent $c */
			foreach ($this->content as $c) {
    			if ($c->getLang() === $lang) {
    				return $c;
				}
			}
			return null;
		}

        return $this->content;
    }

    public function hasLanguage($lang = 'en')
	{
		$content = $this->getContent($lang);
		return !is_null($content);
	}

    /**
     * Set node content
     * @param string $content
     * @return string
     */
    public function setContent(string $content)
    {
        return $this->content = $content;
    }

    /**
     * Return node slug
     * @return string
     */
    public function getSlug()
    {
        return $this->slug;
    }

    /**
     * Set node slug
     * @param $slug
     * @return string
     */
    public function setSlug($slug)
    {
        return $this->slug = $slug;
    }

    /**
     * @return bool
     */
    public function getTitleVisible()
    {
        return $this->titleVisible;
    }

    /**
     * @param $titleVisible
     * @return bool
     */
    public function setTitleVisible($titleVisible)
    {
        return $this->titleVisible = $titleVisible;
    }

    /**
     * @return string
     */
    public function getLinkExternal()
    {
        return $this->linkExternal;
    }

    /**
     * @param $linkExternal
     */
    public function setLinkExternal($linkExternal): void
    {
        $this->linkExternal = $linkExternal;
    }

    /**
     * @return bool
     */
    public function isExternalLinkType()
    {
        return $this->externalLinkType;
    }

    /**
     * @param $externalLinkType
     */
    public function setExternalLinkType($externalLinkType): void
    {
        $this->externalLinkType = $externalLinkType;
    }

    /**
     * Return the status of the node
     * @return string
     */
    public function getStatus() {
        return $this->status;
    }

    /**
     * Set the status of the node (draft/published/pending/etc...
     * @param string $status
     * @return string
     */
    public function setStatus(string $status) {
        if ($status != self::STATUS_DRAFT && $status != self::STATUS_PUBLISHED) {
            $status = self::STATUS_DRAFT;
        }
        return $this->status = $status;
    }


    /**
     * Return the authorId
     * @return integer
     */
    public function getAuthor()
    {
        return $this->author;
    }

    /**
     * Set author id
     * @param $id
     * @return mixed
     */
    public function setAuthor($id)
    {
        return $this->author = $id;
    }

    /**
     * @return string|null
     */
    public function getTemplate():?string
    {
        return $this->template;
    }

    /**
     * @param string|null $template
     */
    public function setTemplate(?string $template)
    {
        $this->template = $template;
    }


    public function getTitle()
	{
		if ($this->content[1]) {
			return $this->content[1]->getTitle();
		} else {
			return 'N/A';
		}
	}

	public function getBody()
	{
		if ($this->content[0]) {
			return $this->content[0]->getBody();
		} else {
			return 'N/A';
		}
	}

    public function getLink()
    {
        if ($this->content[0]) {
            return $this->content[0]->getLink();
        } else {
            return 'N/A';
        }
    }

    public function getLinkText()
    {
        if ($this->content[0]) {
            return $this->content[0]->getLinkText();
        } else {
            return 'N/A';
        }
    }

    public function getBackgroundImage()
    {
        return $this->backgroundImage;
    }

	public function setBackgroundImage($backgroundImage)
    {
        $this->backgroundImage = $backgroundImage;
    }

    /**
     * @return string
     */
    public function __toString()
    {
        if ($this->getTitle()) {
            return $this->getTitle();
        } else {
            return "";
        }
    }

    public function jsonSerialize(): array {
        return [
            'author' => $this->author,
            'content' => $this->content,
            'template' =>$this->template,
            'slug' => $this->slug
        ];
    }

    /**
     * @return mixed
     */
    public function getOrderNode()
    {
        return $this->orderNode;
    }

    /**
     * @param mixed $orderNode
     */
    public function setOrderNode($orderNode)
    {
        $this->orderNode = $orderNode;
    }

    /**
     * @param $publishedAt
     * @throws \Exception
     */
    public function setPublishedAt ($publishedAt)
    {
        $this->publishedAt = $publishedAt;
    }

    /**
     * Get publishedAt
     *
     * @return \DateTime
     */
    public function getPublishedAt ()
    {
        return $this->publishedAt;
    }

    /**
     * @param $endedAt
     * @throws \Exception
     */
    public function setEndedAt ($endedAt)
    {
        $this->endedAt = $endedAt;
    }

    /**
     * Get endedAt
     *
     * @return \DateTime
     */
    public function getEndedAt ()
    {
        return $this->endedAt;
    }


}
