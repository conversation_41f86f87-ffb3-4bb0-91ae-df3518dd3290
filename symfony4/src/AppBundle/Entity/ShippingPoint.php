<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use JsonSerializable;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Validator\Constraints as Assert;


/**
 * Site
 * */
#[UniqueEntity(fields: 'id')]
#[ORM\Table(name: 'shipping_points')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\ShippingPointRepository::class)]
#[ORM\HasLifecycleCallbacks]
class ShippingPoint implements JsonSerializable
{

    use TimestampedTrait;
    use ExportableEntityTrait;

    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

    /**
     * Many shipping point have One address.
     */
    #[Assert\Type(type: 'AppBundle\Entity\Address')]
    #[Assert\Valid]
    #[ORM\JoinColumn(name: 'address_id', referencedColumnName: 'id', nullable: true)]
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\Address::class, inversedBy: 'shippingPoints', cascade: ['persist'])]
    private $address;

    /**
     * Many shipping point have One contact.
     */
    #[Assert\Type(type: 'AppBundle\Entity\Contact')]
    #[Assert\Valid]
    #[ORM\JoinColumn(name: 'contact_id', referencedColumnName: 'id', nullable: true)]
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\Contact::class, inversedBy: 'shippingPoints', cascade: ['persist'])]
    private $contact;

    /**
     * Many Shipping point have One site.
     */
    #[ORM\JoinColumn(name: 'site_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\Site::class, inversedBy: 'shippingPoints', cascade: ['persist'])]
    private $site;

    /**
     * @var string
     */
    #[Assert\NotBlank]
    #[Assert\Length(max: 50)]
    #[ORM\Column(name: 'name', type: 'string', length: 50, nullable: true)]
    private $name;

    /**
     * @var string
     */
    #[Assert\Length(max: 512)]
    #[ORM\Column(name: 'comment', type: 'string', length: 512, nullable: true)]
    private $comment;

    /**
     * @var string|null
     */
    #[ORM\Column(name: 'accountant_email', type: 'string', nullable: true)]
    private $accountantEmail;

    /**
     * @var string|null
     */
    #[ORM\Column(name: 'packaging_request_1', type: 'text', nullable: true)]
    private $packagingRequest1;

    /**
     * @var string|null
     */
    #[ORM\Column(name: 'packaging_request_2', type: 'text', nullable: true)]
    private $packagingRequest2;

    /**
     * @var string|null
     */
    #[ORM\Column(name: 'packaging_request_3', type: 'text', nullable: true)]
    private $packagingRequest3;

    /**
     * @var string|null
     */
    #[ORM\Column(name: 'documentation_request_1', type: 'text', nullable: true)]
    private $documentationRequest1;

    /**
     * @var string|null
     */
    #[ORM\Column(name: 'documentation_request_2', type: 'text', nullable: true)]
    private $documentationRequest2;

    /**
     * @var string|null
     */
    #[ORM\Column(name: 'documentation_request_3', type: 'text', nullable: true)]
    private $documentationRequest3;

    /**
     * @var string|null
     */
    #[ORM\Column(name: 'documentation_request_4', type: 'text', nullable: true)]
    private $documentationRequest4;

    /**
     * @var string|null
     */
    #[ORM\Column(name: 'documentation_request_5', type: 'text', nullable: true)]
    private $documentationRequest5;

    /**
     * @var string|null
     */
    #[ORM\Column(name: 'documentation_request_6', type: 'text', nullable: true)]
    private $documentationRequest6;

    /**
     * @var string|null
     */
    #[ORM\Column(name: 'documentation_request_7', type: 'text', nullable: true)]
    private $documentationRequest7;

    /**
     * @var string|null
     */
    #[ORM\Column(name: 'documentation_request_8', type: 'text', nullable: true)]
    private $documentationRequest8;

    /**
     * @var string|null
     */
    #[ORM\Column(name: 'documentation_request_9', type: 'text', nullable: true)]
    private $documentationRequest9;

    /**
     * @var string|null
     */
    #[ORM\Column(name: 'documentation_request_10', type: 'text', nullable: true)]
    private $documentationRequest10;

    public function getName()
    {
        return $this->name;
    }

    public function setName($name)
    {
        $this->name = $name;
    }

    public function getComment()
    {
        return $this->comment;
    }

    public function setComment($comment)
    {
        $this->comment = $comment;
    }

    public function getSite(): Site
    {
        return $this->site;
    }

    public function setSite($site)
    {
        return $this->site = $site;
    }

    public function setId($id): void
    {
        $this->id = $id;
    }

    public function setContact($contact): void
    {
        $this->contact = $contact;
    }

    public function getContact(): ?Contact
    {
        return $this->contact;
    }

    public function setAddress($address): void
    {
        $this->address = $address;
    }

    public function getAddress(): ?Address
    {
        return $this->address;
    }

    public function jsonSerialize(): array
    {
        return [];
    }

    public function getId()
    {
        return $this->id;
    }

    public function getAccountantEmail(): ?string
    {
        return $this->accountantEmail;
    }

    public function setAccountantEmail(?string $accountantEmail): void
    {
        $this->accountantEmail = $accountantEmail;
    }

    public function getPackagingRequest1(): ?string
    {
        return $this->packagingRequest1;
    }

    public function setPackagingRequest1(?string $packagingRequest1): void
    {
        $this->packagingRequest1 = $packagingRequest1;
    }

    public function getPackagingRequest2(): ?string
    {
        return $this->packagingRequest2;
    }

    public function setPackagingRequest2(?string $packagingRequest2): void
    {
        $this->packagingRequest2 = $packagingRequest2;
    }

    public function getPackagingRequest3(): ?string
    {
        return $this->packagingRequest3;
    }

    public function setPackagingRequest3(?string $packagingRequest3): void
    {
        $this->packagingRequest3 = $packagingRequest3;
    }

    public function getDocumentationRequest1(): ?string
    {
        return $this->documentationRequest1;
    }

    public function setDocumentationRequest1(?string $documentationRequest1): void
    {
        $this->documentationRequest1 = $documentationRequest1;
    }

    public function getDocumentationRequest2(): ?string
    {
        return $this->documentationRequest2;
    }

    public function setDocumentationRequest2(?string $documentationRequest2): void
    {
        $this->documentationRequest2 = $documentationRequest2;
    }

    public function getDocumentationRequest3(): ?string
    {
        return $this->documentationRequest3;
    }

    public function setDocumentationRequest3(?string $documentationRequest3): void
    {
        $this->documentationRequest3 = $documentationRequest3;
    }

    public function getDocumentationRequest4(): ?string
    {
        return $this->documentationRequest4;
    }

    public function setDocumentationRequest4(?string $documentationRequest4): void
    {
        $this->documentationRequest4 = $documentationRequest4;
    }

    public function getDocumentationRequest5(): ?string
    {
        return $this->documentationRequest5;
    }

    public function setDocumentationRequest5(?string $documentationRequest5): void
    {
        $this->documentationRequest5 = $documentationRequest5;
    }

    public function getDocumentationRequest6(): ?string
    {
        return $this->documentationRequest6;
    }

    public function setDocumentationRequest6(?string $documentationRequest6): void
    {
        $this->documentationRequest6 = $documentationRequest6;
    }

    public function getDocumentationRequest7(): ?string
    {
        return $this->documentationRequest7;
    }

    public function setDocumentationRequest7(?string $documentationRequest7): void
    {
        $this->documentationRequest7 = $documentationRequest7;
    }

    public function getDocumentationRequest8(): ?string
    {
        return $this->documentationRequest8;
    }

    public function setDocumentationRequest8(?string $documentationRequest8): void
    {
        $this->documentationRequest8 = $documentationRequest8;
    }

    public function getDocumentationRequest9(): ?string
    {
        return $this->documentationRequest9;
    }

    public function setDocumentationRequest9(?string $documentationRequest9): void
    {
        $this->documentationRequest9 = $documentationRequest9;
    }

    public function getDocumentationRequest10(): ?string
    {
        return $this->documentationRequest10;
    }

    public function setDocumentationRequest10(?string $documentationRequest10): void
    {
        $this->documentationRequest10 = $documentationRequest10;
    }
}
