<?php

namespace AppBundle\Entity;

use AppBundle\Util\AsAccountingEmail;
use AppBundle\Util\CreatedByApiTrait;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'orders')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\OrderRepository::class)]
class Order
{
    use CreatedByApiTrait;
    use AsAccountingEmail;

    public const STATUS_RUNNING = 1;
    public const STATUS_PAST = 2;
    public const STATUS_CANCELLED = 3;
    public const STATUS_DELETED = 4;
    public const STATUS_PENDING_CREATION = 5;

    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

    /**
     * @var int
     */
    #[ORM\Column(name: 'izberg_id', type: 'integer', nullable: true)]
    private $izbergId;

    /**
     * @var string
     */
    #[ORM\Column(name: 'number_id', type: 'string', nullable: true)]
    private $numberId;

    /**
     * @var \DateTimeImmutable
     */
    #[ORM\Column(name: 'created_on', type: 'datetime_immutable', nullable: false)]
    private $createdOn;

    /**
     * @var ?Address
     */
    #[ORM\JoinColumn(name: 'address_id', referencedColumnName: 'id', nullable: true)]
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\Address::class, inversedBy: 'orders', cascade: ['persist'])]
    private $address;

    /**
     * @var int
     */
    #[ORM\Column(name: 'status', type: 'integer', nullable: true)]
    private $status;

    /**
     * @var int
     */
    #[ORM\Column(name: 'izberg_status', type: 'integer', nullable: true)]
    private $izbergStatus;

    /**
     * @var float
     */
    #[ORM\Column(name: 'amount', type: 'float', nullable: true)]
    private $amount;

    /**
     * @var float
     */
    #[ORM\Column(name: 'amount_vat', type: 'float', nullable: true)]
    private $amountVat;

    /**
     * @var float
     */
    #[ORM\Column(name: 'amount_vat_included', type: 'float', nullable: true)]
    private $amountVatIncluded;

    /**
     * @var ?Company
     */
    #[ORM\JoinColumn(name: 'company_id', referencedColumnName: 'id', nullable: true)]
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\Company::class, inversedBy: 'orders', cascade: ['persist'])]
    private $company;

    /**
     * @var ?Site
     */
    #[ORM\JoinColumn(name: 'site_id', referencedColumnName: 'id', nullable: true)]
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\Site::class, inversedBy: 'orders', cascade: ['persist'])]
    private $site;

    /**
     * @var ?string
     */
    #[ORM\Column(name: 'payment_code', type: 'string', nullable: true)]
    private $paymentCode;

    /**
     * @var ?string
     */
    #[ORM\Column(name: 'validation_number', type: 'string', nullable: true)]
    private $validationNumber;

    /**
     * @var ?string
     */
    #[ORM\Column(name: 'currency', type: 'string', nullable: true)]
    private $currency;

    /**
     * @var int
     */
    #[ORM\Column(name: 'cart_id', type: 'integer', nullable: true)]
    private $cartId;

    /**
     * @var ?ShippingPoint
     */
    #[ORM\JoinColumn(name: 'shipping_point_id', referencedColumnName: 'id', nullable: true)]
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\ShippingPoint::class, inversedBy: 'orders', cascade: ['persist'])]
    private $shippingPoint;

    /**
     * @var string|null
     */
    #[ORM\Column(name: 'user_email', type: 'string', nullable: true)]
    private $userEmail;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private $paymentTerms;

    /**
     * @var Collection
     */
    #[ORM\OneToMany(targetEntity: \MerchantOrder::class, mappedBy: 'order', cascade: ['persist', 'remove'])]
    private Collection $merchantOrders;

    #[ORM\Column(type: 'boolean')]
    private bool $createdByApi = false;

    /**
     * Order constructor.
     */
    public function __construct()
    {
        $this->merchantOrders = new ArrayCollection();
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getIzbergId(): ?int
    {
        return $this->izbergId;
    }

    public function setIzbergId(?int $izbergId): self
    {
        $this->izbergId = $izbergId;

        return $this;
    }

    public function getNumberId(): ?string
    {
        return $this->numberId;
    }

    public function setNumberId(?string $numberId): self
    {
        $this->numberId = $numberId;

        return $this;
    }

    /**
     * @return \DateTimeImmutable
     */
    public function getCreatedOn(): \DateTimeImmutable
    {
        return $this->createdOn;
    }

    public function setCreatedOn(\DateTimeImmutable $createdOn): self
    {
        $this->createdOn = $createdOn;

        return $this;
    }

    public function getAddress(): ?Address
    {
        return $this->address;
    }

    public function setAddress(?Address $address): self
    {
        $this->address = $address;

        return $this;
    }

    public function getStatus(): int
    {
        return $this->status;
    }

    public function setStatus(int $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getAmount(): float
    {
        return $this->amount;
    }

    public function setAmount(float $amount): self
    {
        $this->amount = $amount;

        return $this;
    }

    public function getAmountVat(): float
    {
        return $this->amountVat;
    }

    public function setAmountVat(float $amountVat): self
    {
        $this->amountVat = $amountVat;
        return $this;
    }

    public function getAmountVatIncluded(): float
    {
        return $this->amountVatIncluded;
    }

    public function setAmountVatIncluded(float $amountVatIncluded): self
    {
        $this->amountVatIncluded = $amountVatIncluded;

        return $this;
    }

    public function getCompany(): ?Company
    {
        return $this->company;
    }

    public function setCompany(?Company $company): self
    {
        $this->company = $company;

        return $this;
    }

    public function getSite(): ?Site
    {
        return $this->site;
    }

    public function setSite(?Site $site): self
    {
        $this->site = $site;

        return $this;
    }

    public function getIzbergStatus(): int
    {
        return $this->izbergStatus;
    }

    public function setIzbergStatus(int $izbergStatus): self
    {
        $this->izbergStatus = $izbergStatus;

        return $this;
    }

    public function getPaymentCode(): ?string
    {
        return $this->paymentCode;
    }

    public function setPaymentCode(?string $paymentCode): self
    {
        $this->paymentCode = $paymentCode;

        return $this;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function setCurrency($currency): self
    {
        $this->currency = $currency;

        return $this;
    }

    public function getValidationNumber()
    {
        return $this->validationNumber;
    }

    public function setValidationNumber($validationNumber): self
    {
        $this->validationNumber = $validationNumber;

        return $this;
    }

    public function getCartId(): ?int
    {
        return $this->cartId;
    }

    public function setCartId(?int $cartId): self
    {
        $this->cartId = $cartId;
        return $this;
    }

    public function isPendingCreation()
    {
        return ($this->status === self::STATUS_PENDING_CREATION);
    }

    public function getShippingPoint(): ?ShippingPoint
    {
        return $this->shippingPoint;
    }

    public function setShippingPoint($shippingPoint): self
    {
        $this->shippingPoint = $shippingPoint;

        return $this;
    }

    public function getUserEmail(): ?string
    {
        return $this->userEmail;
    }

    public function setUserEmail(?string $userEmail): Order
    {
        $this->userEmail = $userEmail;
        return $this;
    }

    /**
     * @return Collection
     */
    public function getMerchantOrders()
    {
        return $this->merchantOrders;
    }

    /**
     * @param Collection $merchantOrders
     */
    public function setMerchantOrders($merchantOrders): void
    {
        $this->merchantOrders = $merchantOrders;
    }

    /**
     * @return ?string
     */
    public function getPaymentTerms():?string
    {
        return $this->paymentTerms;
    }

    /**
     * @param ?string $paymentTerms
     */
    public function setPaymentTerms(?string $paymentTerms): self
    {
        $this->paymentTerms = $paymentTerms;
        return $this;
    }

    public function labelStatus(): string
    {
        return match ($this->status) {
            self::STATUS_PENDING_CREATION => 'Pending creation',
            self::STATUS_CANCELLED => 'Cancelled',
            self::STATUS_PAST => 'Past',
            self::STATUS_DELETED => 'Deleted',
            self::STATUS_RUNNING => 'Running',
        };
    }
}
