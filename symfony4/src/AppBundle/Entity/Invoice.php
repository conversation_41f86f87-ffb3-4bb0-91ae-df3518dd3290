<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'invoices')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\InvoiceRepository::class)]
class Invoice
{
    public const CURRENCY_EUR = 'EUR';
    public const CURRENCY_USD = 'USD';

    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

    /**
     * @var int
     */
    #[ORM\Column(name: 'izberg_id', type: 'integer', nullable: false)]
    private $izbergId;

    /**
     * @var string
     */
    #[ORM\Column(name: 'number_id', type: 'string', nullable: true)]
    private $numberId;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'pdf_file', type: 'boolean', nullable: false)]
    private $pdfFile;

    /**
     * @var string
     */
    #[ORM\Column(name: 'issuer_name', type: 'string', nullable: true)]
    private $issuerName;

    /**
     * @var \DateTimeImmutable
     */
    #[ORM\Column(name: 'created_on', type: 'datetime_immutable', nullable: false)]
    private $createdOn;

    /**
     * @var int
     */
    #[ORM\Column(name: 'order_id', type: 'integer', nullable: true)]
    private $orderId;

    /**
     * @var \DateTimeImmutable|null
     */
    #[ORM\Column(name: 'order_created_on', type: 'datetime_immutable', nullable: true)]
    private $orderCreatedOn;

    /**
     * @var string
     */
    #[ORM\Column(name: 'num_order', type: 'string', nullable: true)]
    private $numOrder;

    /**
     * @var double
     */
    #[ORM\Column(name: 'total_amount_with_taxes', type: 'float', nullable: true)]
    private $totalAmountWithTaxes;

    /**
     * @var string
     */
    #[ORM\Column(name: 'currency', type: 'string', nullable: true)]
    private $currency;

    /**
     * @var \DateTimeImmutable
     */
    #[ORM\Column(name: 'due_on', type: 'datetime_immutable', nullable: false)]
    private $dueOn;

    /**
     * @var double
     */
    #[ORM\Column(name: 'remaining_amount', type: 'float', nullable: true)]
    private $remainingAmount;

    /**
     * @var ?Company
     */
    #[ORM\JoinColumn(name: 'company_id', referencedColumnName: 'id', nullable: true)]
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\Company::class, inversedBy: 'invoices', cascade: ['persist'])]
    private $company;

    /**
     * @var string
     */
    #[ORM\Column(name: 'payment_status', type: 'string', nullable: true)]
    private $paymentStatus;

    /**
     * @var string
     */
    #[ORM\Column(name: 'status', type: 'string', nullable: true)]
    private $status;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId(int $id): void
    {
        $this->id = $id;
    }

    /**
     * @return int
     */
    public function getIzbergId(): int
    {
        return $this->izbergId;
    }

    /**
     * @param int $izbergId
     */
    public function setIzbergId(int $izbergId): void
    {
        $this->izbergId = $izbergId;
    }

    /**
     * @return string
     */
    public function getNumberId(): ?string
    {
        return $this->numberId;
    }

    /**
     * @param string $numberId
     */
    public function setNumberId(?string $numberId): void
    {
        $this->numberId = $numberId;
    }

    public function setPdfFile(bool $pdfFile): void
    {
        $this->pdfFile = $pdfFile;
    }

    public function hasPdfFile(): bool
    {
        return ($this->pdfFile);
    }

    /**
     * @return string
     */
    public function getIssuerName(): string
    {
        return $this->issuerName;
    }

    /**
     * @param string $issuerName
     */
    public function setIssuerName(string $issuerName): void
    {
        $this->issuerName = $issuerName;
    }

    /**
     * @return \DateTimeImmutable
     */
    public function getCreatedOn(): \DateTimeImmutable
    {
        return $this->createdOn;
    }

    /**
     * @param \DateTimeImmutable $createdOn
     */
    public function setCreatedOn(\DateTimeImmutable $createdOn): void
    {
        $this->createdOn = $createdOn;
    }

    /**
     * @return int
     */
    public function getOrderId(): ?int
    {
        return $this->orderId;
    }

    /**
     * @param int $orderId
     */
    public function setOrderId(?int $orderId): void
    {
        $this->orderId = $orderId;
    }

    /**
     * @return string
     */
    public function getNumOrder(): ?string
    {
        return $this->numOrder;
    }

    /**
     * @param string $numOrder
     */
    public function setNumOrder(?string $numOrder): void
    {
        $this->numOrder = $numOrder;
    }

    /**
     * @return \DateTimeImmutable|null
     */
    public function getOrderCreatedOn(): ?\DateTimeImmutable
    {
        return $this->orderCreatedOn;
    }

    /**
     * @param \DateTimeImmutable|null $orderCreatedOn
     * @return $this
     */
    public function setOrderCreatedOn(?\DateTimeImmutable $orderCreatedOn): self
    {
        $this->orderCreatedOn = $orderCreatedOn;
        return $this;
    }

    /**
     * @return float
     */
    public function getTotalAmountWithTaxes(): float
    {
        return $this->totalAmountWithTaxes;
    }

    /**
     * @param float $totalAmountWithTaxes
     */
    public function setTotalAmountWithTaxes(float $totalAmountWithTaxes): void
    {
        $this->totalAmountWithTaxes = $totalAmountWithTaxes;
    }

    /**
     * @return string
     */
    public function getCurrency(): string
    {
        return $this->currency;
    }

    /**
     * @param string $currency
     */
    public function setCurrency(string $currency): void
    {
        $this->currency = $currency;
    }

    /**
     * @return \DateTimeImmutable
     */
    public function getDueOn(): \DateTimeImmutable
    {
        return $this->dueOn;
    }

    /**
     * @param \DateTimeImmutable $dueOn
     */
    public function setDueOn(\DateTimeImmutable $dueOn): void
    {
        $this->dueOn = $dueOn;
    }

    /**
     * @return float
     */
    public function getRemainingAmount(): float
    {
        return $this->remainingAmount;
    }

    /**
     * @param float $remainingAmount
     */
    public function setRemainingAmount(float $remainingAmount): void
    {
        $this->remainingAmount = $remainingAmount;
    }

    /**
     * @return Company|null
     */
    public function getCompany(): ?Company
    {
        return $this->company;
    }

    /**
     * @param mixed $company
     */
    public function setCompany(?Company $company): void
    {
        $this->company = $company;
    }

    /**
     * @return string
     */
    public function getPaymentStatus(): string
    {
        return $this->paymentStatus;
    }

    /**
     * @param string $paymentStatus
     */
    public function setPaymentStatus(string $paymentStatus): void
    {
        $this->paymentStatus = $paymentStatus;
    }

    /**
     * @return string
     */
    public function getStatus(): string
    {
        return $this->status;
    }

    /**
     * @param string $status
     */
    public function setStatus(string $status): void
    {
        $this->status = $status;
    }

    public function isInvoiceType ()
    {
        return true;
    }

    public function isCreditNoteType()
    {
        return false;
    }
}
