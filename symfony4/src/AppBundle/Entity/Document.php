<?php
/**
 * Created by PhpStorm.
 * User: AQU04740
 * Date: 04/04/2017
 * Time: 17:39
 */

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Open\TicketBundle\Entity\Traits\IdentifiedEntityTrait;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * Document
 */
#[ORM\Table(name: 'document')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\DocumentRepository::class)]
#[ORM\HasLifecycleCallbacks]
class Document implements \JsonSerializable
{
    const BUSINESS_REGISTRATION = "businessRegistration";
    const LEGALES_DOC = "legalesDoc";


    const MIME_CONSTRAINT = ["application/pdf","image/jpeg", "image/gif", "image/png", "image/tiff", "text/plain", "application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"];
    const SIZE_CONSTRAINT = 10*1024*1024;

    use TimestampedTrait;
    use TechnicalIdentifierTrait;

    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

    /**
     * @var int
     */
    #[ORM\Column(name: 'size', type: 'integer', nullable: true)]
    private $size;

    /**
     * @var string
     */
    #[ORM\Column(name: 'filename', type: 'string', length: 255)]
    private $filename;

    /**
     * @var string
     */
    #[ORM\Column(name: 'mimetype', type: 'string', length: 30)]
    private $mimetype;

    /**
     * @var string
     */
    #[ORM\Column(name: 'type', type: 'string', length: 30)]
    private $type;

    #[ORM\Column(name: 'binary_file', type: 'blob', nullable: true)]
    protected $binary;

    #[ORM\JoinColumn(name: 'company_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\Company::class, inversedBy: 'documents')]
    private $company;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return string
     */
    public function getFilename()
    {
        return $this->filename;
    }

    /**
     * @param string $filename
     */
    public function setFilename($filename)
    {
        $this->filename = $filename;
    }

  /**
   * @return string
   */
  public function getMimeType()
  {
    return $this->mimetype;
  }

  /**
   * @param string $mimetype
   */
  public function setMimeType($mimetype)
  {
    $this->mimetype = $mimetype;
  }

  /**
   * @return string
   */
  public function getType()
  {
    return $this->type;
  }

  /**
   * @param string $mimetype
   */
  public function setType($type)
  {
    $this->type = $type;
  }

  /**
     * @return mixed
     */
    public function getBinary()
    {
        return $this->binary;
    }

    /**
     * @param mixed $binary
     */
    public function setBinary($binary)
    {
        $this->binary = $binary;
    }

    public function __toString()
    {
        return $this->filename;
    }

    public function jsonSerialize(): array {
        return [
            "filename" => $this->filename,
            "mimetype" => $this->mimetype
        ];
    }


    /**
     * Set company.
     *
     * @param \AppBundle\Entity\Company|null $company
     *
     * @return Document
     */
    public function setCompany(\AppBundle\Entity\Company $company = null)
    {
        $this->company = $company;

        return $this;
    }

    /**
     * Get company.
     *
     * @return \AppBundle\Entity\Company|null
     */
    public function getCompany()
    {
        return $this->company;
    }

    /**
     * Set size.
     *
     * @param int $size
     *
     * @return Document
     */
    public function setSize($size)
    {
        $this->size = $size;

        return $this;
    }

    /**
     * Get size.
     *
     * @return int
     */
    public function getSize()
    {
        return $this->size;
    }
}
