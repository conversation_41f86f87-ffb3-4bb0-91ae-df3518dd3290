<?php

namespace AppBundle\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * WishList
 *
 *
 */
#[ORM\Table(name: 'wishList')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\WishListRepository::class)]
#[ORM\HasLifecycleCallbacks]
class WishList {

    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

    /**
     * One cart have ont user that created it
     */
    #[ORM\JoinColumn(name: 'user_id', referencedColumnName: 'id', nullable: true)]
    #[ORM\ManyToOne(targetEntity: \User::class)]
    private $user;

    /**
     * @var string
     */
    #[Assert\Length(max: 50)]
    #[Assert\NotBlank]
    #[ORM\Column(name: 'name', type: 'string', length: 50, nullable: false)]
    private $name;

    /**
     * @var ArrayCollection
     *
     * One company has Many sites.
     */
    #[ORM\OneToMany(targetEntity: \WishListItem::class, mappedBy: 'wishList', cascade: ['persist', 'remove'])]
    private $items;

    /**
     * @var string
     */
    #[Assert\NotBlank]
    #[ORM\Column(name: 'currency', type: 'string', length: 50, nullable: false)]
    private $currency;

    public function __construct()
    {
        $this->items = new ArrayCollection();
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId(int $id)
    {
        $this->id = $id;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @param string $name
     */
    public function setName(string $name)
    {
        $this->name = $name;
    }

    public function getItems(): Collection
    {
        return $this->items;
    }

    /**
     * @param mixed $items
     */
    public function setItems($items)
    {
        $this->items = $items;
    }

    /**
     * @return mixed
     */
    public function getUser()
    {
        return $this->user;
    }

    /**
     * @param mixed $user
     */
    public function setUser($user)
    {
        $this->user = $user;
    }

    public function addItem(WishListItem $item): self
    {
        $this->items->add($item);

        return $this;
    }

    /**
     * @return mixed
     */
    public function getCurrency()
    {
        return $this->currency;
    }

    /**
     * @param mixed $currency
     */
    public function setCurrency($currency)
    {
        $this->currency = $currency;
    }
}
