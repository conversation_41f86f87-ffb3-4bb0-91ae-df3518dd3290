<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Security\Core\User\UserInterface;

#[ORM\Table(name: 'vendors')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\VendorRepository::class)]
class Vendor implements UserInterface
{
    #[ORM\Id]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Id]
    #[ORM\Column(type: 'string', length: 255)]
    private $name;

    #[ORM\Column(type: 'string', length: 255)]
    private $slug;

    #[ORM\Column(type: 'string', length: 255)]
    private $preferedLanguage;

    #[ORM\Column(type: 'string', length: 255)]
    private $companyName;

    /**
     * @return mixed
     */
    public function getSlug()
    {
        return $this->slug;
    }

    /**
     * @param mixed $slug
     */
    public function setSlug($slug): self
    {
        $this->slug = $slug;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getPreferedLanguage()
    {
        return $this->preferedLanguage;
    }

    /**
     * @param mixed $prefered_language
     */
    public function setPreferedLanguage($preferedLanguage): self
    {
        $this->preferedLanguage = $preferedLanguage;
        return $this;
    }

    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getName()
    {
        return $this->name;
    }

    /**
     * @param mixed $name
     */
    public function setName($name): self
    {
        $this->name = $name;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getCompanyName()
    {
        return $this->companyName;
    }

    /**
     * @param mixed $companyName
     */
    public function setCompanyName($companyName): self
    {
        $this->companyName = $companyName;
        return $this;
    }

    public function getRoles(): array
    {
        return ['ROLE_VENDOR'];
    }

    public function getPassword()
    {
        return null;
    }

    public function getSalt()
    {
        return null;
    }

    public function getUsername()
    {
        return $this->id;
    }

    public function eraseCredentials(): void
    {
    }

    public function getUserIdentifier():string
    {
        return $this->id;
    }
}
