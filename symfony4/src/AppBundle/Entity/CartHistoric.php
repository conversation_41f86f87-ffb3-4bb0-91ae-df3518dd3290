<?php
/**
 * Created by PhpStorm.
 * User: PCH07650
 * Date: 23/05/2018
 * Time: 16:09
 */

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;


/**
 * Class CartHistoric
 *
 */
#[ORM\Table(name: 'cart_historic')]
#[ORM\Entity(repositoryClass: \AppBundle\Repository\CartHistoricRepository::class)]
#[ORM\HasLifecycleCallbacks]
class CartHistoric implements \JsonSerializable
{
    use TechnicalIdentifierTrait;

    /**
     * @var int
     */
    #[ORM\Id]
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $historicId;

    /**
     * @var User
     */
    #[Assert\Type(type: 'AppBundle\Entity\User')]
    #[ORM\JoinColumn(name: 'user_id', referencedColumnName: 'id', nullable: false)]
    #[ORM\ManyToOne(targetEntity: \User::class)]
    private $user;

    /**
     * @var User
     */
    #[Assert\Type(type: 'AppBundle\Entity\User')]
    #[ORM\JoinColumn(name: 'user_assigned_id', referencedColumnName: 'id', nullable: false)]
    #[ORM\ManyToOne(targetEntity: \User::class)]
    private $assignedUser;

    /**
     * @var string
     */
    #[Assert\NotBlank]
    #[Assert\Length(max: 20)]
    #[ORM\Column(name: 'status', type: 'string', length: 20)]
    private $status;

    /**
     * @var string
     */
    #[Assert\NotBlank]
    #[Assert\Length(max: 100)]
    #[ORM\Column(name: 'comment', type: 'string', length: 1024)]
    private $comment;

    /**
     * @var \DateTime
     */
    #[Assert\NotBlank]
    #[ORM\Column(name: 'date', type: 'datetime', nullable: false)]
    private $date;

    /**
     * @var Cart
     */
    #[Assert\Type(type: 'AppBundle\Entity\Cart')]
    #[ORM\JoinColumn(name: 'cart_id', referencedColumnName: 'id', nullable: false)]
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\Cart::class, inversedBy: 'cartHistoric')]
    private $cart;

    /**
     * @return int
     */
    public function getHistoricId(): ?int
    {
        return $this->historicId;
    }

    /**
     * @param int $historicId
     */
    public function setHistoricId(?int $historicId): void
    {
        $this->historicId = $historicId;
    }

    /**
     * @return User
     */
    public function getUser(): User
    {
        return $this->user;
    }

    /**
     * @param User $userId
     */
    public function setUser(User $user): void
    {
        $this->user = $user;
    }

    /**
     * @return string
     */
    public function getStatus(): string
    {
        return $this->status;
    }

    /**
     * @param string $status
     */
    public function setStatus(string $status): void
    {
        $this->status = $status;
    }

    /**
     * @return string
     */
    public function getComment(): string
    {
        return $this->comment;
    }

    /**
     * @param string $comment
     */
    public function setComment(string $comment): void
    {
        $this->comment = $comment;
    }

    /**
     * @return \DateTime
     */
    public function getDate(): \DateTime
    {
        return $this->date;
    }

    /**
     * @param \DateTime $date
     */
    public function setDate(\DateTime $date): void
    {
        $this->date = $date;
    }

    /**
     * @return Cart
     */
    public function getCart(): Cart
    {
        return $this->cart;
    }

    /**
     * @param Cart $cart
     */
    public function setCart(Cart $cart): void
    {
        $this->cart = $cart;
    }

    /**
     * @return User
     */
    public function getAssignedUser(): User
    {
        return $this->assignedUser;
    }

    /**
     * @param User $assignedUser
     */
    public function setAssignedUser(User $assignedUser): void
    {
        $this->assignedUser = $assignedUser;
    }

    public function jsonSerialize(): array {
        return [
            'user' => $this->user->getFirstname() . ' ' . $this->user->getLastname(),
            'status' => $this->status,
            'comment' => $this->comment,
            'date' => $this->date
        ];
    }
}
