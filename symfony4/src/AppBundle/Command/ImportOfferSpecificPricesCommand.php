<?php

namespace AppBundle\Command;

use AppBundle\Entity\SpecificPrice;
use AppBundle\Exception\MailException;
use AppBundle\Model\Merchant;
use Exception;
use Open\IzbergBundle\Api\KycApi;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use stdClass;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use AppBundle\Services\SpecificPriceService;
use AppBundle\Services\MerchantService;
use AppBundle\Services\MailService;

class ImportOfferSpecificPricesCommand extends Command implements LoggerAwareInterface
{

    private const LINE_SEPARATOR = "\n";
    private const NB_COLS = 17;
    private const COL_COMPANY_IDENTIFICATION_POSITION = 0;
    private const COL_VENDOR_REFERENCE_POSITION = 1;
    private const COL_INCOTERM_POSITION = 2;
    private const COL_COUNTRY_POSITION = 3;
    private const COL_BASIC_PRICE_POSITION = 4;
    private const COL_THRESHOLD1_POSITION = 5;
    private const COL_PRICE1_POSITION = 6;
    private const COL_THRESHOLD2_POSITION = 7;
    private const COL_PRICE2_POSITION = 8;
    private const COL_THRESHOLD3_POSITION = 9;
    private const COL_PRICE3_POSITION = 10;
    private const COL_THRESHOLD4_POSITION = 11;
    private const COL_PRICE4_POSITION = 12;
    private const COL_MOQ_POSITION = 13;
    private const COL_VALIDITY_DATE_POSITION = 14;
    private const COL_DELAY_OF_DELIVERY_POSITION = 15;
    private const COL_FRAME_CONTRACT_POSITION = 16;

    private const COMMAND_NAME = "COMMAND_NAME";

    private SpecificPriceService $specificPriceService;
    private LoggerInterface $logger;
    private MerchantService $merchantService;
    private MailService $mailService;
    private KycApi $kycApi;

    /**
     * ImportOfferSpecificPricesCommand constructor.
     *
     * @param SpecificPriceService $specificPriceService
     * @param MerchantService      $merchantService
     * @param MailService          $mailService
     * @param KycApi               $kycApi
     */
    public function __construct(SpecificPriceService $specificPriceService, MerchantService $merchantService, MailService $mailService, KycApi $kycApi)
    {
        $this->specificPriceService = $specificPriceService;
        $this->merchantService = $merchantService;
        $this->mailService = $mailService;
        $this->kycApi = $kycApi;

        parent::__construct();
    }

    /**
     * {@inheritdoc}
     */
    protected function configure():void
    {
        $this
            ->setName('open:import:offer_specific_prices')
            ->setDescription("Import offer's specific prices")
            ->setHelp("This command allows you to import offer's specific prices from csv file (stored in IZB web service)");
    }

    /***
     *
     * Try to validate file
     *
     * @param null|array $initialBlob
     * @param $msg
     *
     * @return bool
     */
    private function isBlobOK(?array $initialBlob, string &$msg):bool
    {
        if (!is_array($initialBlob)) {
            $msg .= "Data is empty" . self::LINE_SEPARATOR;
            return false;
        }

        if (count($initialBlob) == 0) {
            $msg .= "No Data" . self::LINE_SEPARATOR;
            return false;
        }

        // remove empty lines
        $blob = [];
        foreach ($initialBlob as $line) {
            if (!empty($line)) {
                $blob[] = $line;
            }
        }

        if (count($blob) == 0) {
            $msg .= "No Data (all lines are empty)" . self::LINE_SEPARATOR;
            return false;
        }

        $firstLine = $blob[0];
        $sep = $this->guessSeparator($firstLine);
        if ($sep === null) {
            $msg .= "Wrong separator" . self::LINE_SEPARATOR;
            return false;
        }

        $cols = str_getcsv($firstLine, $sep);
        if (count($cols) < self::NB_COLS) {
            $msg .= "Wrong columns count : " . count($cols) . " column(s) found, " . self::NB_COLS . " expected" . self::LINE_SEPARATOR;
            return false;
        }
        return $this->isLineCorrect($cols, null, $msg);
    }


    /**
     * Try to validate a line
     *
     * @param array $line
     * @param int|null $currentLine
     * @param string $message
     *
     * @return bool
     */
    private function isLineCorrect(array $line, ?int $currentLine, string &$message): bool
    {
        if (count($line) < self::NB_COLS) {
            $message .= "Error: Line number " . ($currentLine??'null') . ", Column count (" . count($line) . ") < expected minimum columns count (" . self::NB_COLS . ")" . self::LINE_SEPARATOR;
            return false;
        }

        if (empty(trim($line[self::COL_VENDOR_REFERENCE_POSITION])) && empty(trim($line[self::COL_COMPANY_IDENTIFICATION_POSITION]))) {
            $message .= "Error: Line number " . ($currentLine??'null') . ", Column " . self::COL_VENDOR_REFERENCE_POSITION . " and " . self::COL_COMPANY_IDENTIFICATION_POSITION . " are empty" . self::LINE_SEPARATOR;
            return false;
        }

        return true;
    }


    /***
     * @param int $merchantId
     * @param array $line
     *
     * @return bool
     */
    private function putInDB(int $merchantId, array $line):bool
    {
        $convertStringToFloat = function(string $value):float {
            $value = str_replace(',', '.',$value);
            $value = floatval(trim($value));
            return $value;
        };

        try {
            $price = new SpecificPrice();
            /**
             * @psalm-suppress PossiblyNullArgument
             * catch by try/catch
             */
            $price->setCompanyIdentification(trim($line[self::COL_COMPANY_IDENTIFICATION_POSITION]));
            $price->setIZBmerchantId($merchantId);
            /**
             * @psalm-suppress PossiblyNullArgument
             * catch by try/catch
             */
            $price->setVendorReference(trim($line[self::COL_VENDOR_REFERENCE_POSITION]));
            /**
             * @psalm-suppress PossiblyNullArgument
             * catch by try/catch
             */
            $price->setMoq(intval(trim($line[self::COL_MOQ_POSITION])));

            /**
             * @psalm-suppress PossiblyNullArgument
             * catch by try/catch
             */
            $price->setBasicPrice(call_user_func($convertStringToFloat, $line[self::COL_BASIC_PRICE_POSITION]));

            /**
             * @psalm-suppress PossiblyNullArgument
             * catch by try/catch
             */
            $price->setThreshold1(intval(trim($line[self::COL_THRESHOLD1_POSITION])));
            /**
             * @psalm-suppress PossiblyNullArgument
             * catch by try/catch
             */
            $price->setPrice1(call_user_func($convertStringToFloat, $line[self::COL_PRICE1_POSITION]));

            /**
             * @psalm-suppress PossiblyNullArgument
             * catch by try/catch
             */
            $price->setThreshold2(intval(trim($line[self::COL_THRESHOLD2_POSITION])));
            /**
             * @psalm-suppress PossiblyNullArgument
             * catch by try/catch
             */
            $price->setPrice2(call_user_func($convertStringToFloat, $line[self::COL_PRICE2_POSITION]));

            /**
             * @psalm-suppress PossiblyNullArgument
             * catch by try/catch
             */
            $price->setThreshold3(intval(trim($line[self::COL_THRESHOLD3_POSITION])));
            /**
             * @psalm-suppress PossiblyNullArgument
             * catch by try/catch
             */
            $price->setPrice3(call_user_func($convertStringToFloat, $line[self::COL_PRICE3_POSITION]));

            /**
             * @psalm-suppress PossiblyNullArgument
             * catch by try/catch
             */
            $price->setThreshold4(intval(trim($line[self::COL_THRESHOLD4_POSITION])));
            /**
             * @psalm-suppress PossiblyNullArgument
             * catch by try/catch
             */
            $price->setPrice4(call_user_func($convertStringToFloat, $line[self::COL_PRICE4_POSITION]));

            /**
             * @psalm-suppress PossiblyNullArgument
             * catch by try/catch
             */
            $price->setIncoterm(trim($line[self::COL_INCOTERM_POSITION]));
            /**
             * @psalm-suppress PossiblyNullArgument
             * catch by try/catch
             */
            $price->setCountry(trim($line[self::COL_COUNTRY_POSITION]));

            /**
             * @psalm-suppress PossiblyNullArgument
             * catch by try/catch
             */
            $price->setValidityDate(new \DateTime(trim($line[self::COL_VALIDITY_DATE_POSITION])));

            /**
             * @psalm-suppress PossiblyNullArgument
             * catch by try/catch
             */
            $price->setDelayOfDelivery($line[self::COL_DELAY_OF_DELIVERY_POSITION]);

            /**
             * @psalm-suppress PossiblyNullArgument
             * catch by try/catch
             */
            $price->setFrameContract(trim($line[self::COL_FRAME_CONTRACT_POSITION]));

            // Save data
            $this->specificPriceService->save($price);
        } catch (Exception $e) {
            $this->logger->error(
                'Error while writing data in DB',
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::SPECIFIC_PRICE_TECHNICAL_ERROR,
                    'exception' => $e->getMessage(),
                    'line' => $line,
                    'merchantId' => $merchantId
                ])
            );
            return false;
        }
        return true;
    }

    /**
     * @param int       $merchantId
     * @param stdClass $fileInfo
     *
     * @throws MailException
     */
    private function importData(int $merchantId, stdClass $fileInfo):void
    {
        $errorMessage = '';
        $logMessages = [];

        $merchant = $this->merchantService->findMerchantById($merchantId);
        if(!$merchant instanceof Merchant){
            return;
        }
        $name = $merchant->getName();
        $id = $merchant->getId();
        if(!$name || !$id){
            return;
        }
        $msg = "Importing file for merchant [" . $name . '] id = ' . $id;
        $logMessages[] = $msg . self::LINE_SEPARATOR;

        $msg = "Validating file : " . $fileInfo->filename;
        $logMessages[] = $msg . self::LINE_SEPARATOR;

        $blob = $fileInfo->blob;
        if (!$this->isBlobOK($blob, $errorMessage)) {
            // log & send email to merchant
            $this->logger->error(
                'All files are BAD !',
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::SPECIFIC_PRICE_IMPORT_INVALID_FILES,
                    'message' => $errorMessage,
                    'merchantId' => $merchantId,
                ])
            );

            $data = array(MailService::FIRST_NAME_VAR => $merchant->getMainContactFirstName(),
                MailService::LAST_NAME_VAR => $merchant->getMainContactLastName(),
                "merchantName" => $merchant->getName(),
                'message' => 'Date : ' . date('Y-M-d H:i:s') . self::LINE_SEPARATOR . 'No file were imported.' . self::LINE_SEPARATOR . 'While importing [' . $fileInfo->filename . ']' . self::LINE_SEPARATOR . '' . $errorMessage);

            $this->mailService->sendEmailMessage(
                MailService::SPECIFIC_PRICE_UPLOADED,
                'en',
                $merchant->getMainContactEmail(),
                $data
            );

            return;
        }

        // Clean data in DB
        $this->specificPriceService->cleanPerMerchantId($merchantId);

        $msg = "Importing " . $fileInfo->filename;
        $logMessages[] = $msg . self::LINE_SEPARATOR;

        $blob = $fileInfo->blob;
        if ($this->isBlobOK($blob, $errorMessage)) {
            // do not handle first line of csv file
            array_shift($blob);
            $totalLines = count($blob);
            $sep = $this->guessSeparator($blob[0]);
            if(!$sep){
                return;
            }
            $currentLine = 0;
            $nbGoodLines = 0;
            foreach ($blob as $line) {
                $cols = str_getcsv($line, $sep);
                if ($this->isLineCorrect($cols, $currentLine, $errorMessage)) {
                    if ($this->putInDB($merchantId, $cols)) {
                        $nbGoodLines++;
                    }
                }
                $currentLine++;
            }

            // Final report
            if ($nbGoodLines != $totalLines) {
                // Some lines are ignored
                // LOG + EMAIL
                $this->logger->error(
                    'Warning ' . ($totalLines - $nbGoodLines) . ' line(s) ignored',
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME=>EventNameEnum::SPECIFIC_PRICE_IMPORT_INVALID_LINES,
                        'merchant.name' => $merchant->getName(),
                        'merchant.id' => $merchant->getId(),
                        'totalLinesImported' => $nbGoodLines,
                        'totalLines' => $totalLines,
                        'message' => $errorMessage,
                    ])
                );

                $data = array(MailService::FIRST_NAME_VAR => $merchant->getMainContactFirstName(),
                    MailService::LAST_NAME_VAR => $merchant->getMainContactLastName(),
                    "merchantName" => $merchant->getName(),
                    'message' => 'Date : '.date('Y-M-d H:i:s').self::LINE_SEPARATOR.'Some lines were not imported.'.self::LINE_SEPARATOR.'While importing ['.$fileInfo->filename.']'.self::LINE_SEPARATOR.''.$errorMessage);

                $this->mailService->sendEmailMessage(MailService::SPECIFIC_PRICE_UPLOADED, 'en', $merchant->getMainContactEmail(), $data);

            } else {
                // everything is OK
                // LOG + EMAIL
                $this->logger->info(
                    'Import OK ' . ($totalLines) . ' line(s) imported successfully',
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME=>EventNameEnum::SPECIFIC_PRICE_IMPORTED,
                        'merchant.name' => $merchant->getName(),
                        'merchant.id' => $merchant->getId(),
                        'totalLinesImported' => $totalLines,
                        'totalLines' => $totalLines,
                    ])
                );

                $data = array(MailService::FIRST_NAME_VAR => $merchant->getMainContactFirstName(),
                    MailService::LAST_NAME_VAR => $merchant->getMainContactLastName(),
                    "merchantName" => $merchant->getName(),
                    'message' => 'Date : '.date('Y-M-d H:i:s').self::LINE_SEPARATOR.'Import OK '.($totalLines).' line(s) imported successfully'.self::LINE_SEPARATOR.'While importing ['.$fileInfo->filename.']');

                $this->mailService->sendEmailMessage(
                    MailService::SPECIFIC_PRICE_UPLOADED,
                    'en',
                    $merchant->getMainContactEmail(),
                    $data
                );
            }
        } else {
            $this->logger->error(
                'File was not imported.' . self::LINE_SEPARATOR . 'While importing [' . $fileInfo->filename . ']',
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::SPECIFIC_PRICE_IMPORT_INVALID_LINES,
                    'merchant.name' => $merchant->getName(),
                    'merchant.id' => $merchant->getId(),
                    'message' => $errorMessage,
                ])
            );

            $data = array(MailService::FIRST_NAME_VAR => $merchant->getMainContactFirstName(),
                MailService::LAST_NAME_VAR => $merchant->getMainContactLastName(),
                "merchantName" => $merchant->getName(),
                'message' => 'Date : '.date('Y-M-d H:i:s').self::LINE_SEPARATOR.'File was not imported.'.self::LINE_SEPARATOR.'While importing ['.$fileInfo->filename.']'.self::LINE_SEPARATOR.''.$errorMessage);


            $this->mailService->sendEmailMessage(MailService::SPECIFIC_PRICE_UPLOADED, 'en', $merchant->getMainContactEmail(), $data);
        }
    }

    /***
     * @param InputInterface $input
     * @param OutputInterface $output
     *
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $name = $this->getName();

        $this->logger->info(
            "Command " . $name . " started",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME=>EventNameEnum::SPECIFIC_PRICE_GENERAL_INFO,
                self::COMMAND_NAME => $name,
                'event' => "started",
            ])
        );

        foreach ($this->kycApi->findAllSpecificPriceKyc() as $kyc) {
            $this->importSpecificPrice($kyc);
        }

        //log end
        $this->logger->info(
            "Command " . $name . " ended",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME=>EventNameEnum::SPECIFIC_PRICE_GENERAL_INFO,
                self::COMMAND_NAME => $name,
                "event" => "end",
            ])
        );

        return 0;
    }

    private function importSpecificPrice(stdClass $kyc):void
    {
        $merchantId = $this->kycApi->getMerchantId($kyc->merchant);

        foreach($this->findSpecificPriceFiles($kyc) as $fileInfo) {
            $this->importData($merchantId, $fileInfo);
            $this->deleteFile($fileInfo);
        }
    }

    private function findSpecificPriceFiles(stdClass $kyc): \Generator
    {
        foreach ($kyc->parts as $file) {
            yield $this->kycApi->getFile($file);
        }
    }

    private function deleteFile(stdClass $fileInfo):void
    {
        // Delete KYC FILE IN IZB (file is retrieved)
        if ($this->kycApi->deleteKycFileById($fileInfo->id) == false) {
            // LOG ERROR DELETE
            $this->logger->error(
                'Error deleting KYC file While importing [' . $fileInfo->filename . ']',
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::SPECIFIC_PRICE_API_DELETE_ERROR,
                    'id' => $fileInfo->id
                ])
            );
        } else {
            // LOG SUCCESS DELETE
            $this->logger->info(
                'Deleting KYC file While importing [' . $fileInfo->filename . '] successfully',
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::SPECIFIC_PRICE_GENERAL_INFO
                ])
            );
        }
    }

    /***
     * Guess between comma, semi colon and tab separators
     *
     * @param string $ligne
     *
     * @return null|string
     */
    private function guessSeparator(string $ligne):?string
    {
        // Detect CSV separator !
        foreach ([",", ";", "\t"] as $sep) {
            if (count(str_getcsv($ligne, $sep)) == self::NB_COLS) {
                return $sep;
            }
        }
        return null;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
