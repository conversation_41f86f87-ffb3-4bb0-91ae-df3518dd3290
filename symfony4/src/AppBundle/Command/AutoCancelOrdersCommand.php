<?php

namespace AppBundle\Command;

use AppBundle\Services\MerchantOrderService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class AutoCancelOrdersCommand extends Command
{
    use UtilCommandTrait;

    private MerchantOrderService $merchantOrderService;

    /**
     * AutoCancelOrdersCommand constructor.
     *
     * @param MerchantOrderService $merchantOrderService
     */
    public function __construct(MerchantOrderService $merchantOrderService)
    {
        $this->merchantOrderService = $merchantOrderService;
        parent::__construct();
    }

    protected function configure():void
    {
        $this
            ->setName('open:orders:auto-cancel')
            ->addOption('jobs', null, InputOption::VALUE_NONE)
            ->setDescription('Auto cancel all orders from <PERSON><PERSON><PERSON> with "initial" status and creation date > 15 days');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->startCommand($output);
        $hasJobsOption = $input->getOption('jobs');

        $offset = 0;
        $limit = 10;

        $merchantOrderIdsToCancel = $this->merchantOrderService->fetchMerchantOrdersIdsToCancel($offset, $limit);

        while (count($merchantOrderIdsToCancel)) {
            $output->writeln(sprintf('Auto-cancel offset %d limit %d', $offset, $limit));
            $this->merchantOrderService->autoCancel($merchantOrderIdsToCancel, $hasJobsOption);

            $offset += $limit;
            $merchantOrderIdsToCancel = $this->merchantOrderService->fetchMerchantOrdersIdsToCancel($offset, $limit);
        }

        $this->endCommand($output);

        return 0;
    }
}
