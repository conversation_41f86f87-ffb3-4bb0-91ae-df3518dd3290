<?php

namespace AppBundle\Command;

use AppBundle\Services\JobService;
use AppBundle\Services\OrderService;
use AppBundle\Services\StorageService;
use DateTimeImmutable;
use Doctrine\ORM\ORMException;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class SyncOrdersCommand extends Command
{
    use UtilCommandTrait;

    private OrderService $orderService;
    private JobService $jobService;
    private StorageService $storageService;

    public function __construct(OrderService $orderService, JobService $jobService,
        StorageService $storageService)
    {
        $this->orderService = $orderService;
        $this->jobService = $jobService;
        $this->storageService = $storageService;

        parent::__construct();
    }

    protected function configure():void
    {
        $this
            ->setName('open:orders:sync')
            ->addOption('jobs', null, InputOption::VALUE_NONE)
            ->addOption('full', null, InputOption::VALUE_NONE)
            ->setDescription('fetch all orders from Izberg and sync them with local database');
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws ORMException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->startCommand($output);
        $hasJobsOption = $input->getOption('jobs');
        $fullSync = $input->getOption('full');

        $offset = 0;
        $limit = 100;

        $today = new DateTimeImmutable('now', (new \DateTimeZone('UTC')));

        $lastSync = (!$fullSync) ? $this->storageService->get("order_last_sync") : null;
        $lastSyncDate = ($lastSync !== null) ? new DateTimeImmutable($lastSync) : null;

        $izbergOrderResult = $this->orderService->fetchIzbergOrders($offset, $limit, $lastSyncDate);

        while($izbergOrderResult->hasOrder()) {
            $output->writeln(sprintf('sync offset %d limit %d', $offset, $limit));
            if ($hasJobsOption) {
                $this->jobService->syncOrder($izbergOrderResult);
            } else {
                $this->orderService->sync($izbergOrderResult);
            }

            $offset += $limit;
            $izbergOrderResult = $this->orderService->fetchIzbergOrders($offset, $limit);
        }

        $this->storageService->set("order_last_sync", $today->format(\DateTimeInterface::ATOM));

        $this->endCommand($output);

        return 0;
    }
}
