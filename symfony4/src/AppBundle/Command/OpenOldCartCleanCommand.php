<?php

namespace AppBundle\Command;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Driver\Result;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class OpenOldCartCleanCommand extends Command
{
    private Connection $dbal;

    /**
     * OpenOldCartCleanCommand constructor.
     * @param Connection $dbal
     */
    public function __construct(Connection $dbal)
    {
        $this->dbal = $dbal;

        parent::__construct();
    }


    protected function configure():void
    {
        $this
            ->setName('open:oldcart:clean')
            ->setDescription('Clean old cart (> 6 month)')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $nullablesEurCart = $this->findOldCart('cart_eur_id');
        $nullablesUsdCart = $this->findOldCart('cart_usd_id');
        $nullablesComparEurCart = $this->findOldCart('comparison_sheet_eur_id');
        $nullablesComparUsdCart = $this->findOldCart('comparison_sheet_usd_id');
        $nullablesAssignedCart = $this->findOldAssignCart();
        $nullablesRejectedCart = $this->findOldRejectedCart();

        foreach ($nullablesEurCart as $nullableCart) {
            $this->cleanOldCartRequest($nullableCart);
            $this->cleanUserCart($nullableCart, 'cart_eur_id');
        }

        foreach ($nullablesUsdCart as $nullableCart) {
            $this->cleanOldCartRequest($nullableCart);
            $this->cleanUserCart($nullableCart, 'cart_usd_id');
        }

        foreach ($nullablesComparEurCart as $nullableCart) {
            $this->cleanOldCartRequest($nullableCart);
            $this->cleanUserCart($nullableCart, 'comparison_sheet_eur_id');
        }

        foreach ($nullablesComparUsdCart as $nullableCart) {
            $this->cleanOldCartRequest($nullableCart);
            $this->cleanUserCart($nullableCart, 'comparison_sheet_usd_id');
        }

        foreach ($nullablesAssignedCart as $nullableCart) {
            $this->cleanOldCartRequest($nullableCart);
            $this->cleanOldAssignedCart($nullableCart);
        }

        foreach ($nullablesRejectedCart as $nullableCart) {
            $this->cleanOldCartRequest($nullableCart);
            $this->cleanOldAssignedCart($nullableCart);
        }

        $this->cleanOldRenameCartStatusHistory();
        $this->cleanOldRenameCartStatusRequest();

        $output->writeln(count($nullablesEurCart). ' EUR cart(s)');
        $output->writeln(count($nullablesUsdCart). ' USD cart(s)');
        $output->writeln(count($nullablesComparEurCart). ' Comparison sheet EUR cart(s)');
        $output->writeln(count($nullablesComparUsdCart). ' Comparison sheet USD cart(s)');
        $output->writeln(count($nullablesAssignedCart). ' Assigned cart(s)');
        $output->writeln(count($nullablesRejectedCart). ' Rejected cart(s)');

        $output->writeln('Old cart clean.');

        return 0;
    }

    private function findOldCart(string $devise): array
    {

        $queryBuilder = $this->dbal->createQueryBuilder();

        $queryBuilder->select('c.id, c.created_at')
            ->from('users','u')
            ->leftJoin('u','cart','c', 'c.id = u.' . $devise)
            ->where('u.'. $devise .' IS NOT NULL')
            ->andWhere('c.created_at <= DATE_ADD(Now(), INTERVAL - 6 MONTH)')
            ->andWhere('(c.status = "CREATE" OR c.status = "ASSIGN")');
        $result = $queryBuilder->execute();
        if(! $result instanceof Result){
            return [];
        }
        return $result->fetchAllAssociative();
    }

    private function findOldAssignCart(): array
    {
        $queryBuilder = $this->dbal->createQueryBuilder();

        $queryBuilder->select('c.id, c.created_at')
            ->from('cart','c')
            ->where('c.created_at <= DATE_ADD(Now(), INTERVAL - 6 MONTH)')
            ->andWhere('c.status = "ASSIGN"');
        $result = $queryBuilder->execute();
        if(! $result instanceof Result){
            return [];
        }
        return $result->fetchAllAssociative();
    }

    private function findOldRejectedCart():array {

        $queryBuilder = $this->dbal->createQueryBuilder();

        $queryBuilder->select('c.id, c.created_at')
            ->from('cart','c')
            ->where('c.created_at <= DATE_ADD(Now(), INTERVAL - 6 MONTH)')
            ->andWhere('c.status = "REJECTED"');

        $result = $queryBuilder->execute();
        if(! $result instanceof Result){
            return [];
        }

        return $result->fetchAllAssociative();
    }

    /**
     * @param array $nullableCart
     */
    private function cleanOldAssignedCart(array $nullableCart): void
    {
        $queryBuilder = $this->dbal->createQueryBuilder();
        $queryBuilder->update('cart_historic')->set('status', '"CANCELLED"')->where('id = ?')->setParameter(0, $nullableCart['id'])->execute();
    }

    /**
     * @param array $nullableCart
     */
    private function cleanOldCartRequest(array $nullableCart):void {
        $queryBuilder = $this->dbal->createQueryBuilder();

        $queryBuilder->update('cart')->set('status', '"CANCELLED"')->where('id = ?')->setParameter(0, $nullableCart['id'])->execute();
    }

    private function cleanOldRenameCartStatusRequest():void {
        $queryBuilder = $this->dbal->createQueryBuilder();

        $queryBuilder->update('cart')->set('status', '"CANCELLED"')->where('status = ?')->setParameter(0, "CANCELED")->execute();
    }

    private function cleanOldRenameCartStatusHistory():void {
        $queryBuilder = $this->dbal->createQueryBuilder();

        $queryBuilder->update('cart_historic')->set('status', '"CANCELLED"')->where('status = ?')->setParameter(0, "CANCELED")->execute();
    }

    /**
     * This function will clean user's Cart.
     *
     * @param array  $nullableCart
     * @param string $devise
     */
    private function cleanUserCart(array $nullableCart, string $devise):void {
        $queryBuilder = $this->dbal->createQueryBuilder();

        $queryBuilder->update('users')->set($devise, 'null')->where($devise.' = ?')->setParameter(0, $nullableCart['id'])->execute();
    }
}
