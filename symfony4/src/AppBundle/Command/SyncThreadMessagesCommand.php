<?php

namespace AppBundle\Command;

use AppBundle\Entity\ThreadMessage;
use AppBundle\Exception\TemplateException;
use DateTime;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Result;
use Doctrine\ORM\EntityManagerInterface;
use Open\IzbergBundle\Api\MessageApi;
use Open\IzbergBundle\Service\RedisService;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Open\TicketBundle\Services\TicketService;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class SyncThreadMessagesCommand extends Command implements LoggerAwareInterface
{
    private LoggerInterface $logger;
    private Connection $dbal;
    private EntityManagerInterface $entityManager;
    private MessageApi $messageApi;
    private TicketService $ticketService;
    private RedisService $cache;

    private const DATE_FORMAT = "d-m-Y G:i:s";

    public function __construct(
        EntityManagerInterface $entityManager,
        MessageApi $messageApi,
        Connection $dbal,
        TicketService $ticketService,
        RedisService $cache,
    ){
        $this->entityManager = $entityManager;
        $this->messageApi = $messageApi;
        $this->dbal = $dbal;
        $this->ticketService = $ticketService;
        $this->cache = $cache;

        parent::__construct();
    }


    protected function configure(): void
    {
        $this
            ->setName('open:messages:sync')
            ->setDescription('sync message from Izberg with local database')
            ->addOption(
                'months-back',
                null,
                InputOption::VALUE_OPTIONAL,
                'The number of months back to fetch messages from',
            );
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws TemplateException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $monthsBack = (int)$input->getOption('months-back');
        $batchSize = 50;
        $now = new DateTime();
        $output->writeln('<comment>Start : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');
        $this->cache->saveItem("start-sync-message-" . $now->format('d-m-Y'), [], 86400);
        $companies = $this->findCompany();
        $onlyList = "id,subject,from_display_name,to_display_name,sent_at,status,next_messages,sender";
        if ($monthsBack > 0) {
            $date = new \DateTime();
            $date->modify("-$monthsBack months");
            $onlyList .= '&sent_at__gt=' . $date->format('Y-m-d');
        }
        foreach ($companies as $company) {
            try {
                $izbergUserId = $company['izberg_user_id'];
                $messagesOutbox = $this->messageApi->getBuyerOutboxMessages($izbergUserId, $onlyList);
                $messagesInbox = $this->messageApi->getBuyerInboxMessages($izbergUserId, $onlyList);
                $izbMessages = array_merge($messagesOutbox, $messagesInbox);
                $izbergUnreadMessageIds = $this->ticketService->getUnreadMessageIds($izbergUserId);
                $arr = [];
                foreach ($izbMessages as $messageData) {
                    $modifiedDate = $messageData->sent_at;
                    if (!empty($messageData->next_messages)) {
                        $nextUrl = substr($messageData->next_messages[0], 0, -1);
                        $nextId = substr($nextUrl, strrpos($nextUrl, '/') + 1);
                        $next = $this->messageApi->getMessageById($nextId, 'sent_at');
                        $modifiedDate = $next->sent_at;
                    }
                    $arr[] = [
                        'izberg_user_id' => $izbergUserId,
                        'izberg_id' => $messageData->id,
                        'subject' => $messageData->subject,
                        'sender_resource_uri' => $messageData->sender->resource_uri,
                        'from_display_name' => $messageData->from_display_name,
                        'to_display_name' => $messageData->to_display_name,
                        'original_created_date' => $messageData->sent_at,
                        'last_modified_date' => $modifiedDate,
                        'has_unread_messages' => in_array($messageData->id, $izbergUnreadMessageIds) ? 1 : 0,
                    ];
                    if (count($arr) >= $batchSize) {
                        $this->saveMessages($arr);
                        $arr = [];
                    }
                }
                if (!empty($arr)) {
                    $this->saveMessages($arr);
                }
            } catch (\Throwable $throwable) {
                $this->logger->error("open:messages:sync Error",
                    LogUtil::buildContext([
                        "file" => $throwable->getFile(),
                        "line" => $throwable->getLine(),
                        "error" => $throwable->getMessage(),
                    ])
                );
            }
        }
        $this->cache->saveItem("done-sync-message-" . $now->format('d-m-Y'), [], 86400);

        $now = new DateTime();
        $output->writeln('<comment>End : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');

        return 0;
    }

    private function findCompany(): array
    {
        $queryBuilder = $this->dbal->createQueryBuilder();

        $queryBuilder->select('c.id, c.izberg_user_id')
            ->from('company','c')
            ->where('c.enabled = 1')
            ->andWhere('c.izberg_user_id IS NOT NULL');
        $result = $queryBuilder->executeQuery();
        if(! $result instanceof Result) {
            return [];
        }
        return $result->fetchAllAssociative();
    }

    private function saveMessages(array $messages)
    {
        foreach ($messages as $messageData) {
            $message = $this->entityManager->getRepository(ThreadMessage::class)->findOneByIzbergId($messageData['izberg_id']);
            if (!$message) {
                $message = new ThreadMessage();
                $message->setIzbergUserId($messageData['izberg_user_id']);
                $message->setIzbergId($messageData['izberg_id']);
                $message->setSubject($messageData['subject']);
                $message->setSenderResourceUri($messageData['sender_resource_uri']);
                $message->setFromDisplayName($messageData['from_display_name']);
                $message->setToDisplayName($messageData['to_display_name']);
                $message->setOriginalCreatedDate($messageData['original_created_date']);
            }
            $message->setLastModifiedDate($messageData['last_modified_date']);
            $message->setHasUnreadMessages($messageData['has_unread_messages']);

            $this->entityManager->persist($message);
        }
        $this->entityManager->flush();
        $this->entityManager->clear();
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
