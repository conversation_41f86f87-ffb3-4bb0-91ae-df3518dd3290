<?php

namespace AppBundle\Command;

use \InvalidArgumentException;
use Symfony\Component\HttpFoundation\File\Exception\FileException;

class CsvToArrayService
{
    /**
     * @param string $filename
     * @param string $delimiter
     * @param array  $csvHeader
     *
     * @return array
     */
    public function convertWithDelimiter(string $filename, string $delimiter, array $csvHeader = []):array
    {
        if (($csvFileHandle = $this->isValidFile($filename)) === false) {
            throw new FileException(sprintf('Cannot open %s file for csv import', $filename));
        }

        return $this->readCsvContent($csvFileHandle, $delimiter, $csvHeader);
    }


    /**
     * @param string $filename
     * @param int $nbColumnPerRow
     * @param array $csvHeader
     * @throws InvalidArgumentException
     * @throws FileException
     * @return array
     */
    public function convert(string $filename, int $nbColumnPerRow, array $csvHeader = []):array
    {
        // method params validation ($nbColumnPerRow and $csvHeader)
        if (count($csvHeader) > 0) {
            if ($nbColumnPerRow != count($csvHeader)) {
                throw new InvalidArgumentException('Incoherence in nbColumnPerRow and csvHeader given');
            }
        }
        // file existence and reading validation
        if (($csvFileHandle = $this->isValidFile($filename)) === false) {
            throw new FileException(sprintf('Cannot open %s file for csv import', $filename));
        }
        // read the file
        $headerRow = fgets($csvFileHandle);
        // guess the delimiter
        $delimiter = $this->guessDelimiter($headerRow, $nbColumnPerRow);

        return $this->readCsvContent($csvFileHandle, $delimiter, $csvHeader);
    }

    private function isHeaderValid(array $currentFileHeader, array $headerTemplate): bool
    {
        if (count($currentFileHeader) !== count($headerTemplate)) {
            return false;
        }
        return $currentFileHeader === $headerTemplate;
    }

    /**
     * @param  false|string   $headerRow
     * @param int             $nbColumnPerRow
     *
     * @return string
     */
    private function guessDelimiter($headerRow, int $nbColumnPerRow): string
    {
        if(!$headerRow){
            throw new FileException(sprintf('Invalid CSV file for specific prices import'));
        }
        $delimiter = array_reduce(
            [',', ';', "\t"],
            function (?string $defaultDelimiter, string $delimiter) use ($headerRow, $nbColumnPerRow) {
                return ($nbColumnPerRow === count(str_getcsv($headerRow, $delimiter))) ? $delimiter : $defaultDelimiter;
            }
        );

        if (!$delimiter) {
            throw new FileException(sprintf('Invalid CSV file for specific prices import'));
        }

        return $delimiter;
    }

    /**
     * @param string $filename
     *
     * @return false|resource
     */
    private function isValidFile(string $filename)
    {
        if (!file_exists($filename) || !is_readable($filename)) {
            return false;
        }

        if (($csvFileHandle = fopen($filename, 'r')) === false) {
            throw new FileException(sprintf('Cannot open %s file for csv import', $filename));
        }

        return $csvFileHandle;
    }

    /**
     * @param resource $csvFileHandle
     * @param string   $delimiter
     * @param array    $csvHeader
     *
     * @return array
     */
    private function readCsvContent($csvFileHandle, string $delimiter, array $csvHeader = [])
    {
        // read header and data
        rewind($csvFileHandle);

        $data = [];
        $header = null;

        while (($row = fgetcsv($csvFileHandle, 1000, $delimiter)) !== false) {
            if (!$header) {
                $header = json_decode(str_replace("\ufeff",'', json_encode($row)));
            } else {
                $data[] = array_combine($header, $row);
            }
        }
        fclose($csvFileHandle);

        // csv file header validation (if it's set in method params)
        if (count($csvHeader) > 0) {
            if (!$this->isHeaderValid($header, $csvHeader)) {
                throw new FileException(sprintf('Invalid CSV file for specific prices import'));
            }
        }

        return $data;
    }
}
