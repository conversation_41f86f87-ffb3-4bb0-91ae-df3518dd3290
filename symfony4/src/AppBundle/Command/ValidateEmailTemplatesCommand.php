<?php

namespace AppBundle\Command;

use App<PERSON><PERSON>le\Entity\Node;
use AppBundle\Entity\NodeContent;
use AppBundle\Exception\MailException;
use AppBundle\Exception\MailValidationException;
use AppBundle\Exception\TemplateException;
use AppBundle\Repository\NodeRepository;
use AppBundle\Services\EmailTemplateService;
use AppBundle\Services\MailService;
use DateTime;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class ValidateEmailTemplatesCommand extends Command
{
    private NodeRepository $nodeRepository;
    private MailService $mailService;
    private EmailTemplateService $emailTemplateService;

    private const DATE_FORMAT = "d-m-Y G:i:s";

    protected function configure():void
    {
        // Name and description for app/console command
        $this
            ->setName('open:check:emails')
            ->setDescription('Checks all the email templates')
            ->setHelp("This command allows you to check the validity of email templates");
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws TemplateException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        // Showing when the script is launched
        $now = new DateTime();
        $output->writeln('<comment>Start : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');

        $errors = false;
        $warning = false;

        //iterate over all email templates
        /** @var Node $node */
        foreach ($this->nodeRepository->findByType("email") as $node){
            $output->writeln('<comment>testing email template with slug '.$node->getSlug().'</comment>');

            //get base template
            $baseTemplate = $this->emailTemplateService->getTemplate($node->getSlug());
            if ($baseTemplate === null){
                $output->writeln("<error>email template with slug ".$node->getSlug()." doesn't exist in template list (see emails.yml) </error>");
                $errors = true;
            }
            else {
                //testing each content of the node
                /** @var NodeContent $content */
                foreach ($node->getContent() as $content) {
                    $currentLanguage = $content->getLang();
                    try {
                        $baseTemplate->setContent($content->getBody());
                        $baseTemplate->setSubject($content->getTitle());
                        $this->mailService->validateEmailTemplate($baseTemplate);
                    } catch (MailException $e) {
                        $output->writeln("<error>template [" . $baseTemplate->getTemplateName() . ", " . $currentLanguage . "] is invalid: " . preg_replace("/\"__string_template__.*\"/", "template", $e->getMessage()) . " </error>");
                        $errors = true;
                    } catch (MailValidationException $e) {
                        $output->writeln("<question>template [" . $baseTemplate->getTemplateName() . ", " . $currentLanguage . "] has incorrect formatted variables: ". $e->getMessage() ." </question>");
                        $warning = true;
                    }
                }
            }

        }

        $now = new DateTime();
        $output->writeln('<comment>End : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');

        //Reminder: On POSIX systems the standard exit code is 0 for success and any number from 1 to 255 for anything else
        if ($errors){
            return 2;
        }

        if ($warning) {
            return 1;
        }

        return 0;
    }
}
