<?php

namespace AppBundle\Command;

use AppBundle\Services\MailService;
use S<PERSON>fony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:test-async-email',
    description: 'Test async email sending functionality'
)]
class TestAsyncEmailCommand extends Command
{
    private MailService $mailService;

    public function __construct(MailService $mailService)
    {
        parent::__construct();
        $this->mailService = $mailService;
    }

    protected function configure(): void
    {
        $this
            ->addOption('email', 'e', InputOption::VALUE_REQUIRED, 'Email address to send test email to')
            ->addOption('sync', 's', InputOption::VALUE_NONE, 'Send email synchronously instead of async')
            ->addOption('high-priority', 'p', InputOption::VALUE_NONE, 'Use a high priority email template');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        
        $email = $input->getOption('email');
        if (!$email) {
            $io->error('Please provide an email address with --email option');
            return Command::FAILURE;
        }

        $async = !$input->getOption('sync');
        $highPriority = $input->getOption('high-priority');
        
        // Choose email template based on priority
        $emailTemplate = $highPriority ? 'USER_RESET_MDP' : 'SUPPORT_TECHNICAL_ERROR';
        $priorityText = $highPriority ? 'high' : 'low';
        $modeText = $async ? 'asynchronously' : 'synchronously';
        
        $io->info(sprintf('Sending %s priority email %s to: %s', $priorityText, $modeText, $email));

        try {
            $result = $this->mailService->sendEmailMessage(
                $emailTemplate,
                'en',
                $email,
                [
                    'firstName' => 'Test',
                    'lastName' => 'User',
                    'url' => 'https://example.com/test',
                    'subject' => 'Test Email',
                    'content' => 'This is a test email sent through the async queue system.',
                    'errorMessage' => 'Test error message for technical support'
                ],
                null,
                null,
                [],
                $async
            );

            if ($result) {
                if ($async) {
                    $io->success(sprintf('Email queued successfully for %s priority processing!', $priorityText));
                    $io->note('The email will be processed by the messenger worker.');
                    $io->note('Run: php bin/console messenger:consume async_priority_high async_priority_low');
                } else {
                    $io->success('Email sent synchronously!');
                }
            } else {
                $io->error('Failed to send/queue email');
                return Command::FAILURE;
            }

        } catch (\Exception $e) {
            $io->error('Error occurred: ' . $e->getMessage());
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }
}
