<?php

namespace AppBundle\Command;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Driver\Result;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class OpenBafvrequestCleanCommand extends Command
{
    private Connection $dbal;

    /**
     * OpenBafvrequestCleanCommand constructor.
     * @param Connection $dbal
     */
    public function __construct(Connection $dbal)
    {
        $this->dbal = $dbal;

        parent::__construct();
    }

    protected function configure():void
    {
        $this
            ->setName('open:bafvrequest:clean')
            ->setDescription('Clean bafv request table for multiple request')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $duplicateBafvRequests = $this->findDuplicateBafvRequests();

        foreach ($duplicateBafvRequests as $duplicateBafvRequest) {
            $this->cleanBafvRequest($duplicateBafvRequest);
        }

        $output->writeln('BAFV Request clean.');

        return 0;
    }

    private function findDuplicateBafvRequests():array {

        $queryBuilder = $this->dbal->createQueryBuilder();
        /** @psalm-suppress TooManyArguments */
        $queryBuilder = $queryBuilder->select('b.company_id', 'b.merchant_id', 'COUNT(b.id) AS bafvRequestCount')
            ->from('bafv_request', 'b')
            ->groupBy('b.company_id')
            ->addGroupBy('b.merchant_id')
            ->having('bafvRequestCount > 1');
        $result = $queryBuilder->execute();
        if(! $result instanceof Result){
            return [];
        }
        return $result->fetchAllAssociative();
    }

    private function cleanBafvRequest(array $bafvRequest):void {
        $queryBuilder = $this->dbal->createQueryBuilder();

        $queryBuilder = $queryBuilder->select('b.id')
                    ->from('bafv_request', 'b')
                    ->where('b.company_id = ?')
                    ->andWhere('b.merchant_id = ?')
                    ->orderBy('b.status', 'DESC')
                    ->setParameter(0, $bafvRequest['company_id'])
                    ->setParameter(1, $bafvRequest['merchant_id']);

        $result = $queryBuilder->execute();
        if(! $result instanceof Result){
            return ;
        }
        $duplicateBafvRequestByCompany = $result->fetchAllAssociative();

        array_shift($duplicateBafvRequestByCompany);
        array_map(function($bafvRequestId) {
            $this->deleteDuplicatedBafvRequest($bafvRequestId);
        },$duplicateBafvRequestByCompany);
    }

    private function deleteDuplicatedBafvRequest(array $bafvRequestId):void {
        $queryBuilder = $this->dbal->createQueryBuilder();

        $queryBuilder->delete('bafv_request')->where('id = ?')->setParameter(0, $bafvRequestId['id'])->execute();
    }

}
