<?php

namespace AppBundle\Command;

use Doctrine\Bundle\DoctrineBundle\Registry;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class GenerateMissingIDCommand extends Command
{
    private EntityManagerInterface $entityManager;
    private array $historizedEntities;

    /**
     * GenerateMissingIDCommand constructor.
     * @param EntityManagerInterface $entityManager
     * @param array $historizedEntities
     */
    public function __construct(EntityManagerInterface $entityManager, array $historizedEntities)
    {
        $this->entityManager = $entityManager;
        $this->historizedEntities = $historizedEntities;

        parent::__construct();
    }

    protected function configure():void
    {
        // Name and description for app/console command
        $this
            ->setName('open:ids:generate')
            ->setDescription('Generate missing IDS ')
            // the full command description shown when running the command with
            // the "--help" option
            ->setHelp("This command allows you to generate missing technical IDs on entities");
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $count = 0;
        foreach ($this->historizedEntities as $entityType){
           $entities = $this->entityManager->createQueryBuilder()->select("e")->from($entityType, "e")->getQuery()->getResult();
           foreach ($entities as $entity){
               if ($entity->getTechnicalId() === null){
                   $output->writeln('<comment>Generate ID for entityType'.$entityType.' with id '.$entity->getId().'</comment>');
                   $entity->setTechnicalId(md5(uniqid()));
                   $count++;
                   if ($count%1000 === 0){
                       $this->entityManager->flush();
                   }

               }
           }
        }
        $this->entityManager->flush();

        return 0;
    }
}
