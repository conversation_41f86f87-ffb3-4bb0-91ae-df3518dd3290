<?php

namespace AppBundle\Command;

use AppBundle\Entity\Country;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Helper\ProgressBar;

class ImportCountriesCommand extends Command
{
    private EntityManagerInterface $entityManager;
    private CsvToArrayService $csvToArrayService;

    private const DATE_FORMAT = "d-m-Y G:i:s";

    /**
     * ImportCountriesCommand constructor.
     * @param EntityManagerInterface $entityManager
     * @param CsvToArrayService $csvToArrayService
     */
    public function __construct(EntityManagerInterface $entityManager, CsvToArrayService $csvToArrayService)
    {
        $this->entityManager = $entityManager;
        $this->csvToArrayService = $csvToArrayService;

        parent::__construct();
    }


    protected function configure(): void
    {
        // Name and description for app/console command
        $this
            ->setName('open:import:countries')
            ->setDescription('Import countries from a CSV file')
            // the full command description shown when running the command with
            // the "--help" option
            ->setHelp("This command allows you to import countries  from a csv file")
            ->addArgument('filename', InputArgument::REQUIRED, 'The filename.');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        // Showing when the script is launched
        $now = new DateTime();
        $output->writeln('<comment>Start : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');

        // Importing CSV on DB via Doctrine ORM
        $this->import($input, $output);

        // Showing when the script is over
        $now = new DateTime();
        $output->writeln('<comment>End : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');

        return 0;
    }


    protected function import(InputInterface $input, OutputInterface $output): void
    {
        // Getting php array of data from CSV
        $data = $this->getData($input);

        // Turning off doctrine default logs queries for saving memory
        // turning off can be done by using the option --no-debug or --env prod when running the command

        // Define the size of record, the frequency for persisting the data and the current index of records
        $size = count($data);

        $batchSize = 1;
        $i = 1;

        // Starting progress
        $progress = new ProgressBar($output, $size);
        $progress->start();

        $countries = $this->entityManager->getRepository(Country::class)->findAll();

        $ids = [];
        $country_map = [];

        /** @var Country $country */
        foreach ($countries as $country) {
            $ids[] = $country->getId();
            $country_map[$country->getId()] = $country;
        }


        // Processing on each row of data
        foreach ($data as $row) {
            $progress->advance();

            if (!in_array(intval($row['id']), $ids)) {
                $country = new Country();
                $country->setEnabled(true);
                $country->setId(intval($row['id']));
                $country->setCode($row['code']);
                $country->setIzbFcaCountry($row['izb-fca-country']);
                $country->setBuyer(intval($row['buyer']) == 1);
                $country->setVendor(intval($row['vendor']) == 1);
                $country->setLocale($row['locale']);
                $country->setCompanyIdentRegex($row['regex']);
                $country->setIzbergCode($row['izberg-code']);
                $country->setIzbergId($row['izberg-id']);
                $country->setInEU($row['EU'] == 1);
                $country->setBusinessEverywhere($row['business-everywhere'] == 1);

                $country->setLegalNoticeProductExportEU($row['legal-product-EU-export']);
                $country->setLegalNoticeProductExportNonEU($row['legal-product-NON-EU-export']);
                $country->setLegalNoticeServiceExportEU($row['legal-service-EU-export']);
                $country->setLegalNoticeServiceExportNonEU($row['legal-service-NON-EU-export']);

                $this->entityManager->persist($country);

                // Each batchSize companies persisted we flush everything
                if (($i % $batchSize) === 0) {
                    $this->entityManager->flush();
                    $this->entityManager->clear();
                }
            } else {
                // update
                $id = intval($row['id']);
                $country = $country_map[$id];
                $country->setCode($row['code']);
                $country->setIzbFcaCountry($row['izb-fca-country']);
                $country->setBuyer(intval($row['buyer']) == 1);
                $country->setVendor(intval($row['vendor']) == 1);
                $country->setLocale($row['locale']);
                $country->setCompanyIdentRegex($row['regex']);
                $country->setIzbergCode($row['izberg-code']);
                $country->setIzbergId($row['izberg-id']);
                $country->setInEU($row['EU'] == 1);
                $country->setBusinessEverywhere($row['business-everywhere'] == 1);

                $country->setLegalNoticeProductExportEU($row['legal-product-EU-export']);
                $country->setLegalNoticeProductExportNonEU($row['legal-product-NON-EU-export']);
                $country->setLegalNoticeServiceExportEU($row['legal-service-EU-export']);
                $country->setLegalNoticeServiceExportNonEU($row['legal-service-NON-EU-export']);

                $this->entityManager->persist($country);
            }
            $i++;
        }

        // Flushing and clear data on queue
        $this->entityManager->flush();
        $this->entityManager->clear();

        // Ending the progress bar process
        $progress->finish();
    }

    protected function getData(InputInterface $input): array
    {
        $fileName = $input->getArgument('filename');

        // Getting the CSV from filesystem
        $fileName = 'import/' . $fileName;

        return $this->csvToArrayService->convertWithDelimiter($fileName, ';');
    }
}
