<?php

namespace AppBundle\Command;

use AppBundle\Entity\Category;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;


class ImportCompanyCategoriesCommand extends Command
{
    private EntityManagerInterface $entityManager;
    private CsvToArrayService $csvToArrayService;

    private const DATE_FORMAT = "d-m-Y G:i:s";

    /**
     * ImportCompanyCategoriesCommand constructor.
     * @param EntityManagerInterface $entityManager
     * @param CsvToArrayService $csvToArrayService
     */
    public function __construct(EntityManagerInterface $entityManager, CsvToArrayService $csvToArrayService)
    {
        $this->entityManager = $entityManager;
        $this->csvToArrayService = $csvToArrayService;

        parent::__construct();
    }


    protected function configure(): void
    {
        // Name and description for app/console command
        $this
            ->setName('open:import:categories')
            ->setDescription('Import company categories from a CSV file')
            // the full command description shown when running the command with
            // the "--help" option
            ->setHelp("This command allows you to import company categories from a csv file")
            ->addArgument('filename', InputArgument::REQUIRED, 'The filename.');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        // Showing when the script is launched
        $now = new DateTime();
        $output->writeln('<comment>Start : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');

        // Importing CSV on DB via Doctrine ORM
        $this->import($input, $output);

        // Showing when the script is over
        $now = new DateTime();
        $output->writeln('<comment>End : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');

        return 0;
    }

    protected function import(InputInterface $input, OutputInterface $output): void
    {
        // Getting php array of data from CSV
        $data = $this->getData($input);

        // Define the size of record, the frequency for persisting the data and the current index of records

        $categories = $this->entityManager->getRepository(Category::class)->findAll();
        foreach ($data as $row) {
            $output->writeln("Importing " . $row['label'] . ' id = ' . $row['id']);
            $found = false;
            /** @var Category $category */
            foreach ($categories as $category) {
                if (intval($row['id']) == $category->getId()) {
                    $found = true;
                    $category->setLabel(trim($row['label']));
                    $this->entityManager->persist($category);
                }
            }
            if (!$found) {
                // Create category
                $cat = new Category();
                $cat->setId(intval($row['id']));
                $cat->setLabel(trim($row['label']));

                $this->entityManager->persist($cat);
            }
        }

        // Flushing and clear data on queue
        $this->entityManager->flush();
        $this->entityManager->clear();
    }

    /**
     * @param InputInterface $input
     * @return array
     */
    private function getData(InputInterface $input): array
    {
        $fileName = $input->getArgument('filename');

        // Getting the CSV from filesystem
        $fileName = 'import/' . $fileName;

        return $this->csvToArrayService->convertWithDelimiter($fileName, ';');
    }
}
