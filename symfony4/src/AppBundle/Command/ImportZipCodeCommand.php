<?php

namespace AppBundle\Command;

use AppBundle\Entity\Country;
use AppBundle\Entity\ZipCode;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Helper\ProgressBar;

class ImportZipCodeCommand extends Command
{
    private EntityManagerInterface $entityManager;
    private CsvToArrayService $csvToArrayService;

    private const DATE_FORMAT = "d-m-Y G:i:s";

    /**
     * ImportCountriesCommand constructor.
     * @param EntityManagerInterface $entityManager
     * @param CsvToArrayService $csvToArrayService
     */
    public function __construct(EntityManagerInterface $entityManager, CsvToArrayService $csvToArrayService)
    {
        $this->entityManager = $entityManager;
        $this->csvToArrayService = $csvToArrayService;

        parent::__construct();
    }

    protected function configure():void
    {
        // Name and description for app/console command
        $this
            ->setName('open:import:zipcode')
            ->setDescription('Import zipCode from CSV file')
            // the full command description shown when running the command with
            // the "--help" option
            ->setHelp("This command allows you to import zipcode from csv file")
            ->addArgument('filename', InputArgument::REQUIRED, 'The filename.')
            ->addArgument('country', InputArgument::REQUIRED, 'The country.');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        // Showing when the script is launched
        $now = new DateTime();
        $output->writeln('<comment>Start : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');

        // Importing CSV on DB via Doctrine ORM
        $this->import($input, $output);

        // Showing when the script is over
        $now = new DateTime();
        $output->writeln('<comment>End : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');

        return 0;
    }

    protected function import(InputInterface $input, OutputInterface $output):void
    {
        // Getting php array of data from CSV
        $data = $this->get($input);
        $country_code = $input->getArgument('country');

        // Turning off doctrine default logs queries for saving memory
        /**
         * @psalm-suppress InternalMethod
         */
        $this->entityManager->getConnection()->getConfiguration()->setSQLLogger(null);

        /** @var Country $country */
        $country = $this->entityManager->getRepository(Country::class)->findOneBy(array("code"=>$country_code));

        // Define the size of record, the frequency for persisting the data and the current index of records
        $size = count($data);
        if ($size > 0) {
            $output->writeln("Deleting zipCode...");
            $this->entityManager->persist($country);
            $this->entityManager->flush();
            $output->writeln("ZipCode deleted");
        }
        $batchSize = 20;
        $i = 1;

        // Starting progress
        $progress = new ProgressBar($output, $size);
        $progress->start();



        // Processing on each row of data
        foreach ($data as $row) {

            //insee_code city zipcode gps

            $zipcode = new ZipCode();

            if (array_key_exists('insee_code', $row)) {
                $zipcode->setInseeCode($row['insee_code']);
            }
            $zipcode->setCity($row['city']);
            $zipcode->setZipCode($row['zipcode']);
            if (array_key_exists('gps', $row)) {
                $zipcode->setGps($row['gps']);
            }
            $zipcode->setLabel($row['city']." (".$row['zipcode'].")");

            /** @var Country|null $country */
            $zipcode->setCountry($country);
            $this->entityManager->persist($zipcode);

            // Each 20 users persisted we flush everything
            if (($i % $batchSize) === 0) {

                $this->entityManager->flush();
                $this->entityManager->clear();


                // Advancing for progress display on console
                $progress->advance($batchSize);

                $now = new DateTime();
                $output->writeln(' of zipCode imported ... | ' . $now->format(self::DATE_FORMAT));

            }

            $i++;

        }
        // Flushing and clear data on queue
        $this->entityManager->flush();
        $this->entityManager->clear();

        // Ending the progress bar process
        $progress->finish();
    }

    protected function get(InputInterface $input):array
    {

        $fileName = $input->getArgument('filename');

        // Getting the CSV from filesystem
        $fileName = 'import/' . $fileName;

        // Using service for converting CSV to PHP Array
        return $this->csvToArrayService->convertWithDelimiter($fileName, ';');
    }
}
