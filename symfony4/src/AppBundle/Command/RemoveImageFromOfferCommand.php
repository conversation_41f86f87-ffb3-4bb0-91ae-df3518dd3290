<?php

namespace AppBundle\Command;

use AppBundle\Services\OfferImageService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class RemoveImageFromOfferCommand extends Command
{
    private const DATE_FORMAT = "d-m-Y G:i:s";

    private CsvToArrayService $csvToArrayService;
    private OfferImageService $offerImageService;

    public function __construct(CsvToArrayService $csvToArrayService, OfferImageService $offerImageService)
    {
        $this->csvToArrayService = $csvToArrayService;
        $this->offerImageService = $offerImageService;

        parent::__construct();
    }

    protected function configure()
    {
        // Name and description for app/console command
        $this
            ->setName('open:offer-image:unassign')
            ->setDescription('Massively unassign images')
            ->addArgument('csvFile', InputArgument::REQUIRED, 'The csv file.');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        // Showing when the script is launched
        $now = new \DateTime();
        $output->writeln('<comment>Start : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');

        $csvFile = $input->getArgument('csvFile');
        $csvRows = $this->csvToArrayService->convertWithDelimiter($csvFile, ';');

        // Define the size of record, the frequency for persisting the csvRows and the current index of records
        $size = count($csvRows);

        // Starting progress
        $progress = new ProgressBar($output, $size);
        $progress->start();

        // Processing on each row of csvRows
        foreach ($csvRows as $row) {
            $offerId = $row['Offer ID'];
            $this->offerImageService->removeImageFromOffer($offerId);
        }

        // Ending the progress bar process
        $progress->finish();

        // Showing when the script is over
        $now = new \DateTime();
        $output->writeln('<comment>End : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');

        return 0;
    }
}
