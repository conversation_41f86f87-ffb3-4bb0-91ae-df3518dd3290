<?php

namespace AppBundle\Command;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Open\IzbergBundle\Api\MerchantApi;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class OpenMerchantsSyncCommand extends Command
{
    use UtilCommandTrait;

    /**
     * @var MerchantApi
     */
    private MerchantApi $merchantApi;

    /**
     * @var array * default query params for izberg request api
     */
    private array $params = [
        'status' => 10,
        'only' => 'name',
        'limit' => 100
    ];

    /**
     * @var array * merchants list can update on bdd
     */
    private array $merchants = [];

    /**
     * @var int * merchants count can update on bdd
     */
    private int $merchantsCount;

    /**
     * @var Connection
     */
    private Connection $dbal;

    /**
     * @var SymfonyStyle
     */
    private SymfonyStyle $io;

    /**
     * OpenBafvrequestCleanCommand constructor.
     * @param MerchantApi $merchantApi
     * @param Connection $dbal
     */
    public function __construct(MerchantApi $merchantApi, Connection $dbal)
    {
        $this->merchantApi = $merchantApi;
        $this->dbal = $dbal;
        parent::__construct();
    }

    protected function configure()
    {
        $this
            ->setName('open:merchants:sync')
            ->setDescription('Sync users name and status from izberg');
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->startCommand($output);
        $this->io = new SymfonyStyle($input, $output);
        $this->io->section('Get merchants name when status is active(10) from izberg');
        $this->getIzbergMerchants();
        if($this->merchantsCount === count($this->merchants))
        {
            $this->io->success(sprintf('All Izberg active merchants are receive (%d)', $this->merchantsCount));
            $this->resetAllVendorStatus();
            $this->updateMerchantsStatus();
        } else {
            $this->io->error('All Izberg merchants not receive');
        }
        $this->endCommand($output);
        return 0;
    }

    private function getIzbergMerchants()
    {
        do {
            $merchantsApiResponse = $this->merchantApi->getMerchantsWithParams($this->params);
            $this->merchantsCount = $merchantsApiResponse->meta->total_count;
            $this->merchants = array_merge($this->merchants, $merchantsApiResponse->objects);
            $url = $merchantsApiResponse->meta->next;
            parse_str( parse_url( $url, PHP_URL_QUERY), $this->params );
        } while ($url !== null);
    }

    private function resetAllVendorStatus()
    {
        $this->io->section('Reset all merchants status on bdd');
        try {
            $queryBuilder = $this->dbal->createQueryBuilder();
            $update = $queryBuilder->update('merchant', 'm')
                ->set('m.izb_status','""');
            $update->execute();
            $this->io->success('All merchants status on bdd are reset');
        } catch (Exception $e) {
            $this->io->error('Reset all merchants status on bdd is on error');
            $this->io->error($e->getMessage());
        }
    }

    private function updateMerchantsStatus()
    {
        $missingMerchants = [];
        $this->io->section('Update merchants status and name on bdd from Izberg active merchants data');
        try {
            $queryBuilder = $this->dbal->createQueryBuilder();
            $this->io->progressStart($this->merchantsCount);
            foreach ($this->merchants as $merchant)
            {
                $update = $queryBuilder->update('merchant', 'm')
                    ->set('m.izb_status','"active"')
                    ->set('m.izb_merchant_name', $queryBuilder->createPositionalParameter($merchant->name))
                    ->where('m.izberg_id = ' . $merchant->id);
                if(!$update->execute()){
                    array_push($missingMerchants,'izb_id : ' . $merchant->id . ',name : ' . $merchant->name);
                }
                $this->io->progressAdvance(1);
            }
            $this->io->progressFinish();
            if (count($missingMerchants) === 0) {
                $this->io->success('All merchants are updated');
            } else {
                $this->io->error(sprintf('Missing merchants %d on bdd', count($missingMerchants)));
                $this->io->text(implode(" | ", $missingMerchants));
            }
        } catch (Exception $e) {
            $this->io->error('Update merchants on bdd is on error');
            $this->io->error($e->getMessage());
        }
    }
}
