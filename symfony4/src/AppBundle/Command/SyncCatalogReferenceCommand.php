<?php

namespace AppBundle\Command;

use Algolia\AlgoliaSearch\Exceptions\AlgoliaException;
use AppBundle\Services\CatalogReferenceService;
use DateTimeImmutable;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use const DATE_ISO8601;

class SyncCatalogReferenceCommand extends Command
{
    private CatalogReferenceService $catalogReferenceService;

    /**
     * SyncCatalogReferenceCommand constructor.
     * @param CatalogReferenceService $catalogReferenceService
     */
    public function __construct(CatalogReferenceService $catalogReferenceService)
    {
        $this->catalogReferenceService = $catalogReferenceService;

        parent::__construct();
    }


    protected function configure():void
    {
        $this
            ->setName('open:catalog-reference:sync')
            ->setAliases(['open:manufacturer-reference:sync'])
            ->setDescription('fetch all manufacturer references from izberg and store them in redis');
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws AlgoliaException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln(sprintf('<comment>Start : %s ---</comment>', (new DateTimeImmutable())->format(DATE_ISO8601)));

        $this->catalogReferenceService->syncCatalogReference();

        $output->writeln(sprintf('<comment>End : %s ---</comment>',(new DateTimeImmutable())->format(DATE_ISO8601)));

        return 0;
    }
}
