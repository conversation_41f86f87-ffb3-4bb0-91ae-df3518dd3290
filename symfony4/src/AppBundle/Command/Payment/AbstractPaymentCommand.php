<?php

namespace AppBundle\Command\Payment;

use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

abstract class  AbstractPaymentCommand extends Command implements LoggerAwareInterface
{
    protected const COMMAND_EVENT = "COMMAND_EVENT";
    protected const COMMAND_NAME = "COMMAND_NAME";

    protected const LOG_EVENT_START = "COMMAND_START";
    protected const LOG_EVENT_END = "COMMAND_END";

    protected const DATE_FORMAT = "d-m-Y G:i:s";

    protected LoggerInterface $logger;

    /**
     * this method needs to be defined in the subclasses
     * @param InputInterface $input
     */
    public abstract function runCommand (InputInterface $input): void;

    /**
     * all stuff that need to be done when a payment command starts
     */
    protected function start():void
    {
        $name = $this->getName();
        if(!$name){
            return;
        }
        $this->logger->info("Command ".$name. " started",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME=>EventNameEnum::PAYMENT_COMMAND,
                self::COMMAND_EVENT => self::LOG_EVENT_START,
                self::COMMAND_NAME => $this->getName()
            ])
        );

    }

    /**
     * all stuff that need to be done when a payment command ends
     */
    protected function end():void
    {
        $name = $this->getName();
        if(!$name){
            return;
        }
        $this->logger->info("Command ".$name. " ended",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME=>EventNameEnum::PAYMENT_COMMAND,
                self::COMMAND_EVENT => self::LOG_EVENT_END,
                self::COMMAND_NAME => $this->getName()
            ])
        );
    }

    /**
     * run the command
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->start();

        $this->runCommand($input);

        $this->end();

        return 0;
    }

    /**
     * write a simple log with info level
     * @param string $message
     * @param array $context
     */
    protected function writeSimpleInfoLog(string $message, array $context = []):void{
        try {
            $data = array_merge($context, array(
                LogUtil::EVENT_NAME=>EventNameEnum::PAYMENT_COMMAND,
                self::COMMAND_NAME => $this->getName()));

            $this->logger->info($message,
                LogUtil::buildContext($data)
                );
        }catch(\Exception $e){
            //do nothing.... We don't want to throw exception on a log method
        }
    }


    /**
     * write a simple log with error level
     * @param string $message
     * @param array $context
     */
    protected function writeSimpleErrorLog(string $message, array $context = []):void{
        try {
            $data = array_merge($context, array(
                LogUtil::EVENT_NAME=>EventNameEnum::PAYMENT_COMMAND,
                self::COMMAND_NAME => $this->getName()));

            $this->logger->error($message,
                LogUtil::buildContext($data)
            );
        }catch(\Exception $e){
            //do nothing.... We don't want to throw exception on a log method
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
