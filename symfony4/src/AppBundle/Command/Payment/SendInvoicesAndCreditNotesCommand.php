<?php

namespace AppBundle\Command\Payment;

/**
 * Command Name
 * -----------
 * open:payment:sendInvoicesAndCreditNotes
 *
 *
 * Description:
 * ------------
 * Preparing invoice of https://www.websequencediagrams.com/files/render?link=oFDX8P7c6-F4uiiEv_Dc
 * In prepaid transfer process, send invoice abd credit note to buyer
 *
 *
 * Frequency:
 * ----------
 * Every day at 02h am
 */

use AppBundle\Entity\Company;
use AppBundle\Entity\Order as OrderEntity;
use AppBundle\Entity\ShippingPoint;
use AppBundle\Entity\User;
use AppBundle\Exception\UnexpectedValueException;
use AppBundle\Services\CompanyService;
use AppBundle\Services\DownloadPdfService;
use AppBundle\Services\MailService;
use AppBundle\Services\OrderService;
use Exception;
use Open\IzbergBundle\Api\CreditNoteApi;
use Open\IzbergBundle\Api\InvoiceApi;
use Open\IzbergBundle\Api\IzbergUtils;
use Open\IzbergBundle\Model\Order;
use Open\IzbergBundle\Model\OrderDetails;
use stdClass;
use Swift_Attachment;
use Symfony\Component\Console\Input\InputInterface;

/***
 *
 * Class PPVIREMENTSendInvoicesAndCreditNotesCommand
 *
 * @package AppBundle\Command
 */
class SendInvoicesAndCreditNotesCommand extends AbstractPaymentCommand
{
    private InvoiceApi $invoiceApi;
    private MailService $mailService;
    private CompanyService $companyService;
    private OrderService $orderService;
    private CreditNoteApi $creditNoteApi;
    private DownloadPdfService $downloadPdfService;
    private string $fosuserFromEmail;
    private string $fosuserFromName;

    /**
     * SendInvoicesAndCreditNotesCommand constructor.
     * @param InvoiceApi $invoiceApi
     * @param MailService $mailService
     * @param CompanyService $companyService
     * @param OrderService $orderService
     * @param CreditNoteApi $creditNoteApi
     * @param DownloadPdfService $downloadPdfService
     * @param string $fosuserFromEmail
     * @param string $fosuserFromName
     */
    public function __construct(
        InvoiceApi $invoiceApi,
        MailService $mailService,
        CompanyService $companyService,
        OrderService $orderService,
        CreditNoteApi $creditNoteApi,
        DownloadPdfService $downloadPdfService,
        string $fosuserFromEmail,
        string $fosuserFromName
    )
    {
        $this->invoiceApi = $invoiceApi;
        $this->mailService = $mailService;
        $this->companyService = $companyService;
        $this->orderService = $orderService;
        $this->creditNoteApi = $creditNoteApi;
        $this->downloadPdfService = $downloadPdfService;
        $this->fosuserFromEmail = $fosuserFromEmail;
        $this->fosuserFromName = $fosuserFromName;

        parent::__construct();
    }


    protected function configure():void
    {
        // Name and description for app/console command
        $this->setName('open:payment:sendInvoicesAndCreditNotes')
            ->setDescription('Send invoices and credit notes to buyer')
            ->setHelp("This command send invoices and credit notes to buyer");
    }

    public function runCommand(InputInterface $input): void
    {
        // get all emitted invoices
        // send mail
        // log that action
        $invoices = $this->invoiceApi->getEmittedInvoice();

        $this->writeSimpleInfoLog("number of emitted invoices : " . count($invoices));

        foreach ($invoices as $i) {
            $invoice = null;
            try {
                $invoice = $this->invoiceApi->fetchInvoiceById($i->id);
            } catch (Exception $e) {
                //just log, we don't want to stop the command
                $this->writeSimpleErrorLog(
                    "Couldn't find invoice with id " . $i->id . ": " . $e->getMessage(),
                    [
                        'parameter' => json_encode($i),
                        'errorType' => get_class($e),
                    ]
                );
            }

            if (!$invoice) {
                continue;
            }

            try {
                $this->writeSimpleInfoLog("send new mail notif for invoice of id : " . $invoice->getId());
                $company = $this->companyService->findByIzbergUserId($invoice->getReceiver()->getId());

                if (!$company) {
                    throw new Exception(
                        sprintf(
                            'Cannot find company with izberg user id %s - invoice id %s',
                            $invoice->getReceiver()->getId(),
                            $invoice->getId()
                        )
                    );
                }

                //we also need to retrieve the order...
                $orderNumber = "";
                $orderCreatedOn = "";
                $orderId = null;
                if (!empty($invoice->getInvoiceLines())) {
                    $merchantOrderId = IzbergUtils::parseIzbergResourceAndGetId($invoice->getInvoiceLines()->first()->getOrderItem()->getMerchantOrder());
                    $merchantOrder = $this->orderService->fetchMerchantsOrderByOrderId($merchantOrderId);
                    if (!$merchantOrder) {
                        throw new UnexpectedValueException(sprintf('Cannot find merchant order with id %s', strval($merchantOrderId)));
                    }

                    $order = $merchantOrder->getOrder();

                    if($order instanceof Order || $order instanceof OrderDetails){
                        $orderNumber = $order->getIdNumber();
                        $orderId = $order->getId();
                    }
                    $orderCreatedOn = $merchantOrder->getCreatedOn()->format("d/m/Y");
                }

                /** @var OrderEntity $invoiceOrder */
                $invoiceOrder = $this->orderService->fetchOrderEntity($orderId);

                // Send email to all company's users
                /** @var User $user */
                foreach ($company->getUsers() as $user) {
                    if ($user->isEnabled()) {
                        $this->mailService->sendEmailMessage(
                            MailService::BUYER_SEND_INVOICE,
                            $user->getLocale(),
                            $user->getEmail(),
                            [
                                'url' => $this->mailService->generateUrl('front.invoice.pdf', ['invoiceId' => $invoice->getId()]),
                                'orderNumber' => $orderNumber,
                                'orderDate' => $orderCreatedOn,
                                'customerInvoiceNumber' => $invoice->getIdNumber(),
                                MailService::FIRST_NAME_VAR => $user->getFirstname(),
                                MailService::LAST_NAME_VAR => $user->getLastname(),
                                'currency' => $invoice->getCurrency(),
                                'amount' => $invoice->getTotalAmountWithTaxes()
                            ]
                        );
                    }
                }
                $this->invoiceApi->changeCustomerEmittedInvoiceToSend($invoice->getId());

                $invoiceSite = $invoiceOrder->getShippingPoint();
                $accountantEmail = $invoiceOrder->getShippingPoint()->getAccountantEmail();

                if ($invoiceSite instanceof ShippingPoint && $accountantEmail !== null) {
                    $language = $invoice->getLanguage() ?? 'en';

                    $mailParams = [
                        'invoiceNumber' => $invoice->getIdNumber(),
                        'orderNumber' => $orderNumber, // ou $orderId
                        'cartValidatorEmail' => $invoiceOrder->getUserEmail()
                    ];

                    $attachFiles = [];

                    $url = $this->invoiceApi->fetchCustomerInvoicePdf($invoice->getId());
                    if ($url) {
                        $filename = 'invoice_' . $invoice->getIdNumber() . '.pdf';

                        try {
                            $file = $this->downloadPdfService->downloadPdf($url, $filename);
                            $attachment = Swift_Attachment::fromPath($file, 'application/pdf');

                            $attachFiles[] = $attachment;
                        } catch (Exception $exception) {
                            $this->writeSimpleErrorLog(
                                sprintf(
                                    'Error when trying to download pdf invoice from invoice id %s %s %s',
                                    $invoice->getId(),
                                    $url,
                                    $exception->getMessage()
                                )
                            );
                        }
                    }

                    $this->mailService->sendEmailMessage(
                        MailService::EMITTED_INVOICE_TO_ACCOUNTING_DEPARTMENT,
                        $language,
                        $invoiceOrder->getAccountingEmail() ?? $accountantEmail,
                        $mailParams,
                        $this->fosuserFromEmail,
                        $this->fosuserFromName,
                        $attachFiles
                    );
                }

            } catch (Exception $e) {
                //just log, we don't want to stop the command
                $this->writeSimpleErrorLog("Error while sending notification for emitted invoices  " . $invoice->getId() . ": " . $e->getMessage(), [
                    "invoiceId" => $invoice->getId(),
                    "errorType" => get_class($e)
                ]);
            }
        }

        // get all emitted credit note
        // send mail
        // log that action
        $creditNotes = $this->creditNoteApi->getEmittedCreditNote();
        $this->writeSimpleInfoLog("number of emitted credit notes : " . count($creditNotes));

        /** @var stdClass $cn */
        foreach ($creditNotes as $cn) {
            try {

                /////////////////////////////////////////////////////////////////////////
                /// Fetching the invoice
                /////////////////////////////////////////////////////////////////////////

                /** @var stdClass $creditNote */
                $creditNote = $this->creditNoteApi->fetchCustomerCreditNote($cn->id);
                /** @var Company $company */
                $company = $this->companyService->findByIzbergUserId($creditNote->receiver->id);

                //fetch the invoice
                $invoice = $this->invoiceApi->fetchInvoiceById((int)IzbergUtils::parseIzbergResourceAndGetId($creditNote->invoice));

                //now try to get the order number from the invoice
                $orderNumber = "";
                $orderCreatedOn = null;
                if (!empty($invoice->getInvoiceLines())) {
                    $merchantOrderId = IzbergUtils::parseIzbergResourceAndGetId($invoice->getInvoiceLines()->first()->getOrderItem()->getMerchantOrder());
                    $merchantOrder = $this->orderService->fetchMerchantsOrderByOrderId($merchantOrderId);
                    if (!$merchantOrder) {
                        throw new UnexpectedValueException(sprintf('Cannot find merchant order with id %s', strval($merchantOrderId)));
                    }
                    $order = $merchantOrder->getOrder();
                    if($order instanceof Order){
                        $orderNumber = $order->getIdNumber();
                    }
                    $orderCreatedOn = $merchantOrder->getCreatedOn();
                }

                /////////////////////////////////////////////////////////////////////////
                /// Loading needing services
                /////////////////////////////////////////////////////////////////////////

                foreach ($company->getUsers() as $user) {
                    if ($user->isEnabled()) {
                        $this->mailService->sendEmailMessage(
                            MailService::BUYER_SEND_CREDIT_NOTE,
                            $user->getLocale(),
                            $user->getEmail(),
                            [
                                'url' => $this->mailService->generateUrl('front.credit_note.pdf', ['creditNoteId' => $cn->id]),
                                'orderNumber' => $orderNumber,
                                'orderDate' => $orderCreatedOn,
                                'customerInvoiceNumber' => $invoice->getIdNumber(),
                                MailService::FIRST_NAME_VAR => $user->getFirstname(),
                                MailService::LAST_NAME_VAR => $user->getLastname(),
                                'currency' => $creditNote->currency,
                                'amount' => $creditNote->total_amount_with_taxes
                            ]
                        );
                    }
                }

                /////////////////////////////////////////////////////////////////////////
                /// Update the status of the credit note
                /////////////////////////////////////////////////////////////////////////
                $this->creditNoteApi->changeCustomerEmittedCreditNoteToSend($cn->id);

            } catch (Exception $e) {
                //just log, we don't want to stop the command
                $this->writeSimpleErrorLog("Error while sending notification for emitted Credit Note  " . $cn->id . ": " . $e->getMessage(), [
                    "creditNoteId" => $cn->id,
                    "errorType" => get_class($e)
                ]);
            }

        }
    }
}
