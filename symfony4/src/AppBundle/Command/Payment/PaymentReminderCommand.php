<?php

namespace AppBundle\Command\Payment;

use AppBundle\Entity\Company;
use AppBundle\Entity\User;
use AppBundle\Services\CompanyService;
use AppBundle\Services\WebHelpIbanService;
use DateInterval;
use DateTime;
use DateTimeImmutable;
use Exception;
use Open\IzbergBundle\Api\CartApi;
use Open\IzbergBundle\Api\OrderApi;
use Open\IzbergBundle\Model\MerchantOrder;
use Symfony\Component\Console\Input\InputInterface;
use AppBundle\Services\MailService;


/**
 * Command Name
 * -----------
 * open:payment:pre_transfer:reminder
 *
 *
 * Description:
 * ------------
 * Step 4 of https://www.websequencediagrams.com/files/render?link=oFDX8P7c6-F4uiiEv_Dc
 * In pre payment process with bank transfer, send emails to remind users to pay if needed
 *
 * Frequency:
 * ----------
 * Every day at 08h00 AM
 */
class PaymentReminderCommand extends AbstractPaymentCommand
{
    private WebHelpIbanService $webHelpIbanService;
    private OrderApi $orderApi;
    private CartApi $cartApi;
    private CompanyService $companyService;
    private int $paymentNbDaysBeforeReminder;
    private MailService $mailService;
    private string $fosuserFromEmail;
    private string $fosuserFromName;

    /**
     * PaymentReminderCommand constructor.
     * @param WebHelpIbanService $webHelpIbanService
     * @param OrderApi $orderApi
     * @param CartApi $cartApi
     * @param CompanyService $companyService
     * @param int $paymentNbDaysBeforeReminder
     * @param MailService $mailService
     * @param string $fosuserFromEmail
     * @param string $fosuserFromName
     */
    public function __construct(
        WebHelpIbanService $webHelpIbanService,
        OrderApi $orderApi,
        CartApi $cartApi,
        CompanyService $companyService,
        int $paymentNbDaysBeforeReminder,
        MailService $mailService,
        string $fosuserFromEmail,
        string $fosuserFromName
    )
    {
        $this->webHelpIbanService = $webHelpIbanService;
        $this->orderApi = $orderApi;
        $this->cartApi = $cartApi;
        $this->companyService = $companyService;
        $this->paymentNbDaysBeforeReminder = $paymentNbDaysBeforeReminder;
        $this->mailService = $mailService;
        $this->fosuserFromEmail = $fosuserFromEmail;
        $this->fosuserFromName = $fosuserFromName;

        parent::__construct();
    }


    protected function configure():void
    {
        // Name and description for app/console command
        $this
            ->setName('open:pp_virement:reminder')
            ->setDescription('Send x days late pre payment bank transfer reminder mail notification (x is configured with parameters payment_nb_days_before_reminder in parameters.yml)')
            ->setHelp("This command sends x days late pre payment bank transfer reminder mail notification with instructions (x is configured with parameters payment_nb_days_before_reminder in parameters.yml)");
    }

    /**
     * @param InputInterface $input
     */
    public function runCommand(InputInterface $input): void
    {

        /////////////////////////////////////////////////////////////////////////
        /// Fetching the merchant orders with the initial status
        /////////////////////////////////////////////////////////////////////////
        $merchantOrders = $this->orderApi->getMerchantOrdersByStatus(OrderApi::MERCHANT_ORDER_STATUS_INITIAL);

        $this->writeSimpleInfoLog(" + number of orders to process: " . count($merchantOrders));
        $nbDays = $this->paymentNbDaysBeforeReminder;
        $this->writeSimpleInfoLog(" + payment_nb_days_before_reminder parameter value: " . $nbDays);


        /** @var MerchantOrder $merchantOrder */
        foreach ($merchantOrders as $merchantOrder) {
            $order = $merchantOrder->getOrder();
            try {
                //also fetch the cart id

                if ($order->getCartId() !== null) {
                    /** @var \stdClass $cart */
                    $cart = $this->cartApi->getCart($order->getCartId(), true);

                    $createdOn = new DateTimeImmutable($order->getCreatedOn());
                    $createdOn->add(DateInterval::createFromDateString($nbDays . " DAYS"));
                    $now = (new DateTime())->format('Ymd');

                    $this->writeSimpleInfoLog(" + testing merchant order ", [
                        "merchantOrderId" => $merchantOrder->getId(),
                        "createdOn" => $order->getCreatedOn(),
                        "paymentType" => $cart->selected_payment_type,
                        "reminderDate" => $createdOn,
                        "needNotification" => $now == $createdOn->format('Ymd') && $cart->selected_payment_type === "prepayment"
                    ]);

                    if ($now == $createdOn->format('Ymd') && $cart->selected_payment_type === "prepayment") {

                        $this->writeSimpleInfoLog("   -> sending reminder for merchant order: ", [
                            "merchantOrderId" => $merchantOrder->getId(),
                            "createdOn" => $order->getCreatedOn()
                        ]);

                        $attributes = $merchantOrder->getAttributes();
                        $reconciliationKey = "";
                        if (array_key_exists('reconciliation_key', $attributes)) {
                            $reconciliationKey = $attributes['reconciliation_key'];
                        }

                        $notifiedUsers = [];
                        /** @var Company|null $company */
                        $company = $this->companyService->findByIzbergUserId($merchantOrder->getUser()->getId());

                        if ($company !== null) {

                            /** @var User|null $user */
                            foreach ($company->getUsers() as $user) {
                                if ($user !== null && $user->isEnabled() && !in_array($user->getEmail(), $notifiedUsers)) {
                                    $notifiedUsers [] = $user->getEmail();
                                    $this->mailService->sendEmailMessage(
                                        MailService::BUYER_PRE_PAYMENT_TRANSFER_INITIAL,
                                        $user->getLocale(),
                                        $user->getEmail(),
                                        [
                                            MailService::FIRST_NAME_VAR => $user->getFirstname(),
                                            MailService::LAST_NAME_VAR => $user->getLastname(),
                                            'orderNumber' => $order->getIdNumber(),
                                            'iban' => $this->webHelpIbanService->getIbanFromCurrency($merchantOrder->getCurrency()->getCode()),
                                            'ibanAccountName' => $this->webHelpIbanService->getIbanAccountNameFromCurrency($merchantOrder->getCurrency()->getCode()),
                                            'reconciliationKey' => $reconciliationKey,
                                            'currency' => $merchantOrder->getCurrency()->getCode(),
                                            "amount" => $order->getAmountVatIncluded(),
                                        ],
                                        $this->fosuserFromEmail,
                                        $this->fosuserFromName
                                    );
                                }
                            }
                        } else {
                            $this->writeSimpleErrorLog(" * no company found with izbergUserId " . $merchantOrder->getUser()->getId(), [
                                "orderId" => $order->getId(),
                                "izbergUserId" => $merchantOrder->getUser()->getId()
                            ]);
                        }
                    }
                } else {
                    $this->writeSimpleErrorLog(" * order has no cart_id parameter ", [
                        "orderId" => $order->getId(),
                    ]);
                }
            } catch (Exception $e) {
                //just log, we don't want to stop the command
                $this->writeSimpleErrorLog(" * Error while sending payment reminder for merchant order " . $order->getId() . ": " . $e->getMessage(), [
                    "orderId" => $order->getId(),
                    "errorType" => get_class($e)
                ]);
            }
        }
    }
}
