<?php

namespace AppBundle\Command\Payment;

/**
 * Command Name
 * -----------
 * open:payment:prepareInvoice
 *
 * Description:
 * ------------
 * Step 3 of https://www.websequencediagrams.com/files/render?link=YDqKjcrRxyY2jjOf_kyF
 * In time payment process, create WPS Transaction for each izberg pending invoicing
 *
 *
 * Frequency:
 * ----------
 * Every day at 21h
 */

use AppBundle\Command\Payment\Exception\PaymentCommandException;
use AppBundle\Command\Payment\Utils\PaymentUtils;
use AppBundle\Entity\Company;
use AppBundle\Entity\User;
use AppBundle\Services\AlstomCustomAttributes;
use AppBundle\Services\CompanyService;
use AppBundle\Services\PaymentService;
use AppBundle\Services\WPSService;
use AppBundle\Util\DateUtil;
use DateTime;
use Exception;
use Open\IzbergBundle\Api\GatewayApi;
use Open\IzbergBundle\Api\InvoiceApi;
use Open\IzbergBundle\Api\MerchantApi;
use Open\IzbergBundle\Model\PaymentTerm;
use stdClass;
use Symfony\Component\Console\Input\InputInterface;

class FrontPrepareInvoiceCommand extends AbstractPaymentCommand
{
    private const INVOICE_ID = "invoiceId";
    private const DEFAULT_LOCALE = "en";

    private InvoiceApi $invoiceApi;
    private MerchantApi $merchantApi;
    private CompanyService $companyService;
    private WPSService $wpsService;
    private GatewayApi $gatewayApi;
    private PaymentService $paymentService;
    private AlstomCustomAttributes $customAttributes;

    /**
     * FrontPrepareInvoiceCommand constructor.
     *
     * @param InvoiceApi             $invoiceApi
     * @param MerchantApi            $merchantApi
     * @param CompanyService         $companyService
     * @param WPSService             $wpsService
     * @param GatewayApi             $gatewayApi
     * @param PaymentService         $paymentService
     * @param AlstomCustomAttributes $customAttributes
     */
    public function __construct(
        InvoiceApi $invoiceApi,
        MerchantApi $merchantApi,
        CompanyService $companyService,
        WPSService $wpsService,
        GatewayApi $gatewayApi,
        PaymentService $paymentService,
        AlstomCustomAttributes $customAttributes
    )
    {
        $this->invoiceApi = $invoiceApi;
        $this->merchantApi = $merchantApi;
        $this->companyService = $companyService;
        $this->wpsService = $wpsService;
        $this->gatewayApi = $gatewayApi;
        $this->paymentService = $paymentService;
        $this->customAttributes = $customAttributes;

        parent::__construct();
    }

    protected function configure():void
    {
        $this
            ->setName('open:payment:prepareInvoice')
            ->setDescription('Create WPS Transactions for each izberg pending invoicing')
            ->setHelp("This command creates WPS Transactions for each izberg pending invoicing");
    }

    /**
     * @param InputInterface $input
     */
    public function runCommand(InputInterface $input): void
    {
        /////////////////////////////////////////////////////////////////////////
        /// FETCHING PENDING INVOICES
        /////////////////////////////////////////////////////////////////////////

        $pendingInvoices = $this->invoiceApi->getPendingCustomerInvoices();

        $this->writeSimpleInfoLog("number of pending invoices to process: " . count($pendingInvoices));

        /////////////////////////////////////////////////////////////////////////
        /// ITERATE ON EACH PENDING INVOICE
        /////////////////////////////////////////////////////////////////////////
        foreach ($pendingInvoices as $pendingInvoice) {
            if (property_exists($pendingInvoice, "payment_type") && $pendingInvoice->payment_type === "term_payment") {

                $this->writeSimpleInfoLog("+ processing invoice: " . $pendingInvoice->id,
                    [
                        "paymentType" => $pendingInvoice->payment_type,
                        "invoiceId" => $pendingInvoice->id
                    ]);
                $invoice = $this->invoiceApi->fetchCustomerInvoice($pendingInvoice->id);
                try {
                    //fetch full invoice
                    /** @var stdClass $invoice */
                    $this->writeSimpleInfoLog(" -> fetching invoice with id " . $pendingInvoice->id . " from izberg",
                        [
                            "invoiceId" => $pendingInvoice->id
                        ]);
                    $this->validateInvoice($invoice);

                    //now fetch the merchant
                    $this->writeSimpleInfoLog(" -> fetching merchant with id " . $invoice->issuer->id . " from izberg",
                        [
                            "merchantId" => $invoice->issuer->id
                        ]);
                    $merchant = $this->merchantApi->getMerchant($invoice->issuer->id);
                    if(!$merchant instanceof stdClass){
                        throw new PaymentCommandException(" * no merchant found in db for invoice id " . $invoice->issuer->id);
                    }
                    $pspMerchantId = $this->merchantApi->getMerchantCustomAttribute($merchant->id, $this->customAttributes->getPspMerchantId());

                    if (!$pspMerchantId) {
                        $errorMessage = sprintf(
                            " * the merchant with id %s has no attribute %s => can't create transaction",
                            $merchant->id,
                            $this->customAttributes->getPspMerchantId()
                        );

                        $this->writeSimpleErrorLog($errorMessage, ["merchantId" => $merchant->id]);
                        throw new PaymentCommandException($errorMessage);
                    }

                    //search the company from our database
                    /** @var Company $company */
                    $this->writeSimpleInfoLog(" -> fetching company with izberg user id " . $invoice->receiver->id . " from db");

                    $company = $this->companyService->findByIzbergUserId($invoice->receiver->id);
                    if ($company !== null) {
                        $this->writeSimpleInfoLog("     -> company found local DB: " . $company->getId());
                    }

                    //check company and also create wps user if not exists
                    $this->validateCompany($company, $invoice->receiver->id);

                    /////////////////////////////////////////////////////////////////////////
                    //CREATE TRANSACTION
                    /////////////////////////////////////////////////////////////////////////

                    //get the payment term => the locale is not important here as we only want the number of days
                    $term = $this->paymentService->fetchPaymentTerm("en");
                    if(!$term instanceof PaymentTerm){
                        throw new PaymentCommandException(" * no payment term found");
                    }
                    $installments = [DateUtil::addDaysEndOfMonth(new DateTime(), $term->getDays())];

                    $this->writeSimpleInfoLog(" -> creating transaction");
                    /** @var Company $company */
                    $transaction = $this->wpsService->createRawTransaction(
                        $invoice->total_amount_with_taxes,
                        WPSService::CODE_QUALIFIER_INVOICE,
                        $invoice->id,
                        PaymentUtils::getCurrencyFromInvoice($invoice),
                        $company->getIdCustomerWPS(),
                        $this->getLocaleFromCompany($company),
                        $installments,
                        WPSService::WPS_PC_PE_VIREMENT,
                        1,
                        null
                    );

                    /////////////////////////////////////////////////////////////////////////
                    //CREATE SUB TRANSACTION
                    /////////////////////////////////////////////////////////////////////////
                    $this->writeSimpleInfoLog(" -> creating sub transaction");
                    $subTransact = $this->wpsService->createSubTransactions(
                        $invoice->total_amount_with_taxes,
                        WPSService::CODE_QUALIFIER_INVOICE . $invoice->id,
                        $transaction->getCodeTransactionWps(),
                        $pspMerchantId,
                        null
                    );

                    /////////////////////////////////////////////////////////////////////////
                    //CREATE PSP GATEWAY
                    /////////////////////////////////////////////////////////////////////////

                    $this->writeSimpleInfoLog(" -> creating izberg gateway with externalId = " . $subTransact->getCodeSubTransactionWps());
                    $this->gatewayApi->createGateway(
                        $subTransact->getCodeSubTransactionWps(),
                        'term_payment',
                        GatewayApi::QUALIFIER_INVOICE,
                        $invoice->id
                    );

                    /////////////////////////////////////////////////////////////////////////
                    //UPDATE INVOICE: external_status: ready, payment_details: reconciliationKey
                    /////////////////////////////////////////////////////////////////////////
                    $this->writeSimpleInfoLog(" -> set external status to ready for the invoice");
                    $this->invoiceApi->patchInvoice($invoice->id, [
                        "external_status" => "ready",
                        "payment_details" => $transaction->getReconciliationKey()
                    ]);

                } catch (Exception $e) {
                    //just log, we don't want to stop the command
                    $this->writeSimpleErrorLog(" * Unable to process invoice with id " . $invoice->id . ": " . $e->getMessage(), [
                        self::INVOICE_ID => $invoice->id,
                        "errorType" => get_class($e)
                    ]);

                }

            } else if (property_exists($pendingInvoice, "payment_type") && $pendingInvoice->payment_type === "prepayment") {
                $this->writeSimpleInfoLog("+ processing invoice: " . $pendingInvoice->id,
                    [
                        "paymentType" => $pendingInvoice->payment_type,
                        "invoiceId" => $pendingInvoice->id
                    ]);
                $this->invoiceApi->patchInvoice($pendingInvoice->id, ["external_status" => "ready"]);
            } else {
                $this->writeSimpleErrorLog(" -> ignoring invoice with id " . $pendingInvoice->id . " because it is not time payment");
            }
        }
    }

    /**
     * @param Company|null $company
     * @param string $izbergUserId
     * @throws PaymentCommandException
     */
    private function validateCompany(?Company $company, string $izbergUserId):void
    {
        if ($company === null) {
            $this->writeSimpleErrorLog(" * no company found in db for izberg user id " . $izbergUserId, [
                "izbergUserId" => $izbergUserId
            ]);
            throw new PaymentCommandException(" * no company found in db for izberg user id " . $izbergUserId);
        }
        if ($company->getIdCustomerWPS() === null) {
            $this->wpsService->createCustomerWPS($company);
        }
    }

    /**
     * @param stdClass $invoice
     * @throws PaymentCommandException
     */
    private function validateInvoice(stdClass $invoice):void
    {
        if (empty($invoice->invoice_lines)) {
            $this->writeSimpleErrorLog(" * Invoice " . $invoice->id . " has no line", [
                "invoiceId" => $invoice->id
            ]);
            throw new PaymentCommandException(" * Invoice " . $invoice->id . " has no line");
        }
    }

    /**
     * try to guess the locale for this company
     * @param Company $company
     * @return string
     */
    private function getLocaleFromCompany(Company $company)
    {
        if (!empty($company->getUsers())) {
            /** @var User $user */
            $user = $company->getUsers()[0];
            return $user->getLocale();
        }
        return self::DEFAULT_LOCALE;
    }
}
