<?php

namespace AppBundle\Command\Payment;

use AppBundle\Entity\Company;
use AppBundle\Entity\Order;
use AppBundle\Entity\Site;
use AppBundle\Entity\User;
use AppBundle\Exception\UnexpectedValueException;
use AppBundle\Services\MailService;
use AppBundle\Services\OrderService;
use AppBundle\Services\ProcessService;
use AppBundle\Services\SecurityService;
use Open\IzbergBundle\Api\GatewayApi;
use Open\IzbergBundle\Api\InvoiceApi;
use Open\IzbergBundle\Api\IzbergUtils;
use Open\IzbergBundle\Api\RefundApi;
use Open\IzbergBundle\Model\Invoice;
use Open\IzbergBundle\Model\Merchant;
use Open\IzbergBundle\Model\OrderItem;
use Open\IzbergBundle\Model\OrderMerchant;
use Open\IzbergBundle\Model\Refund;
use Open\IzbergBundle\Model\Order as IzbergOrder;
use Open\WebhelpBundle\Api\TransactionApi;
use Open\WebhelpBundle\ApiException;
use Open\IzbergBundle\Model\Resource;
use stdClass;
use Symfony\Component\Console\Input\InputInterface;

class CreateOrderRefundToWPSCommand extends AbstractPaymentCommand
{
    private RefundApi $refundApi;
    private GatewayApi $gatewayApi;
    private TransactionApi $transactionApi;
    private InvoiceApi $invoiceApi;
    private OrderService $orderService;
    private MailService $mailService;
    private SecurityService $securityService;
    private ProcessService $processService;

    /**
     * CreateOrderRefundToWPSCommand constructor.
     *
     * @param RefundApi       $refundApi
     * @param GatewayApi      $gatewayApi
     * @param TransactionApi  $transactionApi
     * @param InvoiceApi      $invoiceApi
     * @param OrderService    $orderService
     * @param MailService     $mailService
     * @param SecurityService $securityService
     * @param ProcessService  $processService
     */
    public function __construct(
        RefundApi $refundApi,
        GatewayApi $gatewayApi,
        TransactionApi $transactionApi,
        InvoiceApi $invoiceApi,
        OrderService $orderService,
        MailService $mailService,
        SecurityService $securityService,
        ProcessService $processService
    )
    {
        $this->refundApi = $refundApi;
        $this->gatewayApi = $gatewayApi;
        $this->transactionApi = $transactionApi;
        $this->invoiceApi = $invoiceApi;
        $this->orderService = $orderService;
        $this->mailService = $mailService;
        $this->securityService = $securityService;
        $this->processService = $processService;

        parent::__construct();
    }

    protected function configure():void
    {
        $this
            ->setName('open:payment:createOrderRefundToWPS')
            ->setDescription('Create refunds to WPS')
            ->setHelp("This command creates refunds to WPS");
    }

    /**
     * @param InputInterface $input
     */
    public function runCommand(InputInterface $input): void
    {
        //get all pending refund
        /** @var Refund $refund */
        foreach ($this->refundApi->findByFilters(["status" => "manual_process"]) as $refund){
            $this->processService->processBeginFor($refund);

            $this->writeSimpleInfoLog("processing refund with id ".$refund->getId(), [
               "refundId" => $refund->getId()
            ]);

            /** @var Resource|null $merchantOrderResource */
            $merchantOrderResource = $refund->getMerchantOrder();
            $invoiceId = IzbergUtils::parseIzbergResourceAndGetId($refund->getCustomerInvoice());

            /** @var stdClass $gateway */
            $gateway = null;
            $merchantOrderId = null;
            $wpsInvoiceId = null;
            if ($merchantOrderResource !== null) {
                $gateway = $this->gatewayApi->getGatewayByMerchantOrderId($merchantOrderResource->getId());
                $merchantOrderId = $merchantOrderResource->getId();
            }
            else if ($invoiceId){
                $gateway = $this->gatewayApi->getGatewayByCustomerInvoiceId($invoiceId);
                $invoice = $this->invoiceApi->fetchInvoiceById((int)$invoiceId);
                $wpsInvoiceId = $invoice->getIdNumber();
                $merchantOrderId = IzbergUtils::parseIzbergResourceAndGetId($invoice->getInvoiceLines()->first()->getOrderItem()->getMerchantOrder());
            }

            if ($gateway !== null){
                $codeRefund = $this->transactionApi->refund($refund->getAmountVatIncluded(), $gateway->external_id, $wpsInvoiceId);
                if($codeRefund === null){
                    throw new UnexpectedValueException(sprintf('Cannot find codeRefund'));
                }
                try {
                    //create gateway
                    $gatewayId = $this->gatewayApi->createGateway($codeRefund, GatewayApi::TYPE_REFUND, GatewayApi::QUALIFIER_REFUND, $refund->getId());
                    $this->writeSimpleInfoLog("izberg gateway created",
                        [
                           "id" => $gatewayId,
                           "externalId" =>  $codeRefund,
                            "gatewayType" => GatewayApi::TYPE_REFUND,
                            "gatewayQualifier" => GatewayApi::QUALIFIER_REFUND,
                            "value" => $refund->getId()
                        ]);

                    //set gateway as paid
                    $this->gatewayApi->payGateway($codeRefund);
                    $this->writeSimpleInfoLog("izberg gateway paid",
                    [
                        "externalId" =>  $codeRefund
                    ]);


                    //send email to notify buyer
                    if ($merchantOrderId !== null){
                        /** @var OrderMerchant|null $merchantOrder */
                        $merchantOrder = $this->orderService->fetchMerchantsOrderByOrderId($merchantOrderId);
                        if (!$merchantOrder) {
                            throw new UnexpectedValueException(sprintf('Cannot find merchant order with id %s', strval($merchantOrderId)));
                        }
                        $order = $merchantOrder->getOrder();
                        if(!$order instanceof IzbergOrder){
                            throw new UnexpectedValueException(sprintf('Cannot find order on merchant id %s', strval($merchantOrderId)));
                        }

                        $merchant = $merchantOrder->getMerchant();
                        if(!$merchant instanceof Merchant){
                            throw new UnexpectedValueException(sprintf('Cannot find merchant on merchant id %s', strval($merchantOrderId)));
                        }

                        /** @var Order|null $orderEntity */
                        $orderEntity = $this->orderService->fetchOrderEntity($order->getId());
                        if ($orderEntity !== null){
                            $this->writeSimpleInfoLog("start to notify users for refund", [
                               "orderIzbergId" => $orderEntity->getIzbergId(),
                               "orderEntityId" => $orderEntity->getId()
                            ]);

                            $usersToNotify = [];
                            $site = $orderEntity->getSite();
                            if(!$site instanceof Site){
                                throw new UnexpectedValueException(sprintf('Cannot find site on order Entity'));
                            }
                            $company = $orderEntity->getCompany();
                            if ($company instanceof Company) {
                                $usersToNotify = $this->securityService->getCompanyAdminUsers($company);
                            }

                            if ($orderEntity->getSite() !== null && $site->getUsers() !== null){
                                $usersToNotify = array_merge($site->getUsers()->toArray(), $usersToNotify);
                            }

                            /** @var User $user */
                            foreach ($usersToNotify as $user) {
                                if ($user->isEnabled()) {
                                    $this->mailService->sendEmailMessage(MailService::BUYER_REFUND_DONE, $user->getLocale(), $user->getEmail(),
                                        [
                                            MailService::FIRST_NAME_VAR => $user->getFirstname(),
                                            MailService::LAST_NAME_VAR => $user->getLastname(),
                                            "orderNumber" => $order->getIdNumber(),
                                            "amount" => $refund->getAmountVatIncluded(),
                                            "currency" => $orderEntity->getCurrency(),
                                            "memo" => $refund->getMemo(),
                                            "vendorName" => $merchant->getName(),
                                            "invoiceNumber" => $invoiceId,
                                            "products" => array_map(
                                                fn (OrderItem $item): array => [
                                                    'sellerReference' => $item->getSellerRef(),
                                                    'offerName' => $item->getName(),
                                                ],
                                                $merchantOrder->getItems()
                                            ),
                                        ]
                                    );
                                }
                            }
                        }
                        else{
                            $this->writeSimpleErrorLog("enable to notify buyer: order doesn't exist in Alstom db", [
                                "refundId" => $refund->getId(),
                                "orderId" => $order->getId()
                            ]);
                        }
                    }
                    else{
                        $this->writeSimpleErrorLog("unable to notify buyer: can't get merchant order identifier", [
                            "refundId" => $refund->getId(),
                        ]);
                    }

                }catch (ApiException $e){
                    $this->writeSimpleErrorLog("enable to create wps refund: an unknown error occurred while invoking wps api", [
                        "refundId" => $refund->getId(),
                        "amount" => $refund->getAmountVatIncluded(),
                        "wpsCodeTransactionId" => $gateway->external_id,
                        "message" => $e->getMessage()
                    ]);
                }catch(\Open\IzbergBundle\Api\ApiException $e){
                    $this->writeSimpleErrorLog("enable to create wps refund: an unknown error occurred while invoking izberg api", [
                        "refundId" => $refund->getId(),
                        "wpsCodeTransactionId" => $gateway->external_id,
                        "codeRefundWPS" => $codeRefund,
                        "message" => $e->getMessage()
                    ]);
                }
            } else {

                $this->writeSimpleErrorLog("unable to get gateway for this refund: skip it!", [
                   "refundId" => $refund->getId()
                ]);
            }

            $this->processService->processEndFor();
        }
    }
}
