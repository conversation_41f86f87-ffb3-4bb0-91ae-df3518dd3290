<?php

namespace AppBundle\Command\Payment;

/**
 * Command Name
 * -----------
 * open:payment:term:bankTransferReminderInvoice
 *
 *
 * Description:
 * ------------
 * Step 5 of https://www.websequencediagrams.com/files/render?link=YDqKjcrRxyY2jjOf_kyF
 * In time payment process, send reminder emails to buyer on the basis of the invoice object
 *
 *
 * Frequency:
 * ----------
 * Every day at 8h am
 */

use AppBundle\Command\Payment\Exception\PaymentCommandException;
use AppBundle\Services\InvoiceService;
use AppBundle\Services\MailService;
use DateInterval;
use DateTime;
use Exception;
use Open\IzbergBundle\Api\InvoiceApi;
use Open\IzbergBundle\Api\IzbergUtils;
use stdClass;
use Symfony\Component\Console\Input\InputInterface;

class BankTransferReminderCommand extends AbstractPaymentCommand
{
    private const INVOICE_ID = "invoiceId";

    private InvoiceApi $invoiceApi;
    private InvoiceService $invoiceService;
    private string $izbergDatePattern;
    private int $paymentNbDaysBeforeReminder;
    private array $paymentNbDaysBeforeLateReminder;

    /**
     * BankTransferReminderCommand constructor.
     * @param InvoiceApi $invoiceApi
     * @param InvoiceService $invoiceService
     * @param string $izbergDatePattern
     * @param int $paymentNbDaysBeforeReminder
     * @param array $paymentNbDaysBeforeLateReminder
     */
    public function __construct(InvoiceApi $invoiceApi, InvoiceService $invoiceService, string $izbergDatePattern, int $paymentNbDaysBeforeReminder, array $paymentNbDaysBeforeLateReminder)
    {
        $this->invoiceApi = $invoiceApi;
        $this->invoiceService = $invoiceService;
        $this->izbergDatePattern = $izbergDatePattern;
        $this->paymentNbDaysBeforeReminder = $paymentNbDaysBeforeReminder;
        $this->paymentNbDaysBeforeLateReminder = $paymentNbDaysBeforeLateReminder;

        parent::__construct();
    }


    protected function configure():void
    {
        $this
            ->setName('open:payment:term:bankTransferReminder')
            ->setDescription('send payment reminder email to buyer')
            ->setHelp("This command sends payment reminder email to buyer");
    }

    /**
     * @param InputInterface $input
     */
    public function runCommand(InputInterface $input): void
    {
        /////////////////////////////////////////////////////////////////////////
        /// Load all emitted invoices
        /////////////////////////////////////////////////////////////////////////
        $status = array(
            'status' => InvoiceApi::STATUS_EMITTED,
            'paymentStatus' => 'not_paid'
        );
        $invoices = $this->invoiceApi->findInvoicesByStatutes($status);
        $this->writeSimpleInfoLog("number of pending invoices to process: ".count($invoices));

        /////////////////////////////////////////////////////////////////////////
        /// ITERATE ON EACH EMITTED INVOICE
        /////////////////////////////////////////////////////////////////////////
        foreach ($invoices as $invoice){
            try {
                $this->writeSimpleInfoLog(" + processing invoice: " . $invoice->id);
                //only notify when remaining amount is not null

                if (floatval($invoice->remaining_amount) > 0.0) {

                    $template = null;

                    //first validate the invoice
                    $this->validateInvoice($invoice);
                    $nbDays = $this->paymentNbDaysBeforeReminder;
                    $nbLateDays = $this->paymentNbDaysBeforeLateReminder;

                    $reminderAfterDate = IzbergUtils::parseIzbergDate($invoice->due_on, $this->izbergDatePattern);
                    $reminderBeforeDate = IzbergUtils::parseIzbergDate($invoice->due_on, $this->izbergDatePattern);
                    $reminderLateDate = IzbergUtils::parseIzbergDate($invoice->due_on, $this->izbergDatePattern);

                    //also validate date
                    $this->validateDate($reminderAfterDate, $invoice->due_on, $invoice->id);
                    $this->validateDate($reminderBeforeDate, $invoice->due_on, $invoice->id);
                    $this->validateDate($reminderLateDate, $invoice->due_on, $invoice->id);

                    /** @var DateTime $reminderAfterDate*/
                    $reminderAfterDate->add(new DateInterval('P' . strval($nbDays) . 'D'));
                    /** @var DateTime $reminderBeforeDate*/
                    $reminderBeforeDate->modify("-" . strval($nbDays) . " day");
                    $today = new DateTime();

                    //log dates info
                    $this->writeSimpleInfoLog("     - invoice dates: ", [
                        "invoiceId" => $invoice->id,
                        "dueOn" => $invoice->due_on,
                        "reminderBeforeDate" => $reminderBeforeDate,
                        "reminderAfterDate" => $reminderAfterDate
                    ]);

                    //if reminder is needed
                    if (
                        $today->format('Y-m-d') === $reminderBeforeDate->format('Y-m-d')
                        || $today->format('Y-m-d') === $reminderAfterDate->format('Y-m-d')
                    ) {
                        $template = MailService::PAYMENT_TERMPAYMENT_BANK_TRANSFER_INSCTRUCTIONS_TO_BUYER;
                    }else {
                        foreach ($nbLateDays as $lateDay){
                            /** @var DateTime $reminderLateDate*/
                            $lateDate = clone $reminderLateDate;
                            $lateDate->add(new DateInterval('P' . strval($lateDay) . 'D'));
                            if($today->format('Y-m-d') === $lateDate->format('Y-m-d')){
                                $template = MailService::PAYMENT_TERMPAYMENT_BANK_TRANSFER_REMINDER_TO_BUYER;
                                break;
                            }
                        }
                    }

                    if($template != null){
                        $this->writeSimpleInfoLog("     - need to send reminder for invoice: " . $invoice->id);
                        $this->invoiceService->sendInvoiceReminderByInvoice($invoice, $template);

                    }else{
                        $this->writeSimpleInfoLog("     - don't need to send reminder for invoice: " . $invoice->id);
                    }
                } else {
                    $this->writeSimpleInfoLog("     - invoice already paid: " . $invoice->id,
                        [
                            "invoiceId" => $invoice->id
                        ]);
                }

            }catch (Exception $e){
                //if exception is not a PaymentCommandException, we log it
                if (!$e instanceof PaymentCommandException)
                $this->writeSimpleErrorLog("Term Payment reminder: Unexpected error while processing invoice". $invoice->id . ": ".$e->getMessage() , [
                    self::INVOICE_ID => $invoice->id,
                    "errorType" => get_class($e)
                ]);
            }
        }

    }





    /**
     * @param stdClass $invoice
     * @throws PaymentCommandException
     */
    private function validateInvoice(stdClass $invoice):void
    {
        if (!property_exists($invoice, "payment_details") || empty($invoice->payment_details)) {
            $this->writeSimpleErrorLog(" * Ignoring invoice because it is invalid: the attribute payment_details is not set", [
                self::INVOICE_ID => $invoice->id
            ]);
            throw new PaymentCommandException("ignoring invoice because it is invalid - " . $invoice->id . ": the attribute payment_details is not set");
        }
        if (!property_exists($invoice, "due_on") || empty($invoice->due_on)) {
            $this->writeSimpleErrorLog(" * Ignoring invoice because it is invalid: no due_on field is set", [
                self::INVOICE_ID => $invoice->id
            ]);
            throw new PaymentCommandException("Ignoring invoice because it is invalid ".$invoice->id.": no due_on field is set");
        }
    }

    /**
     * @param DateTime|null $date
     * @param string $originalDateString
     * @param string $invoiceId
     * @throws PaymentCommandException
     */
    private function validateDate(?DateTime $date, string $originalDateString, string $invoiceId):void {
        if (!$date){
            $this->writeSimpleErrorLog(" * Ignoring invoice because it is invalid: unable to parse date => ".$originalDateString, [
                self::INVOICE_ID => $invoiceId
            ]);
            throw new PaymentCommandException("Ignoring invoice with id: ".$invoiceId." unable to parse date => ".$originalDateString);
        }
    }


}
