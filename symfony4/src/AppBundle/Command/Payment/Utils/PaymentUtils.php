<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 22/06/2018
 * Time: 15:11
 */

namespace AppBundle\Command\Payment\Utils;


use AppBundle\Command\Payment\Exception\PaymentCommandException;

class PaymentUtils
{

    /**
     * @param \stdClass $invoice
     * @throws PaymentCommandException
     * @return string the currency for this invoice
     */
    public static function getCurrencyFromInvoice(\stdClass $invoice){
        foreach ($invoice->invoice_lines as $line){
            if (property_exists($line, "order_item") &&
                property_exists($line->order_item, 'currency') &&
                property_exists($line->order_item->currency, 'code')){
                return $line->order_item->currency->code;
            }
        }
        throw new PaymentCommandException("Enable to get currency for invoice with id ".$invoice->id);
    }

    /**
     * @param \stdClass $invoice
     * @throws PaymentCommandException
     * @return string the merchant order for this invoice
     */
    public static function getMerchantOrderFromInvoice(\stdClass $invoice){
        foreach ($invoice->invoice_lines as $line){
            if (property_exists($line, "order_item") &&
                property_exists($line->order_item, 'merchant_order')){
                return $line->order_item->merchant_order;
            }

        }
        throw new PaymentCommandException("Enable to get merchant Order from invoice with id ".$invoice->id);
    }




}
