<?php


namespace AppBundle\Command;

use Api\Domain\Invoice\Message\InvoicePayloadMessage;
use Api\Domain\Invoice\ValueObject\DocumentType;
use AppBundle\Services\CreditNoteService;
use AppBundle\Services\JobService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Api\Domain\Invoice\Mapper\CreditNoteToInvoiceMapper;

class SyncCreditNotesCommand  extends Command
{
    use UtilCommandTrait;

    protected CreditNoteService $creditNoteService;
    protected JobService $jobService;
    private MessageBusInterface $messageBus;

    public function __construct(
        CreditNoteService $creditNoteService,
        JobService $jobService,
        MessageBusInterface $messageBus
    ) {
        $this->creditNoteService = $creditNoteService;
        $this->jobService = $jobService;

        parent::__construct();
        $this->messageBus = $messageBus;
    }


    protected function configure():void
    {
        $this
            ->setName('open:credit_notes:sync')
            ->addOption('jobs', null, InputOption::VALUE_NONE)
            ->setDescription('fetch all credit notes from Izberg and sync them with local database');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->startCommand($output);
        $hasJobsOption = $input->getOption('jobs');

        $offset = 0;
        $limit = 10;

        // todo sync from last sync date like in invoices command

        $creditNoteResult = $this->creditNoteService->fetchIzbergCreditNotes($offset, $limit);
        while($creditNoteResult->hasCreditNotes()) {
            $output->writeln(sprintf('sync offset %d limit %d', $offset, $limit));

            if ($hasJobsOption) {
                $this->jobService->syncCreditNote($creditNoteResult);
            } else {
                $this->creditNoteService->sync($creditNoteResult);
            }
            $mapper = new CreditNoteToInvoiceMapper();
            foreach ($creditNoteResult->getObjects() as $creditNote) {
                $this->messageBus->dispatch(
                    new InvoicePayloadMessage($mapper($creditNote), DocumentType::CREDIT_NOTE)
                );
            }

            $offset += $limit;
            $creditNoteResult = $this->creditNoteService->fetchIzbergCreditNotes($offset, $limit);
        }

        $this->endCommand($output);

        return 0;
    }
}
