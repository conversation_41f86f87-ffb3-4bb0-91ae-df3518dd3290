<?php

namespace AppBundle\Command;

use AppBundle\Entity\Country;
use AppBundle\Entity\TvaGroup;
use AppBundle\Entity\TvaRate;
use AppBundle\Repository\CountryRepository;
use AppBundle\Repository\TvaGroupRepository;
use AppBundle\Repository\TvaRateRepository;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class ImportTVACommand extends Command
{
    private EntityManagerInterface $entityManager;
    private CsvToArrayService $csvToArrayService;
    private CountryRepository $countryRepository;
    private TvaGroupRepository $tvaGroupRepository;
    private TvaRateRepository $tvaRateRepository;

    private const DATE_FORMAT = "d-m-Y G:i:s";

    /**
     * ImportCountriesCommand constructor.
     * @param EntityManagerInterface $entityManager
     * @param CsvToArrayService $csvToArrayService
     * @param CountryRepository $countryRepository
     * @param TvaGroupRepository $tvaGroupRepository
     * @param TvaRateRepository $tvaRateRepository
     */
    public function __construct(
        EntityManagerInterface $entityManager,
        CsvToArrayService $csvToArrayService,
        CountryRepository $countryRepository,
        TvaGroupRepository $tvaGroupRepository,
        TvaRateRepository $tvaRateRepository
    )
    {
        $this->entityManager = $entityManager;
        $this->csvToArrayService = $csvToArrayService;
        $this->countryRepository = $countryRepository;
        $this->tvaGroupRepository = $tvaGroupRepository;
        $this->tvaRateRepository = $tvaRateRepository;

        parent::__construct();
    }

    protected function configure():void
    {
        // Name and description for app/console command
        $this
            ->setName('open:import:tva')
            ->setDescription('Import tva from a CSV file')
            // the full command description shown when running the command with
            // the "--help" option
            ->setHelp("This command allows you to import tva  from a csv file")
            ->addArgument('filename', InputArgument::REQUIRED, 'The filename.');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        // Showing when the script is launched
        $now = new DateTime();
        $output->writeln('<comment>Start : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');

        // Importing CSV on DB via Doctrine ORM
        $this->import($input, $output);

        // Showing when the script is over
        $now = new DateTime();
        $output->writeln('<comment>End : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');

        return 0;
    }

    protected function import(InputInterface $input, OutputInterface $output):void
    {
        // Getting php array of data from CSV
        $data = $this->getData($input);

        // Turning off doctrine default logs queries for saving memory
        // turning off can be done by using the option --no-debug when running the command

        // Define the size of record, the frequency for persisting the data and the current index of records
        $size = count($data);

        // Starting progress
        $progress = new ProgressBar($output, $size);
        $progress->start();

        $countries = $this->countryRepository->findAll();
        $groups = $this->tvaGroupRepository->findAll();
        $rates = $this->tvaRateRepository->findAll();

        $ids = [];
        $country_map = [];
        $rateIds = [];

        /** @var Country $country */
        foreach ($countries as $country) {
            $ids[] = $country->getId();
            $country_map[$country->getId()] = $country;
        }

        $group_map = [];

        /** @var TvaGroup $group */
        foreach ($groups as $group){
            $group_map[] = $group->getGroupName();
        }

        /** @var TvaRate $rate */
        foreach ($rates as $rate){
            $rateIds[] = $rate->getId();
        }


        // Processing on each row of data
        foreach ($data as $row) {

            $progress->advance();

            if (!empty(intval($row['id'])) && in_array(intval($row['id']), $ids)) {

                if(!in_array($row['group'], $group_map)){
                    $tvaGroup = new TvaGroup();
                    $country = $country_map[$row['id']];
                    $tvaGroup->setCountry($country);
                    $tvaGroup->setGroupName($row['group']);

                    $this->entityManager->persist($tvaGroup);
                    $this->entityManager->flush();

                    $tvaRate = new TvaRate();
                    $tvaRate->setId($row['rateId']);
                    $tvaRate->setGroup($tvaGroup);
                    $tvaRate->setRate(intval($row['rate']));
                    $tvaRate->setFromDate(new DateTime());

                    $this->entityManager->persist($tvaRate);
                    $this->entityManager->flush();
                }else{
                    /** @var TvaGroupRepository $tvaGroupRepository */
                    $tvaGroupRepository = $this->entityManager->getRepository(TvaGroup::class);
                    $tvaGroup = $tvaGroupRepository->findByGroupName($row['group']);
                    if(!in_array($row['rateId'], $rateIds)){
                        $tvaRate = new TvaRate();
                        $tvaRate->setId($row['rateId']);
                        $tvaRate->setGroup($tvaGroup);
                        $tvaRate->setRate(intval($row['rate']));
                        $tvaRate->setFromDate(new DateTime());

                        $this->entityManager->persist($tvaRate);
                        $this->entityManager->flush();
                    }
                }
            }

        }

        // Flushing and clear data on queue
        $this->entityManager->clear();

        // Ending the progress bar process
        $progress->finish();
    }

    protected function getData(InputInterface $input):array
    {
        $fileName = $input->getArgument('filename');

        // Getting the CSV from filesystem
        $fileName = 'import/' . $fileName;

        // Using service for converting CSV to PHP Array
        return $this->csvToArrayService->convertWithDelimiter($fileName, ';');
    }
}
