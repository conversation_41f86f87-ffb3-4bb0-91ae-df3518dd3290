<?php

namespace AppBundle\Command;

use AppBundle\Entity\User;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class CleanDataCommand extends Command
{
    private EntityManagerInterface $entityManager;

    /**
     * CleanDataCommand constructor.
     * @param EntityManagerInterface $entityManager
     */
    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;

        parent::__construct();
    }

    protected function configure():void
    {
        $this
            ->setName("open:build:clean")
            ->setDescription('clean data for a deployment')
            ->setHelp('clean data for a deployment');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln(sprintf('<comment>Start : %s ---</comment>', (new \DateTime())->format(\DATE_ISO8601)));

        if(!$this->entityManager instanceof EntityManager){
            return 2;
        }

        $users = $this->entityManager->getRepository(User::class)->findAll();

        /** @var User $user */
        foreach ($users as $user){
            $user->setCartEURId(null);
            $user->setCartUSDId(null);
            $user->setItemInCartEUR(0);
            $user->setItemInCartUSD(0);
        }
        $this->entityManager->flush();

        return 0;
    }

}
