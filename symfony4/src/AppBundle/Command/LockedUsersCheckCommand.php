<?php

namespace AppBundle\Command;

use AppBundle\Entity\User;
use AppBundle\Util\SettingsProvider;
use DateInterval;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Input\InputOption;

class LockedUsersCheckCommand extends Command implements LoggerAwareInterface
{
    private LoggerInterface $logger;
    private SettingsProvider $settingsProvider;
    private EntityManagerInterface $entityManagerInterface;

    private const DATE_FORMAT = 'm/d/Y H:i:s';

    /**
     * LockedUsersCheckCommand constructor.
     * @param SettingsProvider $settingsProvider
     * @param EntityManagerInterface $entityManagerInterface
     */
    public function __construct(SettingsProvider $settingsProvider, EntityManagerInterface $entityManagerInterface)
    {
        $this->settingsProvider = $settingsProvider;
        $this->entityManagerInterface = $entityManagerInterface;

        parent::__construct();
    }


    /**
     * {@inheritdoc}
     */
    protected function configure():void
    {
        $this
            ->setName('open:unlock:users')
            ->setDescription('Check for locked users and unlock them if needed')
            ->addOption('dry', null, InputOption::VALUE_NONE, 'Only output locked users that should be unlocked');
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $disabledUsers = $this->entityManagerInterface->getRepository(User::class)
            ->findBy(
                array(
                    'enabled' => 0
                )
            );

        $now = new DateTime('now');

        $unlock_timeout = $this->settingsProvider->get('security.login_banned_user_unlock_timeout');

        $unlockedUsers = false;

        /** @var User $disabled_user */
        foreach ($disabledUsers as $disabledUser) {

            if ($disabledUser->getDisabledAt() === null) {
                $disabledUser->setDisabledAt($now);
                $this->entityManagerInterface->flush();
            }

            /** @var DateInterval $diff */
            $diff = $now->diff($disabledUser->getDisabledAt());

            //get number of minutes
            $minutes = ($diff->days * 24 * 60) + ($diff->h * 60) + $diff->i;

            if ($disabledUser->getLoginAttempt() !== null &&
                $disabledUser->getLoginAttempt() > 0 &&
                $minutes >= $unlock_timeout) {

                if (!$input->getOption('dry')) {

                    //at least one user has been unlocked
                    $unlockedUsers = true;

                    //add log entry
                    $this->logger->info("Unlocking user",
                        LogUtil::buildContext([
                            LogUtil::EVENT_NAME=>EventNameEnum::SECURITY_EVENT,
                            LogUtil::USER_NAME=>$disabledUser->getUsername(),
                            "lockedSince" => $disabledUser->getDisabledAt()
                        ])
                    );

                    //write command output
                    $output->writeln(
                        sprintf(
                            '<comment>User [%s] has been unlocked on %s (was locked since %s)</comment>',
                            $disabledUser->getUsername(),
                            $now->format(self::DATE_FORMAT),
                            $disabledUser->getDisabledAt()->format(self::DATE_FORMAT)
                        )
                    );

                    $disabledUser->setDisabledAt(null);
                    $disabledUser->setEnabled(true);
                    $disabledUser->setLoginAttempt(0);
                    $this->entityManagerInterface->flush();

                } else {
                    $output->writeln(
                        sprintf(
                            'User [%s] can be unlocked',
                            $disabledUser->getUsername()
                        )
                    );
                }
            }
        }

        if (!$unlockedUsers) {
            $output->writeln('<info>Nothing to do</info>');
        }

        return 0;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
