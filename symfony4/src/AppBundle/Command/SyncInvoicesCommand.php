<?php

namespace AppBundle\Command;

use Api\Domain\Invoice\Message\InvoicePayloadMessage;
use Api\Domain\Invoice\ValueObject\DocumentType;
use AppBundle\Services\InvoiceService;
use AppBundle\Services\JobService;
use AppBundle\Services\StorageService;
use DateTimeImmutable;
use Exception;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Messenger\MessageBusInterface;

class SyncInvoicesCommand extends Command
{
    use UtilCommandTrait;

    private InvoiceService $invoiceService;
    private StorageService $storageService;
    private JobService $jobService;
    private MessageBusInterface $messageBus;

    public function __construct(
        InvoiceService $invoiceService,
        StorageService $storageService,
        JobService $jobService,
        MessageBusInterface $messageBus
    )
    {
        $this->invoiceService = $invoiceService;
        $this->storageService = $storageService;
        $this->jobService = $jobService;

        parent::__construct();
        $this->messageBus = $messageBus;
    }

    protected function configure():void
    {
        $this
            ->setName('open:invoices:sync')
            ->addOption('jobs', null, InputOption::VALUE_NONE)
            ->addOption('full', null, InputOption::VALUE_NONE)
            ->setDescription('fetch all invoices from Izberg and sync them with local database');
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws Exception
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->startCommand($output);
        $hasJobsOption = $input->getOption('jobs');
        $fullSync = $input->getOption('full');

        $offset = 0;
        $limit = 20;

        $today = new DateTimeImmutable('now', (new \DateTimeZone('UTC')));

        $lastSync = (!$fullSync) ? $this->storageService->get(InvoiceService::LAST_SYNC) : null;
        $lastSyncDate = ($lastSync !== null) ? new DateTimeImmutable($lastSync) : null;

        $invoiceResult = $this->invoiceService->fetchIzbergInvoices($offset, $limit, $lastSyncDate);

        while($invoiceResult->hasInvoices()) {
            $output->writeln(sprintf('sync offset %d limit %d', $offset, $limit));

            if ($hasJobsOption) {
               $this->jobService->syncInvoice($invoiceResult);
            } else {
                $this->invoiceService->sync($invoiceResult);
            }

            $invoiceResult = $this->invoiceService->fetchIzbergInvoices($offset, $limit, $lastSyncDate);

            foreach ($invoiceResult->getObjects() as $invoice) {
                if ($invoice->getStatus() === 'draft') {
                    continue;
                }
                $this->messageBus->dispatch(new InvoicePayloadMessage($invoice, DocumentType::INVOICE));
            }
            $offset += $limit;
        }

        $this->storageService->set(InvoiceService::LAST_SYNC, $today->format(\DateTimeInterface::ATOM));

        $this->endCommand($output);

        return 0;
    }
}
