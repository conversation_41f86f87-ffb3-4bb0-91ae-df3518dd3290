<?php

namespace AppBundle\Command;

use AppBundle\Entity\Node;
use AppBundle\Entity\NodeContent;
use AppBundle\Exception\TemplateException;
use AppBundle\Model\EmailTemplate;
use AppBundle\Repository\NodeRepository;
use AppBundle\Services\EmailTemplateService;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class ImportEmailTemplates extends Command
{
    private EntityManagerInterface $entityManager;
    private EmailTemplateService $emailTemplateService;

    private const DATE_FORMAT = "d-m-Y G:i:s";

    /**
     * ImportEmailTemplates constructor.
     * @param EntityManagerInterface $entityManager
     * @param EmailTemplateService $emailTemplateService
     */
    public function __construct(EntityManagerInterface $entityManager, EmailTemplateService $emailTemplateService)
    {
        $this->entityManager = $entityManager;
        $this->emailTemplateService = $emailTemplateService;

        parent::__construct();
    }


    protected function configure(): void
    {
        // Name and description for app/console command
        $this
            ->setName('open:import:emails')
            ->setDescription('Create default email templates')
            // the full command description shown when running the command with
            // the "--help" option
            ->addOption('force', 'f', InputOption::VALUE_NONE, 'Recreate all the email templates. !!BE AWARE!!: Will erase the existing email templates')
            ->addOption('force-with-lang', null, InputOption::VALUE_OPTIONAL, 'Force update for a specific language (en|fr|de|es|it|nl)')
            ->setHelp("This command allows you to create default email templates");
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws TemplateException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $force = true === $input->getOption('force');
        $forceLang = $input->getOption('force-with-lang');

        // Showing when the script is launched
        $now = new DateTime();
        $output->writeln('<comment>Start : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');

        /*
         * CREATE TEST EMAIL
         */
        /**
         * @var NodeRepository $nodeRepository
         */
        $nodeRepository = $this->entityManager->getRepository(Node::class);

        foreach ($this->emailTemplateService->fetchAllTemplateNames() as $templateName) {
            /** @var EmailTemplate $template */
            $template = $this->emailTemplateService->getTemplate($templateName);
            $output->writeln('<comment>Create notification with ID ' . $templateName . '...</comment>');

            $existingNotifications = $nodeRepository->findEmailBySlugAndLanguage($templateName, "fr");

            if ($force && count($existingNotifications) >= 1) {
                foreach ($existingNotifications as $existingNotification) {
                    $this->entityManager->remove($existingNotification);
                }

                $this->entityManager->flush();
                $existingNotifications = [];
            }

            if (!empty($existingNotifications) && !empty($forceLang) && in_array($forceLang, ["en", "fr", "de", "es", "it", "nl"])) {
                foreach ($existingNotifications as $node) {
                    $nodeContent = new NodeContent();
                    $nodeContent->setBody('<p>' . $template->getContent() . '</p>');
                    $nodeContent->setTitle($templateName . "_OBJECT_" . strtoupper($forceLang));
                    $nodeContent->setLang($forceLang);
                    $nodeContent->setNode($node);
                    $node->addContent($nodeContent);
                }
                $this->entityManager->flush();
                $output->writeln('<comment>   - Updated for ' . strtoupper($forceLang) . '</comment>');
            }


            /*
             * if notification don't exist, create if
             */
            if (empty($existingNotifications)) {
                $node = new Node();
                $node->setSlug($templateName);
                $node->setType("email");


                //create default 'fr' content
                $nodeContentFR = new NodeContent();
                $nodeContentFR->setBody('<p>' . $template->getContent() . '</p>');
                $nodeContentFR->setTitle($templateName . "_OBJECT_FR");
                $nodeContentFR->setLang("fr");
                $nodeContentFR->setNode($node);

                //create default 'en' content
                $nodeContentEN = new NodeContent();
                $nodeContentEN->setBody('<p>' . $template->getContent() . '</p>');
                $nodeContentEN->setTitle($templateName . "_OBJECT_EN");
                $nodeContentEN->setLang("en");
                $nodeContentEN->setNode($node);

                //create default 'es' content
                $nodeContentES = new NodeContent();
                $nodeContentES->setBody('<p>' . $template->getContent() . '</p>');
                $nodeContentES->setTitle($templateName . "_OBJECT_ES");
                $nodeContentES->setLang("es");
                $nodeContentES->setNode($node);

                $nodeContentDE = new NodeContent();
                $nodeContentDE->setBody('<p>' . $template->getContent() . '</p>');
                $nodeContentDE->setTitle($templateName . "_OBJECT_DE");
                $nodeContentDE->setLang("de");
                $nodeContentDE->setNode($node);

                $nodeContentIT = new NodeContent();
                $nodeContentIT->setBody('<p>' . $template->getContent() . '</p>');
                $nodeContentIT->setTitle($templateName . "_OBJECT_IT");
                $nodeContentIT->setLang("it");
                $nodeContentIT->setNode($node);

                $nodeContentNL = new NodeContent();
                $nodeContentNL->setBody('<p>' . $template->getContent() . '</p>');
                $nodeContentNL->setTitle($templateName . "_OBJECT_NL");
                $nodeContentNL->setLang("it");
                $nodeContentNL->setNode($node);

                $node->addContent($nodeContentEN);
                $node->addContent($nodeContentFR);
                $node->addContent($nodeContentES);
                $node->addContent($nodeContentDE);
                $node->addContent($nodeContentIT);
                $node->addContent($nodeContentNL);


                try {
                    $this->entityManager->persist($node);
                    $this->entityManager->flush();
                    $output->writeln('<comment>   - Done</comment>');
                } catch (Exception $e) {
                    $output->writeln('<error> - Error: enable to persist email with id ' . $templateName . ': ' . $e->getMessage() . '</error>');
                }

            } else {
                $output->writeln('<comment>   - Notification already exist, do nothing</comment>');
            }

        }

        $now = new DateTime();
        $output->writeln('<comment>End : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');

        return 0;
    }
}
