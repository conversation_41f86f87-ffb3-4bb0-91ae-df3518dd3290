<?php

namespace AppBundle\Command;

use Symfony\Component\Console\Output\OutputInterface;

trait UtilCommandTrait
{
    /**
     * @param OutputInterface $output
     */
    private function startCommand(OutputInterface $output):void
    {
        $output->writeln(sprintf('<comment>Start : %s ---</comment>', (new \DateTimeImmutable())->format(\DATE_ISO8601)));
    }

    /**
     * @param OutputInterface $output
     */
    private function endCommand(OutputInterface $output):void
    {
        $output->writeln(sprintf('<comment>End : %s ---</comment>',(new \DateTimeImmutable())->format(\DATE_ISO8601)));
    }
}
