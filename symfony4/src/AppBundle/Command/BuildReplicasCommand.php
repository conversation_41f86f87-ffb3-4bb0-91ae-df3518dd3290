<?php

namespace AppBundle\Command;

use Algolia\AlgoliaSearch\Exceptions\AlgoliaException;
use Algolia\AlgoliaSearch\SearchIndex;
use AppBundle\Services\AlstomCustomAttributes;
use AppBundle\Services\SearchService;
use DateTime;
use Open\IzbergBundle\Algolia\AlgoliaField;
use Open\IzbergBundle\Algolia\AlgoliaService;
use Open\IzbergBundle\Algolia\AlgoliaServiceDe;
use Open\IzbergBundle\Algolia\AlgoliaServiceEn;
use Open\IzbergBundle\Algolia\AlgoliaServiceEs;
use Open\IzbergBundle\Algolia\AlgoliaServiceFr;
use Open\IzbergBundle\Algolia\AlgoliaServiceIt;
use Open\IzbergBundle\Algolia\AlgoliaServiceNl;
use Open\IzbergBundle\Service\AttributeService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use const DATE_ISO8601;

class BuildReplicasCommand extends Command
{
    private array $attributes = [];
    private array $facets = [];
    private AlstomCustomAttributes $customAttributes;
    private AttributeService $facetAttributesService;
    private SearchService $searchService;
    private AlgoliaServiceFr $algoliaServiceFr;
    private AlgoliaServiceEn $algoliaServiceEn;
    private AlgoliaServiceIt $algoliaServiceIt;
    private AlgoliaServiceDe $algoliaServiceDe;
    private AlgoliaServiceEs $algoliaServiceEs;
    private AlgoliaServiceNl $algoliaServiceNl;

    /**
     * BuildReplicasCommand constructor.
     *
     * @param AlstomCustomAttributes $customAttributes
     * @param AttributeService $facetAttributesService
     * @param SearchService $searchService
     * @param AlgoliaServiceFr $algoliaServiceFr
     * @param AlgoliaServiceEn $algoliaServiceEn
     * @param AlgoliaServiceIt $algoliaServiceIt
     * @param AlgoliaServiceDe $algoliaServiceDe
     * @param AlgoliaServiceEs $algoliaServiceEs
     * @param AlgoliaServiceNl $algoliaServiceNl
     */
    public function __construct(
        AlstomCustomAttributes $customAttributes,
        AttributeService $facetAttributesService,
        SearchService $searchService,
        AlgoliaServiceFr $algoliaServiceFr,
        AlgoliaServiceEn $algoliaServiceEn,
        AlgoliaServiceIt $algoliaServiceIt,
        AlgoliaServiceDe $algoliaServiceDe,
        AlgoliaServiceEs $algoliaServiceEs,
        AlgoliaServiceNl $algoliaServiceNl
    )
    {
        $this->customAttributes = $customAttributes;
        $this->facetAttributesService = $facetAttributesService;
        $this->searchService = $searchService;
        $this->algoliaServiceFr = $algoliaServiceFr;
        $this->algoliaServiceEn = $algoliaServiceEn;
        $this->algoliaServiceIt = $algoliaServiceIt;
        $this->algoliaServiceDe = $algoliaServiceDe;
        $this->algoliaServiceEs = $algoliaServiceEs;
        $this->algoliaServiceNl = $algoliaServiceNl;

        parent::__construct();
    }

    protected function configure():void
    {
        $this
            ->setName('open:replicas:build')
            ->setDescription('Builds replicas ALGOLIA indexes used by the application')
            ->setHelp('This command builds replicas ALGOLIA indexes used by the application (no parameters)');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln(sprintf('<comment>Start : %s ---</comment>', (new DateTime())->format(DATE_ISO8601)));

        $this->initSearchableAttributes();

        $algoliaServices = [
            'en' => $this->algoliaServiceEn,
            'fr' => $this->algoliaServiceFr,
            'es' => $this->algoliaServiceEs,
            'de' => $this->algoliaServiceDe,
            'it' => $this->algoliaServiceIt,
            'nl' => $this->algoliaServiceNl,
        ];

        foreach($algoliaServices as $locale => $algoliaService) {
            $this->initFacets($locale);

            try {
                $this->buildReplicasForAlgoliaService($algoliaService, $output);
            } catch(AlgoliaException $algoliaException) {
                $output->writeln(sprintf('<error>%s</error>', $algoliaException->getMessage()));
            }
        }

        $output->writeln(sprintf('<comment>End : %s ---</comment>',(new DateTime())->format(DATE_ISO8601)));

        return 0;
    }

    /**
     * @param AlgoliaService $algoliaService
     * @param OutputInterface $output
     * @throws AlgoliaException
     */
    protected function buildReplicasForAlgoliaService(
        AlgoliaService $algoliaService,
        OutputInterface $output
    ):void
    {
        $output->writeln(sprintf('Retrieving %d attributes', count($this->facets)));
        $output->writeln(sprintf('Processing Base Index : [%s]', $algoliaService->getBaseIndexName()));

        $replicaNames = $algoliaService->getReplicaNames();

        foreach ($replicaNames as $replicaName) {
            $output->writeln(sprintf('Adding [%s]', $algoliaService->getShortName($replicaName)));
            $algoliaService->addReplica($replicaName);
            $replicaIndex = $algoliaService->getWriteIndex($replicaName);

            $this->setReplicaRanking($replicaIndex, $algoliaService->getShortName($replicaName));
            $this->setReplicaCustomRanking($replicaIndex);
            $this->setReplicaSeparatorToIndex($replicaIndex);
            $this->setReplicaAttributeToIndex($replicaIndex);
            $this->setReplicaAttributesForFaceting($replicaIndex);
            $this->setReplicaTypoToleranceAttributeToIgnore($replicaIndex);
        }

        $output->writeln('Done');
    }

    private function setReplicaRanking(SearchIndex $replicaIndex, string $replicaShortName):void
    {
        /**
         * @psalm-suppress DeprecatedConstant
         */
        $sortRanking = [
            AlgoliaService::RANKING_BY_RELEVANCE => ['desc(default_image)'],
            AlgoliaService::RANKING_BY_PRICE_ASC => ['asc(price)'],
            AlgoliaService::RANKING_BY_PRICE_DESC => ['desc(price)'],
            AlgoliaService::RANKING_BY_NEWEST => ['desc(last_modified)'],
            AlgoliaService::RANKING_BY_DELIVERY_TIME_ASC => [sprintf('asc(attributes.%s)', $this->customAttributes->getTotalDelayForCustomer())],
        ];

        $customRanking = [];

        $ranking = array_merge(
            (isset($sortRanking[$replicaShortName])) ? $sortRanking[$replicaShortName] : [],
            ['typo', 'words', 'proximity', 'attribute', 'exact'],
            (isset($customRanking[$replicaShortName])) ? $customRanking[$replicaShortName] : []
        );

        AlgoliaService::updateIndexSettings($replicaIndex, ['ranking' => $ranking]);
    }

    /**
     * @param SearchIndex $replicaIndex
     */
    private function setReplicaCustomRanking(SearchIndex $replicaIndex):void
    {
        AlgoliaService::updateIndexSettings($replicaIndex, ['customRanking' => []]);
    }

    private function setReplicaSeparatorToIndex(SearchIndex $replicaIndex): void
    {
        AlgoliaService::updateIndexSettings($replicaIndex, ['separatorsToIndex' => '&%+-_/\\']);
    }

    private function setReplicaAttributeToIndex(SearchIndex $replicaIndex):void
    {
        AlgoliaService::updateIndexSettings($replicaIndex, ['searchableAttributes' => $this->attributes]);
    }

    private function setReplicaTypoToleranceAttributeToIgnore(SearchIndex $replicaIndex): void
    {
        $attributesToBeIgnored = array_merge(
            [
                AlstomCustomAttributes::createFullAttributeName($this->customAttributes->getManufacturerReference()),
                AlstomCustomAttributes::createFullAttributeName($this->customAttributes->getVendorReference()),
            ],
            array_map(
                fn(string $compProductAttribute): string => AlstomCustomAttributes::createFullAttributeName($compProductAttribute),
                $this->customAttributes->getSupplierProductCompatibilities()
            )
        );

        AlgoliaService::updateIndexSettings($replicaIndex, ['disableTypoToleranceOnAttributes' => $attributesToBeIgnored]);
    }

    private function setReplicaAttributesForFaceting(SearchIndex $replicaIndex):void
    {
        AlgoliaService::updateIndexSettings($replicaIndex, ['attributesForFaceting' => $this->facets]);
    }

    private function initFacets(string $locale):void
    {
        $attributesForFaceting = array_merge(
            [
                AlstomCustomAttributes::createFullAttributeName($this->customAttributes->getCountryOfDelivery()),
                AlgoliaField::PRODUCT_CATEGORY,
                AlgoliaField::CURRENCY,
                AlstomCustomAttributes::createFullAttributeName($this->customAttributes->getIncoTerm()),
                AlstomCustomAttributes::createFullAttributeName($this->customAttributes->getMoq()),
                AlstomCustomAttributes::createFullAttributeName($this->customAttributes->getStockAvailability()),
                AlgoliaField::PRODUCT_MADE_IN,
                AlstomCustomAttributes::createFullAttributeName($this->customAttributes->getTotalDelayForCustomer()),
                AlstomCustomAttributes::createFullAttributeName($this->customAttributes->getRestrictedProductBoolean())
            ],
            $this->facetAttributesService->fetchTechnicalAttributes($locale)
        );

        $searchableAttributesForFaceting = [
            AlgoliaField::PRODUCT_MANUFACTURER,
            AlgoliaField::MERCHANT_NAME,
        ];

        $filterOnlyAttributesForFaceting = array_merge(
            [
                AlgoliaField::STOCK,
                AlgoliaField::STATUS,
                AlgoliaField::PRODUCT_STATUS,
                AlgoliaField::MERCHANT_STATUS,
                AlgoliaField::MERCHANT_ID,
                AlstomCustomAttributes::createFullAttributeName($this->customAttributes->getManufacturerReference()),
                AlstomCustomAttributes::createFullAttributeName($this->customAttributes->getVendorReference()),
            ],
            array_map(
                fn(string $compProductAttribute): string => AlstomCustomAttributes::createFullAttributeName($compProductAttribute),
                $this->customAttributes->getSupplierProductCompatibilities()
            )
        );

        $restrictedProductFieldsCount = $this->customAttributes->getRestrictedProductFieldsCount();
        for ($i=1; $i<=$restrictedProductFieldsCount; $i++) {
            $filterOnlyAttributesForFaceting = array_merge($filterOnlyAttributesForFaceting, [
                str_replace(':i:', strval($i) , AlstomCustomAttributes::createFullAttributeName($this->customAttributes->getRestrictedProductField()))
            ]);
        }

        $this->facets = array_merge(
            array_map(fn($facet): string => sprintf('searchable(%s)', $facet), $searchableAttributesForFaceting),
            array_map(fn($facet): string => sprintf('filterOnly(%s)', $facet), $filterOnlyAttributesForFaceting),
            $attributesForFaceting
        );
    }

    private function initSearchableAttributes():void
    {
        $this->attributes = $this->searchService->getSearchableAttributes();
    }
}
