<?php


namespace AppBundle\Command;


use Exception;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Open\BackBundle\Service\HealthMonitorService;

class SaveHealthMonitoringStatus extends Command
{
    private HealthMonitorService $healthMonitorService;

    /**
     * SaveHealthMonitoringStatus constructor.
     * @param HealthMonitorService $healthMonitorService
     */
    public function __construct(HealthMonitorService $healthMonitorService)
    {
        $this->healthMonitorService = $healthMonitorService;

        parent::__construct();
    }


    protected function configure(): void
    {
        // Name and description for app/console command
        $this
            ->setName('open:HealthMonitoring:check')
            ->setDescription('Check Health Monitoring Status')
            ->setHelp('This command allow you to check status about services like Redis, Algolia, Izberg, ...');
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @throws Exception
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {
            $this->healthMonitorService->saveStatus();
            $output->writeln('<comment>saveStatus   -> Done</comment>');
        }catch(Exception $e){
            $output->writeln('<error> - Error:  HealthMonitorService : '.$e->getMessage().'</error>');
            return 1;
        }

        return 0;
    }

}
