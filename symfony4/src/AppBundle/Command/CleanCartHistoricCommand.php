<?php

namespace AppBundle\Command;

use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class CleanCartHistoricCommand extends Command
{

    private EntityManagerInterface $entityManager;

    /**
     * CleanCartHistoricCommand constructor.
     * @param EntityManagerInterface $entityManager
     */
    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;

        parent::__construct();
    }

    protected function configure():void
    {
        $this
            ->setName("open:historic:clean")
            ->setDescription('clean cart historic')
            ->setHelp('clean data cart historic');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln(sprintf('<comment>Start : %s ---</comment>', (new \DateTime())->format(\DATE_ISO8601)));

        if(!$this->entityManager instanceof EntityManager){
            return 2;
        }

        $q = $this->entityManager->createQuery("Delete " . \AppBundle\Entity\CartHistoric::class . " c");
        $q->execute();

        $this->entityManager->flush();

        return 0;
    }
}
