<?php

namespace AppBundle\Command;

use DateTime;
use Open\IzbergBundle\Service\AttributeService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class UpdateOrderAttributesCommand extends Command
{
    private array $izbergOrderAttributes;
    private array $izbergMerchantAttributes;
    private AttributeService $attributeService;

    private const DATE_FORMAT = "d-m-Y G:i:s";

    /**
     * UpdateOrderAttributesCommand constructor.
     * @param array $izbergOrderAttributes
     * @param array $izbergMerchantAttributes
     * @param AttributeService $attributeService
     */
    public function __construct(array $izbergOrderAttributes, array $izbergMerchantAttributes, AttributeService $attributeService)
    {
        $this->izbergOrderAttributes = $izbergOrderAttributes;
        $this->izbergMerchantAttributes = $izbergMerchantAttributes;
        $this->attributeService = $attributeService;

        parent::__construct();
    }


    protected function configure():void
    {
        $this
            ->setName('open:update:orderAttribute')
            ->setDescription('Update order attribute ids in redis cache')
            ->setHelp("This command update order attribute ids in redis cache");
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        // Showing when the script is launched
        $now = new DateTime();
        $output->writeln('<comment>Start : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');

        $this->attributeService->getCachedAttributes(true);

        foreach ($this->izbergOrderAttributes as $attribute) {
            $this->attributeService->getOrderAttributeId($attribute, true);
        }

        foreach ($this->izbergMerchantAttributes as $attribute) {
            $this->attributeService->getMerchantAttributeId($attribute, true);
        }

        $now = new DateTime();
        $output->writeln('<comment>End : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');

        return 0;
    }
}
