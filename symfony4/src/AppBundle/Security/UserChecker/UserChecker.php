<?php

declare(strict_types=1);

namespace AppBundle\Security\UserChecker;

use AppBundle\Entity\User;
use Symfony\Component\Security\Core\Exception\CustomUserMessageAuthenticationException;
use Symfony\Component\Security\Core\User\UserCheckerInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

final class User<PERSON>he<PERSON> implements UserCheckerInterface
{
    private TranslatorInterface $translator;

    public function __construct(TranslatorInterface $translator)
    {
        $this->translator = $translator;
    }

    /**
     * @param UserInterface|User $user
     */
    public function checkPreAuth(UserInterface $user): void
    {
        if ($user->isBuyerApi()) {
            throw new CustomUserMessageAuthenticationException(
                $this->translator->trans('security.login.role_denied', [], 'AppBundle')
            );
        }
    }

    /**
     * @param UserInterface|User $user
     */
    public function checkPostAuth(UserInterface $user): void
    {
    }
}
