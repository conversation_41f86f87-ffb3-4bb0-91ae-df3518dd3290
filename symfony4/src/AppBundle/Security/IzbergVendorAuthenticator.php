<?php

namespace AppBundle\Security;

use AppBundle\Entity\Vendor;
use AppBundle\Repository\VendorRepository;
use Doctrine\ORM\EntityManagerInterface;
use KnpU\OAuth2ClientBundle\Client\ClientRegistry;
use KnpU\OAuth2ClientBundle\Client\OAuth2ClientInterface;
use KnpU\OAuth2ClientBundle\Security\Authenticator\OAuth2Authenticator;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Passport;
use Symfony\Component\Security\Http\Authenticator\Passport\SelfValidatingPassport;
use Symfony\Component\Security\Http\EntryPoint\AuthenticationEntryPointInterface;
use Symfony\Component\Security\Http\Util\TargetPathTrait;

class IzbergVendorAuthenticator extends OAuth2Authenticator implements AuthenticationEntryPointInterface
{
    use TargetPathTrait;

    private $clientRegistry;
    private $em;
    private $router;

    /**
     * @var VendorRepository
     */
    private $vendorRepository;

    public function __construct(
        ClientRegistry $clientRegistry,
        EntityManagerInterface $em,
        RouterInterface $router
    ) {
        $this->clientRegistry = $clientRegistry;
        $this->em = $em;
        $this->router = $router;
        $this->vendorRepository = $this->em->getRepository(Vendor::class);
    }

    public function supports(Request $request): ?bool
    {
        // continue ONLY if the current ROUTE matches the check ROUTE
        return $request->attributes->get('_route') === 'front_vendor_connect_check';
    }

    public function authenticate(Request $request): Passport
    {
        $client = $this->getIzbergVendorClient();
        $accessToken = $this->fetchAccessToken($client);

        return new SelfValidatingPassport(
            new UserBadge($accessToken->getToken(), function() use ($accessToken, $client) {
                /** @var IzbergVendor $izbergVendor */
                $izbergVendor = $client->fetchUserFromToken($accessToken);

                $vendor = $this->vendorRepository->findOneBy([
                    'id' => $izbergVendor->getMerchantId(),
                    'name' => $izbergVendor->getName()
                ]);

                if (!$vendor) {
                    $vendor = (new Vendor())
                        ->setId($izbergVendor->getMerchantId())
                        ->setName($izbergVendor->getName())
                        ->setSlug($izbergVendor->getSlug())
                        ->setPreferedLanguage($izbergVendor->getPreferedLanguage())
                        ->setCompanyName($izbergVendor->getCompanyName());

                    $this->vendorRepository->save($vendor);
                }

                if($vendor) {
                    $vendor->setPreferedLanguage($izbergVendor->getPreferedLanguage());
                    $this->vendorRepository->save($vendor);
                    return $vendor;
                }

                return null;
            })
        );
    }

    public function onAuthenticationSuccess(Request $request, TokenInterface $token, string $firewallName): ?Response
    {
        if ($targetPath = $this->getTargetPath($request->getSession(), $firewallName)) {
            return new RedirectResponse($targetPath);
        }
        $referer = $request->headers->get('referer');
        return new RedirectResponse($referer ?: '/front/vendor/dashboard/');
    }

    public function onAuthenticationFailure(Request $request, AuthenticationException $exception): ?Response
    {
        $message = strtr($exception->getMessageKey(), $exception->getMessageData());

        return new RedirectResponse(
            '/front/vendor/connect/start?error='.urlencode($message)
        );
    }

    /**
     * Called when authentication is needed, but not sent.
     * This redirects to the OAuth login URL.
     */
    public function start(Request $request, AuthenticationException $authException = null): Response
    {
        return new RedirectResponse(
            '/front/vendor/connect/start',
            Response::HTTP_TEMPORARY_REDIRECT
        );
    }

    /**
     * @return OAuth2ClientInterface
     */
    private function getIzbergVendorClient(): OAuth2ClientInterface
    {
        return $this->clientRegistry->getClient('izberg_vendor_client');
    }

    public function getPreviousUrl(Request $request, $providerKey)
    {
        if ($targetPath = $this->getTargetPath($request->getSession(), $providerKey)) {
            return $targetPath;
        }
        $referer = $request->headers->get('referer');
        if ($referer) {
            return $referer;
        }

        return '/front/vendor/dashboard/';
    }
}
