<?php

namespace AppBundle\Security;

use League\OAuth2\Client\Provider\ResourceOwnerInterface;

class IzbergVendor implements ResourceOwnerInterface
{
    /** @var int */
    private $merchantId;

    /** @var string */
    private $name;

    /** @var string */
    private $preferedLanguage;

    /** @var string */
    private $slug;

    /** @var string */
    private $companyName;

    public function getMerchantId(): int
    {
        return $this->merchantId;
    }

    public function setMerchantId(int $merchantId): self
    {
        $this->merchantId = $merchantId;
        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;
        return $this;
    }

    public function getPreferedLanguage(): string
    {
        return $this->preferedLanguage;
    }

    public function setPreferedLanguage(string $preferedLanguage): self
    {
        $this->preferedLanguage = $preferedLanguage;
        return $this;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function setSlug(string $slug): self
    {
        $this->slug = $slug;
        return $this;
    }

    public function getCompanyName(): string
    {
        return $this->companyName;
    }

    public function setCompanyName(string $companyName): self
    {
        $this->companyName = $companyName;
        return $this;
    }

    public function getId(): int
    {
        return $this->getMerchantId();
    }

    public function toArray(): array
    {
        return [
            'merchantId' => $this->getMerchantId(),
            'name' => $this->getName(),
            'preferedLanguage' => $this->getPreferedLanguage(),
            'slug' => $this->getSlug(),
            'companyName' => $this->getCompanyName(),
        ];
    }
}
