<?php

namespace AppBundle\Security;

use League\OAuth2\Client\Provider\GenericProvider;
use League\OAuth2\Client\Token\AccessToken;
use Firebase\JWT\Key;

class IzbergVendorProvider extends GenericProvider
{

    protected $izbergApiDomain;

    public function getResourceOwner(AccessToken $token): IzbergVendor
    {
        $izbergVendor = null;

        $request = $this->getRequest('GET', 'https://api.izberg.me/.well-known/valid-keys.json');
        $keys = $this->getParsedResponse($request);
        $key = array_shift($keys['keys']);
        $decoded = JWT::decode($token->getToken(), new Key($key['n'],'RS256'));
        $decodedTokenId =  JWT::decode($token->getValues()['id_token'], new Key($key['n'], 'RS256'));

        $profil = $this->getParsedResponse($this->getRequest('GET', 'https://'.$this->izbergApiDomain.'/v1/merchant/'.$decoded->merchant_id.'/', [
            'headers' => [
                'Authorization' => 'Bearer ' . $token,
                'Accept'        => 'application/json',
            ]
        ]));

        $izbergVendor = (new IzbergVendor())
            ->setName($decoded->name)
            ->setMerchantId($decoded->merchant_id)
            ->setPreferedLanguage($decodedTokenId->locale ?? 'en')
            ->setSlug($profil['slug'])
            ->setCompanyName($profil['name']);

        return $izbergVendor;
    }

    protected function getRequiredOptions()
    {
        return [
            'urlAuthorize',
            'urlAccessToken',
            'izbergApiDomain',
        ];
    }
}
