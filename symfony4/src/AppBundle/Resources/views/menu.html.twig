{% extends 'knp_menu.html.twig' %}


{%- macro attributes(attributes) -%}
    {%- for name, value in attributes -%}
        {%- if value is not none and value is not same as(false) and name != 'icon' -%}
            {{- ' %s="%s"'|format(name, value is same as(true) ? name|e : value|e)|raw -}}
        {%- endif -%}
    {%- endfor -%}
{%- endmacro -%}

{% block root %}
    {%- set listAttributes = item.childrenAttributes|merge({'class': 'navbar-nav mr-auto'}) %}
    {%- set request        = item.extra('request') ?: app.request %}
    {{ block('list') -}}
{% endblock %}


{%- block compressed_root -%}
    {%- apply spaceless -%}
        {{ block('root') }}
    {%- endapply -%}
{%- endblock -%}


{%- block list -%}
    {%- if item.hasChildren and options.depth is not same as(0) and item.displayChildren -%}
        {%- import _self as knp_menu -%}

        {%- if (listAttributes.class is defined and 'dropdown-menu' in listAttributes.class )  -%}
            <div{{ knp_menu.attributes(listAttributes) }}>
                {{ block('children') }}
            </div>
        {%- else -%}
            <ul{{ knp_menu.attributes(listAttributes) }}>
                {{ block('children') }}
            </ul>
        {%- endif  -%}
    {%- endif -%}
{%- endblock -%}

{%- block children -%}
    {# save current variables #}
    {%- set currentOptions = options -%}
    {%- set currentItem = item -%}
    {# update the depth for children #}
    {%- if options.depth is not none -%}
        {%- set options = options|merge({'depth': currentOptions.depth - 1}) -%}
    {%- endif -%}
    {# update the matchingDepth for children #}
    {%- if options.matchingDepth is not none and options.matchingDepth > 0 -%}
        {%- set options = options|merge({'matchingDepth': currentOptions.matchingDepth - 1}) -%}
    {%- endif -%}
    {%- for item in currentItem.children -%}
        {{ block('item') }}
    {%- endfor -%}
    {# restore current variables #}
    {%- set item = currentItem -%}
    {%- set options = currentOptions -%}
{%- endblock -%}

{%- block item -%}
    {%- import _self as knp_menu -%}
    {%- if item.displayed -%}
        {%- set attributes = item.attributes -%}
        {%- set is_dropdown = attributes.dropdown|default(false) -%}
        {%- set is_header = attributes.header|default(false) -%}
        {%- set divider_prepend = attributes.divider_prepend|default(false) -%}
        {%- set divider_append = attributes.divider_append|default(false) -%}

        {# unset bootstrap specific attributes #}
        {%- set attributes = attributes|merge({'dropdown': null, 'divider_prepend': null, 'divider_append': null, 'header': null }) -%}

        {%- if divider_prepend -%}
            {{ block('dividerElement') }}
        {%- endif -%}

        {# building the class of the item #}
        {%- set classes = item.attribute('class') is not empty ? [item.attribute('class')] : [] -%}
        {%- if matcher.isCurrent(item) -%}
            {%- set classes = classes|merge([options.currentClass]) -%}
        {%- elseif matcher.isAncestor(item, options.depth) -%}
            {%- set classes = classes|merge([options.ancestorClass]) -%}
        {%- endif -%}
        {%- if item.actsLikeFirst -%}
            {%- set classes = classes|merge([options.firstClass]) -%}
        {%- endif -%}
        {%- if item.actsLikeLast -%}
            {%- set classes = classes|merge([options.lastClass]) -%}
        {%- endif -%}

        {# building the class of the children #}
        {%- set childrenClasses = item.childrenAttribute('class') is not empty ? [item.childrenAttribute('class')] : [] -%}
        {%- set childrenClasses = childrenClasses|merge(['menu_level_' ~ item.level]) %}

        {# adding classes for dropdown #}
        {%- if is_dropdown -%}
            {%- set classes = classes|merge(['dropdown']) -%}
            {%- set childrenClasses = childrenClasses|merge(['dropdown-menu']) -%}
        {%- endif -%}

        {# putting classes together #}
        {%- if classes is not empty -%}
            {%- set attributes = attributes|merge({'class': classes|join(' ')}) -%}
        {%- endif -%}

        {%- set listAttributes = item.childrenAttributes|merge({'class': childrenClasses|join(' ') }) -%}

        {# displaying the item #}
        {% if not divider_prepend and not divider_append %}
            {%- set class = 'nav-item' ~ (attributes.class is defined ? ' ' ~ attributes.class : '') -%}
            {%- set class = class|replace({'current_ancestor':'dropdown'}) -%}
            {%- set attr = attributes|merge({'class': class}) %}

            {%- if item.level == 1 -%}
            <li{{ knp_menu.attributes(attr) }}>
                {%- if is_dropdown -%}
                    {{ block('dropdownElement') }}
                {%- elseif item.uri is not empty and (not item.current or options.currentAsLink) -%}
                    {{ block('linkElement') }}
                {%- elseif is_header -%}
                    {{ block('headerElement') }}
                {%- else -%}
                    {{ block('spanElement') }}
                {%- endif -%}
                {# render the list of children#}
                {{ block('list') }}
            </li>
            {%- else -%}
                {%- if is_dropdown -%}
                    {{ block('dropdownElement') }}
                {%- elseif item.uri is not empty and (not item.current or options.currentAsLink) -%}
                    {{ block('linkElement') }}
                {%- elseif is_header -%}
                    {{ block('headerElement') }}
                {%- else -%}
                    {{ block('spanElement') }}
                {%- endif -%}
                {# render the list of children#}
                {{ block('list') }}
            {% endif %}


        {% endif %}

        {%- if divider_append -%}
            {{ block('dividerElement') }}
        {%- endif -%}
    {%- endif -%}
{%- endblock -%}

    {# elements #}
{%- block linkElement -%}
    {%- import _self as knp_menu -%}

    {%- if item.parent.name != 'root' -%}
        {% set class = 'dropdown-item' %}
    {%- else -%}
        {% set class = 'nav-link' %}
    {%- endif -%}


    {%- set linkAttributes = item.linkAttributes|merge({'class': class}) %}
    <a href="{{ item.uri }}"{{ knp_menu.attributes(linkAttributes) }}>
        {%- if item.attribute('icon') is not empty -%}
            <i class="{{ item.attribute('icon') }}" aria-hidden="true"></i>
        {%- endif -%}
        {{ block('label') }}
    </a>
{%- endblock -%}

{%- block dividerElement -%}
    {%- if item.level == 1 -%}
        <li class="divider-vertical"></li>
    {%- else -%}
        <li class="divider"></li>
    {%- endif -%}
{%- endblock -%}

{%- block headerElement -%}
    <li class="dropdown-header">{{ block('label') }}</li>
{%- endblock -%}

{%- block spanElement -%}
    {%- import _self as knp_menu -%}
    <span>{{ knp_menu.attributes(item.labelAttributes) }}>
        {%- if item.attribute('icon') is not empty -%}
            <i class="{{ item.attribute('icon') }}" aria-hidden="true"></i>
        {%- endif -%}
        {{ block('label') }}
    </span>
{%- endblock -%}

{%- block dropdownElement -%}
    {%- import _self as knp_menu -%}
    {%- set classes = item.linkAttribute('class') is not empty ? [item.linkAttribute('class')] : [] -%}
    {%- set classes = classes|merge(['nav-link dropdown-toggle']) -%}
    {%- set attributes = item.linkAttributes -%}
    {%- set attributes = attributes|merge({'class': classes|join(' ')}) -%}
    {%- set attributes = attributes|merge({'data-toggle': 'dropdown'}) -%}
    <a href="#"{{ knp_menu.attributes(attributes) }}>
        {%- if item.attribute('icon') is not empty -%}
            <i class="{{ item.attribute('icon') }}" aria-hidden="true"></i>
        {%- endif -%}
        {{ block('label') }}
    </a>
{%- endblock -%}
