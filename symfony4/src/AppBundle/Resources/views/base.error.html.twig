{% trans_default_domain 'AppBundle' %}
<!DOCTYPE html>
<html lang="{{ app.request.locale|split('_')[0] }}">



    <head>
        <style>
            .error{
                background-color: #F4F5FA;
                width: 80%;
                margin: 30px auto;
                color: #6B6F82;
                box-shadow: 0px 0px 4px #A4A7B3;
                font-family: 'Open Sans', sans-serif;
            }

            .error-content{
                padding: 10px;
                text-align: center;
            }

            a {
                color: #9600FF;
            }

            ul {
                list-style-type: none;
            }

            .intro h1,
            .intro p {
                display: inline;
                line-height: 80px;
            }

            .intro p{
                font-size: 16px;
                color: #e64d5d;
            }

            .intro h1{
                font-size: 30px;
                color: #9600FF;
            }

            li{
                line-height: 25px;
            }

        </style>
    </head>


    <body>
    <div class="error">
        <div class="error-content">
            <div class="intro">
                <h1>{% block title %}{% endblock %}</h1>
                <p>{% block code %}{% endblock %}</p>
            </div>


            <div>
                {% block description %}{% endblock %}
            </div>
            <div>
                {{ 'error.generic.help' | trans }}
            </div>
            <ul>
                <li>
                    {% if is_granted('ROLE_OPERATOR') or is_granted('ROLE_SUPER_ADMIN') %}
                        <a href="{{ path("homepage") }}">{{ 'error.generic.home' | trans}}</a>
                    {% else %}
                        <a href="{{ path("homepage") }}">{{ 'error.generic.home' | trans}}</a>
                    {% endif %}
                </li>
                <li>
                    <a href="{{ path('anonymous.ticket.create') }}">{{ 'error.generic.support' |trans }}</a>
                </li>
            </ul>
        </div>
    </div>

    </body>

</html>
