var jQuery = require('jquery');
var Handlebars = require('handlebars');

(function ($, h) {
    'use strict';

    let zIndex = 9999;

    let _destroyModal = function (id) {
        $('#' + id).remove();
    };

    /**
     * Close modal from its id
     * @param id
     * @param cb
     * @private
     */
    let _closeModal = function (id, cb) {

        zIndex--;

        // Destroy loading modal if launched
        _destroyModal('js-loading-modal');

        // Hide the Modal then destroy it
        $('#' + id).fadeOut(100, function () {

            // Remove DOM elements
            _destroyModal(id);

            // Trigger close callback if needed
            if (typeof cb === 'function') {
                cb();
            }
        });
    };

    /**
     * Show modal
     * @param id
     * @param classes
     * @param $content
     * @returns {{close: close}}
     * @private
     */
    let _show = function (id, classes, $content, canClose, onShow, onClose) {

        const _onClose = onClose;

        // if the modal already exists do nothing
        if (document.getElementById(id) !== null) {
            console.log('Modal already exists : #' + id);
            return {
                show: function (onShow) {
                    $('#' + id).fadeIn(100, function () {
                        if (typeof onShow === 'function') {
                            onShow(
                                {
                                    close: function () {
                                        _closeModal(id, onClose);
                                    }
                                }
                            );
                        }
                    });
                },
                close: function (onClose) {
                    _closeModal(id, onClose);
                }
            };
        }

        if (typeof canClose === 'undefined') {
            canClose = true;
        } else {
            canClose = !!canClose;
        }

        if (canClose === false) {
            onClose = undefined;
        }

        // Compile template
        let $tpl = h.compile($('#js-modal-tpl').html());

        // Build data
        let o = {
            id: id,
            classes: null
        };

        if (typeof classes === 'string' && classes !== '') {
            o.classes = ' ' + classes
        }

        // Append html to dom
        $('body').append($tpl(o));

        // Get the modal container
        let $modal = $('#' + id);


        if (canClose) {
            // Bind click on close button to close the modal
            $modal.find('.js-close-icon').on('click', function () {
                _closeModal(id, onClose);
            });
        } else {
            $modal.find('.js-close-icon').remove();
        }

        $modal.find('.js-modal-overlay').css('zIndex', zIndex);

        // Add content to the modal
        let $contentContainer = $modal.find('.js-modal-content');

        if ($contentContainer.parents('.modal').length) {
            $contentContainer.parents('.modal').css('zIndex', zIndex + 1);
        } else {
            $contentContainer.css('zIndex', zIndex + 1);
        }

        $contentContainer.append($content);
        overrideCSS(id);

        // Show the modal
        $modal.fadeIn(100, function () {
            if (typeof onShow === 'function') {

                onShow(
                    {
                        close: function () {
                            _closeModal(id, _onClose);
                        }
                    }
                );
            }
        });

        zIndex++;

        // Return a close method to close the modal
        return {
            close: function () {
                _closeModal(id, onClose);
            }
        }
    };

    let _showLoading = function () {
        let modalHTML = $('#js-loading-modal-tpl').html();
        return _show('js-loading-modal', 'Modal--loading', $(modalHTML), false);
    };

    let _hideLoading = function () {
        _destroyModal('js-loading-modal');
    };

    let _showLogin = function () {
        let modalHTML = $('#js-login-modal-tpl').html();
        let modal = null;
        if (modalHTML != null) {
           modal = _show('js-login-modal', 'Modal--login', $(modalHTML), true);
           onloadCallback();
        }

        return modal;
    }

    let _showCustomSearchModal = function () {
        let modalHTML = $('#js-custom-search-modal-tpl').html();
        return _show('js-custom-search-modal', 'Modal--custom-search', $(modalHTML), true);
    };

    let _showConfirmModal = function (title, txt, onConfirm, onCancel) {
        const modalHTML = $('#js-confirm-modal-tpl').html();

        // generate the bid confirm modal with the entered value
        const modalContent = h.compile(modalHTML, {noEscape: true})(
            {
                'title': title,
                'txt': txt
            }
        );


        return _show('js-confirm-modal', 'Modal--confirm', modalContent, false, function (modal) {

                if (typeof onConfirm === 'function') {
                    $('#js-confirm-button').on('click', function () {
                        modal.close();
                        onConfirm();
                    });
                }

                if (typeof onCancel === 'function') {
                    $('.js-cancel-button').on('click', function () {
                        modal.close();
                        onCancel();
                    });
                }

            },
            function () {
                // ('Confirm modal closed');
                $('#js-confirm-button').off();
                $('#js-cancel-button').off();
            });

    };

    let _showAlertModal = function (txt, onConfirm) {

        const modalHTML = $('#js-alert-modal-tpl').html();

        // generate the bid confirm modal with the entered value
        const modalContent = h.compile(modalHTML)(
            {
                'txt': txt
            }
        );


        return _show('js-alert-modal', 'Modal--alert', modalContent, false, function (modal) {


                $('#js-confirm-button').on('click', function () {
                    modal.close();
                    if (typeof onConfirm === 'function') {
                        onConfirm();
                    }
                });

                $('.js-cancel-button').on('click', function () {
                    modal.close();
                });

            },
            function () {
                // ('Confirm modal closed');
                $('#js-confirm-button').off();
            });

    };

    // override CSS styles for small screen
    let overrideCSS = function (id) {
        var ratio = 1 / 2;
        var breakpointPx = 700;
        var winH = $(window).height();

        if (winH < breakpointPx) {
            var newModalH = Math.round(ratio * winH) + 'px';
            var smallScreenProps = {
                'max-height': newModalH,
                'top': (ratio * 100) + '%',
                'overflow-y': id == 'js-loading-modal' ? 'visible' : 'scroll'
            };
            // apply css properties
            $('.Modal-content').css(smallScreenProps);
            // remove useless margin/padding for small screens
            $('.Modal-content input').css('margin', '0');
            $('.Modal-content form').css('padding-top', '0');
        } else {
            var otherScreenProps = {
                'max-height': 'inherit',
                'overflow-y': id == 'js-loading-modal' ? 'visible' : 'auto',
                'top': (ratio * 100) + '%'
            };
            $('.Modal-content').css(otherScreenProps);
        }

        // override for login
        $('#js-login-modal').find('.Modal-content').css('max-height', '100%');
        $('#js-login-modal').find('.Modal-content form').css('padding-top', '20px');
    };

    // exports
    module.exports = {
        show: _show,
        showLoading: _showLoading,
        confirm: _showConfirmModal,
        showCustomSearch: _showCustomSearchModal,
        showLogin: _showLogin,
        alert: _showAlertModal,
        hideLoading: _hideLoading
    };

    $(window).on('resize', function () {
        overrideCSS();
        //     var modal = $('.Modal-content');
        //     if($(window).height() < 450) {
        //         modal.css('height', $(window).height());
        //         if (!modal.hasClass('tiny-height')) {
        //             modal.addClass('tiny-height');
        //         }
        //     } else {
        //         modal.css('height', '');
        //         if (modal.hasClass('tiny-height')) {
        //             modal.removeClass('tiny-height');
        //         }
        //     }
    });

})(jQuery, Handlebars);
