(function ($) {
  'use strict';


  let $country;

  let checkCompanyIdentification = function (value) {
    const option = $country.find(':selected')[0];

    const regexp = new RegExp(option.dataset.regexp, "gi");

    return regexp.test(value);
  };



  // Init method
  let init = function (country_field_selector) {

    $country = $(country_field_selector);

    // If not already existing then add a custom validation method for the slug
    if (typeof $.validator.methods['company-identification'] === 'undefined') {
      $.validator.addMethod(
        'company-identification',
        checkCompanyIdentification
      );
    }

  };


  module.exports = {
    init: init
  };


})(jQuery);
