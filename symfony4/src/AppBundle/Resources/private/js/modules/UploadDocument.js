/**
 * Created by QAR14123 on 16/02/2018.
 */
(function ($) {
    'use strict';

    function buildLine(url, file, doc_type, companyId)
    {
        var name = file.name;

        var html = '<li>'+
                '<a href="'+url+'" target="_blank">'+name+'</a>'+
                '<svg class="IconMax js-doc-remove" data-doc-id="'+file.id+'" data-company-id="'+companyId+'" data-doc-type="'+doc_type+'">'+
                '<use xlink:href="#icon-cancel"></use>'+
                '</svg>'+
                '</li>';
        return html;
    }


    function manageUpload(uploadUrl, progressDiv, messageDiv, docsDiv, noDocDiv, typeDoc, boutonAjout, companyId, typeError, sizeMax, sizeError)
    {

        return {
            dropZone: null,
            singleFileUploads: true,
            url : uploadUrl,
            paramName : 'files[]',
            dataType: 'json',
            add: function(e, data) {
                let uploadErrors = [];
                let typeAccept = ["application/pdf","image/jpeg", "image/gif", "image/png", "image/tiff"];
                if(typeAccept.indexOf(data.originalFiles[0].type) === -1) {
                    uploadErrors.push(typeError);
                }
                if(data.originalFiles[0]['size'] > sizeMax) {
                    uploadErrors.push(sizeError);
                }
                if(uploadErrors.length > 0) {
                    displayUploadError(messageDiv, uploadErrors);
                } else {
                    data.submit();
                }
            },
            done: function (e, data) {
                var $target = $(e.target);

                var count = $target.data('count') || 0;

                if(data.result.status === 'OK'){

                    noDocDiv.css('display', 'none');
                    messageDiv.css('display', 'none');
                    removeErrorMessage(boutonAjout);

                    $.each(data.result.files, function (index, file) {
                        var url = Routing.generate("front.document.view", {id:file.id});
                        docsDiv.append(buildLine(url, file, typeDoc, companyId));
                        count++;
                    });

                    $target.data('count', count);

                    docsDiv.show();
                }

                if(data.result.status === 'KO'){
                    displayUploadResult(messageDiv, data.result);
                }

                progressDiv.css('display', 'none');

            },
            start: function (e, data) {
                progressDiv.css('display', 'none');
                messageDiv.css('display', 'none');
                noDocDiv.css('display', 'none');
            },
            fail: function (e, data) {
                messageDiv.css('display', 'none');
                progressDiv.css('display', 'none');
                alert("Request Failed: " + data.errorThrown);
            },
            progressall: function (e, data) {
                var progress = parseInt(data.loaded / data.total * 100, 10);
                progressDiv.css('display', 'block');
                progressDiv.css('width', progress + '%');
                progressDiv.html(progress+' %');
            }
        };
    }

    function displayUploadResult(where, result)
    {
        if (!result.ok)
        {
            var txt;
            var i;

            where.css('display', 'inline');
            txt = '<div class="Message-item Message--error"><p>'+result.global+'</p>';

            for (i=0; i<result.errors.length; i++)
            {
                txt += '<p>'+result.errors[i]+'</p>';
            }
            txt+='</div>';
            where.html(txt);
        }
    }

    function displayUploadError(where, error) {
        var txt;
        where.css('display', 'inline');
        txt = '<div class="Message-item Message--error">';
        txt += '<p>'+error+'</p>';
        txt+='</div>';
        where.html(txt);
    }

    function removeErrorMessage(where)
    {
        var p = where.parent();
        if (p.hasClass('has-error'))
        {
            var tag=p.find("ul");
            tag.remove();
        }
    }

    module.exports = {
        manageUpload : manageUpload
    };


})(jQuery);
