address:
    form:
        address: Address
        address2: 'Additional address details'
        address2_placeholder: 'Additional Information'
        all_country_placeholder: 'All Countries'
        check: Check
        city: City
        country: Country
        country_placeholder: 'Select a country'
        email: Email
        first_lastname: 'Last and First Name'
        phone1: 'Main Phone number'
        phone1_placeholder: 'Mandatory Phone number'
        phone2: 'Phone number'
        region: Region
        region_placeholder: 'Select a Region'
        update:
            error: 'An error occurred during the company update'
            success: 'The company information has been successfully updated'
        zipcode: 'Zip Code'
address_form_all_country_placeholder: 'All Countries'
admin:
    logout: Logout
admin_menu:
    admin_users:
        add_user: 'Add a User'
        admin_list: Administrators
        label: 'Manage users'
        user_list: Users
    bafv_requests_menu:
        label: 'BAFV catalog prices requests'
    companies_menu:
        all_companies: 'All Buyers'
        costs_centers: 'Cost Center'
        label: Buyers
        users: Users
    dashboard:
        label: Dashboard
    merchant_menu:
        label: Vendors
    message: Messages
    messages_menu:
        add: 'Create a discussion'
        label: Messages
        list: 'Open Requests'
        resolved: 'Solved Requests'
    orders: Orders
    other_menu:
        api_doc: 'Api documentation'
        automatic_controls: 'Automatic Controls'
        feeback_form: 'Feedback form'
        label: Other
        logo: 'Logo object'
        notifications: Notifications
        payloads: 'Payloads list'
        redis_keys: 'Redis Keys list'
        search_historization:
            label: 'Search history'
        serches_list: 'Search list'
        slider: 'Slider object'
        term_payment: 'Term payment requests'
        top_mismatch_catalog_references: 'Top mismatched catalog references'
    redirects:
        label: Redirection
    sys_parameters:
        label: Settings
    web_content:
        add: 'Add web content'
        label: 'Web content'
        list: 'All web content'
        popular_offers: 'Popular deals'
algolia:
    attributes:
        BAB10_MOQ: 'Minimum of quantity'
        BAE10_Incoterm: Incoterm
        CAA10_DTR: DTR
        DQL10_Friction_coef: 'Coefficient of friction'
        Threshold_2_Prcie: 'Floor price'
    id: 'Marketplace Id'
    merchant:
        name: Vendor
    product:
        description: Description
        manufacturer: Manufacturer
        name: Product
back:
    admin:
        filter:
            activeBtn: 'Active Administrators'
            inactiveBtn: 'Inactive Administrators'
    back: Back
    back_to_list: 'Back to the list'
    bafv_request:
        list:
            buyer_code: 'Buyer Code'
            buyer_name: 'Buyer Name'
            status: Status
            submitted_on: 'Request Submitted On'
            vendor_company_name: 'Vendor Company Name'
    catalogue_references:
        not_found_mismatch_references: 'Mismatched references not found'
        reference: reference
        top_mismatch_references_title: 'Top mismatched references'
        total: total
    commons:
        actions: Actions
        add: Add
        all: All
        cancel: Cancel
        delete: Delete
        disable: Deactivate
        edit: Edit
        export_csv: 'CSV Export'
        id: ID
        piggy: 'Login As'
        piggy_exit: 'Logout As'
        view: View
    company:
        all_term_payment: All
        all_term_payment_title: 'All companies whith term payments (validated or waiting for validation)'
        companyInfo:
            activation:
                ko: 'An error occurred during company activation'
                ok: 'Company is activated with success'
            address: Address
            addressComplement: 'Additional address details'
            billing:
                service: Service
                title: Billing
            city: City
            contact:
                adv: 'Sell administration contact'
                billing: 'Billing address'
                firstname: 'First Name'
                lastname: 'Last Name'
                logistic: 'Logistic contact'
                mail: Email
                main: 'Main Contact'
                noInformation: 'No Information'
                phone1: 'Main Phone number'
                phone2: 'Phone number'
            country: Country
            deactivate:
                content: 'Are you sure to want to disable this company ?'
                title: 'Company Deactivation'
            deactivation:
                ko: 'An error occurred during company deactivation'
                ok: 'Company is disabled with success'
            deactivationReason: 'Deactivation reason'
            document:
                title: Document
            edit: Edit
            endpointUrl: 'Customer URL'
            info:
                code: 'Company code'
                name: 'Company Name'
                title: 'Company Information'
            invalidate:
                ko: 'An error occurred while refusing the company'
                ok: 'Company is invalidate with success'
            region: Region
            reject:
                ko: 'Error during rejected company'
                ok: 'Company has been rejected with success'
            rejectedReason: 'Rejected reason'
            termpayment:
                active: 'Enable term payment'
                askKo: 'Error during enabled term payment'
                askOk: 'Term payment enabled'
                authorization: 'Term payment authorization'
                deny: 'Reject term payment'
                disabled: disabled
                enabled: enabled
                inactive: 'Disable term payment'
                pending: pending
                reason: 'Deny reason'
                removeKo: 'Error during disabled term payment'
                removeOk: 'Term payment disabled'
                title: 'Term payment Request Date'
            validate:
                ko: 'An error occurred while validating the company'
                ok: 'Company is validate with success'
            zipCode: 'Zip Code'
        endpointUrl: ''
        export: 'CSV Export'
        filter_clear: Clear
        filter_title: Filters
        list:
            add:
                site: 'Add a Cost Center'
                user: 'Add a User'
            category: Category
            city: City
            costCenters: 'Cost Centers'
            country: Country
            created: 'Account Creation'
            empty: 'No company waiting for validation'
            filter:
                activeBuyer: 'Active Buyers'
                all: 'All Companies'
                caMax: 'and :'
                caMin: 'CA between :'
                creationMax: 'and :'
                creationMin: 'Creation date between:'
                disabled: Deactivated
                purchaseMax: 'and :'
                purchaseMin: 'Order date between :'
                submit: Filter
                term_payment_date_max: 'and :'
                term_payment_date_min: 'Request/Acceptation between :'
                toConfirm: 'Pending Approval'
                tonMax: 'and :'
                tonMin: 'Tonnage between :'
            identification: Code
            last: 'Last Login'
            name: Company
            purchases: 'Purchases (USD)'
            revenue: 'CA généré'
            status: Status
            status_company:
                acceptable: Pending
                all: All
                disabled: Deactivated
                draft: Draft
                initial: Initial
                pending: 'Pending Validation'
                rejected: Rejected
                valid: Active
            termpayment_moneytransfert_accept: 'Accept term payments'
            termpayment_moneytransfert_date_accepted: Accepted
            termpayment_moneytransfert_date_requested: Rejected
            termpayment_moneytransfert_deny: 'Reject term payments'
            termpayment_moneytransfert_enabled: Active
            termpayment_moneytransfert_pending: Pending
            tonnes: 'Tons on the meter'
            type_:
                1: Buyer
                2: Vendor
                all: All
                buyer: Buyer
            user_type: 'User type'
            users: Users
        menu:
            general: General
            messages: Messages
            orders: Orders
            sites: 'Cost Center'
            users: Users
        order:
            creation_date: 'Order date'
            export: 'PDF export'
            number: 'Order number'
            price: Price
            shipping: 'Shipping address'
            status:
                cancelled: 'Cancelled Orders'
                past: 'Past Orders'
                running: 'Running Orders'
                title: Status
        pending_term_payment: Pending
        pending_term_payment_title: 'All companies wainting for term payments validation'
        term_payment_empty: 'No company waiting for term payment validation'
        termpayment_moneytransfert_accepted: 'You have accepted the term payments for this company'
        termpayment_moneytransfert_accepted_error: 'Error in the acceptance of term payments process for this company'
        termpayment_moneytransfert_rejected: 'You declined the term payments for this company'
        termpayment_moneytransfert_rejected_error: 'Error refusing term payments for this company'
        users:
            create_ticket: 'Create a ticket'
            term_payment_empty: 'No company awaiting validation of payment terms'
    deactivate:
        ok: Ok
        reason: 'Reason ?'
    index_page:
        companies_term_payment_request_to_validate: 'Companies waiting for term payment acceptance'
        companies_to_validate: 'Companies waiting for validation'
        see_all: 'See all'
        unread_messages: 'Unread messages'
    logo:
        commons:
            add: Add
            addTitle: 'Add a new object to the logo'
            backgroundImage: Image
            create: Create
            editTitle: 'Modify object on the logo'
            link: Link
            lock: Locked
            order: Order
            published: Published
            setTitleVisible: 'Make the title visible on the homepage'
            status: Status
            title: Title
            update: Edit
        create:
            confirm: 'Logo object added'
            error: 'An error occurred during object creation'
        delete:
            confirm: 'Logo object deleted'
            error: 'An error occurred during object deletion'
        edit:
            actualImage: 'Current Image'
            imageP: 'Select a new image to modify the existing one'
        list:
            actions: Actions
            createdAt: Created
            header: 'Logo object list'
            lang: Languages
            updatedAt: Modified
        status:
            draft: Draft
            published: Published
        update:
            confirm: 'Logo object modified'
            error: 'An error occurred during object modification'
    merchant:
        form:
            update:
                error: 'Error while updating the vendor'
                success: 'Vendor has been successfully updated'
        list:
            email: Email
            firstname: 'First Name'
            identification: Identification
            lastname: 'Last Name'
            list: Email
            name: Name
            status:
                accepted: Accepted
                all: All
                pending: Pending
                rejected: Rejected
                title: Status
        merchantInfo:
            country: Country
            currency:
                eur: EUR
                placeholder: 'Choose a currency'
                title: Currency
                usd: USD
            edit: Edit
            email: Email
            firstname: Firstname
            identification: Identification
            info:
                title: 'Vendor Information'
            lastModifiedAt: 'Last modified at'
            lastModifiedBy: 'Last modified by'
            lastname: Lastname
            name: Name
            password: Password
            phoneNumber: 'Phone number'
            registrationDate: 'Registration date'
            reject:
                ko: 'An error occurred while rejecting the vendor'
                ok: 'Vendor has been rejected with success'
            rejectedReason: 'Rejected reason'
            status: Status
            validate:
                ko: 'An error occurred while validating the vendor'
                ok: 'Vendor has been successfully updated'
    notification:
        edit:
            body: Body
            confirm: 'Notification has been updated'
            empty_email_error: 'Please, enter a valid email address'
            externalLinkType: 'Use an external URL'
            link: Link
            linkExternal: 'External URL'
            linkText: 'Text Button'
            send_button: 'Update and send test email'
            slug: Identifier
            test: 'Test email address'
            title: Title
        list:
            creation: 'Created on'
            disable: 'Enabled: click to disable'
            enable: 'Disabled: click to enable'
            header: 'Email Templates'
            lang: 'Available in'
            slug: Identifier
            title: Subject
            update: 'Updated on'
    page:
        add: 'Create a page'
        draft: Draft
        list:
            author: Owner
            creation: Created
            header: 'Static Pages'
            lang: Languages
            modal:
                title: 'Select a Language'
            slug: 'Permalink (Slug)'
            status: Status
            title: Title
            update: Updated
        modal:
            cancel_confirm: 'Are you sure you want to cancel the creation of this content ?'
        published: Published
    payloads:
        detail:
            title: 'Payload : %id%'
        list:
            company_name: 'Company name'
            creation: Date
            header: 'Payloads list'
            identifier: Identifier
            payload_id: 'Payload id'
            status: Status
            type: Type
        statuses:
            status_created: CREATED
            status_failed: FAILED
            status_success: SUCCESS
        types:
            cost_center_payload: 'COST CENTER'
            invoice_payload: INVOICE
            order_payload: ORDER
            purchase_request_item_payload: 'PURCHASE REQUEST'
    redirect:
        add: Redirect
        list:
            confirm_delete: 'Delete the redirection?'
            confirm_delete_title: Confirmation
            creation: Created
            destination: Destination
            header: Redirect
            origin: Origin
            type: Type
            update: Updated
    search_historization:
        company_name: 'Company name'
        date: Date
        datemax: ' to '
        datemin: 'Search date from : '
        filter_label: Filter
        id: id
        is_anonymous: 'Is Anonymous'
        list:
            filter:
                date: 'Filter by search date'
        nb_hits: 'Number of hits'
        offer_name: 'Product title'
        offer_sku: SKU
        searched_term: 'Searched Term'
        user_full_name: 'User full name'
    shipping_points:
        add: 'Add a new shipping address'
        delete: 'Delete Shipping Address'
        delete_card: Delete
        edit: 'Modify Shipping Address'
        edit_card: Edit
        error_add: 'An error occurred during the creation of the Shipping Address'
        error_edit: 'An error occurred during the modification of the Shipping Address'
        form:
            name: 'Name of Shipping Address'
            save: Save
        shipping_points: 'Shipping Addresses'
        success_add: 'Shipping Address Created'
        success_delete: 'Shipping Address Deleted'
        success_edit: 'Shipping Address Modified'
    site:
        activation:
            ko: 'An error occurred while activating the cost center'
            ok: 'Cost Center activated'
        add: 'Add a Cost Center'
        chargement:
            title: 'Loading Sites'
        deactivate:
            content: 'Deactivate the site?'
            title: 'Site Deactivation'
        deactivation:
            ko: 'Error while deactivating the cost center'
            ok: 'Cost Center deactivated'
        delete: Delete
        filter_clear: Clear
        filter_title: Filters
        form:
            address: Address
            address2: 'Additional address details'
            city: City
            company: Company
            contact:
                email: Email
                firstname: 'First Name'
                function: 'Job Title'
                lastname: 'Last Name'
                main: 'Main Contact'
                other: Contact
                phone1: 'Main Phone number'
                phone2: 'Phone number'
            country: Country
            document:
                legalesDoc: 'Prefectural authorizations'
                title: Documents
            id: ID
            info: 'General Information'
            name: 'Cost Center'
            operatorInfos:
                canPack: 'Ability to pack for export'
                chargeWay: 'Charge way'
                device: Device
            region: Region
            siret: SIRET
            zipCode: 'Zip Code'
        infos: Information
        invalidate:
            ko: 'An error occurred while refusing the cost center'
            ok: 'Cost Center has been ???'
        list:
            add: 'Add a new site'
            address: Address
            city: City
            company: Company
            contactName: Contact
            contactPhone: 'Phone number'
            country: Country
            editName: 'Edit'
            filter:
                all: 'All Cost Centers'
                disabled: Deactivated
                submit: Filter
                toConfirmed: 'Pending Approval'
            name: 'Cost Center Name'
            nbShippingPoints: 'Number of Shipping Addresses'
            status: Status
            status_site:
                disabled: Deactivated
                enabled: Enabled
            zipCode: 'Zip Code'
        livraison:
            title: 'Cost Centers'
        modification:
            edit_name: 'Edit Cost Center'
            ko: 'An error occurred while modifying the cost center'
            ok: 'Cost Center has been updated'
        suppression:
            ok: 'Cost Center deleted'
        validate:
            error: 'Please validate the company before validating the cost center'
            ko: 'An error occurred while validating the cost center'
            ok: 'Cost Center added'
    slider:
        commons:
            add: Add
            addTitle: 'Add a new object to the slider'
            backgroundImage: 'Background Image'
            create: Create
            editTitle: 'Modify object on the slider'
            link: Link
            lock: Locked
            order: Order
            published: Published
            setTitleVisible: 'Make the title visible on the homepage'
            status: Status
            title: Title
            update: Edit
        create:
            confirm: 'Slider object added'
            error: 'An error occurred during object creation'
        delete:
            confirm: 'Slider object deleted'
            error: 'An error occurred during object deletion'
        edit:
            actualImage: 'Current Image'
            imageP: 'Select a new image to modify the existing one'
        list:
            actions: Actions
            createdAt: Created
            header: 'Slider object list'
            lang: Languages
            updatedAt: Modified
        status:
            draft: Draft
            published: Published
        update:
            confirm: 'Slider object modified'
            error: 'An error occurred during object modification'
    ticket:
        filter:
            closed: Closed
            company: Company
            creationMax: and
            creationMin: 'Created between'
            export_csv: 'Export en CSV'
            main_contact: 'Ticket creator'
            modificationMax: and
            modificationMin: 'Updated between'
            object: Subject
            opened: Open
            submit: Filter
            title: 'Thread list'
    user:
        change_type:
            content: 'Are you sure you want to change this buyer account type ?'
            title: 'Change buyer account type'
        connection:
            browser: Browser
            date: Date
            hour: Hour
            ip: IP
            os: OS
            title: 'Login History'
            type: Type
            version: Version
        deactivate:
            content: 'Deactivate the user?'
            title: 'User Deactivation'
        filter:
            activeBtn: 'Show all users'
            all: 'All Users'
            connectionMax: 'and :'
            connectionMin: 'Last Login between:'
            creationMax: 'and :'
            creationMin: 'Creation date between:'
            disabled: 'Deactivated Users'
            enabled: 'Active Users'
            filter_clear: 'Clear (or Delete)'
            filter_title: Filter
            inactiveBtn: 'Show Inactive users'
            toConfirmed: 'Confirm User'
        form:
            account: Account
            activate: Activate
            activation:
                ko: 'An error occurred during user activation'
                ok: 'User has been activated'
            company: Company
            confirmPassword: 'Confirm new password'
            creation:
                ko: 'An error occurred during the user creation'
                mailKo: 'This email is already being used'
                mailKoDisabled: 'This email is already being used on an inactive account. You can reactivate that account.'
                ok: 'User has been created'
            deactivate: Deactivate
            deactivation:
                ko: 'An error occurred during user deactivation'
                ok: 'User has been deactivated'
            edit: Edit
            email: Email
            firstname: 'First Name'
            id: ID
            invalidate: Invalidate
            language: Language
            lastname: 'Last Name'
            modification:
                ko: 'An error occurred during user modification'
                ok: 'User has been updated'
            password: 'New password'
            phone1: 'Main Phone number'
            phone2: 'Phone number'
            reject: Reject
            resetPassword: 'Reset Password'
            resetingPassword:
                ko: 'An error occurred during reset password'
                ok: 'Reset password has been sent'
            role: Role
            sites: 'Cost Center'
            sitesUserKo: 'A user must be added to the Cost Center'
            status: Status
            validate: Approved
        function: 'Job Title'
        history:
            class:
                AppBundle\Entity\Address: Address
                AppBundle\Entity\Company: Company
                AppBundle\Entity\ComplementaryInformation: 'Complementary information'
                AppBundle\Entity\Contact: 'Contact information'
                AppBundle\Entity\Country: Country
                AppBundle\Entity\Document: Document
                AppBundle\Entity\NodeContent\mail: 'Content Template'
                AppBundle\Entity\NodeContent\page: 'Content Page'
                AppBundle\Entity\Node\mail: 'Email Template'
                AppBundle\Entity\Node\page: 'Static Page'
                AppBundle\Entity\Redirect: 'Set redirection rules'
                AppBundle\Entity\Region: Region
                AppBundle\Entity\Setting: Configuration
                AppBundle\Entity\Site: Site
                AppBundle\Entity\User: User
                AppBundle\Entity\ZipCode: 'Zip Code'
                Open\TicketBundle\Entity\Ticket: Ticket
                Open\TicketBundle\Entity\TicketMessage: 'Ticket Message'
            date: Date
            hour: Hour
            id: Identification
            modifications: Modifications
            new: New
            objet: 'Modified Subject'
            old: Old
            operation: Operation
            title: History
            type:
                create: Created
                delete: Delete
                update: Updated
        list:
            activeStatus: Active
            allStatus: All
            company: Company
            creation: 'Account Creation'
            disable: Disable
            disabled: Deactivation
            email: Email
            enable: Enable
            firstname: 'First Name'
            inactiveStatus: Inactive
            lastLogin: 'Last Login'
            lastname: 'Last Name'
            phone: 'Phone number'
            role: Role
            sites: 'Cost Center'
            status: Status
        role:
            all: All
            main:
                ROLE_API: API
                ROLE_BUYER_ADMIN: 'Account manager'
                ROLE_BUYER_BUYER: Requestor
                ROLE_BUYER_PAYER: Buyer
                ROLE_OPERATOR: Operator
                ROLE_SUPER_ADMIN: Administrator
            secondary:
                ROLE_API: API
                ROLE_BUYER_ADMIN: 'Account manager'
                ROLE_BUYER_BUYER: Requestor
                ROLE_BUYER_PAYER: Buyer
                ROLE_OPERATOR: Operator
                ROLE_SUPER_ADMIN: Administrator
buyerMenu:
    complete_profile: 'Complete your profile information'
    complete_profile_why: 'Complete the information below before purchasing products.'
cart:
    accept:
        error: 'An error occurred during the validation of the assignment.'
    add:
        error: 'An error occured while the product was added to your cart'
        success: 'Product successfully added to your cart'
        successUpdate: 'Product successfully updated on your cart'
    article: item
    articles: articles
    assign:
        assign_to: USER
        assign_to_myself: 'Assign cart to myself'
        comment: COMMENT
        error: 'An error occured during assignation.'
        modal_btn_cancel: Cancel
        modal_btn_confirm: Assign
        modal_title: 'Assign my cart'
        noUser: 'No user to assign cart'
        success: 'Cart correctly assigned.'
    assignment_history: 'Assignment history'
    buttons:
        assign: Assign
        back_to_cart: 'Back to cart'
        checkout: 'Proceed to checkout'
        clear_cart: 'Clear cart'
        continue_shopping: 'Continue shopping'
        save_in_wishlist: 'Save for later'
        see_assignment: 'See assignment history'
        validate: Validate
    checkout:
        accounting_email: 'Accounting email'
        add_new_address: 'Add a new address'
        add_new_cost_center: 'Add a new cost center'
        address: Address
        address_complement: 'Address complement'
        area: Area
        assign:
            error: 'There is missing information to assign your cart (cost center, delivery address, billing address, email…)'
        billing_address: 'Billing Address'
        city: City
        cost_center: 'Cost center'
        minimum_order_amount: 'One or several merchant sub-order do not reach the minimum order amount. Please modify your cart'
        notReady: 'This feature is not available yet'
        payment_mode: 'Payment mode'
        select:
            address_placeholder: 'Choose an address'
            cost_center_placeholder: 'Choose a cost center'
            error: 'Please select a cost center, a delivery address and a payment mode'
            error_billing: 'There are missing data to validate your cart (cost center, delivery address, billing address, email…)'
            payment_mode_placeholder: 'Choose a payment mode'
        title: 'Proceed to checkout'
        validation_number: 'Buyer Order Number'
        zipcode: 'Zip code'
        term_and_conditions_must_accept: 'All terms and conditions must be accepted to validate your order'
        buyer_need_to_accept_term_and_conditions: "Buyer needs to accept the vendor general sales conditions to validate the order"
    cost_center:
        comments: Comments
        contact:
            email: Email
            name: Name
            title: 'Reception - General information shared with the vendor'
        error: 'An error occured while refreshing Cost Center.'
        noSiteId: 'Please select a cost center and an address'
        packaging_request:
            label: 'Packaging instructions'
            none: 'No packaging specifications. You can configure it on your Cost Center page'
        requested_documents:
            label: 'Select required documents for the delivery'
            none: 'No documents required. You can configure it from your Cost center page'
    days: days
    detail: 'Cart details'
    empty:
        back: 'Back to search page'
        no_user_title: 'Cost center without user'
        text: 'Your Shopping Cart lives to serve. Give it purpose.</br>Continue shopping on the search page.'
        title: 'Your Shopping Cart is empty'
        user_list: 'No user avaible for this cost center.'
    fca_info: 'This product will be made available at this address : '
    fca_shipping: ' + Shipment'
    fca_warning: 'For FCA products, see the vendor delivery address.'
    historic:
        comment: Comment
        date_assign: 'Assignment date'
        user_assigned: 'User assigned'
        user_who: 'User who assigned'
    lostItem:
        multiple_removed: '%number% offers are no longer available and have been removed from your cart'
        single_removed: 'one offer is no longer available and has been removed from your cart'
    notification_message:
        offer_price_changed: 'Offer price changed'
    pending:
        ability_to_assign: 'ability to assign'
        ability_to_pay: 'ability to pay'
        amount_excl_taxes: 'Amount taxes excl.'
        cart_reference: 'Cart reference'
        creation_date: 'Creation date'
        last_comment: 'Last comment'
        me: Me
        now_assigned_to: 'Now assigned to'
        number_of_products: 'Number of products'
        number_of_sellers: 'Number of vendors'
        previous_assignments: 'Previous assignments'
        rejected: rejected
    quotation:
        error: 'An error occurred while calculating the quotation'
    reject:
        comment: 'Reasons for refusal'
        error: 'Error while rejecting cart'
        success: 'Cart rejected with success'
        title: Reject
    remove:
        error: 'An error occured while removing your product from your cart'
    select:
        placeholder: 'Please choose a cost center'
    step:
        step_1: 'Products selection'
        step_2: 'Delivery options'
        step_3: 'Order confirmation'
    table_label:
        buyer_internal_reference: 'Buyer Reference'
        cart_item_comment: 'Comment'
        delivery_time: 'Delivery time'
        expected_date: 'Expected delivery date'
        no_vat: 'No VAT for this transaction'
        product_detail: 'Product Details'
        product_name: 'Product Name'
        purchase_request_id: 'Purchase Request ID'
        quantity: Quantity
        shippingtotal: 'Total Shipping (excl VAT)'
        subtotal: 'Subtotal (excl tax)'
        subtotal_vat: Sub-Total
        taxes: Taxes
        total: 'Total (excl tax)'
        total_cart: 'Total cart (excl tax)'
        total_order: 'Total order amount'
        total_price: 'Total Price'
        total_vat: Total
        unit_no_price: 'No price'
        unit_price: 'Unit Price'
    update_quantity:
        error: 'An error occurred while changing the quantity.'
    warning: Warning
    wrong_price_message: 'The price for one or several items has changed. Please re-assign the cart to update the offer(s)'
category:
    all_categories_placeholder: 'All categories'
    category_bafv: 'BAFV customer'
    category_new: 'New customer'
    category_normal: 'Normal customer'
    category_premium: 'Premium customer'
    category_undefined: 'To define'
cgu:
    accept: 'Accept the terms and conditions of use'
    error: 'Error during the acceptance of general terms of use'
    errorMessage: 'Please validate the GTU before submitting your company'
    read: 'Read and accept the terms and conditions of use'
    validated: 'You have accepted <a href="%link%" target="_blank">GTU</a>.'
company:
    form:
        address: 'Company address'
        back: Back
        billingService: 'Department Name'
        billing_address:
            address: 'Billing Address'
            title: 'Billing Address'
            use_main: 'Use the company headquarter address'
        businessRegistration: 'Company identification'
        cgu: 'General Terms of Use'
        fullAuto: 'API order full auto'
        eCatalog: 'E-catalog'
        check: 'Billing address differs from the main address?'
        company_info: 'Company information'
        contact:
            add: 'Add a Contact'
            adv: 'Sales Administration Contact'
            billing: 'Billing Contract'
            check:
                adv: 'Check the box to enter a different ADV address'
                billing: 'Check the box to enter a different billing address'
                logistic: 'Check the box to enter a different logistic address'
            logistic: 'Logistics contact'
            logistic_subtitle: 'Add contacts for each site'
            main: 'Main Contact'
        contract: 'Send the signed contract'
        document:
            title: 'Add documents'
            titleReadOnly: Documents
        edit-site: 'Modify the Cost Center'
        endpointUrl: 'Middleware url'
        finish: Finish
        iban: 'IBAN legal person'
        ident_number:
            invalid: Invalid
        identification: 'Identification Number'
        identity: 'Identification document of the signatory'
        info:
            cgu_not_accepted: 'General Terms and Conditions of Use must be signed to continue'
            company_registration: 'Your user account has been created, please fill the form below to submit your company information and finalize registration.'
            incomplete: 'Company information is incomplete'
            operator_needed: 'Some company information cannot be modified. Please contact StationOne via the Contact Us form to request modifications.'
        legal_documents: 'Legal Documents'
        main_contact:
            title: Address
        middleware: 'Middleware info'
        name: 'Legal Company Name'
        next: Next
        password:
            change: 'Change my password'
        profile: 'My Profile'
        save: Save
        service: Service
        siren: SIREN
        siren_placeholder: '(9 numbers)'
        social: Company
        submit: Submit
        submit_infos: 'Register your company'
        tax_rate: 'VAT rates'
        title: 'Administrative Company Information'
        type: Type
        update:
            error: 'An error occurred during the modification of the Shipping Address'
            success: 'Company Information updated'
company_catalog:
    add_in_catalog: 'Add my own reference'
    buyer_reference: 'Reference in my catalog'
    cancel: Cancel
    custom_search:
        title: 'Search in my catalog'
    delete: 'Delete my catalog'
    delete_confirm: 'Are you sure you want to delete your catalog?'
    delete_confirmed: 'Catalog deleted'
    delete_error: 'Only account managers can delete the file'
    delete_reference: Delete
    export: 'Export my catalog'
    export_matching: 'Export matching references'
    export_mismatching: 'Export mismatching references'
    import:
        total_matching_references: 'Number of imported matching references:'
        total_mismatching_references: 'Number of imported mismatching references:'
    import_in_progress: 'catalogue import is in progress'
    imported_references: 'imported references'
    instruction_title: 'Instructions :'
    instructions: 'You can upload your own product references onto the Marketplace. By doing so, it will give you possibility to see, search products under the given reference in your orders (only users from your company can see these references).</br></br>2 options to add your own references :</br>- On a product details page, press "My Own Reference" to add your own reference number/name to the selected product</br>- Import all your products references in the "My catalog" menu, using the .csv file template available <a href="%link%" target="_blank">here</a>.</br>Enter the manufacturer reference in the first column, and your own reference in the second column. Once done, upload your catalogue by clicking the upload button.</br></br>Once your file is uploaded, products can be searched under your own reference.</br></br>The catalogue is available for download at any time by clicking the "Export my own catalog" button.</br></br>The "Export missmatch references" button helps you to check the references added to "my catalog" which are not available on StationOne Marketplace.'
    overwrite: 'Warning, upload a new catalog will overwrite the last one. Do you really want to continue ?'
    save: Save
    title: 'My Catalog'
    upload: 'Upload my catalog'
    upload_success: '%count% reference(s) added.'
    wrong_file_format: 'Wrong file format.'
comparaisonSheet:
    add:
        error: 'An error occured while the product was added to the comparator'
        itemAlreadyexist: 'The product is already in the comparator'
        maxItemError: 'You cant add product in the comparator. Max : %maxItem%'
        success: 'Product added to the comparator'
    back_to_home: 'Back to homepage'
    comment: 'Add a comment you want to see on the comparator sheet pdf : '
    export: Export
    information: 'This comparison is only valid at the time of generation of this pdf. StationOne does not commit to holding these price over time.'
    no_article: 'Your comparison sheet is empty'
    page:
        author: Author
        comment: Comment
        company_name: 'Company name'
        date: 'Comparator sheet date'
        delivery: Delivery
        discount: Discount
        no_discount: 'no discount'
        no_price: 'Price upon request'
        price_excl_tax: 'Price excl. tax'
        product: Product
        title: 'Price Comparison Sheet'
        unit_price: 'Unit price'
        unit_price_vat: 'Unit price converted'
    sticky: Comparator
contact:
    adv: 'Sales Administration Contact'
    form:
        email: Email
        firstname: 'First Name'
        function: 'Job Title'
        lastname: 'Last Name'
        phone1: 'Main Phone number'
        phone1_placeholder: 'Mandatory Phone number'
        phone2: 'Phone number'
    logistic: 'Logistics contact'
contactMerchant:
    add_file: 'Add a file'
    attachment_limit: 'maximum %limit% attachments'
    authorized_types: '(Allowed files  : pdf, jpeg, gif, png, tiff)'
    file_too_big: 'the maximum size of an attachment is %size-limit%'
    form:
        error: 'An error occurred while sending message'
        message: Message
        object: Object
        save: Send
        success: 'Your message has been sent'
    message:
        object:
            feedback: 'Feedback from %buyer%'
            quotation: 'Commercial request from %buyer%'
            technical: 'Technical request from %buyer%'
contactWAYLF:
    add_file: 'Add a file'
    attachment_limit: 'maximum %limit% attachments'
    authorized_types: '(Allowed files  : pdf, jpeg, gif, png, tiff)'
    company_name: 'Company Name'
    email: Email
    file_too_big: 'The maximum size of an attachment is %size-limit%'
    first_name: 'First Name'
    last_name: 'Last Name'
    message: Message
cookie:
    accept: 'I accept'
    message: 'By clicking on I accept, you accept the terms and conditions of use, including the use of cookies to perform statistics of audiences.'
cost_center:
    name:
        first: 'Main Cost Center'
country:
    australia: Australia
    austria: Austria
    azerbaijan: Azerbaïjan
    bahrain: Bahrain
    belgium: Belgium
    brasil: Brazil
    bulgaria: Bulgaria
    canada: Canada
    canary_islands: 'Canary Islands'
    china: China
    croatia: Croatia
    cyprus: Cyprus
    czech_republic: 'Czech Republic'
    denmark: Denmark
    estonia: Estonia
    finland: Finland
    france: France
    germany: Germany
    greece: Greece
    hongkong: 'Hong Kong'
    hungary: Hungary
    ident:
        australia: 'A.C.N. - Australian Company Number'
        austria: 'intra-community VAT number'
        azerbaijan: 'Tax Identification Number'
        bahrain: 'Company Registration Number'
        belgium: 'intra-community VAT number'
        brasil: 'CNPJ Number (N° de TVA)'
        bulgaria: 'intra-community VAT number'
        canada: DUNS
        canary_islands: 'CIF Number'
        china: 'RNCN (reg number China)'
        croatia: 'Company Registration Number'
        cyprus: 'intra-community VAT number'
        czech_republic: 'intra-community VAT number'
        denmark: 'intra-community VAT number'
        estonia: 'intra-community VAT number'
        finland: 'intra-community VAT number'
        france: 'intra-community VAT number'
        germany: 'intra-community VAT number'
        greece: 'intra-community VAT number'
        hongkong: 'RNHK (reg number Hong Kong)'
        hungary: 'intra-community VAT number'
        india: 'CRO Number for EBSG or VAT number India'
        ireland: 'intra-community VAT number'
        italy: 'intra-community VAT number'
        latvia: 'intra-community VAT number'
        lithuania: 'intra-community VAT number'
        luxembourg: 'intra-community VAT number'
        malta: 'intra-community VAT number'
        mexico: 'RFC (Mexico)'
        morocco: 'RNMA (reg number Morocco)'
        netherlands: 'intra-community VAT number'
        norway: 'intra-community VAT number'
        peru: 'Single Register of Taxpayers'
        poland: 'intra-community VAT number'
        portugal: 'intra-community VAT number'
        qatar: 'Company Registration Number'
        romania: 'Intra-community VAT number'
        russia: 'OGRN (ident RU)'
        saudi_arabia: 'Company Registration Number'
        senegal: 'Company Registration Number'
        singapore: 'CRO number or CRO Number for EBSG'
        slovak_republic: 'intra-community VAT number'
        slovenia: 'intra-community VAT number'
        spain: 'intra-community VAT number'
        sweden: 'intra-community VAT number'
        switzerland: 'VAT Identification Number'
        taiwan: 'RNTW (company registration number- reg number Taiwan)'
        thailand: 'CRO or ROC number'
        tunisia: 'RNTN (RN Tunisia)'
        turkey: 'TAXTR (tax identifier for Turkey)'
        united_arab_emirates: 'Company Registration Number'
        united_kingdom: 'intra-community VAT number'
        united_states: 'DUNS Number'
    ident_helper:
        australia: '9 numbers'
        austria: 'ATU + 8  digits'
        azerbaijan: '10 digits corresponding to your TIN code (Tax Identification Number)'
        bahrain: '15 digits'
        belgium: 'Use intra-community VAT number format'
        brasil: '14 numbers'
        bulgaria: 'Use intra-community VAT number format'
        canada: '9 numbers'
        canary_islands: 'A + 8 digits'
        china: '18 numbers / letters maximum'
        croatia: 'HR + 11 digits'
        cyprus: 'Use intra-community VAT number format'
        czech_republic: 'Use intra-community VAT number format'
        denmark: 'Use intra-community VAT number format'
        estonia: 'Use intra-community VAT number format'
        finland: 'Use intra-community VAT number format'
        france: 'Use intra-community VAT number format'
        germany: 'DE + 9 digits'
        greece: 'Use intra-community VAT number format'
        hongkong: '4 - 8 numbers'
        hungary: 'Use intra-community VAT number format'
        india: '2 numbers & 13 characters'
        ireland: 'Use intra-community VAT number format'
        italy: 'Use intra-community VAT number format'
        latvia: 'Use intra-community VAT number format'
        lithuania: 'Use intra-community VAT number format'
        luxembourg: 'Use intra-community VAT number format'
        malta: 'Use intra-community VAT number format'
        mexico: '12 - 13 characters'
        morocco: '35 characters maximum'
        netherlands: 'Use intra-community VAT number format'
        norway: 'Use intra-community VAT number format'
        peru: '11-digit format'
        poland: 'Use intra-community VAT number format'
        portugal: 'Use intra-community VAT number format'
        qatar: '5 digits'
        romania: '"RO" + 2/10 digits'
        russia: '13 - 15 numbers'
        saudi_arabia: 'AS + 15 digits'
        senegal: '5 letters + 4 digits + 1 letter + 5 digits'
        singapore: '9 numbers maximum & 1 letter'
        slovak_republic: 'Use intra-community VAT number format'
        slovenia: 'Use intra-community VAT number format'
        spain: 'Use intra-community VAT number format'
        sweden: 'Use intra-community VAT number format'
        switzerland: CHE-XXXXXXXXX
        taiwan: '8 numbers'
        thailand: '13 numbers'
        tunisia: '10 - 11 characters'
        turkey: 'Use intra-community VAT number format'
        united_arab_emirates: '15 digits'
        united_kingdom: 'Use intra-community VAT number format'
        united_states: '9 numbers'
    india: India
    ireland: Ireland
    italy: Italy
    latvia: Latvia
    lithuania: Lithuania
    luxembourg: Luxembourg
    malta: Malta
    mexico: Mexico
    morocco: Morocco
    netherlands: Netherlands
    norway: Norway
    peru: Peru
    poland: Poland
    portugal: Portugal
    qatar: Qatar
    region:
        CA-AB: Alberta
        CA-BC: 'British Columbia'
        CA-MB: Manitoba
        CA-NB: 'New Brunswick'
        CA-NL: 'Newfoundland and Labrador'
        CA-NS: 'Nova Scotia'
        CA-NT: 'Northwest Territories'
        CA-NU: Nunavut
        CA-ON: Ontario
        CA-PE: 'Prince Edward Island'
        CA-QC: Quebec
        CA-SK: Saskatchewan
        CA-YT: Yukon
        FR-ARA: Auvergne-Rhône-Alpes
        FR-BFC: Bourgogne-Franche-Comté
        FR-BRE: Brittany
        FR-COR: Corsica
        FR-CVL: 'Centre-Val de Loire'
        FR-GES: 'Grand Est'
        FR-GF: 'French Guiana'
        FR-GP: Guadeloupe
        FR-HDF: Hauts-de-France
        FR-IDF: 'Paris Region'
        FR-MQ: Martinique
        FR-NAQ: Nouvelle-Aquitaine
        FR-NOR: Normandy
        FR-OCC: Occitanie
        FR-PAC: 'Provence-Alpes-Côte dAzur'
        FR-PDL: 'Pays de la Loire'
        FR-RE: Réunion
        FR-YT: Mayotte
    romania: Romania
    russia: Russia
    saudi_arabia: 'Saudi Arabia'
    senegal: Senegal
    singapore: Singapore
    slovak_republic: 'Slovak republic'
    slovenia: Slovenia
    spain: Spain
    sweden: Sweden
    switzerland: Switzerland
    taiwan: Taiwan
    thailand: Thailand
    tunisia: Tunisia
    turkey: Turkey
    united_arab_emirates: 'United Arab Emirates'
    united_kingdom: 'United Kingdom'
    united_states: 'United States'
currency: currency
customs:
    info:
        domestic: Domestic
        export_EU: 'Export EU'
        export_non_EU: Export
default:
    placeholder: Undetermined
dispute:
    create:
        ko: 'An error occurred during the sending of your dispute'
        ok: 'Your dispute has been sent'
    form:
        message: Comments
        new: Dispute
        placeholder: 'Explain here issues faced with products selected'
        products: 'Select products with issues'
        see: 'See all disputes'
        subject: Subject
        table:
            all: All
            expected_date: 'Expected date'
            product_name: 'Product name'
            quantity: Quantity
            reference: Reference
            total_price: 'Total price'
            unit_price: 'Unit price'
        title: Dispute
    list:
        creation_date: 'Creation date'
        id: Id
        messages: Messages
        read: Read
        receiver: Receiver
        subject: Subject
        unread: Unread
document:
    contract:
        download: 'Download here pre-filled contract'
    noDoc: 'No file -- Add here an official document proving the existence of your company'
    upload:
        delete: 'Would you like to delete this file?'
        deleteError: 'An error occurred while deleting the file'
        error: 'Please upload at least 1 document'
        ignored: '1 or more files have been ignored??'
        ko: 'An error occurred during upload'
        mime: 'The document type should include: %contrainte%'
        ok: 'Document uploaded'
        size: 'The document should not exceed %contrainte% Mo'
        title: Select
        type: 'Invalid file'
        typeOrSizeError: 'An error occurred while updating the file. Please verify the document.'
        working: 'In progress'
error:
    forbidden:
        code: '(Error 403)'
        description: 'You dont have sufficient access to view the content of this page. Please contact the support team.'
        title: 'Access denied'
    generic:
        help: 'The following links may help:'
        home: 'Back to Home Page'
        support: 'Contact the Support Team'
    internal:
        code: '(Error 500)'
        description: 'An error occurred during the progress of the request. Please contact the support team.'
        title: 'An error occurred'
    notfound:
        code: '(Error 404)'
        description: 'Page requested does not exist. Please contact the support team.'
        title: 'Page not found'
    zipCode: 'Zip Code should contain 5 numbers'
filter:
    all: All
    'no': 'No'
    'yes': 'Yes'
flag: '#icon-flag-en'
footer:
    about_us:
        code_of_conduct: 'Code of conduct'
        company: 'The company'
        cookies: Cookies
        data_privacy_chart: 'Data Privacy Charter'
        join_us: 'Join us'
        legal_notice: 'Legal notice'
        our_mission: 'Our mission'
        title: 'About us'
    additional:
        alstom: StationOne
        copyright: '© 2018 StationOne. All rights reserved.'
        purchase_conditions: 'Purchase conditions according to General Terms of Use of Station One and then to the sales conditions of each vendor.'
    buy:
        benefits_for_buyers: 'Benefits for buyers'
        create_an_account: 'Create an account'
        general_terms_and_conditions: 'General Terms of Use'
        title: 'Buy on StationOne'
    follow_us:
        title: 'Follow us'
    help:
        call_us_at: 'Call us at:'
        contact_us: 'Send a message'
        illegal_content: 'Illegal content alert'
        phone_number_1: '+33 6 59 35 58 54'
        questions: 'Q&A'
        title: Help
    mentions: 'Legal notice'
    newsletter:
        placeholder: 'Email address'
        title: 'Sign up to our newsletter'
    press:
        blog: Blog
        news: News
        press_releases: 'Press releases'
        title: 'Press & News'
    sell:
        benefits_for_sellers: 'Benefits for vendors'
        create_an_account: 'Create an account'
        general_terms_and_conditions: 'General Terms of Use'
        title: 'Sell on StationOne'
    visit:
        address_1: '69-73 Boulevard Victor Hugo'
        address_2: '93400 Saint-Ouen-sur-Seine'
        address_3: FRANCE
        title: 'Visit us'
form:
    invoice:
        search: Search
    order:
        search: Search
    user:
        add: 'Add a User'
        definition:
            account_manager: 'Account Manager'
            account_manager_definition: 'Buyer rights + Global account administration'
            buyer: Buyer
            buyer_definition: 'Requestor rights + Orders approval for his cost centers'
            requestor: Requestor
            requestor_definition: 'Select products, prepare orders and send them for approval to the buyer'
        delete:
            content: 'Would you like to delete this user ?'
            content_for_default_user: 'Would you like to delete this user ? <br/> Watch-out this user is necessary for automatic order process, please reassign another user at cost center page (<a href="/en/company/sites">cost centers</a>)'
            title: 'Delete a User'
        email: Email
        error_update: 'An error occurred while updating the user. Please, check the entered data'
        firstname: 'First Name'
        function: 'Job Title'
        invalid_role: 'Invalid Role'
        lastname: 'Last Name'
        myself: Myself
        role:
            admin: 'Account manager'
            buy: Requestor
            pay: Buyer
            placeholder: 'Select a Role'
            sell: Order
            view: View
        roles: Profile
        save_edit: Update
        save_new: Create
        sites:
            label: 'Cost Center:'
            mandatory: 'Select at least one Cost Center'
        success_new: 'User has been created'
        success_update: 'User has been updated'
        title: 'List of Users'
        title_common: User
        title_edit: Edit
        title_new: Add
front:
    site:
        no_default_user: 'No default user is assign'
generic:
    cancel: Cancel
    delete: Delete
    save: Save
header:
    address: Address
    buyer_id: 'Buyer ID'
    company: Company
    delivery_delay: 'Estimated delivery date'
    incoterm: Incoterm
    purchase_order: 'Purchase order'
    site: 'Cost center'
    tva_identification_number: 'VAT Number'
    vendor_id: 'Vendor ID'
    vendor_ref: 'Vendor''s reference'
home:
    autres_industriels: 'Other industrialists'
    collectivite_locale: 'Local community'
    dashboard_admin: 'Administrator Dashboard'
    description: 'Cum Homerici cum Roma nobilium'
    disclaimer:
        market:
            line1: 'More opportunities'
            line2: 'Plus de gisements'
            line3: 'Market Price'
            title: 'Market access'
        process:
            line1: 'Time saving'
            line2: 'Reporting and dashboard'
            line3: 'Lettre de voiture digitale'
            title: 'An efficient process'
        transactions:
            line1: 'Transparent (photos & characterization)'
            line2: 'Audit of vendors & buyers'
            line3: 'Inviting sales contract'
            title: 'Secured transaction'
        video: 'Discover on video'
    grande_distribution: 'Large retailers'
    insert:
        all_news: 'All news'
        contact: Contact
        news_description: 'Alstom will supply Île-de-France Mobilité and the RATP with 20 MP14 metros, consisting of 5 cars each, for line 11 of the Paris metro for an amount worth 157 million euros.'
        savoir: 'See more'
        sustainability_description: 'Meeting todays needs without compromising tomorrow'
        title: FAQ
        title_duree: Sustainability
        title_news: 'Latest news'
    login: 'Login buyer/vendor'
    login_admin: 'Administrator Login / Operator'
    logout: Logout
    register: Subscribe
    register_buyer: Subscription
    register_merchant: 'Vendor registration'
    register_merchant_confirmation_message: 'Your registration was successful.'
    register_merchant_confirmation_title: Congratulations
    register_merchant_confirmation_url: 'Your registration request has been successfully submitted and will be reviewed by the StationOne team'
    register_merchant_error: 'An error occurred during your subscription. Please contact the support team.'
    register_merchant_success: 'Your registration was successful.'
    register_merchant_tva_not_checked: 'The identification number could not be checked for this vendor. Please proceed to a manual check'
    slider:
        subtitle: 'Fruticeta prona valido inmanium fortiter'
        title: 'Cum Homerici cum Roma nobilium'
    start_buy: 'Start purchasing (or buying)?'
    start_sale: 'Start selling'
    ticket_admin: 'Ticket List (administrators)'
    ticket_anonymous: 'Create a ticket anonymously'
    ticket_buyer: 'List of Tickets'
    title: Home
    video_src: MECmgIz36nU
    why_buy:
        certified: Qualified
        certified_text: 'A catalog of quality suppliers selected by StationOne'
        fast: Fast
        fast_text: 'One website and thousands of references to optimize your supplies'
        simple: Simple
        simple_text: 'A secure payment system and insured delivery times'
        title: 'Why buy on StationOne?'
illegal_content:
    form:
        ko: 'An error occurred while sending your illegal content'
        ok: 'Your request has been sent successfully'
        save: Send
        title: 'Describe the illegal content that you detected'
import:
    csv:
        invalid_separator: 'list separator not recognized and/or wrong column count'
        no_error: 'Import OK'
        not_all_lines_were_imported: 'Not all the lines were imported'
        not_enough_lines: 'Not enough line in this file'
invoice:
    detail:
        already_invoiced: 'Already invoiced :'
        credit_note: 'Credit note'
        frame_contract: 'Frame contract'
        go_back: 'Back to order ID : '
        including_taxes: 'incl. taxes'
        invoice: Invoice
        invoice_amount: Amount
        invoice_date: Date
        invoice_due_date: 'Due Date'
        invoice_payment: Payment
        not_paid: 'Not paid'
        not_yet_invoiced: 'Not yet invoiced :'
        order_amount: 'Total :'
        order_date: 'Date :'
        order_id: 'Sub-order Id :'
        order_payment_status: 'Payment status :'
        order_seller: 'Vendor :'
        order_status: 'Status sub-order :'
        paid: Paid
        payment: Payment
        refund: Refund
        remaining_to_pay: 'Remaining amount to pay'
        title: Invoices
        total: Total
        total_invoiced: 'Total invoiced and credit notes :'
        total_paid: 'Total paid :'
        total_remaining_to_pay: 'Total remaining amount to pay :'
    export:
        invoice_amount: 'Invoice amount'
        invoice_creation_date: 'Invoice creation date'
        invoice_number: 'Invoice No.'
        order_creation_date: 'Order creation date'
        order_number: 'Order No.'
        payment_status: 'Invoice Status'
        vendor_name: 'Vendor Name'
    list:
        amount: Amount
        date: Date
        due_date: 'Due date'
        due_date_total_eur: 'To pay this month (€):'
        due_date_total_usd: 'To pay this month ($):'
        export: Export
        from: 'From date'
        invoice: 'Invoice / Credit note'
        late_payment_eur: 'Late payment (€):'
        late_payment_usd: 'Late payment ($):'
        order: Order
        paid_date: 'Paid date'
        remain: Balance
        seller: Vendor
        tab:
            empty: 'You have no invoices in this section.'
            not_paid: 'Not paid invoices'
            paid: 'Paid invoices'
        title: Invoices
        to: 'Until date'
        total_to_pay_eur: 'Total invoice(s) received (€):'
        total_to_pay_usd: 'Total invoice(s) received ($):'
    reminder: 'Invoice reminder'
    seeInvoices: 'Invoices and payments'
key: EN
keyToIgnore: ''
label_next: Next
label_previous: Previous
login:
    companyError: 'Company has been deactivated'
    userCreatedButCompanyError: 'We are pleased to inform you that your company account has been submitted and we thank you for your interest.<br />Your account submission will be reviewed by the StationOne team as soon as possible.'
main_menu:
    account: 'My Account'
    buyer:
        mybids: 'My bids'
        myorders: 'My orders'
    categories: Categories
    concept: Concept
    contact: Contact
    contact_us: 'Contact Us'
    dashboard: Dashboard
    eur_cart: 'EUR CART'
    explore_categories: 'Search Categories'
    faq: FAQ
    help: Help
    how: ''
    join_us: 'Free registration'
    languages: Languages
    locked: 'Please complete the previous step to proceed.'
    merchant:
        myoffers: 'My deals'
        mysales: 'My sales'
        offers:
            active: Active
            draft: Drafts
            nosale: 'Without translation'
            pending: 'Pending Validation'
    messages: Messages
    offers: 'See all deals'
    products: Products
    signin: Login
    terms: 'Terms & Conditions'
    usd_cart: 'USD CART'
    e_catalog_label: 'E-Catalog'
menu:
    desktop:
        api_doc: 'Api documentation'
        company: Company
        document: Documents
        invoices: Invoices
        my_catalog: 'My catalog'
        orders: Orders
        payment_modes: 'Payment modes'
        pending_carts: 'Pending carts'
        profile: Profile
        purchase_request: 'Purchase Requests'
        sites: 'Cost Centers'
        stats: Statistics
        users: Users
        wishlist: 'Kit List'
merchant:
    name: Vendor
    update: 'Request to see the price was updated with success'
merchant_order:
    status:
        canceled: Canceled
        confirmed: Confirmed
        finalized: Finalized
        initial: Initial
        partially_refunded: 'Partially refunded'
        payment_authorized: 'Payment authorized'
        processed: Processed
        received: Received
        return_in_progress: 'Return in progress'
        sent: Sent
modal:
    cancel: Cancel
    confirm: Ok
    'no': 'No'
    'yes': 'Yes'
month: Month
'no': 'No'
node:
    form:
        author:
            label: Owner
        body:
            label: Message
        checkboxLinkType:
            label: 'Redirect to external URL'
        content: Content
        delete:
            content: 'Delete static page?'
            error: 'An error occurred during the deletion'
            success: 'Static Page deleted'
            title: 'Delete static page'
        error:
            template_validation: 'An error occurred while validating content for "%locale%" language: %message%'
        header:
            edit: 'Content Update (%type) :'
            new: 'New Content'
        lang:
            button: Add
            de: German
            en: English
            es: Spanish
            fr: French
            it: Italian
            nl: Nederlands
            select: 'Select a Language'
        sections:
            content: Content
            main: General
        slug:
            help: Slug
            label: Permalink
        status:
            label: Status
        submit:
            create: Create
            error:
                create: 'An error occurred during the creation'
                update: 'An error occurred during the update'
            success:
                create: 'The page has been created'
                delete: 'The page has been deleted'
                update: 'Static Page updated'
            update: Update
        template:
            choices:
                default: Default
                default_with_faq: 'Default FAQ'
                default_with_products: 'Default Product List'
                default_with_products_and_faq: 'Default Product List & FAQ'
                fullwidth: 'Full screen'
                fullwidth_with_faq: 'Full screen FAQ'
                fullwidth_with_products: 'Full screen Product List'
                fullwidth_with_products_and_faq: 'Full screen Product List & FAQ'
            label: Canevas
        test: 'Test emails have been successfully sent to your email address: %email%'
        textLinkExternal:
            label: 'External URL'
        title:
            label: Title
offer_detail:
    add_error: 'You already have this product in your cart.'
    add_success: 'Your product is successfully added to your cart.'
    add_to_cart: 'Add to cart'
    add_to_wishlist: 'Add to kit list'
    add_wishlist_success: 'Your product has been successfully added to your kit list'
    anonym_contact_merchant: 'Please login for contacting the vendor'
    anonymous:
        add_to_cart: 'You must be authenticated to add product to your cart'
        not_authorized: 'Not authorized'
    ask_question: 'Contact the vendor'
    ask_title: 'You need support ?'
    ask_vendor: 'Request access to catalog prices'
    ask_vendor_message_content: 'Hello,\nCould you please grant me access to your catalog prices ?\nThank you\n'
    ask_vendor_no_price_offer_message_content: 'Hello,\nCould you please propose your best price of this offer ?\nQuantity expected :\nDelivery date expected :\n\nThank you\n'
    ask_vendor_pending: 'Access to catalog prices requested'
    ask_vendor_price: 'Request this price to the vendor'
    ask_vendor_price_reference: 'Request a price for item %reference%'
    ask_vendor_rejeted: 'Catalog prices not available'
    back_to_search: 'Back to search results'
    buy_more_for_discount: 'Buy more and get discount'
    company_code: 'Company code :'
    company_not_valid: 'Please complete your company informations before checking out your cart'
    contact_seller:
        modal:
            send: Send
            title: 'Write your message to %vendor%'
    contact_the_vendor: 'You can send a message to the vendor through the product page'
    continue_shopping: 'Continue shopping'
    description: 'See More'
    expired_offer: 'This product is no longer available on StationOne. Please contact the vendor for further information.'
    frame_contract_valid_date: 'Valid until'
    from_company: 'From company :'
    in_stock: 'On stock'
    login: Login
    not_available_country: 'This product is not available in your country. Please contact the vendor for further information.'
    not_batch_size_multiple: 'The quantity must be a multiple of %batchSize%.'
    not_business_everywhere: 'This product cannot be sold in your country. Please contact StationOne for further information'
    on_demand: 'On demand'
    on_stock: 'On stock'
    out_of_stock: 'Out of stock'
    out_of_stock_description: 'This product is no longer in stock on StationOne. Please contact the vendor for further information.'
    price_quantity: 'For %quantity% %unit%'
    proforma:
        0: proforma
        version: 'Proforma Version'
    proforma_pdf:
        quantity: 'Quantity requested'
        title: proforma
        total_price: 'Total Price (excl. Tax)'
    quantity: Quantity
    related_products: 'Associated Services'
    see_cart: 'See my cart'
    see_less: 'See less'
    see_more: 'See more'
    see_wishlist: 'See my kit list'
    seller:
        label: 'Vendor''s presentation'
        link_product: 'See all products'
    sign_in_to_buy: 'Sign in to buy'
    similar_products: 'Associated Products'
    title: 'Product Details'
    too_much_quantity: 'The selected quantity exceed the stock (maximum: %max%).'
    too_small_quantity: 'The quantity selected is less than the minimum order (minimum: %min%).'
    total_price: 'Total price'
    wrong_quantity: 'Please enter a valid amount of quantity.'
    warranty_period: 'Guarantee %month% months'
offers:
    bidbutton_msg: 'Bid on your favorite offers soon'
    description: 'All deals'
    title: 'All deals'
orders:
    createCart:
        errorMoq: 'The item %reference% could not be added to the cart because the minimum order quantity is less than the quantity requested'
        errorNoPrice: 'The item %reference% could not be added to the cart because it has no price'
        errorPrice: 'The item %reference% has been added to the cart, however the price has changed.'
        errorStatus: 'The item %reference% could not be added to the cart because the item and / or the vendor are disabled.'
        errorStock: 'The item %reference% could not be added to cart because the remaining stock on this product is less than the quantity requested'
    detail:
        go_back: 'Go back to orders list'
        shipping:
            delivered: 'Delivered %separator%'
            last_delivery: 'Last delivery %separator%'
            title: 'Shipping offers'
        title: Order
        tracking:
            delivery_date: 'Delivery date'
            error: 'We can''t display tracking info for the moment'
            quantity: 'Quantity %separator%'
            status_delivered: Delivered
            status_pickuped: 'Pick-up done'
            title: 'Delivery tracking'
            vendor_ref: 'Vendor''s ref %separator%'
    empty:
        title: 'No order'
    export:
        address: 'Delivery address'
        amount: 'Order Amount (tax excl.)'
        amountTaxIncluded: 'Order Amount (tax incl.)'
        amountVat: 'Order VAT Amount'
        buyerRef: 'Buyer Ref'
        costCenter: 'Cost center'
        currency: Currency
        date: 'Order date'
        documentRequirement1: 'Document requirement 1'
        documentRequirement10: 'Document requirement 10'
        documentRequirement2: 'Document requirement 2'
        documentRequirement3: 'Document requirement 3'
        documentRequirement4: 'Document requirement 4'
        documentRequirement5: 'Document requirement 5'
        documentRequirement6: 'Document requirement 6'
        documentRequirement7: 'Document requirement 7'
        documentRequirement8: 'Document requirement 8'
        documentRequirement9: 'Document requirement 9'
        expectedDeliveryDate: 'Expected Delivery Date'
        frameContractNumber: 'Frame contract number'
        id: 'Order id'
        internalBuyerOrderId: 'Buyer''s ERP Order id'
        itemPrice: 'Order line amount (tax excl.)'
        orderLine: OrderLine
        packagingRequirement1: 'Packaging requirement 1'
        packagingRequirement2: 'Packaging requirement 2'
        packagingRequirement3: 'Packaging requirement 3'
        paymentTerms: 'Payment Terms'
        productName: 'Product Name'
        quantity: Quantity
        status: Status
        subOrderId: 'Sub Order Id'
        unitPrice: 'Unit price (tax excl.)'
        validationNumber: 'Buyer Order Number'
        vendorName: 'Vendor Name'
        vendorRef: 'Vendor Ref'
    list:
        address: 'Delivery address'
        block:
            addition_information: 'Additional information'
            additional_information: 'Additional information'
            address_title: 'Delivery address:'
            billing_address_title: 'Invoicing address:'
            buyer_internal_order_id: 'Buyer''s internal Order ID'
            cost_center_title: 'Cost center:'
            date_title: 'Date of order:'
            expectedDate: '1st expected delivery:'
            iban_account_name: 'IBAN account name:'
            iban_number: 'IBAN Number:'
            key: 'Reconciliation key:'
            order_line: 'Order line'
            order_title: 'Order id:'
            packaging_specifications: 'Packaging instructions'
            payment_information: 'Payment Information:'
            payment_terms: 'Payment terms:'
            requested_documents: 'Required documents'
            status: 'Status:'
            total_title: 'TOTAL AMOUNT ORDER:'
            validation_number_title: 'Buyer Order Number: '
        date: 'Date of order'
        detail: Detail
        download_pdf: Download.PDF
        export: Export
        id: 'Order id'
        link:
            buy_again: 'Buy again'
            details: Details
            export: 'PDF export'
            invoice: Facture
            refund: Refund
            document: Document
            documents: Documents
        merchant_product: item
        merchant_products: items
        sub_order_id: 'Sub order ID'
        tab:
            cancelled: 'Cancelled Orders'
            empty: 'You have no orders in this section.'
            past: 'Past Orders'
            running: 'Running Orders'
        total: Total
    status:
        pending_creation: 'Pending creation'
        status_0: 'Pending payment'
        status_110: 'Issue solved'
        status_11111111: 'Pending creation'
        status_2000: Cancelled
        status_3000: Deleted
        status_60: 'Pending vendor confirmation'
        status_80: Confirmed
        status_85: 'Shipment & Invoice'
payment:
    error: 'An unexpected error occurred during payment process. Your order has been cancelled. If the error persists, please, contact the support'
    form:
        not_authorized: 'You are not authorized to request term payment mode. Please contact your manager.'
        submit: 'Request authorization for term-payment'
    pre_cc_mode:
        error: 'A technical error occurred while retrieving your order information %id%. If the problem persists, please contact support'
        ko: 'An error occurred during the payment: Your order %id% has been cancelled'
        ok: 'Your payment has been successfully performed. Your order %id% is now authorized'
        success:
            text: 'Your order %id% has been confirmed in pre-payment by credit card.'
            text_2: 'You can already check your purchase in the order page.'
            text_3: 'The vendor(s) will soon confirm your purchase.'
            title: Congratulations!
        title: 'Payment verification'
        title_error: 'Technical error'
        title_ko: 'Payment failure'
        title_ok: 'Payment confirmed'
        waiting_status: 'Please wait, waiting for the payment authorization for your order %id% ...'
    pre_virement_mode:
        pending:
            text: 'Your order has been registered in pre-payment by bank transfer.'
            text_2: 'You will receive an email with all details to proceed to the payment.'
            text_3: 'Once your payment is done, vendor(s) will see your order and will be able to confirm it.'
            text_4: 'Please take into account that the leadtime displayed on the order might be slightly longer than expected as it depends on the time your organisation needs to perform the bank transfer, until its reception by StationOne.'
            text_link1: 'Go back to search page'
            text_link2: 'See my orders'
            title: Congratulations!
        success:
            payment_detail_number: 'Transaction number: %number%'
            payment_details: 'Here is the needed information to perform your bank transfer:'
            payment_details_iban: 'IBAN Number: %iban%'
            payment_details_iban_account_name: 'IBAN account name: %iban_account_name%'
            payment_details_key: 'Reconciliation key: %key%'
            text: 'Your order %numOrder% has been successfully confirmed. It will be processed as soon as your bank transfer is received'
            text_link1: 'Go back to search page'
            text_link2: 'See my orders'
            title: 'Order confirmed'
    select_mode:
        error:
            cc: 'Error while creation transaction. Please contact the support'
        preCreditCard: 'Pre-payment by credit card'
        preTransferWire: 'Pre-payment by bank transfer'
        select: Select
        termTransferWire: 'Term payment (45 days end of month, after issuing of invoice)'
        title: 'Select your payment mode'
    time_mode:
        error:
            text: 'An error occured occured while confirming your order. Please contact the support if the problem remains'
            text_link1: 'See my cart'
            text_link2: 'Go back to search page'
            text_link3: 'Contact support'
            title: 'Error while confirming your order'
        pending:
            text: 'Your order has been registered in payment at 45 days end of month after invoicing.'
            text_2: 'You will receive a confirmation from your vendor(s) directly on your order page.'
            text_3: 'Once you receive the invoice, you will be able to manage your payment.'
            text_link1: 'Go back to search page'
            text_link2: 'See my orders'
            title: Congratulations!
        succes:
            text: 'Your order %numOrder% has been successfully confirmed'
            text_2: 'You will receive an email with payment details (IBAN and reconciliation key) when the invoice is emitted.'
            text_link1: 'Go back to search page'
            text_link2: 'See my orders'
            title: 'Order confirmed'
payment_mode:
    Prepayment_creditcard: 'Pre-payment by credit card'
    Prepayment_moneytransfert: 'Pre-payment by bank transfer'
    Termpayment_moneytransfert: '45 days end of month, after issuing of invoice'
    ask_for_term_error: 'Error processing the request for a Term Payment Authorization'
    click_button: 'Click the button below to use the term payment.'
    enabled: 'Your account is also authorized to use bank transfer term-payment method.'
    info: 'The prepayment methods authorized by default are the bank card and the bank transfer.'
    pending: 'Payment authorization is under analysis'
    save_error: 'An error occurred while updating the payment'
    saved: 'Payment option has been updated'
    title: 'Payment modes'
product:
    about_seller: 'About vendor'
    application_categories: Categories
    buy: 'Purchase / Buy?'
    buyer_reference: 'Buyer''s ref'
    cart_item_comment: 'Comment'
    comparison: 'Comparison sheet'
    converted_price: 'Based on the daily exchange rate'
    delivery_time: 'Delivery time'
    description: Description
    info_button_buy: 'Products will be available soon. Create your account to be informed on the latest news.'
    info_converted_price: 'For information purposes only.'
    logistics_informations: 'Logistical Information'
    made_in: 'Manufacturing country'
    manufacturer: Manufacturer
    manufacturer_reference: 'Manufacturer''s ref'
    private: Private
    quantity: 'Available quantity'
    max_quantity: 'Buyable quantity'
    seller: Vendor
    seller_reference: 'Vendor''s ref'
    technical:
        bearing_type: 'Bearing type'
        code_command_rs: 'Code command RS'
        conditioning: Conditioning
        dtr: DTR
        flange_outer_diameter: 'Flange outer diameter'
        inner_diameter: 'Inner diameter'
        manufacturer_ref: 'Manufacturer References'
        marketplace_id: 'Marketplace ID'
        material: Material
        min_order_quantity: 'Minimum quantity'
        nb_row: 'Number of rows'
        outer_diameter: 'Outer diameter'
        rated_static_load: 'Rated static load'
        ring_width: 'Ring width'
        seller_name: 'Vendor name'
        seller_ref: 'Vendor referencce'
        termination_type: 'Termination type'
        trendmark: Brand
    technical_detail: 'Technical details'
    technical_details: 'Technical details'
products:
    all_offers: 'See all deals'
    home_list_header: 'All deals'
    home_product_list_header: 'Most searched products'
profile:
    form:
        email: Email
        firstname: 'First Name'
        lastname: 'Last Name'
        main_phone_number: 'Phone Number'
        optional_phone_number: 'Optional Phone Number'
        submit: Save
        update:
            error: 'An error occurred while updating your personal information'
            success: 'Your contact information has been successfully updated'
    password:
        help: '8 char min with one capital letter, number and spec char'
        submit: Apply
        title: 'Change your password'
proforma:
    address: Address
    address_title: 'Proforma from STATION ONE in the name and on behalf of'
    billing_address: 'Billing address'
    capital: 'to the capital of'
    condition:
        adress: 'StationOne, capital 20.000 Euros, 69/73 Boulevard Victor Hugo, 93400 Saint-Ouen (France). VAT NUMBER: FR18752364885'
        payment: 'Payment to be made to our representative'
        purchase: 'Purchase conditions according to General Terms of Use of Station One and then to the online sales conditions of %vendor_name% or to the contract number if any'
        title: 'Conditions of this offer'
    date: 'Proforma generated on'
    rcs: RCS
    siret: SIRET
    text: 'This proforma is only valid at the time of generation of this pdf. StationOne does not commit to holding this price over time.'
    title: proforma
    vat_number: 'VAT Number'
purchase_request:
    cart:
        add: 'Add to cart'
        remove: 'Remove from cart'
    clear: 'Clear page'
    detail: 'Purchase Requests'
    export: 'Export not found references'
    exportFound: 'Export found references'
    import:
        error:
            csv_format: 'Invalid file format. Only .csv format is accepted'
            csv_size: 'File size limit exceeded. Maximum file size is 2MB'
            internal_error: 'Error in launching the request, please contact StationOne'
        title: Import
    import_waiting: 'Please import a new file'
    instruction: 'The imported file must be under the following format : <a href="%link%" target="_blank">format_import_demande_achat.csv</a>'
    offer:
        batch_price: 'Price for 50 items'
        error_bafv: 'This product price is not available as you do not have access to this vendor''s catalogue prices. To request access, click on the product name and press "Request access to catalog prices" on the product page.'
        error_businesseverywhere: 'This product cannot be sold in your country. Reason : At least one stakeholder must be located in Europe'
        error_noprice: 'This product is available upon price request'
        manufacturer: Manufacturer
        merchant: Vendor
        quantity: Quantity
        quantity_available: 'Available quantity'
        see_more: 'See more'
        select: Select
        selected: Selected
        sku_price: 'Price for'
        unit_price: 'Unit price'
    pr_item:
        buyer_order_number: 'Buyer order number'
        buyer_reference: 'Buyer reference'
        cost_center: 'Cost center'
        details: Details
        expected_delivery_date: 'Expected delivery date'
        manufacturer_name: 'Manufacturer name'
        manufacturer_reference: 'Manufacturer reference'
        merchant: Vendor
        no_ref: 'No reference found'
        order_line: 'Order line'
        product_name: 'Product name'
        purchase_request_number: 'Purchase request number'
        quantity: Quantity
        quantity_expected: 'Expected quantity'
        ref: Reference
        see_more: 'See more'
        unit_price: 'Unit price'
        unit_price_of_reference: 'Unit price of reference'
    title: 'Purchase Requests'
redirect:
    form:
        delete:
            error: 'An error occurred during deletion'
            success: 'Redirection has been deleted'
        destination:
            help: 'Verify url'
            label: Destination
        header:
            edit: 'Redirection modification:'
            new: 'New redirection'
        origin:
            help: Slug
            label: Origin
        submit:
            error:
                create: 'An error occurred during the creation'
                update: 'An error occurred during the update'
            success:
                create: 'Redirection has been created'
                update: 'Redirection has been updated'
        type:
            help: '301 = permanent / 302 = temporary'
            label: 'Redirection type'
redislist:
    alert:
        delete_all: 'All Redis keys have been deleted.'
        error: 'An error has occurred ! The key %key% has not been deleted.'
        success: 'The Key %key% has been deleted !'
    delete_all_keys: 'Delete All'
    delete_all_keys_title: 'Delete all keys'
    delete_title: 'Delete the key %key%'
    no_key: 'No Redis Key stored.'
registration:
    buyer: Buyer
    error:
        identification_already_used: 'Identification has already been used'
        identification_already_used_alert: 'Your company already has an account on StationOne. Contact the account manager to grant you access. If you do not know who the account manager is, please send us a request through the contact form. Do you want to go to the contact form ?'
        technical: 'An error occurred during the creation of your account. Please contact the support team.'
        userDisabled: 'User has been deactivated. Please contact StationOne via the Contact Us form to request the reactivation of the user account'
    label: 'I want to join as'
    selectType: 'Choose your account'
    vendor: Vendor
resetting:
    check_email: 'An email has been sent. It contains a link to reset your password.</br></br> If you do not receive an email, check your spam folder or try resetting your password again.'
    newpwd: 'Reset password'
    request:
        submit: 'Reset password'
    reset:
        submit: Edit
search:
    advanced_search:
        submit: Search
        title: 'Advanced search'
    compatible_products_with: 'Compatible products with'
    departments:
        all_departments: 'All departments'
        bearing: Bearing
        brake_disc: 'Brake disc'
        camera: Camera
        filter: Filter
        glazing: Glazing
        screen: Screen
    filters: Filters
    help: Help
    in_compatible_products: 'Search in compatible products'
    no_custom_ref_found: 'No reference found in your catalog.'
    no_offer: 'Sorry, but we couldn''t find any results. '
    no_offer_in_catalog: 'There is no result that matches your search.'
    page: page
    pagination:
        next: Next
        'on': 'on'
        previous: Previous
    products: products
    result_label: 'Search Results (%total% results)'
    results: results
    results_for: for
    searchbar:
        advanced_search: 'Advanced search'
        custom_search: 'My catalog search'
        in_catalog: 'My catalog'
        in_marketplace: Marketplace
        in_product_compatibility: 'Compatible products'
        mobile_placeholder: 'Search a product'
        placeholder: 'Start typing to search for a product'
    show: Show
    sidebar:
        category: category
        commons: Commons
        departments: Categories
        refine_by: 'Refine by'
        search: 'Search term'
        see_more: 'See more'
        specific: Specifics
        up: '< Up'
    sort_by: Sort
    sort_form:
        delivery_time: 'By delivery time (lowest first)'
        newest: 'By offer date (newest first)'
        price_max: 'By price (highest first)'
        price_min: 'By price (lowest first)'
        relevance: relevance
    title: Search
security:
    login:
        create_account: 'Create an account'
        fail: 'Invalid credentials.'
        footer_text: 'Not a member yet?'
        login: 'Log in'
        role_denied: 'Your account has not authorized to connected'
        title: 'Buyer Login'
shipping:
    option:
        cheapest: 'Price optimized'
        fastest: Urgent
        no_merchant_shipping: 'This merchant does not offer a shipping service through StationOne'
        no_shipping_available: 'No shipping available. For more details, please contact StationOne'
        noshipping: 'No shipping'
shipping_point:
    form:
        address:
            label: Address
        comment:
            label: 'Comments (opening hours at goods reception, specificities to reach the reception desk,…)'
        contact:
            accountant: 'Accounting Department'
            label: 'Contact of the receptionist'
        documents_requests:
            label: 'Required documents for receipt of goods'
        first: 'Main Shipping Address'
        name: 'Name of Shipping Address'
        packaging_request:
            label: 'Packaging instructions'
            tips: 'max char = 255'
        save: Save
        title_common: 'Shipping Address'
        title_edit: Edit
        title_new: New
site:
    form:
        accountant_email: 'Invoices will be sent to the email address noted below'
        add: 'Add a Cost Center'
        add_contact: 'Add a Contact'
        add_modal: 'New Cost Center'
        add_shipping_address: 'Add a Shipping Address'
        adresse: Address
        afternoon:
            end: 'Closing Time (afternoon)'
            start: 'Opening Hours (afternoon)'
        authorization: authorization
        cancel: cancel
        comment: Comment
        complement: 'Additional Information'
        copy: Copy
        corporate_name: 'Company Legal Name'
        cpZipCode: 'Zip Code'
        create: Create
        created: 'Your Cost Center has been created'
        days: Days
        default_user:
            label: 'User to assign automatic cart'
            placeholder: 'User to assign automatic cart'
        delete:
            content: 'Delete the Cost Center?'
            shipping_point: 'Delete the Shipping Address'
        delete_shipping_address: Delete
        documentation_request_1: 'Required document 1'
        documentation_request_10: 'Required document 10'
        documentation_request_2: 'Required document 2'
        documentation_request_3: 'Required document 3'
        documentation_request_4: 'Required document 4'
        documentation_request_5: 'Required document 5'
        documentation_request_6: 'Required document 6'
        documentation_request_7: 'Required document 7'
        documentation_request_8: 'Required document 8'
        documentation_request_9: 'Required document 9'
        error: 'Error while creating your Cost Center ! Please fill all fields'
        friday: Friday
        identification: SIRET
        info:
            operator_needed: 'Certain site information can not be modified. Please contact StationOne via the Contact Us form to request modifications.'
        legales_doc: 'Legal Documents'
        main_contact: 'Main Contact'
        modify_shipping_address: Modify
        monday: Monday
        morning:
            end: 'Morning close'
            start: 'Morning open'
        name: 'Cost Center Name'
        opening_time: 'Opening time'
        other_contacts: 'Other Contact'
        packaging_request_1: 'Packaging instructions 1'
        packaging_request_2: 'Packaging instructions 2'
        packaging_request_3: 'Packaging instructions 3'
        placeholder:
            name: 'Cost center name'
        save: Save
        submit: Submit
        submitError: 'Register your company before enrolling your site'
        thursday: Thursday
        title: 'Cost Center'
        tuesday: Tuesday
        wednesday: Wednesday
    header:
        edit: 'Modify Cost Center:'
        list: 'Cost Center List'
    list:
        deactivation:
            ko: 'An error has occurred while deleting the Cost Center'
            ok: 'The Cost Center has been deleted'
            users_exist: 'Users are attached to this cost center, so you cannot delete this cost center'
        default_user:
            not_defined: 'No user is defined for this cost center'
            title: 'Default user for this cost center'
        no_user: 'No user affiliated'
        title: 'Affiliated users to this cost center'
        users:
            action: Action
            firstname: Firstname
            function: Function
            id: '#'
            lastname: Lastname
            role: Role
stats:
    accrued:
        'no': 'No'
        title: 'Cumulated amount'
        'yes': 'Yes'
    cost_center:
        all: All
        title: 'Cost center'
    orderAmount: 'Orders amount'
    year:
        title: Year
status:
    draft: 'To be completed'
    pending: 'Pending Operators validation'
    valid: 'Validated by the Operator'
system:
    setting:
        form:
            submit: Edit
    settings:
        homepage:
            disclaimer:
                title: 'Video ID'
                video_src: Video
            slider:
                title: 'Slider settings'
            title: 'Homepage Settings'
            video_1_src: 'YouTube video ID from Slide 2'
            video_2_src: 'YouTube video ID from Slide 3'
            video_en_src: 'Video in English'
            video_fr_src: 'Video in French'
        notifications:
            command:
                title: Orders
            completion_recall_number_of_day: 'Number of days before completion notification'
            title: Notifications
        offers:
            offer_1: 'Izberg identifier 1st popular offer'
            offer_2: 'Izberg identifier 2nd popular offer'
            offer_3: 'Izberg identifier 3rd popular offer'
            offer_4: 'Izberg identifier 4th popular offer'
            offer_5: 'Izberg identifier 5th popular offer'
            offer_6: 'Izberg identifier 6th popular offer'
            offer_7: 'Izberg identifier 7th popular offer'
            offer_8: 'Izberg identifier 8th popular offer'
            popular:
                title: 'Popular deals'
            title: 'Deal Settings'
        security:
            login:
                title: 'Login Security Settings'
            login_attempt_max: 'Maximum login attempts'
            login_banned_user_unlock_timeout: 'Unblock users banned after {x} minutes'
            title: 'Security Settings'
        testimonials:
            fader_speed: 'Testimonial rotation speed (in milliseconds)'
            max_age: 'Maximum age in months (testimonials greater than that date will not be visible)'
            max_items: 'Maximum number of visible testimonials'
            parameters:
                title: 'Testimonial Settings'
            title: Testimonials
        update_error: 'An error occurred while updating the settings'
        update_success: 'Settings updated'
tab_infos_seller:
    cgv: 'General sales condition'
    frame_contract: 'Under particular condition of frame contract : %frame_contract%'
    minimum_order_amount: 'Minimum order amount in the cart for this vendor: %amount%'
    presentation: 'Vendor presentation'
    see_all_products: 'See all products'
ticket:
    common:
        add_file: 'Add a file'
        administrator_user: '%fullname% (operator)'
        authorized_files_extensions_message: 'Only pdf, jpg, gif,png,tiff, xls, xlsx are allowed'
        file_reset: Reset
        message: Message
        nofiles: 'No file selected'
        number: 'Ticket number'
        subject: Subject
        submit: 'Send (Submit)'
        update: 'Send (Submit)'
    create:
        company: Company
        email: Email
        firstname: 'First Name'
        foradmin: StationOne
        forvendor: 'a vendor'
        function: 'Job Title'
        lastname: 'Last Name'
        message_text: Message
        phone: 'Phone number'
        recipient: 'Select a Company'
        title: 'You wish to contact :'
    edit:
        add: 'New Response'
        attachment: Attachment
        attachment_button: 'Add files'
        author: Owner
        close: 'Discussion closed'
        close_thread: 'This thread was closed by %firstname% %lastname% on %date%'
        close_thread_anonymous: 'This thread was closed by an anonymous user on %date%'
        closed: Solved
        company: Company
        date: 'Ticket created'
        id: ID
        link: 'Go back to threads list'
        message_label: Messages
        message_placeholder: 'Send a message'
        message_text: Response
        new_message: 'NEW MESSAGE :'
        operator: Operator
        recipient: Recipient
        reopen: Reopen
        save_message: SAVE
        subject: Subject
        timeFormat: 'à $1h$2mn$3s'
        title: Ticket
    error:
        create: 'An error occurred during the ticket creation'
        update: 'An error occurred during the ticket modification'
    list:
        actions: Actions
        add: 'Send a message'
        all: All
        author: 'Created by'
        closed: Solved
        company: Company
        createdAt: Created
        empty: 'You have no messages'
        export_csv: 'CSV Export'
        knp_next: Next
        knp_previous: Previous
        lastAt: Modified
        main_contact: 'Ticket created by'
        me: You
        nb_messages: 'Number of messages'
        next: 'Aging Tickets'
        number: ID
        opened: Open
        previous: 'Recent Tickets'
        sent_to: 'Sent to'
        status: Status
        sujet: Subject
        title:
            resolved: Resolved
            standard: Messages
    status:
        STATUS_CLOSED: Solved
        STATUS_INFORMATION_REQUESTED: 'Request Information'
        STATUS_INVALID: Invalid
        STATUS_IN_PROGRESS: 'In Progress'
        STATUS_NEW: New
        STATUS_ON_HOLD: Pending
        STATUS_OPEN: Open
        STATUS_OPERATOR_RESPONDED: Open
        STATUS_RESOLVED: Solved
        STATUS_USER_RESPONDED: Open
    success:
        create: 'Your request has been created'
        update: 'Your request has been modified'
    waylf:
        title: 'What are you looking for ?'
user:
    form:
        ROLE_API: API
        ROLE_BUYER_ADMIN: 'Account manager'
        ROLE_BUYER_BUYER: Requestor
        ROLE_BUYER_PAYER: Buyer
        ROLE_OPERATOR: Operator
        ROLE_SUPER_ADMIN: Administrator
        add: 'Create a user'
        company: Company
        email: Email
        firstname: 'First Name'
        function:
            0: ''
            label: 'Job Title'
            mandatory: 'Job title is mandatory'
        lastname: 'Last Name'
        phone1: 'Main Phone number'
        phone2: 'Phone number'
        role: Role
        site: 'Cost Centers'
    registration:
        email: Email
    resetting:
        title: 'Reset password'
validator:
    date: 'Please enter a valid date.'
    email: 'Please enter a valid email address.'
    number: 'Please enter a valid number.'
    remote: 'Please fix this field.'
    required: 'This field is required.'
    url: 'Please enter a valid URL.'
wishlist:
    add_new: 'Add a new kit list'
    add_to_cart: 'Add to cart'
    charge:
        confirm: 'This action will delete the contents of your cart before adding the offers from this list'
        error: 'An error occurred while adding offers to your cart'
        success: 'The offers from your list have been added to the cart'
    delete:
        error: 'An error occured during the deleting of your kit list'
        success: 'Your list has been deleted'
    delete_confirm: 'Are you sure you want to delete this wish list?'
    go_back: 'Go back to the list'
    item:
        delete:
            error: 'An error occured during the deleting of your offer'
            success: 'Your offer has been deleted'
        delete_confirm: 'Are you sure you want to delete this offer?'
        noItem: 'You have not offer in your list'
        update:
            error: 'An error occured during the updating of the quantity'
            success: 'The quantity has been updated'
    new_name: 'Name of the new list'
    none: 'You have no kit list'
    notification_message:
        no_price_offer: 'invalid kit list'
    save: Save
    save_error: 'An error occurred when saving the kit list'
    save_in: 'Add in kit list'
    save_success: 'Wishlist successfully saved'
    table:
        days: days
        delivery_time_item: 'Leadtime before Delivery'
        delivery_time_wishlist: 'Leadtime of kit List'
    title: 'Kit list'
'yes': 'Yes'
seller:
    general_condition: 'Sales general conditions'
