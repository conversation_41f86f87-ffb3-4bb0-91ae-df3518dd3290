address:
    form:
        address: Indirizzo
        address2: 'Riga supplementare (edificio / residenza / ...)'
        address2_placeholder: 'Edificio / Residenza / ...'
        all_country_placeholder: 'Tutti i paesi'
        check: Verifica
        city: Città
        country: Paese
        country_placeholder: '<PERSON>eg<PERSON> un paese'
        email: Email
        first_lastname: 'Cognome e nome del contatto'
        phone1: 'Telefono principale'
        phone1_placeholder: 'Questo telefono è richiesto'
        phone2: 'Telefono secondario'
        region: Regione
        region_placeholder: 'Scegli una regione'
        update:
            error: 'Si è verificato un errore durante l''aggiornamento dei dati dell''azienda'
            success: 'I dati dell''azienda sono stati aggiornati'
        zipcode: 'Codice postale'
address_form_all_country_placeholder: 'Tutti i paesi'
admin:
    logout: Logout
admin_menu:
    admin_users:
        add_user: 'Aggiungi un utente'
        admin_list: Amministratori
        label: 'Utenti amministratori'
        user_list: Utenti
    bafv_requests_menu:
        label: 'BAFV catalog prices requests'
    companies_menu:
        all_companies: 'Tutte le acquirenti'
        costs_centers: 'Centri di costo'
        label: Acquirenti
        users: Utenti
    dashboard:
        label: 'Pannello centrale'
    merchant_menu:
        label: Vendors
    message: Messaggi
    messages_menu:
        add: 'Crea una discussione'
        label: Messaggi
        list: 'Richieste aperte'
        resolved: 'Richieste risolte'
    orders: Ordini
    other_menu:
        api_doc: ''
        automatic_controls: 'Automatic Controls'
        feeback_form: 'Modulo di feedback'
        label: Altro
        logo: 'Logo object'
        notifications: Notifiche
        payloads: ''
        redis_keys: 'Elenco delle chiavi Redis'
        search_historization:
            label: 'Cronologia di ricerca'
        serches_list: 'Lista delle ricerche'
        slider: 'Elemento dello slider'
        term_payment: 'Richieste di pagamento differito'
        top_mismatch_catalog_references: 'Top mismatched catalog references'
    redirects:
        label: Reindirizzamenti
    sys_parameters:
        label: 'Impostazioni di sistema'
    web_content:
        add: 'Aggiungi contenuti web'
        label: 'Contenuto Web'
        list: 'Tutti i contenuti web'
        popular_offers: 'Offerte popolari'
algolia:
    attributes:
        BAB10_MOQ: 'Quantità minima'
        BAE10_Incoterm: Incoterm
        CAA10_DTR: DTR
        DQL10_Friction_coef: 'Coefficiente di attrito'
        Threshold_2_Prcie: 'Soglia di prezzo'
    id: 'ID Marketplace    '
    merchant:
        name: Venditore
    product:
        description: Descrizione
        manufacturer: Fabbricante
        name: Prodotto
back:
    admin:
        filter:
            activeBtn: 'Mostra amministratori attivi'
            inactiveBtn: 'Mostra amministratori inattivi'
    back: Indietro
    back_to_list: 'Torna alla lista'
    bafv_request:
        list:
            buyer_code: 'Buyer Code'
            buyer_name: 'Buyer Name'
            status: Status
            submitted_on: 'Request Submitted On'
            vendor_company_name: 'Vendor Company Name'
    catalogue_references:
        not_found_mismatch_references: 'Mismatched references not found'
        reference: reference
        top_mismatch_references_title: 'Top mismatched references'
        total: total
    commons:
        actions: Azioni
        add: Aggiungi
        all: Tutti
        cancel: Cancel
        delete: Elimina
        disable: Disattiva
        edit: Modifica
        export_csv: 'Esporta in CSV'
        id: Id
        piggy: 'Accedi come'
        piggy_exit: 'Disconnetti come'
        view: Visualizza
    company:
        all_term_payment: Tutti
        all_term_payment_title: 'Tutte le aziende con pagamenti differiti (convalidate o in attesa)'
        companyInfo:
            activation:
                ko: 'Errore durante l''attivazione dell’azienda'
                ok: 'L’azienda è stata attivata con successo'
            address: Indirizzo
            addressComplement: Integrazione
            billing:
                service: Ufficio
                title: Fatturazione
            city: Città
            contact:
                adv: 'Contatto per l''amministrazione vendite'
                billing: 'Indirizzo di fatturazione'
                firstname: Nome
                lastname: Cognome
                logistic: 'Contatto logistica'
                mail: Email
                main: 'Contatto principale'
                noInformation: 'Nessuna informazione'
                phone1: 'Telefono principale'
                phone2: 'Telefono secondario'
            country: Paese
            deactivate:
                content: 'Sei sicuro di voler disattivare questa azienda?'
                title: 'Disattivazione dell’azienda'
            deactivation:
                ko: 'Errore durante la disattivazione dell''azienda'
                ok: 'L''azienda è stata disattivata con successo'
            deactivationReason: 'Deactivation reason'
            document:
                title: Document
            edit: Modifica
            endpointUrl: ''
            info:
                code: 'Codice dell''azienda'
                name: 'Nome dell''azienda'
                title: 'Informazioni aziendali'
            invalidate:
                ko: 'Errore durante l’invalidazione dell''azienda'
                ok: 'L''azienda è stata invalidata con successo'
            region: Regione
            reject:
                ko: 'Error during rejected company'
                ok: 'Company has been rejected with success'
            rejectedReason: 'Rejected reason'
            termpayment:
                active: 'Abilita il pagamento differito'
                askKo: 'Si è verificato un errore durante la richiesta di pagamento differito'
                askOk: 'La richiesta di pagamento differito è stata completata con successo.'
                authorization: 'Termine dell''autorizzazione di pagamento'
                deny: 'Negare il pagamento differito'
                disabled: 'Il pagamento differito è stato disattivato.'
                enabled: 'La richiesta è stata accettata il'
                inactive: 'Disattiva il pagamento differito'
                pending: 'La richiesta è stata effettuata il'
                reason: 'Deny reason'
                removeKo: 'Si è verificato un errore durante l''eliminazione del pagamento differito'
                removeOk: 'La cancellazione del pagamento differito è stata completata con successo'
                title: 'Data di richiesta di pagamento a termine'
            validate:
                ko: 'Errore durante la convalida dell''azienda'
                ok: 'L''azienda è stata validata con successo'
            zipCode: 'Codice postale'
        endpointUrl: ''
        export: 'Esporta in CSV'
        filter_clear: CANCELLA
        filter_title: Filtri
        list:
            add:
                site: 'Aggiungi un centro di costo'
                user: 'Aggiungi un utente'
            category: Categoria
            city: Città
            costCenters: 'Centri di costo'
            country: Paese
            created: 'Creazione di account'
            empty: 'Nessuna azienda in attesa di convalida'
            filter:
                activeBuyer: 'Acquirenti attivi'
                all: 'Tutte le aziende'
                caMax: 'e:'
                caMin: 'Fatt. tra:'
                creationMax: 'e:'
                creationMin: 'Data di creazione tra:'
                disabled: Disattivate
                purchaseMax: 'e:'
                purchaseMin: 'Data dell''ordine tra:'
                submit: FILTRARE
                term_payment_date_max: 'e:'
                term_payment_date_min: 'Data di richiesta/accettazione tra:'
                toConfirm: 'Da convalidare'
                tonMax: 'e:'
                tonMin: 'Tonnellaggio tra:'
            identification: Codice
            last: 'Ultimo accesso'
            name: Azienda
            purchases: 'Acquisti (USD)'
            revenue: 'Fatt. generato'
            status: Status
            status_company:
                acceptable: 'In attesa di convalida'
                all: Tutte
                disabled: Disattivata
                draft: Bozza
                initial: Iniziale
                pending: 'In attesa di convalida'
                rejected: Rejected
                valid: Attivo
            termpayment_moneytransfert_accept: 'Convalida i pagamenti differiti'
            termpayment_moneytransfert_date_accepted: Accettato
            termpayment_moneytransfert_date_requested: Richiesto
            termpayment_moneytransfert_deny: 'Rifiuta i pagamenti differiti'
            termpayment_moneytransfert_enabled: Attivo
            termpayment_moneytransfert_pending: 'In attesa'
            tonnes: 'Tonnellate sul metro'
            type_:
                1: Tutti
                2: Acquirente
                all: Acquirente
                buyer: Acquirente
            user_type: 'Tipologia di utente'
            users: Utenti
        menu:
            general: Generale
            messages: Messaggi
            orders: Ordini
            sites: 'Centro di costi'
            users: Utenti
        order:
            creation_date: 'Order date'
            export: 'PDF export'
            number: 'Order number'
            price: Price
            shipping: 'Shipping address'
            status:
                cancelled: 'Cancelled Orders'
                past: 'Past Orders'
                running: 'Running Orders'
                title: Status
        pending_term_payment: 'In attesa'
        pending_term_payment_title: 'Tutte le aziende in attesa di pagamento differito'
        term_payment_empty: 'No company waiting for term payment validation'
        termpayment_moneytransfert_accepted: 'Hai accettato i pagamenti differiti per questa azienda'
        termpayment_moneytransfert_accepted_error: 'Errore nell''elaborazione dell''accettazione dei pagamenti differiti per questa azienda'
        termpayment_moneytransfert_rejected: 'Hai rifiutato i pagamenti differiti per questa azienda'
        termpayment_moneytransfert_rejected_error: 'Errore in fase di rifiuto di pagamenti differiti per questa azienda'
        users:
            create_ticket: 'Crea un ticket'
            term_payment_empty: 'Nessuna azienda in attesa di convalida per il pagamento differito'
    deactivate:
        ok: Ok
        reason: 'Reason ?'
    index_page:
        companies_term_payment_request_to_validate: 'Companies waiting for term payment acceptance'
        companies_to_validate: 'Companies waiting for validation'
        see_all: 'See all'
        unread_messages: 'Unread messages'
    logo:
        commons:
            add: Add
            addTitle: 'Add a new object to the logo'
            backgroundImage: Image
            create: Create
            editTitle: 'Modify object on the logo'
            link: Link
            lock: Locked
            order: Order
            published: Published
            setTitleVisible: 'Make the title visible on the homepage'
            status: Status
            title: Title
            update: Edit
        create:
            confirm: 'Logo object added'
            error: 'An error occurred during object creation'
        delete:
            confirm: 'Logo object deleted'
            error: 'An error occurred during object deletion'
        edit:
            actualImage: 'Current Image'
            imageP: 'Select a new image to modify the existing one'
        list:
            actions: Actions
            createdAt: Created
            header: 'Logo object list'
            lang: Languages
            updatedAt: Modified
        status:
            draft: Draft
            published: Published
        update:
            confirm: 'Logo object modified'
            error: 'An error occurred during object modification'
    merchant:
        form:
            update:
                error: 'Error while updating the vendor'
                success: 'Vendor has been successfully updated'
        list:
            email: Email
            firstname: 'First Name'
            identification: Identification
            lastname: 'Last Name'
            list: Email
            name: Name
            status:
                accepted: Accepted
                all: All
                pending: Pending
                rejected: Rejected
                title: Status
        merchantInfo:
            country: Country
            currency:
                eur: EUR
                placeholder: 'Choose a currency'
                title: Moneta
                usd: USD
            edit: Edit
            email: Email
            firstname: Firstname
            identification: Identification
            info:
                title: 'Vendor Information'
            lastModifiedAt: 'Last modified at'
            lastModifiedBy: 'Last modified by'
            lastname: Lastname
            name: Name
            password: Password
            phoneNumber: 'Phone number'
            registrationDate: 'Registration date'
            reject:
                ko: 'An error occurred while rejecting the vendor'
                ok: 'Vendor has been rejected with success'
            rejectedReason: 'Rejected reason'
            status: Status
            validate:
                ko: 'An error occurred while validating the vendor'
                ok: 'Vendor has been successfully updated'
    notification:
        edit:
            body: Corpo
            confirm: 'La notifica è stata aggiornata con successo'
            empty_email_error: 'Please, enter a valid email address'
            externalLinkType: 'Use an external URL'
            link: 'Link del pulsante'
            linkExternal: 'External URL'
            linkText: 'Testo del pulsante'
            send_button: 'Update and send test email'
            slug: Identificativo
            test: 'Test email address'
            title: Titolo
        list:
            creation: 'Creato il'
            disable: 'Attivato: fare clic per disattivare'
            enable: 'Disattivato: fare clic per attivare'
            header: 'Modelli di email'
            lang: Lingue
            slug: Identificativo
            title: Oggetto
            update: 'Aggiornato il'
    page:
        add: 'Crea una pagina statica'
        draft: Bozza
        list:
            author: Autore
            creation: 'Creato il'
            header: 'Pagine statiche'
            lang: Lingue
            modal:
                title: 'Scegli la lingua di visualizzazione'
            slug: 'Permalink (Slug)'
            status: Status
            title: Titolo
            update: 'Aggiornato il'
        modal:
            cancel_confirm: 'Are you sure you want to cancel the creation of this content ?'
        published: Pubblicata
    payloads:
        detail:
            title: 'Payload : %id%'
        list:
            company_name: ''
            creation: Date
            header: 'Payloads list'
            identifier: Identifier
            payload_id: ''
            status: Status
            type: Type
        statuses:
            status_created: CREATED
            status_failed: FAILED
            status_success: SUCCESS
        types:
            cost_center_payload: ''
            invoice_payload: ''
            order_payload: ''
            purchase_request_item_payload: 'PURCHASE REQUEST'
    redirect:
        add: 'Crea un reindirizzamento'
        list:
            confirm_delete: 'Sei sicuro di voler eliminare questo reindirizzamento?'
            confirm_delete_title: Conferma
            creation: 'Creato il'
            destination: Destinazione
            header: Reindirizzamenti
            origin: Origine
            type: Tipo
            update: 'Aggiornato il'
    search_historization:
        company_name: 'Company name'
        date: Date
        datemax: ' to '
        datemin: 'Search date from : '
        filter_label: Filter
        id: id
        is_anonymous: 'Is Anonymous'
        list:
            filter:
                date: 'Filter by search date'
        nb_hits: 'Number of hits'
        offer_name: 'Product title'
        offer_sku: SKU
        searched_term: 'Searched Term'
        user_full_name: 'User full name'
    shipping_points:
        add: 'Aggiungi un punto di consegna'
        delete: 'Elimina il punto di consegna'
        delete_card: Elimina
        edit: 'Modifica il punto di consegna'
        edit_card: Modifica
        error_add: 'Si è verificato un errore durante la creazione del punto di consegna'
        error_edit: 'Si è verificato un errore durante la modifica del punto di consegna'
        form:
            name: 'Nome del punto di consegna'
            save: Salva
        shipping_points: 'Punti di consegna'
        success_add: 'Il punto di consegna è stato creato con successo'
        success_delete: 'Il punto di consegna è stato cancellato con successo'
        success_edit: 'Il punto di consegna è stato cambiato con successo'
    site:
        activation:
            ko: 'Errore durante l''attivazione del centro di costo'
            ok: 'Il centro di costo è stato attivato con successo'
        add: 'Aggiungi un centro di costo'
        chargement:
            title: 'Siti di caricamento'
        deactivate:
            content: 'Sei sicuro di voler disattivare questo sito?'
            title: 'Disabilitare il sito'
        deactivation:
            ko: 'Errore durante la disattivazione del centro di costo'
            ok: 'Il centro di costo è stato disattivato con successo'
        delete: Elimina
        filter_clear: CANCELLA
        filter_title: Filtri
        form:
            address: Indirizzo
            address2: 'Indirizzo aggiuntivo'
            city: Città
            company: Azienda
            contact:
                email: Email
                firstname: Nome
                function: Funzione
                lastname: Cognome
                main: 'Contatto principale'
                other: Contatto
                phone1: 'Telefono principale'
                phone2: 'Telefono secondario'
            country: Paese
            document:
                legalesDoc: 'Autorizzazioni prefettizie'
                title: Documenti
            id: ID
            info: 'Informazioni generali'
            name: 'Nome del centro di costo'
            operatorInfos:
                canPack: 'Ability to pack for export'
                chargeWay: 'Charge way'
                device: Device
            region: Regione
            siret: SIRET
            zipCode: 'Codice postale'
        infos: Informazioni
        invalidate:
            ko: 'Errore durante l’invalidazione del centro di costo'
            ok: 'Il centro di costo è stato invalidato con successo'
        list:
            add: 'Aggiungi un nuovo sito'
            address: Indirizzo
            city: Città
            company: Azienda
            contactName: Contatto
            contactPhone: 'Recapito telefonico'
            country: Paese
            editName: 'Modifica'
            filter:
                all: 'Tutto il centro di costo'
                disabled: Disattivate
                submit: FILTRARE
                toConfirmed: 'Da convalidare'
            name: Cognome
            nbShippingPoints: 'Numero punti di consegna'
            status: Status
            status_site:
                disabled: Disattivato
                enabled: Attivato
            zipCode: 'Codice postale'
        livraison:
            title: 'Centri di costo'
        modification:
            edit_name: 'Edit Cost Center'
            ko: 'Errore durante la modifica del centro di costo'
            ok: 'Il centro di costo è stato aggiornato con successo'
        suppression:
            ok: 'La cancellazione del centro di costo è stata eseguita.'
        validate:
            error: 'È necessario convalidare l''azienda prima di convalidare questo centro di costo'
            ko: 'Errore durante la convalida del centro di costo'
            ok: 'Il centro di costo è stato validato con successo'
    slider:
        commons:
            add: Aggiungi
            addTitle: 'Aggiungi un nuovo elemento allo slider'
            backgroundImage: 'Immagine di sfondo'
            create: Crea
            editTitle: 'Modifica un elemento dello slider'
            link: Link
            lock: bloccato
            order: Order
            published: pubblicato
            setTitleVisible: 'Make the title visible on the homepage'
            status: Status
            title: Titolo
            update: Modifica
        create:
            confirm: 'L’elemento dello slider è stato aggiunto con successo.'
            error: 'Si è verificato un errore durante la creazione del nuovo elemento.'
        delete:
            confirm: 'L''elemento dello slider è stato rimosso con successo.'
            error: 'Si è verificato un errore durante l''eliminazione dell''elemento.'
        edit:
            actualImage: 'Immagine attuale'
            imageP: 'Seleziona una nuova immagine se vuoi cambiarla.'
        list:
            actions: Azioni
            createdAt: 'Creato il'
            header: 'Elenco degli elementi dello slider'
            lang: Lingue
            updatedAt: 'Modificato il'
        status:
            draft: Bozza
            published: Pubblicato
        update:
            confirm: 'L''elemento dello slider è stato modificato con successo.'
            error: 'Si è verificato un errore durante la modifica dell''elemento.'
    ticket:
        filter:
            closed: Closed
            company: Azienda
            creationMax: e
            creationMin: 'Data di creazione tra'
            export_csv: 'Export en CSV'
            main_contact: 'Creatore del messaggio'
            modificationMax: e
            modificationMin: 'Data di modifica tra'
            object: Oggetto
            opened: Open
            submit: Invia
            title: Filtro
    user:
        change_type:
            content: 'Are you sure you want to change this buyer account type ?'
            title: 'Change buyer account type'
        connection:
            browser: Browser
            date: Data
            hour: ora
            ip: IP
            os: OS
            title: 'Cronologia delle connessioni'
            type: Tipo
            version: Versione
        deactivate:
            content: 'Sei sicuro di voler disabilitare questo utente?'
            title: 'Disattivazione di questo utente'
        filter:
            activeBtn: 'Visualizza tutti gli utenti'
            all: 'Tutti gli utenti'
            connectionMax: 'e:'
            connectionMin: 'Ultima connessione tra:'
            creationMax: 'e:'
            creationMin: 'Data di creazione tra:'
            disabled: 'Utenti disattivati'
            enabled: 'Utenti attivi'
            filter_clear: Cancella
            filter_title: Filtrare
            inactiveBtn: 'Mostra gli utenti inattivi'
            toConfirmed: 'Utenti da convalidare'
        form:
            account: Account
            activate: Attiva
            activation:
                ko: 'Errore durante l''attivazione dell''utente'
                ok: 'L''utente è stato attivato con successo'
            company: Azienda
            confirmPassword: 'Conferma nuova password'
            creation:
                ko: 'Errore durante la creazione dell''utente'
                mailKo: 'Questo indirizzo email è già in uso.'
                mailKoDisabled: 'Questo indirizzo email è già utilizzato per un utente disattivato, puoi riattivarlo.'
                ok: 'L''utente è stato creato con successo'
            deactivate: Disattiva
            deactivation:
                ko: 'Errore durante la disattivazione dell''utente'
                ok: 'L''utente è stato disattivato con successo'
            edit: Modifica
            email: Email
            firstname: Nome
            id: ID
            invalidate: Invalida
            language: Linguaggio
            lastname: Cognome
            modification:
                ko: 'Errore durante la modifica dell''utente'
                ok: 'L''utente è stato aggiornato con successo'
            password: 'Nuova password'
            phone1: 'Telefono principale'
            phone2: 'Telefono secondario'
            reject: Reject
            resetPassword: 'Reimposta password'
            resetingPassword:
                ko: 'Errore durante l''invio della richiesta di reimpostazione della password'
                ok: 'La tua richiesta di reimpostazione della password è stata inviata'
            role: Ruolo
            sites: 'centro di costi'
            sitesUserKo: 'Un utente deve essere aggiunto ad almeno un centro di costo.'
            status: Status
            validate: Invia
        function: Funzione
        history:
            class:
                AppBundle\Entity\Address: Indirizzo
                AppBundle\Entity\Company: Azienda
                AppBundle\Entity\ComplementaryInformation: 'Complementary information'
                AppBundle\Entity\Contact: 'Informazioni di contatto'
                AppBundle\Entity\Country: Paese
                AppBundle\Entity\Document: Documento
                AppBundle\Entity\NodeContent\mail: 'Modello di contenuto'
                AppBundle\Entity\NodeContent\page: 'Pagina di contenuto'
                AppBundle\Entity\Node\mail: 'Modello di posta elettronica'
                AppBundle\Entity\Node\page: 'Pagina statica'
                AppBundle\Entity\Redirect: 'Regola di reindirizzamento'
                AppBundle\Entity\Region: Regione
                AppBundle\Entity\Setting: Configurazione
                AppBundle\Entity\Site: Sito
                AppBundle\Entity\User: Utente
                AppBundle\Entity\ZipCode: CAP
                Open\TicketBundle\Entity\Ticket: Ticket
                Open\TicketBundle\Entity\TicketMessage: 'Messaggio del ticket'
            date: Data
            hour: ora
            id: Identificativo
            modifications: Modifiche
            new: nuovo
            objet: 'Oggetto modificato'
            old: precedente
            operation: Operazione
            title: 'Cronologia delle azioni'
            type:
                create: Creazione
                delete: Eliminazione
                update: Aggiornamento
        list:
            activeStatus: Attivo
            allStatus: All
            company: Azienda
            creation: 'Creazione di account'
            disable: Disable
            disabled: Disattivazione
            email: Email
            enable: Enable
            firstname: Nome
            inactiveStatus: Inattivo
            lastLogin: 'Ultimo accesso'
            lastname: Cognome
            phone: 'Recapito telefonico'
            role: Ruolo
            sites: 'Centro di costo'
            status: Status
        role:
            all: Tutti
            main:
                ROLE_API: ''
                ROLE_BUYER_ADMIN: 'Acquirente (responsabile dell’account)'
                ROLE_BUYER_BUYER: Acquirente
                ROLE_BUYER_PAYER: Acquirente/Pagatore
                ROLE_OPERATOR: Operatore
                ROLE_SUPER_ADMIN: Amministratore
            secondary:
                ROLE_API: ''
                ROLE_BUYER_ADMIN: 'Responsabile dell’account'
                ROLE_BUYER_BUYER: Acquirente
                ROLE_BUYER_PAYER: Acquirente/Pagatore
                ROLE_OPERATOR: Operatore
                ROLE_SUPER_ADMIN: Amministratore
buyerMenu:
    complete_profile: 'Completa le informazioni del tuo profilo'
    complete_profile_why: 'È necessario completare le informazioni di seguito per iniziare a comprare prodotti.'
cart:
    accept:
        error: 'Si è verificato un errore durante la convalida dell’assegnazione.'
    add:
        error: 'Si è verificato un errore durante l''aggiunta del prodotto al carrello'
        success: 'Il tuo prodotto è stato aggiunto con successo al carrello'
        successUpdate: 'Il tuo prodotto è stato aggiornato'
    article: articolo
    articles: articoli
    assign:
        assign_to: UTENTE
        assign_to_myself: 'Assign cart to myself'
        comment: COMMENTO
        error: 'Si è verificato un errore durante l''assegnazione.'
        modal_btn_cancel: Annulla
        modal_btn_confirm: Assegna
        modal_title: 'Assegna il mio carrello'
        noUser: 'Nessun utente che assegna il carrello'
        success: 'Il carrello è stato assegnato con successo.'
    assignment_history: 'Cronologia delle assegnazioni'
    buttons:
        assign: Assegna
        back_to_cart: 'Torna al carrello'
        checkout: 'Effettua l''ordine'
        clear_cart: 'Cancella il carrello'
        continue_shopping: 'Continua a fare acquisti'
        save_in_wishlist: 'Risparmia per dopo'
        see_assignment: 'Vedi cronologia delle assegnazioni'
        validate: Convalida
    checkout:
        accounting_email: ''
        add_new_address: 'Aggiungi un nuovo indirizzo'
        add_new_cost_center: 'Aggiungi un nuovo centro di costo'
        address: Indirizzo
        address_complement: 'Indirizzo aggiuntivo'
        area: Regione
        assign:
            error: 'Mancano informazioni per assegnare il carrello (centro di costo, indirizzo di consegna, indirizzo di fatturazione, e-mail...)'
        billing_address: 'Indirizzo di fatturazione'
        city: città
        cost_center: 'Centro di costo'
        minimum_order_amount: 'Uno o più sottocomandi non raggiungono l''importo minimo dell''ordine richiesto dal commerciante. Si prega di modificare il carrello.'
        notReady: 'Questa funzione non è ancora disponibile'
        payment_mode: 'Modalità pagamento'
        select:
            address_placeholder: 'Scegli un indirizzo'
            cost_center_placeholder: 'Scegli un centro di costo'
            error: 'Seleziona un centro di costo, un indirizzo di consegna e una modalità di pagamento'
            error_billing: 'Mancano informazioni per convalidare il carrello (centro di costo, indirizzo di consegna, indirizzo di fatturazione, e-mail...)'
            payment_mode_placeholder: 'Scegli una modalità di pagamento'
        title: 'Procedi alla cassa'
        validation_number: 'Numero d''ordine dell''acquirente'
        zipcode: 'codice postale'
        term_and_conditions_must_accept: 'Tutti i termini e le condizioni devono essere accettati per convalidare il tuo ordine'
        buyer_need_to_accept_term_and_conditions: "L'acquirente deve accettare le condizioni generali di vendita del venditore per convalidare l'ordine"
    cost_center:
        comments: Commenti
        contact:
            email: Email
            name: Nome
            title: 'Reception - Informazioni generali condivise con il venditore'
        error: 'Si è verificato un errore durante l''aggiornamento del Centro di costo.'
        noSiteId: 'Si prega di selezionare un centro di costo e un indirizzo'
        packaging_request:
            label: 'Istruzioni per l''imballaggio'
            none: 'Nessuna istruzione di imballaggio. È possibile configurare le istruzioni di imballaggio dalla pagina del centro di costo'
        requested_documents:
            label: 'Seleziona i documenti richiesti per la consegna'
            none: 'Nessun documento richiesto. Puoi configurarli dalla tua pagina Centro di costo'
    days: giorni
    detail: 'Dettagli del carrello'
    empty:
        back: 'Torna alla pagina di ricerca'
        no_user_title: 'Centro di costo senza utente'
        text: 'Il tuo carrello è vuoto.</br>Continua il tuo acquisto sulla pagina di ricerca.'
        title: 'Il tuo carrello è vuoto'
        user_list: 'Nessun utente disponibile per questo centro di costo.'
    fca_info: 'Questo prodotto sarà reso disponibile al seguente indirizzo:'
    fca_shipping: ' + Spedizione'
    fca_warning: 'Per i prodotti FCA, vedere l''indirizzo di consegna del fornitore.'
    historic:
        comment: Commento
        date_assign: 'Data di assegnazione'
        user_assigned: 'Utente assegnato'
        user_who: 'Utente che ha assegnato'
    lostItem:
        multiple_removed: 'Le offerte %number% non sono più disponibili e sono state rimosse dal tuo carrello'
        single_removed: 'un''offerta non è più disponibile ed è stata rimossa dal carrello'
    notification_message:
        offer_price_changed: 'Il prezzo dell''offerta è cambiato'
    pending:
        ability_to_assign: 'Capacità di assegnare'
        ability_to_pay: 'capacità di pagare'
        amount_excl_taxes: 'Imposte escluse'
        cart_reference: 'Riferimento al carrello'
        creation_date: 'Data di creazione'
        last_comment: 'Ultimo commento'
        me: Me
        now_assigned_to: 'Ora assegnato a'
        number_of_products: 'Numero di prodotti'
        number_of_sellers: 'Numero di venditori'
        previous_assignments: 'Incarichi precedenti'
        rejected: respinto
    quotation:
        error: 'Si è verificato un errore durante il calcolo dell''offerta'
    reject:
        comment: 'Ragioni del rifiuto'
        error: 'Si è verificato un errore durante il rifiuto dell’assegnazione.'
        success: 'Carrello rifiutato con successo'
        title: Rifiutare
    remove:
        error: 'Si è verificato un errore durante la rimozione del prodotto dal carrello'
    select:
        placeholder: 'Si prega di scegliere un centro di costo'
    step:
        step_1: 'Selezione del prodotto'
        step_2: Consegna
        step_3: 'Conferma dell''ordine'
    table_label:
        buyer_internal_reference: 'Riferimento dell''acquirente'
        cart_item_comment: 'Commento'
        delivery_time: 'Tempo di consegna'
        expected_date: 'Data prevista'
        no_vat: 'Nessuna IVA per questa transazione'
        product_detail: 'Dettagli del prodotto'
        product_name: 'Nome del prodotto'
        purchase_request_id: 'ID richiesta di acquisto'
        quantity: Quantità
        shippingtotal: 'Totale spedizione (IVA esclusa)'
        subtotal: 'Subtotale (IVA esclusa)'
        subtotal_vat: Subtotale
        taxes: IVA
        total: 'Totale (IVA esclusa)'
        total_cart: 'Total cart (excl tax)'
        total_order: 'Ordine totale'
        total_price: 'Prezzo totale'
        total_vat: Totale
        unit_no_price: 'No prezzo'
        unit_price: 'Prezzo unitario'
    update_quantity:
        error: 'Si è verificato un errore durante la modifica della quantità.'
    warning: Attenzione
    wrong_price_message: 'Il prezzo di uno o più articoli è cambiato. Si prega di riassegnare il carrello per aggiornare le offerte'
category:
    all_categories_placeholder: 'All categories'
    category_bafv: 'BAFV customer'
    category_new: 'New customer'
    category_normal: 'Normal customer'
    category_premium: 'Premium customer'
    category_undefined: 'Per definire'
cgu:
    accept: 'Accetta le condizioni generali di utilizzo'
    error: 'Errore nell''accettare le condizioni generali di utilizzo'
    errorMessage: 'Si prega di confermare le CGU prima di sottoporre la tua azienda'
    read: 'Leggere e accettare le condizioni generali di utilizzo'
    validated: 'Hai accettato le CGU.'
company:
    form:
        address: 'Indirizzo aziendale'
        back: Precedente
        billingService: 'Nome Dipartimento'
        billing_address:
            address: 'Indirizzo di fatturazione'
            title: 'Ufficio fatturazioni'
            use_main: 'Utilizzare l''indirizzo della sede centrale'
        businessRegistration: 'Identificazione dell''azienda'
        cgu: 'Condizioni generali di utilizzo'
        fullAuto: 'API order full auto'
        eCatalog: 'E-catalog'
        check: 'Indirizzo di fatturazione diverso dall''indirizzo principale?'
        company_info: 'Informazioni sull''azienda'
        contact:
            add: 'Aggiungi un contatto'
            adv: 'Contatto per l''amministrazione vendite'
            billing: 'Contatto di fatturazione'
            check:
                adv: 'Seleziona la casella se vuoi inserire coordinate diverse per l''ADV'
                billing: 'Seleziona la casella se desideri inserire dettagli diversi per la fatturazione'
                logistic: 'Seleziona la casella se desideri inserire coordinate diverse per la logistica'
            logistic: 'Contatto logistica'
            logistic_subtitle: 'Potrai aggiungere contatti operativi per ciascun sito'
            main: 'Contatto principale (commerciale)'
        contract: 'Restituisci il contratto firmato'
        document:
            title: 'Aggiungi i tuoi documenti'
            titleReadOnly: 'I tuoi documenti'
        edit-site: 'Cambia il centro di costo'
        endpointUrl: ''
        finish: finitura
        iban: 'IBAN persona giuridica'
        ident_number:
            invalid: Invalido
        identification: 'Numero di identificazione'
        identity: 'Carta d’identità del firmatario'
        info:
            cgu_not_accepted: 'Devi accettare le CGU'
            company_registration: 'Il tuo account utente è stato creato, compila il modulo sottostante per inviare le informazioni sulla tua azienda e completare la registrazione.'
            incomplete: 'Le informazioni dell''azienda sono incomplete'
            operator_needed: 'Alcune informazioni della tua azienda non possono essere modificate direttamente. Se si desidera apportare modifiche, si prega di contattare StationOne tramite il modulo di <a href=''/ticket/create/''>contact</a>.'
        legal_documents: 'Documenti legali dell''azienda'
        main_contact:
            title: Indirizzo
        middleware: ''
        name: 'Ragione sociale'
        next: Successivo
        password:
            change: 'Cambia la mia password'
        profile: 'Il mio profilo'
        save: Salva
        service: Ufficio
        siren: SIREN
        siren_placeholder: '(9 cifre)'
        social: Azienda
        submit: Invia
        submit_infos: 'Presenta la mia azienda'
        tax_rate: 'Aliquota IVA'
        title: 'Dati amministrativi dell''azienda'
        type: Tipo
        update:
            error: 'Errore durante la modifica dell''azienda'
            success: 'L''azienda è stata aggiornata con successo'
company_catalog:
    add_in_catalog: 'Aggiungi il mio riferimento'
    buyer_reference: 'Riferimento nel tuo catalogo'
    cancel: Annullare
    custom_search:
        title: 'Cerca nel mio catalogo'
    delete: 'Elimina il mio catalogo'
    delete_confirm: 'Sei sicuro di voler eliminare il tuo catalogo?'
    delete_confirmed: 'Catalogo eliminato'
    delete_error: 'Solo i gestori degli account possono eliminare il file'
    delete_reference: Rimuovere
    export: 'Esporta il mio catalogo'
    export_matching: 'Esporta riferenze valide'
    export_mismatching: 'Esporta riferimenti non validi'
    import:
        total_matching_references: 'Totale riferimenti di catalogo validi:'
        total_mismatching_references: 'Totale riferimenti di catalogo non validi:'
    import_in_progress: 'l''importazione del catalogo è in esecuzione'
    imported_references: 'riferimenti importati'
    instruction_title: 'Istruzione :'
    instructions: 'Puoi caricare le tue referenze prodotto sul Negozio virtuale. In questo modo potrai vedere, cercare i prodotti secondo le referenze fornite nei tuoi ordini (solo gli utenti della tua azienda possono vedere queste referenze).</br></br>2 opzioni per aggiungere le tue referenze: </br>- Nella pagina dettagli prodotto, premere “La mia referenza" per aggiungere il proprio numero/nome di riferimento al prodotto selezionato</br>- Importa tutte le tue referenze prodotti nel menu "Il mio catalogo", utilizzando il modello di file .csv disponibile <a href="%link%" target="_blank">qui</a>.</br>nserisci la referenza del produttore nella prima colonna e la tua referenza nella seconda colonna. Al termine, carica il tuo catalogo facendo clic sul pulsante di caricamento.</br></br>Una volta caricato il file, i prodotti possono essere cercati secondo la tua referenza personale.</br></br>Il catalogo è disponibile per il download in qualsiasi momento facendo clic sul pulsante "Esporta il mio catalogo personale".</br></br>iIl pulsante "Esporta referenze non corrispondenti" ti aiuta a controllare se le referenze che hai aggiunto al tuo catalogo sono disponibili nel negozio virtuale StationOne.'
    overwrite: 'Attenzione, l''invio di un nuovo catalogo eliminerà quello vecchio. Vuoi continuare?'
    save: Salvare
    title: Catalogo
    upload: 'Invia il mio catalogo'
    upload_success: '%count% referenza/e aggiunta/e.'
    wrong_file_format: 'Formato di file non valido'
comparaisonSheet:
    add:
        error: 'Errore durante l''aggiunta del prodotto al comparatore'
        itemAlreadyexist: 'L''articolo è già nel comparatore'
        maxItemError: 'Hai già il numero massimo di prodotti nel comparatore: %maxItem%'
        success: 'Prodotto aggiunto al comparatore'
    back_to_home: 'Torna alla pagina iniziale'
    comment: 'Aggiungi un commento che vuoi vedere nella scheda del comparatore pdf : '
    export: Esportare
    information: 'Questo confronto è valido solo al momento della generazione di questo pdf. StationOne non si impegna a mantenere questo prezzo nel tempo.'
    no_article: 'Il tuo foglio di confronto è vuoto'
    page:
        author: Autore
        comment: Commento
        company_name: 'Nome dell''azienda'
        date: 'Data del foglio di confronto'
        delivery: Consegna
        discount: Sconto
        no_discount: 'senza sconto'
        no_price: 'Prezzo a richiesta'
        price_excl_tax: 'Prezzo al netto delle tasse'
        product: Prodotto
        title: 'Comparatore di prodotti'
        unit_price: 'Prezzo unitario'
        unit_price_vat: 'Prezzo unitario convertito'
    sticky: Comparatore
contact:
    adv: 'Contatto per l''amministrazione delle vendite'
    form:
        email: Email
        firstname: Nome
        function: Funzione
        lastname: Cognome
        phone1: 'Telefono principale'
        phone1_placeholder: 'Questo telefono è richiesto'
        phone2: 'Telefono secondario'
    logistic: 'Contatto di logistica'
contactMerchant:
    add_file: 'Add a file'
    attachment_limit: 'maximum %limit% attachments'
    authorized_types: '(Allowed files  : pdf, jpeg, gif, png, tiff)'
    file_too_big: 'the maximum size of an attachment is %size-limit%'
    form:
        error: 'Errore durante l''invio del messaggio'
        message: Messaggio
        object: Oggetto
        save: Invia
        success: 'Il tuo messaggio è stato inviato'
    message:
        object:
            feedback: 'Feedback da %buyer%'
            quotation: 'Richiesta commerciale da %buyer%'
            technical: 'Richiesta tecnica da %buyer%'
contactWAYLF:
    add_file: 'Aggiungi un file'
    attachment_limit: 'maximum %limit% attachments'
    authorized_types: '(File consentiti : pdf, jpeg, gif, png, tiff)'
    company_name: 'Nome della ditta'
    email: Email
    file_too_big: 'La dimensione massima di un allegato è % size-limit%'
    first_name: Nome
    last_name: Cognome
    message: Message
cookie:
    accept: Capito!
    message: 'Continuando la tua visita a questo sito, accetti i termini e le condizioni di utilizzo, compreso l''uso dei cookie per eseguire statistiche sul pubblico.'
cost_center:
    name:
        first: 'Centro di costo principale'
country:
    australia: Australia
    austria: Austria
    azerbaijan: Azerbaigian
    bahrain: Bahrein
    belgium: Belgio
    brasil: Brasile
    bulgaria: Bulgaria
    canada: Canada
    canary_islands: 'Isole Canarie'
    china: Cina
    croatia: Croazia
    cyprus: Cipro
    czech_republic: 'Repubblica Ceca'
    denmark: Danimarca
    estonia: Estonia
    finland: Finlandia
    france: Francia
    germany: Germania
    greece: Grecia
    hongkong: 'Hong Kong'
    hungary: Ungheria
    ident:
        australia: 'A.C.N. - numero d’impresa australiano'
        austria: 'COFIS o Partita IVA'
        azerbaijan: 'Codice Fiscale'
        bahrain: 'Numero di registrazione della società'
        belgium: 'TVABE (n. partita IVA Belgio)'
        brasil: 'Numero CNPJ (n. partita IVA)'
        bulgaria: 'numero registro Bulgaria'
        canada: DUNS
        canary_islands: 'Numero CIF'
        china: 'RNCN (numero reg. Cina)'
        croatia: 'Numero di registrazione della società'
        cyprus: 'RNCY (numero reg. Cipro)'
        czech_republic: ICO
        denmark: 'CVR Danimarca - IVA DK'
        estonia: 'Registro delle imprese - Estonia'
        finland: 'TVAFI (n. partita IVA Finlandia)'
        france: 'Numero di partita IVA'
        germany: 'Partita IVA Germania'
        greece: 'VATGR (IVA per la Grecia)'
        hongkong: 'RNHK (numero reg. Hong Kong)'
        hungary: 'RNHU (numero reg. Ungheria) o VATHU'
        india: 'Numero CRO per EBSG o partita IVA India'
        ireland: 'Numero impresa Irlanda (CNIE)'
        italy: 'COFIS o Partita IVA'
        latvia: 'Registro delle imprese - Lettonia'
        lithuania: 'Registro di commercio - Lituania'
        luxembourg: 'IBLC (IVA lussemburghese)'
        malta: 'numero registro Malta'
        mexico: 'RFC (Messico)'
        morocco: 'RNMA (numero registro Marocco)'
        netherlands: 'Camera di commercio Olanda KVK nr'
        norway: 'RNNO (numero di registro imprese - RN Norvegia)'
        peru: 'Anagrafe unica dei contribuenti'
        poland: 'TVAPL (IVA PIN Polonia)'
        portugal: 'NIPC (numero di registro Portogallo)'
        qatar: 'Numero di registrazione della società'
        romania: 'Partita IVA intracomunitaria'
        russia: 'OGRN (ident. RU)'
        saudi_arabia: 'Numero di registrazione della società'
        senegal: 'Numero di registrazione della società'
        singapore: 'Numero CRO per EBSG'
        slovak_republic: 'numero registro Slovak Republic'
        slovenia: 'numero registro Slovenia'
        spain: 'CIF Spagna - IVA'
        sweden: 'RNSE (RN Svezia)'
        switzerland: 'Numero di partita IVA'
        taiwan: 'RNTW (numero di registro imprese Taiwan)'
        thailand: 'Numero CRO o ROC'
        tunisia: 'RNTN (RN Tunisia)'
        turkey: 'TAXTR (identificatore fiscale per la Turchia)'
        united_arab_emirates: 'Numero di registrazione della società'
        united_kingdom: 'CRO (numero di registro impresa)'
        united_states: 'Numero DUNS'
    ident_helper:
        australia: '9 cifre'
        austria: 'ATU + 8 cifre'
        azerbaijan: '10 cifre corrispondenti al tuo codice TIN (Tax Identification Number)'
        bahrain: '15 cifre'
        belgium: '10 cifre'
        brasil: '14 cifre'
        bulgaria: '9 o 10 cifre'
        canada: '9 cifre'
        canary_islands: 'A + 8 cifre'
        china: 'Massimo di 18 cifre/lettere'
        croatia: 'HR + 11 cifre'
        cyprus: 'Da 5 a 6 cifre/lettere'
        czech_republic: '8 cifre'
        denmark: '8 cifre'
        estonia: '8 cifre'
        finland: '8 cifre'
        france: 'FR + XX + N. Siren'
        germany: 'DE + 9 cifre'
        greece: '9 cifre'
        hongkong: 'Da 4 a 8 cifre'
        hungary: '10 cifre o 11 cifre'
        india: '11 cifre + 1 lettera'
        ireland: '7 cifre'
        italy: '11 cifre'
        latvia: '11 cifre'
        lithuania: '9 cifre'
        luxembourg: '8 cifre'
        malta: '8 cifre'
        mexico: '12 o 13 caratteri'
        morocco: 'Massimo 35 caratteri'
        netherlands: '8 cifre'
        norway: '9 cifre'
        peru: 'Formato a 11 numeri'
        poland: '10 cifre'
        portugal: '9 cifre'
        qatar: '5 cifre'
        romania: '"RO" + 2/10 cifres'
        russia: '13 o 15 cifre'
        saudi_arabia: 'AS + 15 cifre'
        senegal: '5 lettere + 4 cifre + 1 lettere + 5 cifre'
        singapore: '9 cifre max + 1 lettera'
        slovak_republic: 'SK+10 cifre'
        slovenia: '8 cifre'
        spain: '1 lettera + 8 cifre'
        sweden: '10 cifre'
        switzerland: CHE-XXXXXXXXX
        taiwan: '8 cifre'
        thailand: '13 cifre'
        tunisia: '10-11 caratteri'
        turkey: '10 cifre'
        united_arab_emirates: '15 cifre'
        united_kingdom: '8 cifre'
        united_states: '9 cifre'
    india: India
    ireland: 'Repubblica d''Irlanda'
    italy: Italia
    latvia: Lettonia
    lithuania: Lituania
    luxembourg: Lussemburgo
    malta: Malta
    mexico: Messico
    morocco: Marocco
    netherlands: 'Paesi Bassi'
    norway: Norvegia
    peru: Perù
    poland: Polonia
    portugal: Portogallo
    qatar: Qatar
    region:
        CA-AB: Alberta
        CA-BC: 'British Columbia'
        CA-MB: Manitoba
        CA-NB: 'New Brunswick'
        CA-NL: 'Terranova e Labrador'
        CA-NS: 'Nova Scotia'
        CA-NT: 'Territori del Nordovest'
        CA-NU: Nunavut
        CA-ON: Ontario
        CA-PE: Île-du-Prince-Édouard
        CA-QC: Quebec
        CA-SK: Saskatchewan
        CA-YT: Yukon
        FR-ARA: Auvergne-Rhône-Alpes
        FR-BFC: Bourgogne-Franche-Comté
        FR-BRE: Bretagna
        FR-COR: Corsica
        FR-CVL: 'Centre-Val de Loire'
        FR-GES: 'Grand Est'
        FR-GF: 'Guyana francese'
        FR-GP: Guadalupa
        FR-HDF: Hauts-de-France
        FR-IDF: Ile-de-France
        FR-MQ: Martinica
        FR-NAQ: Nouvelle-Aquitaine
        FR-NOR: Normandia
        FR-OCC: Occitania
        FR-PAC: 'Provenza-Alpi-Costa azzurra'
        FR-PDL: 'Paesi della Loira'
        FR-RE: 'La Réunion'
        FR-YT: Mayotte
    romania: Romania
    russia: 'Federazione Russa'
    saudi_arabia: 'Arabia Saudita'
    senegal: Senegal
    singapore: Singapore
    slovak_republic: 'Repubblica Slovacca'
    slovenia: Slovenia
    spain: Spagna
    sweden: Svezia
    switzerland: Svizzera
    taiwan: Taiwan
    thailand: Thailandia
    tunisia: Tunisia
    turkey: Turchia
    united_arab_emirates: 'Emirati Arabi Uniti'
    united_kingdom: 'Gran Bretagna'
    united_states: 'Stati Uniti'
currency: Moneta
customs:
    info:
        domestic: Interno
        export_EU: 'Esportazione UE'
        export_non_EU: Esportazione
default:
    placeholder: 'Non definito'
dispute:
    create:
        ko: 'Si è verificato un errore durante l''invio della tua contestazione'
        ok: 'La tua contestazione è stata inviata'
    form:
        message: Commenti
        new: Controversia
        placeholder: 'Spiega qui i problemi relativi ai prodotti selezionati'
        products: 'Seleziona prodotti con problemi'
        see: 'Vedi tutte le controversie'
        subject: Soggetto
        table:
            all: Tutti
            expected_date: 'Data prevista'
            product_name: 'Nome del prodotto'
            quantity: Quantità
            reference: Riferimento
            total_price: 'Prezzo totale'
            unit_price: 'Prezzo unitario'
        title: Controversia
    list:
        creation_date: 'Data di creazione'
        id: ID
        messages: messaggi
        read: Leggere
        receiver: Ricevitore
        subject: Soggetto
        unread: 'Non letto'
document:
    contract:
        download: 'Scarica qui il contratto precompilato'
    noDoc: 'Nessun file selezionato - Devi aggiungere i tuoi documenti'
    upload:
        delete: 'Volete cancellare questo file?'
        deleteError: 'Errore durante l''eliminazione del file'
        error: 'Si prega di aggiungere almeno un file.'
        ignored: '1 o più file sono stati ignorati'
        ko: 'Errore/i durante il caricamento'
        mime: 'Il tipo di documento deve far parte del seguente elenco: %vincolo%'
        ok: 'Caricamento completato'
        size: 'La dimensione del documento non deve superare %vincolo% MB'
        title: 'Seleziona un file'
        type: 'Il file non è valido'
        typeOrSizeError: 'Si è verificato un problema durante l''aggiunta del documento. Il file potrebbe non essere valido'
        working: 'Elaborazione in corso ...'
error:
    forbidden:
        code: '(errore 403)'
        description: 'Non sei autorizzato a visualizzare questo contenuto. Se pensi che questo sia un problema, puoi contattare l''assistenza.'
        title: 'Non autorizzato'
    generic:
        help: 'I seguenti link possono aiutarti:'
        home: 'Ritorno alla Homepage'
        support: 'Contatta l''assistenza'
    internal:
        code: '(errore 500)'
        description: 'Si è verificato un errore sconosciuto durante l''elaborazione della richiesta. Se pensi che questo sia un problema, puoi contattare l''assistenza.'
        title: 'Si è verificato un errore interno'
    notfound:
        code: '(errore 404)'
        description: 'La pagina richiesta non esiste. Se pensi che questo sia un problema, puoi contattare l''assistenza.'
        title: 'La pagina non esiste'
    zipCode: 'Il codice postale deve essere di 5 cifre'
filter:
    all: Tutti
    'no': 'No'
    'yes': Sì
flag: '#icon-flag-it'
footer:
    about_us:
        code_of_conduct: 'Codice di condotta'
        company: L’azienda
        cookies: Cookies
        data_privacy_chart: 'Data Privacy Charter'
        join_us: 'Unisciti a noi'
        legal_notice: 'Note legali'
        our_mission: 'La nostra missione'
        title: Info
    additional:
        alstom: StationOne
        copyright: '@ 2018 StationOne. Tutti i diritti riservati.'
        purchase_conditions: 'Condizioni di acquisto in base alle Condizioni generali d''uso di Station One e quindi alle condizioni di vendita di ciascun venditore.'
    buy:
        benefits_for_buyers: 'Benefici per gli acquirenti'
        create_an_account: 'Crea un account'
        general_terms_and_conditions: 'Termini e condizioni generali'
        title: 'Acquista su StationOne'
    follow_us:
        title: Seguici
    help:
        call_us_at: 'Chiamaci a'
        contact_us: 'Invia un messaggio'
        illegal_content: 'Contenuti illegali'
        phone_number_1: '+33 6 59 35 58 54'
        questions: 'Q&A'
        title: Aiuto
    mentions: 'Note legali'
    newsletter:
        placeholder: 'Indirizzo email'
        title: 'Iscriviti alla nostra newsletter'
    press:
        blog: Blog
        news: News
        press_releases: 'Comunicati stampa'
        title: 'Stampa & Notizia'
    sell:
        benefits_for_sellers: 'Benefici per i fornitori'
        create_an_account: 'Crea un account'
        general_terms_and_conditions: 'Termini e condizioni generali'
        title: 'Vendi su StationOne'
    visit:
        address_1: '69-73 Boulevard Victor Hugo'
        address_2: '93400 Saint-Ouen-sur-Seine'
        address_3: FRANCIA
        title: Visitaci
form:
    invoice:
        search: Ricercare
    order:
        search: Ricerca
    user:
        add: 'Aggiungi un utente'
        definition:
            account_manager: 'Responsabile account'
            account_manager_definition: 'Diritti dell''acquirente + Amministrazione account globale'
            buyer: Acquirente
            buyer_definition: 'Diritti del richiedente + Approvazione degli ordini per i suoi centri di costo'
            requestor: Richiedente
            requestor_definition: 'Seleziona i prodotti, prepara gli ordini e inviali per l''approvazione all''acquirente'
        delete:
            content: 'Sei sicuro di voler cancellare questo utente?'
            content_for_default_user: ''
            title: 'Eliminazione di un utente'
        email: 'Email:'
        error_update: 'Si è verificato un errore durante l''aggiornamento dell''utente. Per favore, controlla i dati inseriti'
        firstname: Nome
        function: Funzione
        invalid_role: 'Questo ruolo non è valido'
        lastname: Cognome
        myself: Mi
        role:
            admin: 'Responsabile dell’account acquirente'
            buy: Acquirente
            pay: 'Acquirente Pagatore'
            placeholder: 'Scegli un ruolo'
            sell: Vendita
            view: Consultazione
        roles: Profilo
        save_edit: Aggiorna
        save_new: Crea
        sites:
            label: 'Centro di costi:'
            mandatory: 'Devi selezionare almeno un centro di costo'
        success_new: 'L''utente è stato creato'
        success_update: 'L''utente è stato aggiornato'
        title: 'Elenco degli utenti'
        title_common: 'un utente'
        title_edit: Modifica
        title_new: Aggiungi
front:
    site:
        no_default_user: ''
generic:
    cancel: Cancel
    delete: Delete
    save: Save
header:
    address: Indirizzo
    buyer_id: 'ID dell''acquirente'
    company: Azienda
    delivery_delay: 'Data stimata di consegna'
    incoterm: Incoterm
    purchase_order: 'Ordinazione d''acquisto'
    site: 'Centro di costo'
    tva_identification_number: 'Partira IVA'
    vendor_id: 'ID del venditore'
    vendor_ref: 'Riferimento del venditore'
home:
    autres_industriels: 'Altri industriali'
    collectivite_locale: 'Ente locale'
    dashboard_admin: 'Amministratore pannello'
    description: 'Cum homerici cum Roma nobilium'
    disclaimer:
        market:
            line1: 'Più opportunità'
            line2: 'Più depositi'
            line3: 'Prezzo di mercato'
            title: 'Accesso al mercato'
        process:
            line1: 'Risparmia tempo'
            line2: 'Creazione di rapporti e pannello unico'
            line3: 'Lettera di vettura digitale'
            title: 'Un processo efficiente'
        transactions:
            line1: 'Trasparenza (foto e caratterizzazione)'
            line2: 'Verifica dei venditori e degli acquirenti'
            line3: 'Contratto di vendita vincolante'
            title: 'Transazioni sicure'
        video: 'Scopri sul video'
    grande_distribution: 'Grande distribuzione'
    insert:
        all_news: 'Vedi tutte le notizie'
        contact: Contatto
        news_description: 'Alstom fornirà l''Ile-de-France Mobilité e il RATP con 20 MP14 metro, costituiti da 5 carrozze ciascuno, per la linea 11 della metropolitana di Parigi per un valore di 157 milioni di euro.'
        savoir: 'Maggiori informazioni'
        sustainability_description: 'Soddisfare le esigenze di oggi senza compromettere il domani'
        title: FAQ
        title_duree: Sostenibilità
        title_news: 'Ultime notizie'
    login: Login
    login_admin: 'Accesso amministratore / operatore'
    logout: Disconnettiti
    register: Iscriviti
    register_buyer: Iscrizione
    register_merchant: 'Registrazione venditore'
    register_merchant_confirmation_message: 'La tua registrazione è andata a buon fine.'
    register_merchant_confirmation_title: Congratulazioni!
    register_merchant_confirmation_url: 'Ora puoi connetterti alla tua area venditore'
    register_merchant_error: 'Si è verificato un errore imprevisto durante la registrazione. Se il problema persiste, contatta l''assistenza.'
    register_merchant_success: 'La tua registrazione è andata a buon fine'
    register_merchant_tva_not_checked: 'The identification number could not be checked for this vendor. Please proceed to a manual check'
    slider:
        subtitle: 'Fruticeta prona valido inmanium fortiter'
        title: 'Cum homerici cum Roma nobilium'
    start_buy: 'Inizia a comprare'
    start_sale: 'Inizia a vendere'
    ticket_admin: 'Elenco dei ticket (amministratore)'
    ticket_anonymous: 'Crea un ticket come utente anonimo'
    ticket_buyer: 'Elenco dei ticket'
    title: Home
    video_src: MECmgIz36nU
    why_buy:
        certified: Autorizzato
        certified_text: 'Un catalogo di fornitori di qualità certificati da StationOne'
        fast: Veloce
        fast_text: 'Un sito web e migliaia di articoli per ottimizzare i tuoi rifornimenti'
        simple: Semplice
        simple_text: 'Un sistema di pagamento sicuro e tempi di consegna garantiti'
        title: 'Perché comprare su StationOne?'
illegal_content:
    form:
        ko: 'Si è verificato un errore durante l''invio di contenuti illegali'
        ok: 'La tua richiesta è stata inviata con successo'
        save: Inviare
        title: 'Descrivi il contenuto illegale che hai rilevato'
import:
    csv:
        invalid_separator: 'Separatore di lista non riconosciuto'
        no_error: 'Importazione eseguita senza errori'
        not_all_lines_were_imported: 'non tutte le righe sono state importate'
        not_enough_lines: 'Riga insufficiente in questo file'
invoice:
    detail:
        already_invoiced: 'Già fatturato :'
        credit_note: 'Nota di credito'
        frame_contract: 'Contratto quadro'
        go_back: 'Torna all''ID ordine:'
        including_taxes: 'tasse incluse'
        invoice: Fattura
        invoice_amount: Quantità
        invoice_date: Data
        invoice_due_date: Scadenza
        invoice_payment: Pagamento
        not_paid: 'Not paid'
        not_yet_invoiced: 'Non ancora fatturato:'
        order_amount: 'Totale:'
        order_date: 'Data:'
        order_id: 'Id del sub:'
        order_payment_status: 'Stato pagamento:'
        order_seller: 'Venditore:'
        order_status: 'Stato:'
        paid: Paid
        payment: Pagamento
        refund: Rimborso
        remaining_to_pay: 'Restante a pagare'
        title: fFtture
        total: Totale
        total_invoiced: 'Totale fatturato e nota di credito'
        total_paid: 'Totale pagato :'
        total_remaining_to_pay: 'Totale rimanente da pagare:'
    export:
        invoice_amount: 'Importo della fattura'
        invoice_creation_date: 'Data di creazione della fattura'
        invoice_number: 'Fattura No.'
        order_creation_date: 'Data di creazione dell''ordine'
        order_number: 'Numero d''ordine'
        payment_status: 'Stato fattura'
        vendor_name: 'Nome del fornitore'
    list:
        amount: Importo
        date: Data
        due_date: 'Data di scadenza'
        due_date_total_eur: 'Per pagare questo mese (€):'
        due_date_total_usd: 'Per pagare questo mese ($):'
        export: Esportare
        from: 'Dalla data'
        invoice: 'Fattura / Nota di credito'
        late_payment_eur: 'Pagamento in ritardo (€):'
        late_payment_usd: 'Pagamento in ritardo ($):'
        order: Ordine
        paid_date: 'Data di pagamento'
        remain: 'Saldo dovuto'
        seller: Venditore
        tab:
            empty: 'Non ci sono fatture in questa sezione.'
            not_paid: 'Fatture non pagate'
            paid: 'Fatture pagate'
        title: Fatture
        to: 'Fino alla data'
        total_to_pay_eur: 'Totale fatture ricevute (€):'
        total_to_pay_usd: 'Totale fatture ricevute ($):'
    reminder: 'Promemoria della fattura'
    seeInvoices: 'Fatture e pagamenti'
key: IT
keyToIgnore: ''
label_next: Successivo
label_previous: Precedente
login:
    companyError: 'L''azienda è stata disattivata'
    userCreatedButCompanyError: 'Siamo lieti di informarti che il tuo account aziendale è stato creato e ti ringraziamo per il tuo interesse.<br />L''invio del tuo account verrà esaminato dal team di StationOne il prima possibile.'
main_menu:
    account: 'Il mio account'
    buyer:
        mybids: 'Le mie aste'
        myorders: 'I miei ordini'
    categories: Categorie
    concept: 'Il concept'
    contact: Contatto
    contact_us: Contattaci
    dashboard: 'Pannello centrale'
    eur_cart: 'CARRELLO EUR'
    explore_categories: 'Sfoglia le categorie'
    faq: FAQ
    help: Aiuto
    how: 'Come funziona ?'
    join_us: 'Registrazione gratuita'
    languages: Lingue
    locked: 'Devi aver completato il passaggio precedente per accedere a questa pagina'
    merchant:
        myoffers: 'Le mie offerte'
        mysales: 'Le mie vendite'
        offers:
            active: Attivi
            draft: Bozze
            nosale: 'Senza transazione'
            pending: 'In attesa di convalida'
    messages: Messaggi
    offers: 'Le offerte'
    products: Prodotti
    signin: Accesso
    terms: 'Condizioni generali'
    usd_cart: 'CARRELLO USD'
    e_catalog_label: 'E-Catalog'
menu:
    desktop:
        api_doc: ''
        company: Azienda
        document: Documenti
        invoices: Fatture
        my_catalog: 'Il mio catalogo'
        orders: Ordini
        payment_modes: 'Metodi di pagamento'
        pending_carts: 'Carrelli in sospeso'
        profile: Profilo
        purchase_request: 'Richieste di acquisto'
        sites: 'Centri di costo'
        stats: Statistiche
        users: Utenti
        wishlist: 'Lista dei kit'
merchant:
    name: Venditore
    update: 'Richiesta di vedere il prezzo è stato aggiornato con successo'
merchant_order:
    status:
        canceled: ''
        confirmed: ''
        finalized: ''
        initial: ''
        partially_refunded: ''
        payment_authorized: ''
        processed: ''
        received: ''
        return_in_progress: ''
        sent: ''
modal:
    cancel: Annulla
    confirm: Ok
    'no': 'No'
    'yes': Sì
month: mese
'no': 'No'
node:
    form:
        author:
            label: Autore
        body:
            label: Corpo
        checkboxLinkType:
            label: 'Redirect to external URL'
        content: Contenuto
        delete:
            content: 'Sei sicuro di voler eliminare questa pagina statica?'
            error: 'si è verificato un errore durante l''eliminazione'
            success: 'La pagina statica è stata rimossa'
            title: 'Eliminazione della pagina statica'
        error:
            template_validation: 'An error occurred while validating content for "%locale%" language: %message%'
        header:
            edit: 'Modifica del contenuto (%tipo):'
            new: 'Nuovo contenuto'
        lang:
            button: Aggiungi
            de: Tedesco
            en: Inglese
            es: Spagnolo
            fr: Francese
            it: Italiano
            nl: Nederlands
            select: 'Scegli una lingua'
        sections:
            content: Contenuto
            main: Generale
        slug:
            help: Slug
            label: Permalink
        status:
            label: Status
        submit:
            create: Crea
            error:
                create: 'si è verificato un errore durante la creazione'
                update: 'si è verificato un errore durante l''aggiornamento'
            success:
                create: 'La pagina statica è stata creata'
                delete: 'La pagina statica è stata rimossa'
                update: 'La pagina statica è stata aggiornata'
            update: Aggiorna
        template:
            choices:
                default: Predefinito
                default_with_faq: 'Predefinito con blocchi FAQ'
                default_with_products: 'Predefinito con elenco di prodotti'
                default_with_products_and_faq: 'Predefinito con l''elenco dei prodotti e blocchi FAQ'
                fullwidth: 'Pagina intera'
                fullwidth_with_faq: 'Pagina intera con blocchi FAQ'
                fullwidth_with_products: 'Pagina intera con elenco dei prodotti'
                fullwidth_with_products_and_faq: 'Pagina intera con l''elenco dei prodotti e blocchi FAQ'
            label: Quadro
        test: 'Test emails have been successfully sent to your email address: %email%'
        textLinkExternal:
            label: 'External URL'
        title:
            label: Titolo
offer_detail:
    add_error: 'Hai già questo prodotto nel tuo carrello.'
    add_success: 'Il tuo prodotto è stato aggiunto con successo al tuo carrello.'
    add_to_cart: 'Aggiungi al carrello'
    add_to_wishlist: 'Aggiungi alla lista dei kit'
    add_wishlist_success: 'Il tuo prodotto è stato aggiunto alla tua lista dei kits'
    anonym_contact_merchant: 'Effettua il login per contattare il venditore.'
    anonymous:
        add_to_cart: 'Devi essere registrato per aggiungere prodotti al tuo carrello.'
        not_authorized: 'Non autorizzato'
    ask_question: 'Contatta il venditore'
    ask_title: 'Bisogno di supporto?'
    ask_vendor: 'Richiedi l''accesso ai prezzi di catalogo'
    ask_vendor_message_content: 'Ciao,\nPotresti concedermi l''''accesso ai prezzi del tuo catalogo?\nGrazie\n\nHello,\nCould you please grant me access to your catalog prices ?\nThank you\n'
    ask_vendor_no_price_offer_message_content: 'Ciao,\nPotresti proporre il prezzo migliore per questa offerta?\nQuantità prevista:\nData di consegna prevista:\n\nGrazie\n'
    ask_vendor_pending: 'Accesso ai prezzi del catalogo richiesto'
    ask_vendor_price: 'Richiedi questo prezzo al venditore'
    ask_vendor_price_reference: 'Richiedi un prezzo per l''articolo %reference%'
    ask_vendor_rejeted: 'Prezzo non disponibile'
    back_to_search: 'Torna alla ricerca'
    buy_more_for_discount: 'Acquista di più e ottieni uno sconto'
    company_code: 'Company code :'
    company_not_valid: 'Si prega di completare le informazioni aziendali prima di controllare il carrello'
    contact_seller:
        modal:
            send: Invia
            title: 'Contatta il venditore.'
    contact_the_vendor: 'Può contattare il venditore nella pagina del prodotto'
    continue_shopping: 'Continua a fare acquisti'
    description: 'Dettaglio di un''offerta'
    expired_offer: 'Questo prodotto non è più disponibile su StationOne. Per ulteriori informazioni, contatta il venditore.'
    frame_contract_valid_date: 'Valido fino al'
    from_company: 'Da:'
    in_stock: 'In magazzino'
    login: 'Disconnettiti -> Accesso'
    not_available_country: 'Questo prodotto non è più disponibile per chi paga. Per ulteriori informazioni, contatta il venditore.'
    not_batch_size_multiple: 'La quantità deve essere un multiplo d i%batchSize%.'
    not_business_everywhere: 'Questo prodotto non può essere venduto nel tuo paese. Si prega di contattare StationOne per ulteriori informazioni'
    on_demand: 'Su richiesta'
    on_stock: 'A magazzino'
    out_of_stock: 'Non disponibile in magazzino'
    out_of_stock_description: 'Questo prodotto non è più disponibile in magazzino su StationOne. Per ulteriori informazioni, contatta il venditore.'
    price_quantity: 'Per %quantity% %unit%'
    proforma:
        0: proforma
        version: 'Proforma Version'
    proforma_pdf:
        quantity: 'Quantità richiesta'
        title: proforma
        total_price: 'Prezzo totale (IVA esclusa)'
    quantity: Quantità
    related_products: 'Servizi Associati'
    see_cart: 'Vedi il carrello'
    see_less: 'Vedi meno'
    see_more: 'Vedi di più'
    see_wishlist: 'Vedi la mia lista dei kit'
    seller:
        label: 'Presentazione del venditore'
        link_product: 'Vedi tutti i prodotti'
    sign_in_to_buy: 'Accedi per acquistare'
    similar_products: 'Prodotti associati'
    title: 'Dettaglio di un''offerta'
    too_much_quantity: 'La quantità selezionata è maggiore dello stock (massimo: %max%).'
    too_small_quantity: 'La quantità selezionata è inferiore all''ordine minimo (minimum: %min%).'
    total_price: 'Prezzo totale'
    wrong_quantity: 'Inserire una quantità valida.'
    warranty_period: 'Garanzia di %month% mesi'
offers:
    bidbutton_msg: 'Presto potrai fare un''offerta per le tue offerte preferite'
    description: 'Le offerte'
    title: 'Le offerte'
orders:
    createCart:
        errorMoq: 'L’articolo %reference% non può essere aggiunto al carrello perché la quantità minima dell''ordine è inferiore alla quantità richiesta'
        errorNoPrice: 'L''articolo % reference% non può essere aggiunto al carrello perché non ha prezzo'
        errorPrice: 'L’articolo %reference% è stato aggiunto al carrello, tuttavia il prezzo è cambiato.'
        errorStatus: 'L’articolo %reference% non può essere aggiunto al carrello perché l''articolo e / o il venditore sono disattivati.'
        errorStock: 'L’articolo %reference% non può essere aggiunto al carrello perché lo stock rimanente su questo prodotto è inferiore alla quantità richiesta'
    detail:
        go_back: 'Torna alla lista degli ordini'
        shipping:
            delivered: 'Consegnato %separator%'
            last_delivery: 'Ultima consegna %separator%'
            title: 'Offerte di spedizione'
        title: Ordine
        tracking:
            delivery_date: 'Data di consegna'
            error: 'Al momento non possiamo visualizzare le informazioni di monitoraggio'
            quantity: 'Quantità %separator%'
            status_delivered: Consegnato
            status_pickuped: 'Ritiro fatto'
            title: 'Monitoraggio della consegna'
            vendor_ref: 'Rif. Venditore %separator%'
    empty:
        title: 'Nessun ordine'
    export:
        address: 'Indirizzo di consegna'
        amount: 'Order Amount (tax excl.)'
        amountTaxIncluded: 'Order Amount (tax incl.)'
        amountVat: 'Order VAT Amount'
        buyerRef: 'Buyer Ref'
        costCenter: 'Cost center'
        currency: Currency
        date: 'Order date'
        documentRequirement1: 'Document requirement 1'
        documentRequirement10: 'Document requirement 10'
        documentRequirement2: 'Document requirement 2'
        documentRequirement3: 'Document requirement 3'
        documentRequirement4: 'Document requirement 4'
        documentRequirement5: 'Document requirement 5'
        documentRequirement6: 'Document requirement 6'
        documentRequirement7: 'Document requirement 7'
        documentRequirement8: 'Document requirement 8'
        documentRequirement9: 'Document requirement 9'
        expectedDeliveryDate: 'Expected Delivery Date'
        frameContractNumber: 'Frame contract number'
        id: 'Order id'
        internalBuyerOrderId: 'Buyer''s ERP Order id'
        itemPrice: 'Order line amount (tax excl.)'
        orderLine: OrderLine
        packagingRequirement1: 'Packaging requirement 1'
        packagingRequirement2: 'Packaging requirement 2'
        packagingRequirement3: 'Packaging requirement 3'
        paymentTerms: 'Payment Terms'
        productName: 'Product Name'
        quantity: Quantity
        status: Status
        subOrderId: 'Sub Order Id'
        unitPrice: 'Unit price (tax excl.)'
        validationNumber: 'Numero d''ordine dell''acquirente'
        vendorName: 'Vendor Name'
        vendorRef: 'Vendor Ref'
    list:
        address: 'Indirizzo di consegna'
        block:
            addition_information: 'Informazioni aggiuntive'
            additional_information: 'Informazioni aggiuntive'
            address_title: 'Indirizzo di consegna:'
            billing_address_title: 'Indirizzo di fatturazione:'
            buyer_internal_order_id: 'Numero d''ordine dell''acquirente'
            cost_center_title: 'Centro di costo:'
            date_title: 'Data dell''ordine:'
            expectedDate: 'Prima consegna prevista:'
            iban_account_name: 'Nome dell''account iban:'
            iban_number: 'Numero IBAN:'
            key: 'Chiave di riconciliazione:'
            order_line: 'Linea d''ordine'
            order_title: 'Numero dell''ordine:'
            packaging_specifications: 'Istruzioni per l''imballaggio'
            payment_information: 'Informazioni di pagamento:'
            payment_terms: 'Condizioni di pagamento:'
            requested_documents: 'Documenti richiesti'
            status: 'Stato:'
            total_title: 'TOTALE:'
            validation_number_title: 'Numero d''ordine dell''acquirente: '
        date: 'Data dell''ordine'
        detail: Dettaglio
        download_pdf: 'Scarica il pdf'
        export: Esportazione
        id: 'ID dell’ordine'
        link:
            buy_again: 'Acquista di nuovo'
            details: Dettagli
            export: 'Esportazione PDF'
            invoice: Fattura
            refund: Rimborso
        merchant_product: Articolo
        merchant_products: Elementi
        sub_order_id: 'ID ordine secondario'
        tab:
            cancelled: 'Ordine annullato'
            empty: 'Non hai ordini in questa sezione.'
            past: 'Ordine completato'
            running: 'Ordine in corso'
        total: Totale
    status:
        pending_creation: 'In attesa di creazione'
        status_0: 'In attesa di Pagamento'
        status_110: 'Problema risolto'
        status_11111111: 'In attesa di creazione'
        status_2000: Annullata
        status_3000: Eliminata
        status_60: 'In attesa di conferma del fornitore'
        status_80: Confermato
        status_85: 'Spedizione e fattura'
payment:
    error: 'Si è verificato un errore imprevisto durante il processo di pagamento. Il tuo ordine è stato cancellato. Se l''errore persiste, contattare l''assistenza'
    form:
        not_authorized: 'Non sei autorizzato a richiedere la modalità di pagamento a termine. Si prega di contattare il vostro manager.'
        submit: 'Richiedi l''autorizzazione per il pagamento differito'
    pre_cc_mode:
        error: 'Si è verificato un errore tecnico durante il recupero delle informazioni sull''ordine% id%. Se il problema persiste, contatta l''assistenza'
        ko: 'Si è verificato un errore durante il pagamento: il tuo ordine % id% è stato annullato'
        ok: 'Il tuo pagamento è stato eseguito con successo. Il tuo ordine% id% è ora autorizzato'
        success:
            text: 'Il tuo ordine% id% è stato confermato in pre-pagamento con carta di credito.'
            text_2: 'Puoi già controllare il tuo acquisto nella pagina dell''ordine.'
            text_3: 'I venditori confermeranno presto il tuo acquisto.'
            title: 'Congratulazioni !'
        title: 'Verifica di pagamento'
        title_error: 'Errore tecnico'
        title_ko: 'Mancato pagamento'
        title_ok: 'Pagamento confermato'
        waiting_status: 'Attendi, in attesa dell''autorizzazione pagamento per il tuo ordine% id% ...'
    pre_virement_mode:
        pending:
            text: 'Il tuo ordine è stato registrato in pre-pagamento tramite bonifico bancario.'
            text_2: 'Riceverai un''email con tutti i dettagli per procedere al pagamento.'
            text_3: 'Una volta effettuato il pagamento, i venditori vedranno il tuo ordine e potranno confermarlo.'
            text_4: 'Si prega di considerare che il tempo di consegna indicato nell''ordine potrebbe essere leggermente più lungo del previsto, in quanto dipende dal tempo necessario alla vostra organizzazione per effettuare il bonifico bancario, fino alla sua ricezione da parte di StationOne'
            text_link1: 'Torna alla pagina di ricerca'
            text_link2: 'Guarda i miei ordini'
            title: 'Congratulazioni !'
        success:
            payment_detail_number: 'Numero di transazione'
            payment_details: 'Ecco le informazioni necessarie per eseguire il bonifico bancario:'
            payment_details_iban: 'Numero IBAN: %iban%'
            payment_details_iban_account_name: 'Nome dell''account iban: %iban_account_name%'
            payment_details_key: 'Chiave di riconciliazione: %chiave%'
            text: 'Il tuo ordine %numOrdine% è stato confermato con successo. Sarà elaborato non appena verrà ricevuto il bonifico bancario'
            text_link1: 'Torna alla pagina di ricerca'
            text_link2: 'Vedi i tuoi ordini'
            title: 'Ordine confermato'
    select_mode:
        error:
            cc: 'Errore durante la transazione di creazione. Si prega di contattare il supporto'
        preCreditCard: 'Pagamento anticipato con carta di credito'
        preTransferWire: 'Pagamento anticipato tramite bonifico bancario'
        select: Seleziona
        termTransferWire: 'Termine di pagamento (45 giorni alla fine del mese, dopo l''emissione della fattura)'
        title: 'Scelta del metodo di pagamento'
    time_mode:
        error:
            text: 'Si è verificato un errore durante la conferma del tuo ordine. Si prega di contattare il supporto se il problema rimane'
            text_link1: 'Vedi il tuo carrello'
            text_link2: 'Torna alla pagina di ricerca'
            text_link3: 'Contatta l''assistenza'
            title: 'Errore durante la conferma del tuo ordine'
        pending:
            text: 'Il tuo ordine è stato registrato in pagamento a 45 giorni dalla fine del mese dopo la fatturazione.'
            text_2: 'Riceverai una conferma dai tuoi fornitori direttamente sulla tua pagina di ordine.'
            text_3: 'Una volta ricevuta la fattura, sarai in grado di gestire il pagamento.'
            text_link1: 'Torna alla pagina di ricerca'
            text_link2: 'Guarda i miei ordini'
            title: 'Congratulazioni !'
        succes:
            text: 'Il tuo ordine %numOrder% è stato creato'
            text_2: 'Riceverai un email con i dettagli del pagamento (IBAN e chiave di riconciliazione) quando la fattura viene emessa.'
            text_link1: 'Torna alla ricerca'
            text_link2: 'Vedi i tuoi ordini'
            title: 'Conferma del tuo ordine'
payment_mode:
    Prepayment_creditcard: 'Pagamento anticipato con carta di credito'
    Prepayment_moneytransfert: 'Pagamento anticipato tramite bonifico bancario'
    Termpayment_moneytransfert: 'Termine di pagamento (45 giorni alla fine del mese, dopo l''emissione della fattura)'
    ask_for_term_error: 'Errore durante l''elaborazione della richiesta di autorizzazione di pagamento differito'
    click_button: 'Fai clic sul pulsante in basso per utilizzare il pagamento differito.'
    enabled: 'Il tuo account è inoltre autorizzato a utilizzare il metodo di pagamento con pagamento tramite bonifico bancario.'
    info: 'Le modalità di pagamento anticipato autorizzate come impostazione predefinita sono la carta bancaria e il bonifico bancario.'
    pending: 'È in corso una richiesta di autorizzazione di pagamento'
    save_error: 'Errore durante l''aggiornamento dei metodi di pagamento'
    saved: 'I mezzi di pagamento sono stati aggiornati'
    title: 'Metodi di pagamento'
product:
    about_seller: 'Informazioni sul venditore'
    application_categories: Categorie
    buy: Acquista
    buyer_reference: 'Riferimento dell''acquirente'
    cart_item_comment: 'Commento'
    comparison: Comparatore
    converted_price: 'Basato sul tasso di cambio giornaliero'
    delivery_time: 'Tempo di consegna'
    description: Descrizione
    info_button_buy: 'I prodotti saranno presto disponibili per l''acquisto. Se non lo hai già fatto, crea un account per essere informato al più presto!'
    info_converted_price: 'Solo a scopo informativo.'
    logistics_informations: 'Informazioni logistiche'
    made_in: 'Paese di produzione'
    manufacturer: Fabbricante
    manufacturer_reference: 'Rif. del fabbricante'
    private: Privato
    quantity: 'Quantità disponibile'
    max_quantity: 'Quantità acquistabile'
    seller: Venditore
    seller_reference: 'Riferimento del venditore'
    technical:
        bearing_type: 'Tipo di cuscinetto a sfere'
        code_command_rs: 'Codice d’ordine RS'
        conditioning: Confezione
        dtr: DTR
        flange_outer_diameter: 'Diametro esterno della flangia'
        inner_diameter: 'Diametro interno'
        manufacturer_ref: 'Riferimento del fabbricante'
        marketplace_id: 'ID Marketplace'
        material: Materiale
        min_order_quantity: 'Quantità minima'
        nb_row: 'Numero di file'
        outer_diameter: 'Diametro esterno'
        rated_static_load: 'Carico statico nominale'
        ring_width: 'Larghezza dell''anello'
        seller_name: 'Nome del venditore'
        seller_ref: 'Riferimento del venditore'
        termination_type: 'Tipo di terminazione'
        trendmark: Marca
    technical_detail: 'Descrizione tecnica'
    technical_details: 'Descrizione tecnica'
products:
    all_offers: 'Guarda tutte le offerte'
    home_list_header: 'Elenco delle offerte'
    home_product_list_header: 'I prodotti più cercati'
profile:
    form:
        email: Email
        firstname: Nome
        lastname: Cognome
        main_phone_number: 'Numero di telefono'
        optional_phone_number: 'Numero di telefono opzionale'
        submit: Salva
        update:
            error: 'Si è verificato un errore durante l''aggiornamento delle informazioni personali'
            success: 'Le tue informazioni personali sono state aggiornate'
    password:
        help: ''
        submit: Applica
        title: 'Modifica la tua password'
proforma:
    address: Address
    address_title: 'Proforma from STATION ONE in the name and on behalf of'
    billing_address: 'Billing address'
    capital: 'to the capital of'
    condition:
        adress: 'StationOne, capital 20.000 Euros, 69/73 Boulevard Victor Hugo, 93400 Saint-Ouen (France). VAT NUMBER: FR18752364885'
        payment: 'Payment to be made to our representative'
        purchase: 'Purchase conditions according to General Terms of Use of Station One and then to the online sales conditions of %vendor_name% or to the contract number if any'
        title: 'Conditions of this offer'
    date: 'Proforma generated on'
    rcs: RCS
    siret: SIRET
    text: 'This proforma is only valid at the time of generation of this pdf. StationOne does not commit to holding this price over time.'
    title: proforma
    vat_number: 'VAT Number'
purchase_request:
    cart:
        add: 'Aggiungi al carrello'
        remove: 'Rimuovere dal carrello'
    clear: 'Eliminare la pagina'
    detail: 'Richieste di acquisto'
    export: 'Inviare i riferimenti non trovati'
    exportFound: 'Export found references'
    import:
        error:
            csv_format: 'Formato file non valido. È accettato solo il formato CSV'
            csv_size: 'La dimensione del file supera il limite. La dimensione massima per file è di 2 MB'
            internal_error: 'Errore durante l''avvio della richiesta, contattare StationOne'
        title: Importare
    import_waiting: 'Si prega di importare un nuovo file'
    instruction: 'Il file importato deve avere il seguente formato: <a href="%link%" target="_blank"> format_import_demande_achat.csv </a>'
    offer:
        batch_price: 'Prezzo per 50 articoli'
        error_bafv: 'Il prezzo di questo prodotto non è disponibile perché non hai accesso ai prezzi nel catalogo di questo venditore. Per richiedere l''accesso, fai clic sul nome del prodotto, quindi fai clic su "Richiedi l''accesso ai prezzi di catalogo" nella pagina del prodotto.'
        error_businesseverywhere: 'Questo prodotto non può essere venduto nel tuo paese. Motivo: almeno una delle parti interessate deve essere situata in Europa'
        error_noprice: 'Questo prodotto è disponibile su richiesta di prezzo'
        manufacturer: Fabbricante
        merchant: Venditore
        quantity: Quantità
        quantity_available: 'Quantità disponibile'
        see_more: 'Vedi di più'
        select: Selezionare
        selected: Selezionato
        sku_price: 'Prezzo per'
        unit_price: 'Prezzo unitario'
    pr_item:
        buyer_order_number: 'Buyer order number'
        buyer_reference: 'Riferimento dell''acquirente'
        cost_center: 'Centro di costo'
        details: Dettagli
        expected_delivery_date: 'Data prevista'
        manufacturer_name: 'Nome del fabbricante'
        manufacturer_reference: 'Riferimento del fabbricante'
        merchant: Venditore
        no_ref: 'Nessun riferimento trovato'
        order_line: 'Linea d''ordine'
        product_name: 'Nome del prodotto'
        purchase_request_number: 'Numero della richiesta d''acquisto'
        quantity: Quantità
        quantity_expected: 'Quantità prevista'
        ref: Riferimento
        see_more: 'Vedi di più'
        unit_price: 'Prezzo unitario'
        unit_price_of_reference: 'Prezzo unitario di riferimento'
    title: 'Richieste di acquisto'
redirect:
    form:
        delete:
            error: 'Si è verificato un errore durante l''eliminazione del reindirizzamento'
            success: 'Il reindirizzamento è stato rimosso'
        destination:
            help: 'assicurati che l''URL esista'
            label: Destinazione
        header:
            edit: 'Modifica del reindirizzamento:'
            new: 'Nuovo reindirizzamento'
        origin:
            help: Slug
            label: Origine
        submit:
            error:
                create: 'si è verificato un errore durante la creazione'
                update: 'si è verificato un errore durante l''aggiornamento'
            success:
                create: 'Il reindirizzamento è stato creato'
                update: 'Il reindirizzamento è stato aggiornato'
        type:
            help: '301 = permanente / 302 = temporaneo'
            label: 'Tipo di reindirizzamento'
redislist:
    alert:
        delete_all: 'All Redis keys have been deleted.'
        error: 'An error has occurred ! The key %key% has not been deleted.'
        success: 'The Key %key% has been deleted !'
    delete_all_keys: 'Delete All'
    delete_all_keys_title: 'Delete all keys'
    delete_title: 'Delete the key %key%'
    no_key: 'No Redis Key stored.'
registration:
    buyer: Acquirente
    error:
        identification_already_used: 'Questa identificazione è già utilizzata su questo sistema'
        identification_already_used_alert: 'La tua azienda ha già un account su StationOne. Il proprietario dell''account può darti accesso. Se non conosci il proprietario dell''account, inviaci una richiesta con il modulo di contatto. Vuoi andare al modulo di contatto?'
        technical: 'Si è verificato un errore tecnico durante la creazione del tuo account. Si prega di contattare il supporto.'
        userDisabled: 'L''utente è stato disattivato. Se desideri riattivare questo account utente, contatta StationOne tramite il modulo di <a href=''%url%''>contact</a>'
    label: 'Voglio unirmi come'
    selectType: 'Seleziona il tuo conto'
    vendor: Venditore
resetting:
    check_email: '| È stata inviata una email Contiene un link su cui cliccare per reimpostare la password.'
    newpwd: 'Modifica della tua password'
    request:
        submit: 'Reimposta password'
    reset:
        submit: Modifica
search:
    advanced_search:
        submit: Cerca
        title: 'Ricerca avanzata'
    compatible_products_with: 'Prodotti compatibili con'
    departments:
        all_departments: 'Tutti i dipartimenti'
        bearing: Cuscinetti
        brake_disc: 'Disco rotto'
        camera: Telecamera
        filter: Filtri
        glazing: Vetri
        screen: Schermo
    filters: Filtri
    help: Aiutami
    in_compatible_products: 'Cerca in prodotti compatibili'
    no_custom_ref_found: 'Nessun riferimento trovato nel tuo catalogo.'
    no_offer: 'Siamo spiacenti, ma non siamo riusciti a trovare risultati.'
    no_offer_in_catalog: 'Nessun risultato corrisponde alla tua ricerca.'
    page: Pagina
    pagination:
        next: Successivo
        'on': su
        previous: Precedente
    products: Prodotti
    result_label: 'Risultato della ricerca (%totale% risultati)'
    results: risultati
    results_for: per
    searchbar:
        advanced_search: 'Ricerca avanzata +'
        custom_search: 'Cerca nel mio catalogo'
        in_catalog: 'Il moi catalogo'
        in_marketplace: Marketplace
        in_product_compatibility: 'Prodotti compatibili'
        mobile_placeholder: 'Cerca un prodotto'
        placeholder: 'Inizia a scrivere per trovare un prodotto'
    show: Mostrare
    sidebar:
        category: categoria
        commons: Comune
        departments: Dipartimenti
        refine_by: 'Affina per'
        search: 'Cerca un termine'
        see_more: 'Vedi di più'
        specific: Specifico
        up: '< Indietro'
    sort_by: 'Ordina per'
    sort_form:
        delivery_time: 'Tempi di consegna (ordine crescente)'
        newest: 'Più recente (per primi)'
        price_max: 'Prezzo (decrescente)'
        price_min: 'Prezzo (crescente)'
        relevance: Rilevanza
    title: Cerca
security:
    login:
        create_account: 'Crea un conto'
        fail: 'Credenziali non valide.'
        footer_text: 'Non sei ancora un membro?'
        login: 'Disconnettiti -> Accesso'
        role_denied: ''
        title: 'Accesso acquirente'
shipping:
    option:
        cheapest: 'Prezzo ottimizzato'
        fastest: Urgente
        no_merchant_shipping: 'Questo venditore non offre un''offerta di trasporto tramite StationOne'
        no_shipping_available: 'Nessun trasporto disponibile. Per ulteriori informazioni, contattare StationOne'
        noshipping: 'Senza transporto'
shipping_point:
    form:
        address:
            label: Indirizzo
        comment:
            label: 'Commenti (orari di apertura al ricevimento merci, specificità per raggiungere la reception, ...)'
        contact:
            accountant: 'Ufficio contabilità'
            label: 'Contatto della persona incaricata dell''accoglienza'
        documents_requests:
            label: 'Documenti richiesti per il ricevimento della merce'
        first: 'Indirizzo di consegna principale'
        name: 'Nome del punto di consegna'
        packaging_request:
            label: 'Istruzioni per l''imballaggio'
            tips: 'max char = 255'
        save: Salva
        title_common: 'indirizzo di consegna'
        title_edit: 'Modifica una'
        title_new: Nuova
site:
    form:
        accountant_email: 'Indirizzo email a cui verranno inviate le fatture'
        add: 'Aggiungi un centro di costo'
        add_contact: 'Aggiungi un contatto'
        add_modal: 'Nuovo centro di costo'
        add_shipping_address: 'Aggiungi un indirizzo di consegna'
        adresse: Indirizzo
        afternoon:
            end: 'Chiusura pomeridiana'
            start: 'Apertura pomeridiana'
        authorization: 'Autorizzazione prefettizia'
        cancel: Annulla
        comment: Commento
        complement: Integrazione
        copy: copia
        corporate_name: 'Ragione sociale'
        cpZipCode: 'CAP / città'
        create: Crea
        created: 'Il tuo centro di costo è stato creato'
        days: Giorni
        default_user:
            label: ''
            placeholder: ''
        delete:
            content: 'Sei sicuro di voler eliminare questo centro di costo?'
            shipping_point: 'Sei sicuro di voler cancellare questo indirizzo di spedizione?'
        delete_shipping_address: Elimina
        documentation_request_1: 'Documento richiesto 1'
        documentation_request_10: 'Documento richiesto 10'
        documentation_request_2: 'Documento richiesto 2'
        documentation_request_3: 'Documento richiesto 3'
        documentation_request_4: 'Documento richiesto 4'
        documentation_request_5: 'Documento richiesto 5'
        documentation_request_6: 'Documento richiesto 6'
        documentation_request_7: 'Documento richiesto 7'
        documentation_request_8: 'Documento richiesto 8'
        documentation_request_9: 'Documento richiesto 9'
        error: 'Errore durante la creazione del tuo centro di costo! Per favore compila tutti i campi'
        friday: Venerdì
        identification: 'Numero di Siret'
        info:
            operator_needed: 'Le informazioni sul tuo sito non possono essere modificate direttamente. Se si desidera apportare modifiche, si prega di contattare StationOne tramite il modulo di <a href=''/ticket/create/''>contact</a>.'
        legales_doc: 'Documenti legali'
        main_contact: 'Contatto principale'
        modify_shipping_address: Modifica
        monday: Lunedi
        morning:
            end: 'Mattino vicino'
            start: 'Mattina aperta'
        name: 'Nome del centro di costo'
        opening_time: 'Orario di apertura'
        other_contacts: 'Altri contatti'
        packaging_request_1: 'Istruzioni per l''imballaggio 1'
        packaging_request_2: 'Istruzioni per l''imballaggio 2'
        packaging_request_3: 'Istruzioni per l''imballaggio 3'
        placeholder:
            name: 'Nome del centro di costo'
        save: Salva
        submit: Invia
        submitError: 'Devi presentare la tua azienda prima di presentare il tuo sito'
        thursday: Giovedi
        title: 'centro di costo'
        tuesday: Martedì
        wednesday: Mercoledì
    header:
        edit: 'Modifica del centro di costo:'
        list: 'Elenco dei centri di costo'
    list:
        deactivation:
            ko: 'Errore durante l''eliminazione del centro di costo'
            ok: 'Il centro di costo è stato eliminato con successo'
            users_exist: 'Gli utenti sono collegati a questo centro di costo, quindi non è possibile eliminare questo centro di costo'
        default_user:
            not_defined: 'No user is defined for this cost center'
            title: 'Default user for this cost center'
        no_user: 'Nessun utente affiliato'
        title: 'Utenti affiliati a questo centro di costo'
        users:
            action: Azione
            firstname: Nome
            function: Funzione
            id: N.º
            lastname: Cognome
            role: Ruolo/Mansione
stats:
    accrued:
        'no': 'No'
        title: 'Importo cumulativo'
        'yes': Si
    cost_center:
        all: Tutto
        title: 'Centro di costo'
    orderAmount: 'Quantità di ordini'
    year:
        title: Anno
status:
    draft: 'Da completare'
    pending: 'In attesa di conferma da parte dell''operatore'
    valid: 'Convalida dall''operatore'
system:
    setting:
        form:
            submit: Modifica
    settings:
        homepage:
            disclaimer:
                title: 'ID video'
                video_src: Video
            slider:
                title: 'Impostazioni dello slider'
            title: 'Impostazioni della pagina iniziale'
            video_1_src: 'ID YouTube del video della Slide 2'
            video_2_src: 'ID YouTube del video della Slide 3'
            video_en_src: 'Video in inglese'
            video_fr_src: 'Video in francese'
        notifications:
            command:
                title: Ordini
            completion_recall_number_of_day: 'Numero di giorni prima della notifica del promemoria di completamento'
            title: Notifiche
        offers:
            offer_1: 'Identificativo Izberg 1a Offerta popolare'
            offer_2: 'Identificativo Izberg 2a Offerta popolare'
            offer_3: 'Identificativo Izberg 3a Offerta popolare'
            offer_4: 'Identificativo Izberg 4a Offerta popolare'
            offer_5: 'Identificativo Izberg 5a Offerta popolare'
            offer_6: 'Identificativo Izberg 6a offerta popolare'
            offer_7: 'Izberg identifier 7th popular offer'
            offer_8: 'Izberg identifier 8th popular offer'
            popular:
                title: 'Offerte popolari'
            title: 'Impostazione delle offerte'
        security:
            login:
                title: 'Impostazioni di sicurezza del Login'
            login_attempt_max: 'Numero massimo di tentativi di accesso'
            login_banned_user_unlock_timeout: 'Sblocco degli utenti bannati dopo {x} minuti'
            title: 'Impostazioni di sicurezza'
        testimonials:
            fader_speed: 'Velocità di rotazione delle testimonianze (in millisecondi)'
            max_age: 'Età massima in mesi (oltre, le testimonianze non verranno visualizzate)'
            max_items: 'Numero massimo di testimonianze da visualizzare'
            parameters:
                title: 'Impostazioni delle testimonianze'
            title: Testimonianze
        update_error: 'Si è verificato un errore durante l''aggiornamento delle impostazioni'
        update_success: 'Impostazioni aggiornate correttamente'
tab_infos_seller:
    cgv: 'Condizioni generali di vendita'
    frame_contract: 'Under particular condition of frame contract : %frame_contract%'
    minimum_order_amount: 'Importo minimo dell''ordine nel carrello per questo venditore : %amount%'
    presentation: 'Presentazione del venditore'
    see_all_products: 'Vedi tutti i prodotti'
ticket:
    common:
        add_file: 'Aggiungi un file'
        administrator_user: '%fullname% (operatore)'
        authorized_files_extensions_message: 'Sono consentiti solo pdf, jpg, gif, png, tiff, xls, xlsx'
        file_reset: Svuota
        message: Messaggio
        nofiles: 'Nessun file selezionato'
        number: 'Numero del ticket'
        subject: Oggetto
        submit: Invia
        update: Invia
    create:
        company: Impresa
        email: Email
        firstname: Nome
        foradmin: StationOne
        forvendor: 'un venditore'
        function: Funzione
        lastname: Cognome
        message_text: Messaggio
        phone: 'Recapito telefonico'
        recipient: 'Scelta dell''azienda'
        title: 'Desideri contattare:'
    edit:
        add: 'Nuova risposta'
        attachment: Allegato
        attachment_button: 'Aggiungere i file'
        author: Autore
        close: 'Chiudi la discussione'
        close_thread: 'This thread was closed by %firstname% %lastname% on %date%'
        close_thread_anonymous: 'This thread was closed by an anonymous user on %date%'
        closed: 'Chiuso il'
        company: Azienda
        date: 'Ticket creato il'
        id: ID
        link: 'Torna all''elenco dei messaggi'
        message_label: Messaggi
        message_placeholder: 'Inserisci il messaggio'
        message_text: Risposta
        new_message: 'NUOVO MESSAGGIO:'
        operator: (Operatore)
        recipient: Destinatario
        reopen: Riapri
        save_message: SALVA
        subject: Oggetto
        timeFormat: 'à $1h$2min$3s'
        title: Ticket
    error:
        create: 'Si è verificato un errore durante la creazione del tuo ticket'
        update: 'Si è verificato un errore durante l''aggiornamento del tuo ticket'
    list:
        actions: Azioni
        add: 'Scrivi un nuovo messaggio'
        all: Tutti
        author: Autore
        closed: Chiusi
        company: Azienda
        createdAt: 'Creato il'
        empty: 'Non hai messaggi'
        export_csv: 'Esporta in CSV'
        knp_next: Successivo
        knp_previous: Precedente
        lastAt: 'Modificato il'
        main_contact: 'Creatore di ticket'
        me: tu
        nb_messages: 'Numero di messaggi'
        next: 'Vedi i vecchi ticket'
        number: ID
        opened: Aperti
        previous: 'Vedi i ticket più recenti'
        sent_to: 'Inviato a'
        status: Status
        sujet: Oggetto
        title:
            resolved: 'Messaggi risolti'
            standard: Messaggi
    status:
        STATUS_CLOSED: Chiusa
        STATUS_INFORMATION_REQUESTED: 'Richiesta di informazioni'
        STATUS_INVALID: Invalido
        STATUS_IN_PROGRESS: 'In corso'
        STATUS_NEW: Nuovo
        STATUS_ON_HOLD: 'In attesa'
        STATUS_OPEN: Aperto
        STATUS_OPERATOR_RESPONDED: Aperto
        STATUS_RESOLVED: Chiusa
        STATUS_USER_RESPONDED: Aperto
    success:
        create: 'La tua richiesta è stata creata con successo'
        update: 'La richiesta è stata aggiornata con successo'
    waylf:
        title: 'Che cosa sta cercando ?'
user:
    form:
        ROLE_API: ''
        ROLE_BUYER_ADMIN: 'Acquirente (responsabile dell’account)'
        ROLE_BUYER_BUYER: Acquirente
        ROLE_BUYER_PAYER: Acquirente/Pagatore
        ROLE_OPERATOR: Operatore
        ROLE_SUPER_ADMIN: Amministratore
        add: 'Crea un utente'
        company: Azienda
        email: Email
        firstname: Nome
        function:
            0: Funzione
            label: ''
            mandatory: ''
        lastname: Cognome
        phone1: 'Telefono principale'
        phone2: 'Telefono secondario'
        role: Ruolo
        site: 'Centri di costo'
    registration:
        email: Email
    resetting:
        title: 'Modifica della password'
validator:
    date: 'Si prega di inserire una data valida.'
    email: 'Inserisci un indirizzo email valido.'
    number: 'Si prega di inserire un numero valido.'
    remote: 'Correggi questo campo.'
    required: 'Questo campo è obbligatorio.'
    url: 'Inserisci un URL valido.'
wishlist:
    add_new: 'Aggiungi una nuova lista dei kit'
    add_to_cart: 'Aggiungi al carrello'
    charge:
        confirm: 'Questa azione eliminerà il contenuto del tuo carrello prima di aggiungere le offerte da questo elenco'
        error: 'Si è verificato un errore durante l''aggiunta di offerte al carrello'
        success: 'Le offerte della tua lista sono state aggiunte al carrello'
    delete:
        error: 'Si è verificato un errore durante l''eliminazione della tua lista dei kit'
        success: 'La tua lista è stata cancellata'
    delete_confirm: 'Sei sicuro di voler cancellare questa lista dei desideri?'
    go_back: 'Torna alla lista'
    item:
        delete:
            error: 'Si è verificato un errore durante l''eliminazione della tua offerta'
            success: 'La tua offerta è stata cancellata'
        delete_confirm: 'Sei sicuro di voler eliminare questa offerta?'
        noItem: 'Non hai offerto nella tua lista'
        update:
            error: 'Si è verificato un errore durante l''aggiornamento della quantità'
            success: 'La quantità è stata aggiornata'
    new_name: 'Nome della nuova lista'
    none: 'Non hai una lista dei kit'
    notification_message:
        no_price_offer: 'Lista dei kit non valida'
    save: Salva
    save_error: 'Si è verificato un errore durante il salvataggio della lista'
    save_in: 'Aggiungi alla lista dei kit'
    save_success: 'La wishlist ha salvato con successo'
    table:
        days: giorni
        delivery_time_item: 'Tempi prima della consegna'
        delivery_time_wishlist: 'Tempo di consegna della lista dei kit'
    title: 'Lista dei kit'
'yes': Sì
seller:
    general_condition: 'Condizioni generali di vendita'
