address:
    form:
        address: Adresse
        address2: '<PERSON><PERSON><PERSON><PERSON>z (Gebäude / Residenz /...)'
        address2_placeholder: 'Gebäude / Residenz /...'
        all_country_placeholder: 'Alle Länder'
        check: Prüfen
        city: Stadt
        country: Land
        country_placeholder: 'Ein Land auswählen'
        email: E-Mail-Adresse
        first_lastname: 'Nachname und Vorname des Kontakts'
        phone1: Haupttelefon
        phone1_placeholder: 'Diese Telefonnummer ist erforderlich'
        phone2: Zweittelefon
        region: Region
        region_placeholder: 'Eine Region auswählen'
        update:
            error: 'Bei der Aktualisierung der Unternehmensdaten ist ein Fehler aufgetreten'
            success: 'Die Unternehmensdaten wurden aktualisiert'
        zipcode: Postleitzahl
address_form_all_country_placeholder: 'Alle Länder'
admin:
    logout: Abmeldung
admin_menu:
    admin_users:
        add_user: 'Einen Benutzer hinzufügen'
        admin_list: Administratoren
        label: '<PERSON><PERSON>'
        user_list: <PERSON><PERSON><PERSON>
    bafv_requests_menu:
        label: 'BAFV catalog prices requests'
    companies_menu:
        all_companies: 'Alle Einkäufer'
        costs_centers: Kostenzentren
        label: Einkäufer
        users: Benutzer
    dashboard:
        label: Dashboard
    merchant_menu:
        label: Vendors
    message: Nachrichten
    messages_menu:
        add: 'Eine Diskussion erstellen'
        label: Nachrichten
        list: 'Offene Anträge'
        resolved: 'Bearbeitete Anträge'
    orders: Bestellungen
    other_menu:
        api_doc: ''
        automatic_controls: 'Automatic Controls'
        feeback_form: Feedback-Formular
        label: Sonstiges
        logo: 'Logo objekt'
        notifications: Nachrichten
        payloads: ''
        redis_keys: 'Redis Schlüsselliste'
        search_historization:
            label: Suchhistorie
        serches_list: Suchliste
        slider: 'Element des Slider'
        term_payment: 'Anträge auf Ratenzahlung'
        top_mismatch_catalog_references: 'Top mismatched catalog references'
    redirects:
        label: Umleitungen
    sys_parameters:
        label: Systemparameter
    web_content:
        add: 'Web-Content hinzufügen'
        label: Web-Content
        list: 'Alle Web-Contents'
        popular_offers: 'Gefragte Angebote'
algolia:
    attributes:
        BAB10_MOQ: Mindestmenge
        BAE10_Incoterm: Incoterm
        CAA10_DTR: DTR
        DQL10_Friction_coef: Reibungskoeffizient
        Threshold_2_Prcie: Mindestpreis
    id: Marktplatz-ID
    merchant:
        name: Verkäufer
    product:
        description: Beschreibung
        manufacturer: Hersteller
        name: Produkt
back:
    admin:
        filter:
            activeBtn: 'Die aktiven Administratoren anzeigen'
            inactiveBtn: 'Die inaktiven Administratoren anzeigen'
    back: Zurück
    back_to_list: 'Zurück zur Liste'
    bafv_request:
        list:
            buyer_code: 'Buyer Code'
            buyer_name: 'Buyer Name'
            status: Status
            submitted_on: 'Antrag erstellt am'
            vendor_company_name: 'Firmennames des Verkäufers'
    catalogue_references:
        not_found_mismatch_references: 'Mismatched references not found'
        reference: Referenzen
        top_mismatch_references_title: 'Top mismatched references'
        total: Gesamt
    commons:
        actions: Aktionen
        add: Hinzufügen
        all: Alle
        cancel: Stornieren
        delete: Löschen
        disable: Deaktivieren
        edit: Bearbeiten
        export_csv: 'Als CSV exportieren'
        id: ID
        piggy: 'Einloggen als'
        piggy_exit: 'Ausloggen als'
        view: Anzeigen
    company:
        all_term_payment: Alle
        all_term_payment_title: 'Alle Unternehmen mit Ratenzahlungen (validiert oder anstehend)'
        companyInfo:
            activation:
                ko: 'Fehler bei der Aktivierung des Unternehmens'
                ok: 'Das Unternehmen wurde erfolgreich aktiviert'
            address: Adresse
            addressComplement: Zusatz
            billing:
                service: Abteilung
                title: Rechnungstellung
            city: Stadt
            contact:
                adv: 'Kontakt Verkaufsverwaltung'
                billing: Rechnungsadresse
                firstname: Vorname
                lastname: Nachname
                logistic: Logistikkontakt
                mail: E-Mail-Adresse
                main: Hauptkontakt
                noInformation: 'Keine Information'
                phone1: Haupttelefon
                phone2: Zweittelefon
            country: Land
            deactivate:
                content: 'Möchten Sie dieses Unternehmen wirklich deaktivieren?'
                title: 'Deaktivierung des Unternehmens'
            deactivation:
                ko: 'Fehler bei der Deaktivierung des Unternehmens'
                ok: 'Das Unternehmen wurde erfolgreich deaktiviert'
            deactivationReason: Deactivationsgrund
            document:
                title: Dokument
            edit: Ändern
            endpointUrl: ''
            info:
                code: Unternehmenscode
                name: Unternehmensname
                title: Unternehmensinformation
            invalidate:
                ko: 'Fehler beim Devalidieren des Unternehmens'
                ok: 'Das Unternehmen wurde erfolgreich devalidiert'
            region: Region
            reject:
                ko: 'Fehler beim Ablehnen des Unternehmens'
                ok: 'Unternehmen wurde erfolgreich abgelehnt'
            rejectedReason: Ablehnungsgrund
            termpayment:
                active: 'Die Ratenzahlung aktivieren'
                askKo: 'Beim Antrag auf Ratenzahlung ist ein Fehler aufgetreten'
                askOk: 'Der Antrag auf Ratenzahlung wurde erfolgreich durchgeführt.'
                authorization: Zahlungsautorisierung
                deny: 'Die Ratenzahlung verweigern'
                disabled: 'Die Ratenzahlung ist deaktiviert.'
                enabled: 'Der Antrag wurde akzeptiert am'
                inactive: 'Die Ratenzahlung deaktivieren'
                pending: 'Der Antrag wurde gestellt am'
                reason: Ablehnungsgrund
                removeKo: 'Beim Löschen der Ratenzahlung ist ein Fehler aufgetreten'
                removeOk: 'Die Ratenzahlung wurde erfolgreich gelöscht.'
                title: 'Zahlungsfrist Antragsdatum'
            validate:
                ko: 'Fehler beim Validieren des Unternehmens'
                ok: 'Das Unternehmen wurde erfolgreich validiert'
            zipCode: Postleitzahl
        endpointUrl: ''
        export: 'Als CSV exportieren'
        filter_clear: Löschen
        filter_title: Filter
        list:
            add:
                site: 'Ein Kostenzentrum hinzufügen'
                user: 'Einen Benutzer hinzufügen'
            category: Kategorie
            city: Stadt
            costCenters: Kostenzentren
            country: Land
            created: Kontoerstellung
            empty: 'Kein Unternehmen wartet auf Validierung'
            filter:
                activeBuyer: 'Aktive Käufer'
                all: 'Alle Unternehmen'
                caMax: 'und:'
                caMin: 'Umsatz zwischen:'
                creationMax: 'und:'
                creationMin: 'Erstellungsdatum zwischen:'
                disabled: Deaktiviert
                purchaseMax: 'und:'
                purchaseMin: 'Bestellungsdatum zwischen:'
                submit: Filtern
                term_payment_date_max: 'und:'
                term_payment_date_min: 'Antragsdatum / Annahme zwischen:'
                toConfirm: 'Zu validieren'
                tonMax: 'und:'
                tonMin: 'Tonnage zwischen:'
            identification: Code
            last: 'Letzte Anmeldung'
            name: Unternehmen
            purchases: 'Einkauf (USD)'
            revenue: 'Erzielter Umsatz'
            status: Status
            status_company:
                acceptable: 'Wartet auf Validierung'
                all: Alle
                disabled: Deaktiviert
                draft: Entwurf
                initial: Initial
                pending: 'Wartet auf Validierung'
                rejected: Abgelehnt
                valid: Aktiv
            termpayment_moneytransfert_accept: 'Die Ratenzahlungen validieren'
            termpayment_moneytransfert_date_accepted: Angenommen
            termpayment_moneytransfert_date_requested: Beantragt
            termpayment_moneytransfert_deny: 'Die Ratenzahlungen ablehnen'
            termpayment_moneytransfert_enabled: Aktiv
            termpayment_moneytransfert_pending: Anstehend
            tonnes: 'Tonnen am Zähler'
            type_:
                1: Einkäufer
                2: Verkäufer
                all: Alle
                buyer: Einkäufer
            user_type: Benutzertyp
            users: Benutzer
        menu:
            general: Allgemein
            messages: Nachrichten
            orders: Bestellungen
            sites: Kostenzentrum
            users: Benutzer
        order:
            creation_date: Auftragsdatum
            export: 'PDF export'
            number: 'Order number'
            price: Preis
            shipping: Lieferadresse
            status:
                cancelled: 'Stornierte Aufträge'
                past: 'Erledigte Aufträge'
                running: 'Offene Aufträge'
                title: Status
        pending_term_payment: Anstehend
        pending_term_payment_title: 'Alle Unternehmen warten auf Ratenzahlung'
        term_payment_empty: 'Kein Unternehmen wartet auf die Validierung der Zahlungsfrist'
        termpayment_moneytransfert_accepted: 'Sie haben die Ratenzahlung für dieses Unternehmen akzeptiert'
        termpayment_moneytransfert_accepted_error: 'Fehler bei der Bearbeitung des Antrags auf Annahme der Ratenzahlungen für dieses Unternehmen'
        termpayment_moneytransfert_rejected: 'Sie haben die Ratenzahlung für dieses Unternehmen abgelehnt'
        termpayment_moneytransfert_rejected_error: 'Fehler bei der Ablehnung der Ratenzahlungen für dieses Unternehmen'
        users:
            create_ticket: 'Ein Ticket erstellen'
            term_payment_empty: 'Kein Unternehmen wartet auf Validierung von Ratenzahlungen'
    deactivate:
        ok: Ok
        reason: 'Grund ?'
    index_page:
        companies_term_payment_request_to_validate: 'Unternehmen, die auf die Annahme von Zahlungsfristen warten'
        companies_to_validate: 'Unternehmen wartend auf Validierung'
        see_all: 'Siehe alle'
        unread_messages: 'Ungelesene Nachrichten'
    logo:
        commons:
            add: Hinzufügen
            addTitle: 'Neues Logo'
            backgroundImage: Hintergrundsbild
            create: Erstellen
            editTitle: 'Logo ändern'
            link: Link
            lock: Gesperrt
            order: Order
            published: veröffentlicht
            setTitleVisible: 'Titel auf der Homepage sichtbar machen'
            status: Status
            title: Titel
            update: Bearbeiten
        create:
            confirm: 'Logo hinzugefügt'
            error: 'An error occurred during object creation'
        delete:
            confirm: 'Logo object deleted'
            error: 'An error occurred during object deletion'
        edit:
            actualImage: 'Current Image'
            imageP: 'Select a new image to modify the existing one'
        list:
            actions: Actions
            createdAt: Created
            header: 'Logo object list'
            lang: Languages
            updatedAt: Modified
        status:
            draft: Draft
            published: Published
        update:
            confirm: 'Logo object modified'
            error: 'An error occurred during object modification'
    merchant:
        form:
            update:
                error: 'Error while updating the vendor'
                success: 'Verkäufer wurde erfolgreich aktualisiert'
        list:
            email: Email
            firstname: 'First Name'
            identification: Identification
            lastname: 'Last Name'
            list: Email
            name: Name
            status:
                accepted: Accepted
                all: All
                pending: Pending
                rejected: Rejected
                title: Status
        merchantInfo:
            country: Country
            currency:
                eur: EUR
                placeholder: 'Choose a currency'
                title: Währung
                usd: USD
            edit: Bearbeiten
            email: Email
            firstname: Vorname
            identification: Identifikation
            info:
                title: 'Verkäufer Information'
            lastModifiedAt: 'Last modified at'
            lastModifiedBy: 'Last modified by'
            lastname: Lastname
            name: Name
            password: Password
            phoneNumber: 'Phone number'
            registrationDate: Anmeldedatum
            reject:
                ko: 'An error occurred while rejecting the vendor'
                ok: 'Vendor has been rejected with success'
            rejectedReason: 'Rejected reason'
            status: Status
            validate:
                ko: 'An error occurred while validating the vendor'
                ok: 'Verkäufer wurde erfolgreich aktualisiert'
    notification:
        edit:
            body: Corps
            confirm: 'Die Benachrichtigung wurde erfolgreich aktualisiert'
            empty_email_error: 'Please, enter a valid email address'
            externalLinkType: 'Use an external URL'
            link: 'Link der Schaltfläche'
            linkExternal: 'External URL'
            linkText: 'Text der Schaltfläche'
            send_button: 'Aktualisieren und E-Mail senden'
            slug: Id
            test: 'Test email address'
            title: Titel
        list:
            creation: 'Erstellt am'
            disable: 'Aktiviert: Klicken Sie zu deaktivieren'
            enable: 'Deaktiviert: Klicken Sie zu aktivieren'
            header: E-Mail-Vorlagen
            lang: Sprachen
            slug: Id
            title: Betreff
            update: 'Aktualisiert am'
    page:
        add: 'Eine statische Seite erstellen'
        draft: Entwurf
        list:
            author: Autor
            creation: 'Erstellt am'
            header: 'Statische Seiten'
            lang: Sprachen
            modal:
                title: 'Wählen Sie die Anzeigesprache'
            slug: 'Permalink (Slug)'
            status: Status
            title: Titel
            update: 'Aktualisiert am'
        modal:
            cancel_confirm: 'Möchten Sie diesen Inhalt wirklich löschen?'
        published: Veröffentlicht
    payloads:
        detail:
            title: 'Payload : %id%'
        list:
            company_name: ''
            creation: Date
            header: 'Payloads list'
            identifier: Identifier
            payload_id: ''
            status: Status
            type: Type
        statuses:
            status_created: CREATED
            status_failed: FAILED
            status_success: SUCCESS
        types:
            cost_center_payload: ''
            invoice_payload: ''
            order_payload: ''
            purchase_request_item_payload: 'PURCHASE REQUEST'
    redirect:
        add: 'Eine Umleitung erstellen'
        list:
            confirm_delete: 'Möchten Sie diese Umleitung wirklich löschen?'
            confirm_delete_title: Bestätigung
            creation: 'Erstellt am'
            destination: Ziel
            header: Umleitungen
            origin: Ursprung
            type: Typ
            update: 'Aktualisiert am'
    search_historization:
        company_name: Firmenname
        date: Datum
        datemax: zu
        datemin: 'Suchdatum ab:'
        filter_label: Filter
        id: id
        is_anonymous: 'Ist anonym'
        list:
            filter:
                date: 'Filtern nach Suchdatum'
        nb_hits: 'Number of hits'
        offer_name: 'Product title'
        offer_sku: SKU
        searched_term: 'Searched Term'
        user_full_name: 'User full name'
    shipping_points:
        add: 'Einen Lieferort hinzufügen'
        delete: 'Den Lieferort löschen'
        delete_card: Löschen
        edit: 'Den Lieferort ändern'
        edit_card: Ändern
        error_add: 'Bei der Erstellung des Lieferorts ist ein Fehler aufgetreten'
        error_edit: 'Bei der Änderung des Lieferorts ist ein Fehler aufgetreten'
        form:
            name: 'Name des Lieferorts'
            save: Speichern
        shipping_points: Lieferorte
        success_add: 'Der Lieferort wurde erfolgreich erstellt'
        success_delete: 'Der Lieferort wurde erfolgreich gelöscht'
        success_edit: 'Der Lieferort wurde erfolgreich geändert'
    site:
        activation:
            ko: 'Fehler bei der Aktivierung des Kostenzentrums'
            ok: 'Das Kostenzentrum wurde erfolgreich aktiviert'
        add: 'Ein Kostenzentrum hinzufügen'
        chargement:
            title: Verladestandorte
        deactivate:
            content: 'Möchten Sie diesen Standort wirklich deaktivieren?'
            title: 'Deaktivierung des Standorts'
        deactivation:
            ko: 'Fehler bei der Deaktivierung des Kostenzentrums'
            ok: 'Das Kostenzentrum wurde erfolgreich deaktiviert'
        delete: Löschen
        filter_clear: LÖSCHEN
        filter_title: Filter
        form:
            address: Adresse
            address2: Adresszusatz
            city: Stadt
            company: Unternehmen
            contact:
                email: E-Mail-Adresse
                firstname: Vorname
                function: Funktion
                lastname: Nachname
                main: Hauptkontakt
                other: Kontakt
                phone1: Haupttelefon
                phone2: Zweittelefon
            country: Land
            document:
                legalesDoc: 'Präfektorale Genehmigungen'
                title: Dokumente
            id: ID
            info: 'Allgemeine Informationen'
            name: 'Name des Kostenzentrums'
            operatorInfos:
                canPack: 'Ability to pack for export'
                chargeWay: 'Charge way'
                device: Device
            region: Region
            siret: SIRET
            zipCode: Postleitzahl
        infos: Informationen
        invalidate:
            ko: 'Fehler bei der Deaktivierung des Kostenzentrums'
            ok: 'Das Kostenzentrum wurde erfolgreich devalidiert'
        list:
            add: 'Einen neuen Standort hinzufügen'
            address: Adresse
            city: Stadt
            company: Unternehmen
            contactName: Kontakt
            contactPhone: Telefon
            country: Land
            editName: 'Ändern'
            filter:
                all: 'Alle Kostenzentren'
                disabled: Deaktiviert
                submit: FILTERN
                toConfirmed: 'Zu validieren'
            name: Nachname
            nbShippingPoints: 'Anzahl Lieferstellen'
            status: Status
            status_site:
                disabled: Deaktiviert
                enabled: Aktiviert
            zipCode: Postleitzahl
        livraison:
            title: Kostenzentren
        modification:
            edit_name: 'Edit Cost Center'
            ko: 'Fehler bei der Änderung des Kostenzentrums'
            ok: 'Das Kostenzentrum wurde erfolgreich aktualisiert'
        suppression:
            ok: 'Das Kostenzentrum wurde erfolgreich gelöscht.'
        validate:
            error: 'Vor der Validierung dieses Kostenzentrums müssen Sie das Unternehmen validieren'
            ko: 'Fehler bei der Validierung des Kostenzentrums'
            ok: 'Das Kostenzentrum wurde erfolgreich validiert'
    slider:
        commons:
            add: Hinzufügen
            addTitle: 'Ein neues Element zum Slider hinzufügen'
            backgroundImage: Hintergrundbild
            create: Erstellen
            editTitle: 'Ein Element des Slider ändern'
            link: Link
            lock: gesperrt
            order: Order
            published: veröffentlicht
            setTitleVisible: 'Make the title visible on the homepage'
            status: Status
            title: Titel
            update: Ändern
        create:
            confirm: 'Das Element des Slider wurde erfolgreich hinzugefügt'
            error: 'Bei der Erstellung des neuen Elements ist ein Fehler aufgetreten'
        delete:
            confirm: 'Das Element des Slider wurde erfolgreich gelöscht'
            error: 'Beim Löschen des Elements ist ein Fehler aufgetreten'
        edit:
            actualImage: 'Aktuelles Bild'
            imageP: 'Wählen Sie ein neues Bild aus, wenn Sie es ändern möchten'
        list:
            actions: Aktionen
            createdAt: 'Erstellt am'
            header: 'Liste der Elemente des Slider'
            lang: Sprachen
            updatedAt: 'Geändert am'
        status:
            draft: Entwurf
            published: Veröffentlicht
        update:
            confirm: 'Das Element des Slider wurde erfolgreich geändert.'
            error: 'Bei der Änderung des Elements ist ein Fehler aufgetreten'
    ticket:
        filter:
            closed: Geschlossen
            company: Unternehmen
            creationMax: und
            creationMin: 'Erstellungsdatum zwischen'
            export_csv: 'CSV Export'
            main_contact: 'Verfasser der Nachricht'
            modificationMax: und
            modificationMin: 'Änderungsdatum zwischen'
            object: Betreff
            opened: Geöffnet
            submit: Senden
            title: Filter
    user:
        change_type:
            content: 'Are you sure you want to change this buyer account type ?'
            title: 'Change buyer account type'
        connection:
            browser: Browser
            date: Datum
            hour: Uhrzeit
            ip: Ip
            os: OS
            title: Verbindungshistorie
            type: Typ
            version: Version
        deactivate:
            content: 'Möchten Sie diesen Benutzer wirklich deaktivieren?'
            title: 'Deaktivierung dieses Benutzers'
        filter:
            activeBtn: 'Alle Benutzer anzeigen'
            all: 'Alle Benutzer'
            connectionMax: 'und:'
            connectionMin: 'Letzte Anmeldung zwischen:'
            creationMax: 'und:'
            creationMin: 'Erstellungsdatum zwischen:'
            disabled: 'Deaktivierte Benutzer'
            enabled: 'Aktive Benutzer'
            filter_clear: Löschen
            filter_title: Filtern
            inactiveBtn: 'Die inaktiven Benutzer anzeigen'
            toConfirmed: 'Zu validierende Benutzer'
        form:
            account: Konto
            activate: Aktivieren
            activation:
                ko: 'Fehler bei der Aktivierung des Benutzers'
                ok: 'Der Benutzer wurde erfolgreich aktiviert'
            company: Unternehmen
            confirmPassword: 'Bestätigung neues Passwort'
            creation:
                ko: 'Fehler bei der Erstellung des Benutzers'
                mailKo: 'Diese E-Mail-Adresse wird bereits verwendet'
                mailKoDisabled: 'Diese E-Mail-Adresse wird bereits für einen deaktivierten Benutzer verwendet, den Sie erneut aktivieren können.'
                ok: 'Der Benutzer wurde erfolgreich erstellt'
            deactivate: Deaktivieren
            deactivation:
                ko: 'Fehler bei der Deaktivierung des Benutzers'
                ok: 'Der Benutzer wurde erfolgreich deaktiviert'
            edit: Ändern
            email: E-Mail-Adresse
            firstname: Vorname
            id: ID
            invalidate: Devalidieren
            language: Sprache
            lastname: Nachname
            modification:
                ko: 'Fehler bei der Änderung des Benutzers'
                ok: 'Der Benutzer wurde erfolgreich aktualisiert'
            password: 'Neues Passwort'
            phone1: Haupttelefon
            phone2: Zweittelefon
            reject: Reject
            resetPassword: 'Passwort zurücksetzen'
            resetingPassword:
                ko: 'Fehler bei der Absendung der Anfrage auf Zurücksetzen des Passworts'
                ok: 'Ihre Anfrage auf Zurücksetzen des Passworts wurde gesendet'
            role: Rolle
            sites: Kostenzentrum
            sitesUserKo: 'Ein Benutzer muss mindestens einem Kostenzentrum hinzugefügt werden'
            status: Status
            validate: Validieren
        function: Funktion
        history:
            class:
                AppBundle\Entity\Address: Adresse
                AppBundle\Entity\Company: Unternehmen
                AppBundle\Entity\ComplementaryInformation: 'Complementary information'
                AppBundle\Entity\Contact: Kontaktinformation
                AppBundle\Entity\Country: Land
                AppBundle\Entity\Document: Dokument
                AppBundle\Entity\NodeContent\mail: Vorlageninhalt
                AppBundle\Entity\NodeContent\page: Seiteninhalt
                AppBundle\Entity\Node\mail: E-Mail-Vorlage
                AppBundle\Entity\Node\page: 'Statische Seite'
                AppBundle\Entity\Redirect: Umleitungsregel
                AppBundle\Entity\Region: Region
                AppBundle\Entity\Setting: Konfiguration
                AppBundle\Entity\Site: Standort
                AppBundle\Entity\User: Benutzer
                AppBundle\Entity\ZipCode: Postleitzahl
                Open\TicketBundle\Entity\Ticket: Ticket
                Open\TicketBundle\Entity\TicketMessage: 'Nachricht Ticket'
            date: Datum
            hour: Uhrzeit
            id: Id
            modifications: Änderungen
            new: neu
            objet: 'Betreff geändert'
            old: früher
            operation: Transaktion
            title: 'Historie der Aktionen'
            type:
                create: Erstellung
                delete: Löschen
                update: Aktualisierung
        list:
            activeStatus: Aktiv
            allStatus: All
            company: Unternehmen
            creation: Kontoerstellung
            disable: Disable
            disabled: Deaktivierung
            email: E-Mail-Adresse
            enable: Enable
            firstname: Vorname
            inactiveStatus: Inaktiv
            lastLogin: 'Letzte Anmeldung'
            lastname: Nachname
            phone: Telefon
            role: Rolle
            sites: Kostenzentrum
            status: Status
        role:
            all: Alle
            main:
                ROLE_API: ''
                ROLE_BUYER_ADMIN: 'Account Manager / Administrator'
                ROLE_BUYER_BUYER: Antragsteller
                ROLE_BUYER_PAYER: 'Einkäufer / Zahler'
                ROLE_OPERATOR: 'Betreiber / Operator'
                ROLE_SUPER_ADMIN: Administrator
            secondary:
                ROLE_API: ''
                ROLE_BUYER_ADMIN: 'Account Manager / Administrator'
                ROLE_BUYER_BUYER: Antragsteller
                ROLE_BUYER_PAYER: 'Einkäufer / Zahler'
                ROLE_OPERATOR: 'Betreiber / Operator'
                ROLE_SUPER_ADMIN: Administrator
buyerMenu:
    complete_profile: 'Geben Sie die Informationen für Ihr Profil ein'
    complete_profile_why: 'Sie müssen die nachfolgenden Informationen eingeben, um mit dem Kauf von Produkten beginnen zu können.'
cart:
    accept:
        error: 'Bei der Validierung der Zuweisung ist ein Fehler aufgetreten.'
    add:
        error: 'Beim Hinzufügen des Produkts in den Warenkorb ist ein Fehler aufgetreten.'
        success: 'Ihr Produkt wurde erfolgreich in den Warenkorb hinzugefügt.'
        successUpdate: 'Ihr Produkt wurde aktualisiert'
    article: Artikel
    articles: Artikel
    assign:
        assign_to: BENUTZER
        assign_to_myself: 'Assign cart to myself'
        comment: KOMMENTAR
        error: 'Bei der Zuweisung ist ein Fehler aufgetreten.'
        modal_btn_cancel: Abbrechen
        modal_btn_confirm: Zuweisen
        modal_title: 'Meinen Warenkorb zuweisen'
        noUser: 'Kein Benutzer zum Zuweisen des Warenkorbs'
        success: 'Der Warenkorb wurde erfolgreich zugewiesen.'
    assignment_history: Zuweisungshistorie
    buttons:
        assign: Zuweisen
        back_to_cart: 'Zurück zum Warenkorb'
        checkout: 'Bestellung vornehmen'
        clear_cart: 'Warenkorb löschen'
        continue_shopping: 'Einkauf fortsetzen'
        save_in_wishlist: 'Für später speichern'
        see_assignment: 'Zuordnungsverlauf anzeigen'
        validate: Validieren
    checkout:
        accounting_email: ''
        add_new_address: 'Eine neue Adresse hinzufügen'
        add_new_cost_center: 'Neue Kostenstelle hinzufügen'
        address: Adresse
        address_complement: 'Zusätzliche Adresse'
        area: Region
        assign:
            error: 'Es fehlen Informationen zur Zuordnung Ihres Warenkorbs (Kostenstelle, Lieferadresse, Rechnungsadresse, E-Mail…)'
        billing_address: Rechnungsadresse
        city: Stadt
        cost_center: Kostenstelle
        minimum_order_amount: 'Ein oder mehrere Unterbefehle erreichen nicht die vom Händler angeforderte Mindestbestellmenge. Bitte ändern Sie Ihren Warenkorb.'
        notReady: 'Diese Funktion ist noch nicht verfügbar'
        payment_mode: Zahlungsmodus
        select:
            address_placeholder: 'Wählen Sie eine Adresse aus'
            cost_center_placeholder: 'Kostenstelle auswählen'
            error: 'Bitte wählen Sie eine Kostenstelle, eine Lieferadresse und eine Zahlungsart aus'
            error_billing: 'Es fehlen Informationen zur Validierung Ihres Warenkorbs (Kostenstelle, Lieferadresse, Rechnungsadresse, E-Mail…)'
            payment_mode_placeholder: 'Zahlungsmodus auswählen'
        title: 'Zur Kasse gehen'
        validation_number: 'Bestellnummer des Käufers'
        zipcode: Postleitzahl
        term_and_conditions_must_accept: 'Alle Allgemeinen Geschäftsbedingungen müssen akzeptiert werden, um Ihre Bestellung zu bestätigen'
        buyer_need_to_accept_term_and_conditions: "Der Käufer muss die allgemeinen Verkaufsbedingungen des Verkäufers akzeptieren, um die Bestellung zu bestätigen"
    cost_center:
        comments: Bemerkungen
        contact:
            email: Email
            name: Name
            title: 'Warenannahme:  Allgemeine Informationen, die dem Verkäufer mitgeteilt werden'
        error: 'Beim Aktualisieren der Kostenstelle ist ein Fehler aufgetreten.'
        noSiteId: 'Bitte wählen Sie eine Kostenstelle und eine Adresse'
        packaging_request:
            label: Packanleitung
            none: 'Keine Verpackungsspezifikationen. Sie können es auf Ihrer Kostenstellenseite konfigurieren'
        requested_documents:
            label: 'Wählen Sie die erforderlichen Dokumente für die Lieferung aus'
            none: 'Keine Dokumente erforderlich. Sie können sie auf Ihrer Kostenstellenseite konfigurieren'
    days: tage
    detail: Warenkorbdetails
    empty:
        back: 'Zurück zur Suchseite'
        no_user_title: 'Kostenstelle ohne Benutzer'
        text: 'Ihr Einkaufswagen ist für Sie da.</br>Geben Sie ihr Zweck. Fahren Sie auf der Suchseite fort.'
        title: 'Ihr Warenkorb ist leer'
        user_list: 'Für diese Kostenstelle ist kein Benutzer verfügbar.'
    fca_info: 'Dieses Produkt wird unter folgender Adresse zur Verfügung gestellt:'
    fca_shipping: ' + Versand'
    fca_warning: 'Informationen zu FCA-Produkten finden Sie unter der Lieferadresse des Anbieters.'
    historic:
        comment: Kommentar
        date_assign: Zuweisungsdatum
        user_assigned: 'Benutzer zugewiesen'
        user_who: 'Benutzer, der zugewiesen hat'
    lostItem:
        multiple_removed: '%number% Angebote sind nicht mehr verfügbar und wurden aus Ihrem Warenkorb entfernt'
        single_removed: 'Ein Angebot ist länger verfügbar und wurde aus Ihrem Warenkorb entfernt'
    notification_message:
        offer_price_changed: 'Angebotspreis geändert'
    pending:
        ability_to_assign: 'Fähigkeit zuzuweisen'
        ability_to_pay: Zahlungsfähigkeit
        amount_excl_taxes: 'Betrag Steuern ausgeschlossen'
        cart_reference: 'Warenkorb Referenz'
        creation_date: Erstellungsdatum
        last_comment: 'Letzter Kommentar'
        me: Mir
        now_assigned_to: 'Jetzt zugeordnet zu'
        number_of_products: 'Anzahl der Produkte'
        number_of_sellers: 'Anzahl der Anbieter'
        previous_assignments: 'Bisherige Aufträge'
        rejected: abgelehnt
    quotation:
        error: 'Bei der Berechnung des Angebots ist ein Fehler aufgetreten'
    reject:
        comment: 'Gründe für die Ablehnung'
        error: 'Bei der Ablehnung der Zuweisung ist ein Fehler aufgetreten.'
        success: 'Wagen mit Erfolg abgelehnt'
        title: Ablehnen
    remove:
        error: 'Beim Entfernen Ihres Produkts aus Ihrem Warenkorb ist ein Fehler aufgetreten'
    select:
        placeholder: 'Bitte wählen Sie ein Kostenzentrum aus'
    step:
        step_1: Produktauswahl
        step_2: Lieferoptionen
        step_3: Bestellbestätigung
    table_label:
        buyer_internal_reference: 'Käufer referenz'
        cart_item_comment: 'Kommentar'
        delivery_time: Lieferzeit
        expected_date: 'Voraussichtliches Datum'
        no_vat: 'Keine Mehrwertsteuer für diese Transaktion'
        product_detail: Produktinformationen
        product_name: Produktname
        purchase_request_id: Bestellanforderungs-ID
        quantity: Menge
        shippingtotal: 'Gesamtversand (ohne MwSt.)'
        subtotal: 'Zwischensumme (exkl. MwSt.)'
        subtotal_vat: Zwischensumme
        taxes: MwSt.
        total: 'Gesamtsumme (exkl. MwSt.)'
        total_cart: 'Total cart (excl tax)'
        total_order: Gesamtbestellung
        total_price: Gesamtpreis
        total_vat: Gesamt
        unit_no_price: 'Kein Preis'
        unit_price: Einheitspreis
    update_quantity:
        error: 'Bei der Änderung der Menge ist ein Fehler aufgetreten.'
    warning: Warnung
    wrong_price_message: 'Der Preis für einen oder mehrere Artikel hat sich geändert. Bitte ordnen Sie den Warenkorb neu zu, um die Angebote zu aktualisieren.'
category:
    all_categories_placeholder: 'Alle Kategorien'
    category_bafv: 'BAFV customer'
    category_new: 'New customer'
    category_normal: 'Normal customer'
    category_premium: 'Premium customer'
    category_undefined: Definieren
cgu:
    accept: 'Die Allgemeinen Geschäftsbedingungen akzeptieren'
    error: 'Fehler bei der Annahme der Allgemeinen Geschäftsbedingungen'
    errorMessage: 'Bitte bestätigen Sie die AGB, bevor Sie Ihr Unternehmen einreichen'
    read: 'Die Allgemeinen Geschäftsbedingungen durchlesen und akzeptieren'
    validated: 'Sie haben die AGB akzeptiert.'
company:
    form:
        address: Firmenanschrift
        back: Vorher
        billingService: Abteilungsname
        billing_address:
            address: Rechnungsadresse
            title: Rechnungsdienst
            use_main: 'Die Adresse des Hauptsitzes verwenden'
        businessRegistration: 'Identifikation des Unternehmens'
        cgu: 'Allgemeine Geschäftsbedingungen'
        fullAuto: 'API order full auto'
        eCatalog: 'E-catalog'
        check: 'Rechnungsadresse abweichend von der Hauptadresse?'
        company_info: Unternehmensinformationen
        contact:
            add: 'Einen Kontakt hinzufügen'
            adv: 'Kontakt Verkaufsverwaltung'
            billing: Rechnungskontakt
            check:
                adv: 'Kreuzen Sie das Kästchen an, wenn Sie für die Verkaufsverwaltung andere Kontaktangaben eintragen möchten'
                billing: 'Kreuzen Sie das Kästchen an, wenn Sie für die Abrechnung andere Kontaktangaben eintragen möchten'
                logistic: 'Kreuzen Sie das Kästchen an, wenn Sie für die Logistik andere Kontaktangaben eintragen möchten'
            logistic: Logistikkontakt
            logistic_subtitle: 'Sie können für jeden Standort operative Kontakte eingeben'
            main: 'Hauptkontakt (Vertrieb)'
        contract: 'Vertrag unterzeichnen und zurückschicken'
        document:
            title: 'Ihre Dokumente hinzufügen'
            titleReadOnly: 'Ihre Dokumente'
        edit-site: 'Das Kostenzentrum ändern'
        endpointUrl: ''
        finish: Fertig
        iban: 'IBAN der juridische Person'
        ident_number:
            invalid: Ungültig
        identification: Identifikationsnummer
        identity: 'Personalausweis des Unterzeichners'
        info:
            cgu_not_accepted: 'Sie müssen die AGB akzeptieren, indem Sie HIER klicken'
            company_registration: 'Ihr Benutzerkonto wurde erstellt. Bitte füllen Sie das folgende Formular aus, um Ihre Unternehmensinformationen einzureichen und die Registrierung abzuschließen.'
            incomplete: 'Die Unternehmensinformationen sind unvollständig'
            operator_needed: 'Bestimmte Informationen Ihres Unternehmens können nicht mehr direkt geändert werden. Wenn Sie Änderungen vornehmen möchten, kontaktieren Sie bitte StationOne über das Formular <a href=''/ticket/create/''>contact</a>.'
        legal_documents: 'Rechtliche Dokumente des Unternehmens'
        main_contact:
            title: Adresse
        middleware: ''
        name: Firmenbezeichnung
        next: Nächstes
        password:
            change: 'Mein Passwort ändern'
        profile: 'Mein Profil'
        save: Speichern
        service: Abteilung
        siren: SIREN
        siren_placeholder: '(9 Ziffern)'
        social: Unternehmen
        submit: Senden
        submit_infos: 'Mein Unternehmen einreichen'
        tax_rate: MwSt.-Satz
        title: 'Verwaltungsdaten des Unternehmens'
        type: Typ
        update:
            error: 'Fehler bei der Änderung des Unternehmens'
            success: 'Das Unternehmen wurde erfolgreich aktualisiert.'
company_catalog:
    add_in_catalog: 'Meine Referenz hinzufügen'
    buyer_reference: 'Referenz in Ihrem Katalog'
    cancel: Stornieren
    custom_search:
        title: 'In meinem Katalog suchen'
    delete: 'Meinen Katalog löschen'
    delete_confirm: 'Möchten Sie Ihren Katalog wirklich löschen?'
    delete_confirmed: 'Katalog gelöscht'
    delete_error: 'Nur Account Manager können die Datei löschen'
    delete_reference: Entfernen
    export: 'Meinen Katalog exportieren'
    export_matching: 'Gültige Referenzen exportieren'
    export_mismatching: 'Ungültige Referenzen exportieren'
    import:
        total_matching_references: 'Insgesamt gültige Katalogreferenzen:'
        total_mismatching_references: 'Ungültige Katalogreferenzen insgesamt:'
    import_in_progress: 'der Katalogimport läuft'
    imported_references: 'importierte Referenzen'
    instruction_title: 'Anleitung:'
    instructions: 'Sie können Ihre eigenen Artikelnummern jetzt auf Marketplace hochladen. So haben Sie die Möglichkeit, Produkte mit dieser Artikelnummer in Ihren Bestellungen einzusehen und zu suchen (nur Nutzer Ihres Unternehmens können diese Artikelnummern sehen).</br></br>2 Optionen, um Ihre eigenen Artikelnummern hinzuzufügen:</br>- Auf der Seite Produktdetails „Meine eigene Artikelnummer“ anklicken, um Ihre eigene Artikelnummer/Ihren eigenen Produktnamen zum ausgewählten Produkt hinzuzufügen</br>- Alle Artikelnummern Ihrer Produkte im Menü „Mein Katalog“ importieren und dazu die <a href="%link%" target="_blank">hier</a> verfügbare .csv-Dateivorlage verwenden.</br>Die Herstellernummer in die erste Spalte eingeben, dann Ihre eigene Artikelnummer in die zweite Spalte. Dann können Sie Ihren Katalog durch Anklicken der Schaltfläche „Upload“ hochladen.</br></br>Nachdem Sie Ihre Datei hochgeladen haben, können Sie Produkte mit Ihrer eigenen Artikelnummer suchen.</br></br>Der Katalog steht jederzeit zum Download durch Anklicken der Schaltfläche „Meinen eigenen Katalog exportieren“ zur Verfügung.</br></br>Mit der Schaltfläche „Nicht übereinstimmende Artikelnummern exportieren“ können Sie prüfen, ob die Artikelnummern, die Sie zum Katalog hinzugefügt haben, auf StationOne Marketplace zur Verfügung stehen.'
    overwrite: 'Achtung, wenn Sie einen neuen Katalog senden, wird der alte Katalog gelöscht. Möchten Sie fortfahren?'
    save: Speichern
    title: Katalog
    upload: 'Meinen Katalog senden'
    upload_success: '%count% hinzugefügte Referenz/en'
    wrong_file_format: 'Dateiformat ungültig.'
comparaisonSheet:
    add:
        error: 'Fehler beim Hinzufügen des Produkts zum Komparator'
        itemAlreadyexist: 'Der Artikel ist bereits im Vergleicher'
        maxItemError: 'Sie haben bereits die maximale Anzahl von Produkten im Vergleicher: %maxItem%'
        success: 'Produkt wurde dem Vergleicher hinzugefügt'
    back_to_home: 'Zurück zur Startseite'
    comment: 'Fügen Sie einen Kommentar hinzu, den Sie auf dem Vergleichsformular pdf sehen möchten :'
    export: Export
    information: 'Dieser Vergleich gilt nur zum Zeitpunkt der Erstellung dieses PDFs. StationOne verpflichtet sich nicht, diesen Preis über einen längeren Zeitraum zu halten.'
    no_article: 'Ihr Vergleicher ist leer'
    page:
        author: Autor
        comment: Kommentar
        company_name: Firmenname
        date: 'Datum des Vergleichsformular'
        delivery: Lieferung
        discount: Rabatt
        no_discount: 'Kein Rabatt'
        no_price: 'Preis auf Anfrage'
        price_excl_tax: 'Preis ohne Steuer'
        product: Produkt
        title: Produktvergleicher
        unit_price: Einheitspreis
        unit_price_vat: 'Konvertierter Einheitspreis'
    sticky: Vergleicher
contact:
    adv: 'Vertriebsadministration Kontakt'
    form:
        email: E-Mail-Adresse
        firstname: Vorname
        function: Funktion
        lastname: Nachname
        phone1: Haupttelefon
        phone1_placeholder: 'Diese Telefonnummer ist erforderlich'
        phone2: Zweittelefon
    logistic: 'Logistischer Kontakt'
contactMerchant:
    add_file: 'Datei hinzufügen'
    attachment_limit: 'maximum %limit% attachments'
    authorized_types: '(Dateiformat  : pdf, jpeg, gif, png, tiff)'
    file_too_big: 'Maximalgrösse der Datei %size-limit%'
    form:
        error: 'Fehler bei der Absendung Ihrer Nachricht'
        message: Nachricht
        object: Betreff
        save: Senden
        success: 'Ihre Nachricht wurde gesendet'
    message:
        object:
            feedback: 'Rückmeldung von %buyer%'
            quotation: 'Kommerzielle Anfrage von %buyer%'
            technical: 'Technische Anfrage von %buyer%'
contactWAYLF:
    add_file: 'Fügen Sie eine Datei hinzu'
    attachment_limit: 'maximum %limit% attachments'
    authorized_types: '(Dateiformat: pdf, jpeg, gif, png, tiff)'
    company_name: 'Name der Firma'
    email: Email
    file_too_big: 'Die maximale Größe eines Anhangs beträgt % size-limit%'
    first_name: Vorname
    last_name: Nachname
    message: Nachricht
cookie:
    accept: Einverstanden
    message: 'Indem Sie Ihren Besuch auf dieser Website fortsetzen, akzeptieren Sie die Nutzungsbedingungen, einschließlich der Verwendung von Cookies, um Statistiken über das Publikum durchzuführen.'
cost_center:
    name:
        first: Hauptkostenzentrum
country:
    australia: Australien
    austria: Österreich
    azerbaijan: Aserbaidschan
    bahrain: Bahrain
    belgium: Belgien
    brasil: Brasilien
    bulgaria: Bulgarien
    canada: Kanada
    canary_islands: 'Kanarische Inseln'
    china: China
    croatia: Kroatien
    cyprus: Zypern
    czech_republic: 'Tschechische Republik'
    denmark: Dänemark
    estonia: Estland
    finland: Finnland
    france: Frankreich
    germany: Deutschland
    greece: Griechenland
    hongkong: Hongkong
    hungary: Ungarn
    ident:
        australia: 'A.C.N. - Australian Company Number'
        austria: USt-IdNr.
        azerbaijan: Steueridentifikationsnummer
        bahrain: Handelsregisternummer
        belgium: 'TVABE (MwSt.-Nr. Belgien)'
        brasil: 'CNPJ Number (MwSt.-Nr.)'
        bulgaria: 'Registrierungsnummer Bulgaria'
        canada: DUNS
        canary_islands: CIF-Nummer
        china: 'RNCN (Registrierungsnummer China)'
        croatia: Handelsregisternummer
        cyprus: 'RNCY (Registrierungsnummer Zypern)'
        czech_republic: ICO
        denmark: 'CVR Danemark- MwSt. Dänemark'
        estonia: 'Handelsregister - Estland'
        finland: 'TVAFI (MwSt.-Nr. Finnland)'
        france: USt-IdNr.
        germany: USt-IdNr.
        greece: 'VATGR (MwSt. für Griechenland)'
        hongkong: 'RNHK (Registrierungsnummer Hongkong)'
        hungary: 'RNHU (Registrierungsnummer Ungarn) oder VATHU'
        india: 'CRO Nummer für EBSG oder MwSt.-Nummer Indien'
        ireland: 'Ireland company number (CNIE)'
        italy: 'COFIS oder Partita IVA'
        latvia: 'Handelsregister - Estland'
        lithuania: 'Handelsregister - Litauen'
        luxembourg: 'IBLC (MwSt. Luxemburg)'
        malta: 'Registrierungsnummer Malta'
        mexico: 'RFC (Mexiko)'
        morocco: 'RNMA (Registrierungsnummer Marokko)'
        netherlands: 'Handelskammer Niederlande KVK Nr.'
        norway: 'RNNO (Firmenregistrierungsnummer- RN Norwegen)'
        peru: 'Einheitliches Steuerzahlerregister'
        poland: 'TVAPL (NIP MwSt. Polen)'
        portugal: 'NIPC (Registrierungsnummer Portugal)'
        qatar: Handelsregisternummer
        romania: 'Registrierungsnummer Rumänien'
        russia: 'OGRN (Ident. RU)'
        saudi_arabia: Handelsregisternummer
        senegal: Handelsregisternummer
        singapore: 'CRO Nummer oder CRO Nummer für EBSG'
        slovak_republic: 'Registrierungsnummer Slovak Republic'
        slovenia: 'Registrierungsnummer Slovenia'
        spain: 'CIF Spanien - MwSt.'
        sweden: 'RNSE (RN Schweden)'
        switzerland: 'Innergemeinschaftliche Ust-Idnr'
        taiwan: 'RNTW (Firmenregistrierungsnummer- Registrierungsnummer Taiwan)'
        thailand: 'CRO oder ROC Nummer'
        tunisia: 'RNTN (RN Tunesien)'
        turkey: 'TAXTR (Steueridentifikationsnummer für Türkei)'
        united_arab_emirates: Handelsregisternummer
        united_kingdom: 'CRO (Company Registration Office Number)'
        united_states: DUNS-Nummer
    ident_helper:
        australia: '9 Ziffern'
        austria: 'ATU + 8 Ziffern'
        azerbaijan: '10 Ziffern, die Ihrem TIN-Code (Tax Identification Number) entsprechen'
        bahrain: '15 Ziffern'
        belgium: '10 Ziffern'
        brasil: '14 Ziffern'
        bulgaria: '9 oder 10 Ziffern'
        canada: '9 Ziffern'
        canary_islands: 'A + 8 Ziffern'
        china: 'Max. 18 Ziffern / Buchstabe'
        croatia: 'HR + 11 Ziffern'
        cyprus: '5 order 6 Ziffern'
        czech_republic: '8 Ziffern'
        denmark: '8 Ziffern'
        estonia: '8 Ziffern'
        finland: '8 Ziffern'
        france: FR+XX+SIREN-Nr.
        germany: 'DE+ 9 Ziffern'
        greece: '9 Ziffern'
        hongkong: '4 bis 8 Ziffern'
        hungary: '10 Ziffern oder 11 Ziffern'
        india: '11 Ziffern + 1 Buchstabe'
        ireland: '7 Ziffern'
        italy: '11 Ziffern'
        latvia: '11 Ziffern'
        lithuania: '9 Ziffern'
        luxembourg: '8 Ziffern'
        malta: '8 Ziffern'
        mexico: '12 oder 13 Zeichen'
        morocco: 'Max. 35 Zeichen'
        netherlands: '8 Ziffern'
        norway: '9 Ziffern'
        peru: '11-numerich Format'
        poland: '10 Ziffern'
        portugal: '9 Ziffern'
        qatar: '5 Ziffern'
        romania: '"RO" + 2/10 Ziffern'
        russia: '13 oder 15 Ziffern'
        saudi_arabia: 'AS + 15 Ziffern'
        senegal: '5 Buchstaben + 4 Ziffern + 1 Buchstabe + 5 Ziffern'
        singapore: 'Max. 9 Ziffern + 1 Buchstabe'
        slovak_republic: 'SK+10 Ziffern'
        slovenia: '8 Ziffern'
        spain: '1 Buchstabe + 8 Ziffern'
        sweden: '10 Ziffern'
        switzerland: CHE-XXXXXXXXX
        taiwan: '8 Ziffern'
        thailand: '13 Ziffern'
        tunisia: '10-11 Zeichen'
        turkey: '10 Ziffern'
        united_arab_emirates: '15 Ziffern'
        united_kingdom: '8 Ziffern'
        united_states: '9 Ziffern'
    india: Indien
    ireland: 'Republik Irland'
    italy: Italien
    latvia: Lettland
    lithuania: Litauen
    luxembourg: Luxemburg
    malta: Malta
    mexico: Mexiko
    morocco: Marokko
    netherlands: Niederlande
    norway: Norwegen
    peru: Peru
    poland: Polen
    portugal: Portugal
    qatar: Katar
    region:
        CA-AB: Alberta
        CA-BC: 'British Columbia'
        CA-MB: Manitoba
        CA-NB: 'New Brunswick'
        CA-NL: 'Neufundland und Labrador'
        CA-NS: Neuschottland
        CA-NT: Nordwest-Territorien
        CA-NU: Nunavut
        CA-ON: Ontario
        CA-PE: Prince-Edward-Insel
        CA-QC: Quebec
        CA-SK: Saskatchewan
        CA-YT: Yukon
        FR-ARA: Auvergne-Rhône-Alpes
        FR-BFC: Bourgogne-Franche-Comté
        FR-BRE: Bretagne
        FR-COR: Korsika
        FR-CVL: 'Centre-Val de Loire'
        FR-GES: 'Grand Est'
        FR-GF: Französisch-Guyana
        FR-GP: Guadeloupe
        FR-HDF: Hauts-de-France
        FR-IDF: Île-de-France
        FR-MQ: Martinique
        FR-NAQ: Nouvelle-Aquitaine
        FR-NOR: Normandie
        FR-OCC: Okzitanien
        FR-PAC: 'Provence-Alpes-Côte d‘Azur'
        FR-PDL: 'Pays de la Loire'
        FR-RE: 'La Réunion'
        FR-YT: Mayotte
    romania: Rumänien
    russia: 'Russische Föderation'
    saudi_arabia: 'Saudi Arabien'
    senegal: Senegal
    singapore: Singapur
    slovak_republic: 'Slowakische Republik'
    slovenia: Slowenien
    spain: Spanien
    sweden: Schweden
    switzerland: Schweiz
    taiwan: Taiwan
    thailand: Thailand
    tunisia: Tunesien
    turkey: Türkei
    united_arab_emirates: 'Vereinigte Arabische Emirate'
    united_kingdom: Großbritannien
    united_states: USA
currency: Währung
customs:
    info:
        domestic: Inland
        export_EU: EU-Export
        export_non_EU: Export
default:
    placeholder: Unbestimmt
dispute:
    create:
        ko: 'Beim Versenden Ihres Einspruchs ist ein Fehler aufgetreten'
        ok: 'Ihr Einspruch wurde verschickt'
    form:
        message: Bemerkungen
        new: Reklamation
        placeholder: 'Erläutern Sie hier Probleme mit ausgewählten Produkten'
        products: 'Wählen Sie Produkte mit Problemen aus'
        see: 'Alle Streitigkeiten anzeigen'
        subject: Gegenstand
        table:
            all: Alles
            expected_date: 'Erwartetes Datum'
            product_name: Produktname
            quantity: Menge
            reference: Referenz
            total_price: Gesamtpreis
            unit_price: Stückpreis
        title: Reklamation
    list:
        creation_date: Erstellungsdatum
        id: ID
        messages: Mitteilungen
        read: Lesen
        receiver: Empfänger
        subject: Gegenstand
        unread: Ungelesen
document:
    contract:
        download: 'Laden Sie den vorausgefüllten Vertrag hier herunter'
    noDoc: 'Keine Datei ausgewählt -- Sie müssen Ihre Dokumente hinzufügen'
    upload:
        delete: 'Möchten Sie diese Datei löschen?'
        deleteError: 'Fehler beim Löschen der Datei'
        error: 'Sie müssen mindestens eine Datei hinzufügen'
        ignored: '1 oder mehrere Dateien wurden ignoriert'
        ko: 'Fehler beim Laden'
        mime: 'Der Dokumententyp muss Teil der folgenden Liste sein: %Einschränkung%'
        ok: 'Ladevorgang beendet'
        size: 'Die Dokumentgröße darf %Einschränkung% Mo nicht übersteigen'
        title: 'Eine Datei auswählen'
        type: 'Die Datei ist ungültig'
        typeOrSizeError: 'Beim Hinzufügen Ihres Dokuments ist ein Fehler aufgetreten Die Datei ist eventuell ungültig.'
        working: 'Bearbeitung läuft...'
error:
    forbidden:
        code: '(Fehler 403)'
        description: 'Sie sind nicht berechtigt, diesen Inhalt anzuzeigen. Wenn Sie denken, dass es sich hierbei um einen Fehler handelt, können Sie den Support kontaktieren.'
        title: 'Nicht berechtigt'
    generic:
        help: 'Folgende Links können Ihnen helfen:'
        home: 'Zurück zum Start'
        support: 'Support kontaktieren'
    internal:
        code: '(Fehler 500)'
        description: 'Bei der Bearbeitung Ihres Antrags ist ein unbekannter Fehler aufgetreten Wenn Sie denken, dass es sich hierbei um einen Fehler handelt, können Sie den Support kontaktieren.'
        title: 'Ein interner Fehler ist aufgetreten'
    notfound:
        code: '(Fehler 404)'
        description: 'Die angeforderte Seite existiert nicht. Wenn Sie denken, dass es sich hierbei um einen Fehler handelt, können Sie den Support kontaktieren.'
        title: 'Die Seite existiert nicht.'
    zipCode: 'Die Postleitzahl muss aus 5 Ziffern bestehen.'
filter:
    all: Alles
    'no': Nein
    'yes': Ja
flag: '#icon-flag-de'
footer:
    about_us:
        code_of_conduct: Verhaltenscodex
        company: 'Das Unternehmen'
        cookies: Cookies
        data_privacy_chart: 'Data Privacy Charter'
        join_us: 'Kommen Sie zu uns'
        legal_notice: Impressum
        our_mission: 'Unser Auftrag'
        title: 'Über uns'
    additional:
        alstom: StationOne
        copyright: '@2018 StationOne.  Alle Rechte vorbehalten.'
        purchase_conditions: 'Einkaufsbedingungen gemäß den Allgemeinen Nutzungsbedingungen von Station One und dann zu den Verkaufsbedingungen jedes Verkäufers.'
    buy:
        benefits_for_buyers: 'Vorteile für Käufer'
        create_an_account: 'Konto erstellen'
        general_terms_and_conditions: 'Allgemeine Geschäftsbedingungen'
        title: 'Auf  StationOne kaufen'
    follow_us:
        title: 'Folgen Sie uns'
    help:
        call_us_at: 'Ruf uns an um'
        contact_us: 'Schreiben Sie eine Nachricht'
        illegal_content: 'Illegale Inhalte'
        phone_number_1: '+33 6 59 35 58 54'
        questions: 'F&A'
        title: Hilfe
    mentions: 'Rechtliche Hinweise'
    newsletter:
        placeholder: E-Mail-Adresse
        title: 'Registriere dich für unseren Newsletter'
    press:
        blog: Blog
        news: News
        press_releases: Pressemitteilungen
        title: 'Presse & Nachrichten'
    sell:
        benefits_for_sellers: 'Vorteile für Verkäufer'
        create_an_account: 'Konto erstellen'
        general_terms_and_conditions: 'Allgemeine Geschäftsbedingungen'
        title: 'Auf StationOne verkaufen'
    visit:
        address_1: '69-73 Boulevard Victor Hugo'
        address_2: '93400 Saint-Ouen-sur-Seine'
        address_3: FRANKREICH
        title: 'Besuchen Sie uns'
form:
    invoice:
        search: Suche
    order:
        search: Suche
    user:
        add: 'Einen Benutzer hinzufügen'
        definition:
            account_manager: 'Account Manager / Administrator'
            account_manager_definition: 'Käuferrechte + Globale Kontoadministration'
            buyer: Käufer
            buyer_definition: 'Antragstellerrechte + Genehmigung von Bestellungen für seine Kostenstellen'
            requestor: Antragsteller
            requestor_definition: 'Wählt Produkte aus, bereitet Bestellungen vor und sendet sie zur Genehmigung an den Käufer'
        delete:
            content: 'Möchten Sie diesen Benutzer wirklich löschen?'
            content_for_default_user: ''
            title: 'Löschen eines Benutzers'
        email: E-Mail-Adresse
        error_update: 'Beim Aktualisieren des Benutzers ist ein Fehler aufgetreten. Bitte überprüfen Sie die eingegebenen Daten'
        firstname: Vorname
        function: Funktion
        invalid_role: 'Diese Rolle ist nicht gültig'
        lastname: Nachname
        myself: Mich
        role:
            admin: 'Account Manager / Administrator'
            buy: Antragsteller
            pay: 'Einkäufer / Zahler'
            placeholder: 'Wählen Sie eine Rolle'
            sell: Verkauf
            view: Konsultation
        roles: Profil
        save_edit: Aktualisieren
        save_new: Erstellen
        sites:
            label: 'Kostenzentrum:'
            mandatory: 'Sie müssen mindestens ein Kostenzentrum auswählen'
        success_new: 'Der Benutzer wurde erstellt'
        success_update: 'Der Benutzer wurde aktualisiert'
        title: 'Liste der Benutzer'
        title_common: 'ein Benutzer'
        title_edit: Ändern
        title_new: Hinzufügen
front:
    site:
        no_default_user: ''
generic:
    cancel: Cancel
    delete: Delete
    save: Save
header:
    address: Adresse
    buyer_id: 'Käufer ID'
    company: Firma
    delivery_delay: 'Voraussichtlicher Liefertermin'
    incoterm: Incoterm
    purchase_order: Bestellung
    site: Kostenstelle
    tva_identification_number: TVA-Nummer
    vendor_id: 'Verkäufer ID'
    vendor_ref: Herstellerreferenz
home:
    autres_industriels: 'Andere Industrielle'
    collectivite_locale: Gebietskörperschaft
    dashboard_admin: Administrator-Dashboard
    description: 'Cum Homerici cum Roma nobilium'
    disclaimer:
        market:
            line1: 'Mehr Absatzmärkte'
            line2: 'Mehr Lagerstätten'
            line3: Marktpreis
            title: Marktzugang
        process:
            line1: Zeitgewinn
            line2: 'Reporting und Dashboard'
            line3: 'Digitaler Frachtbrief'
            title: 'Ein effizienter Prozess'
        transactions:
            line1: 'Transparenz (Fotos & Beschreibung)'
            line2: 'Audit der Verkäufer & Käufer'
            line3: 'Verbindlicher Kaufvertrag'
            title: 'Gesicherte Transaktionen'
        video: 'Auf Video entdecken'
    grande_distribution: Großhandel
    insert:
        all_news: 'Alle News sehen'
        contact: Kontakt
        news_description: 'Alstom wird Île-de-France Mobilité und RATP 20 U-Bahnen der Baureihe MP14 mit jeweils 5 Wagen im Gesamtwert von 157 Mio. Euro. liefern, die für die Linie 11 der Pariser Metro bestimmt sind.'
        savoir: 'Mehr erfahren'
        sustainability_description: 'Den Bedürfnisse von heute erfüllen, ohne die Fähigkeit, den Bedarf von morgen zu decken, zu beeinträchtigen.'
        title: faq
        title_duree: Nachhaltigkeit
        title_news: 'Aktuelle News.'
    login: Login
    login_admin: 'Login Administrator / Bediener'
    logout: 'Sich abmelden'
    register: 'Sich registrieren'
    register_buyer: Registrierung
    register_merchant: 'Registrierung Verkäufer'
    register_merchant_confirmation_message: 'Ihre Registrierung wurde erfolgreich durchgeführt.'
    register_merchant_confirmation_title: 'Herzlichen Glückwunsch'
    register_merchant_confirmation_url: 'Sie können sich nun in Ihrem Verkäuferbereich anmelden'
    register_merchant_error: 'Bei der Registrierung ist ein unerwarteter Fehler aufgetreten Wenn das Problem weiter besteht, kontaktieren Sie bitte den Support.'
    register_merchant_success: 'Ihre Registrierung wurde erfolgreich durchgeführt.'
    register_merchant_tva_not_checked: 'The identification number could not be checked for this vendor. Please proceed to a manual check'
    slider:
        subtitle: 'Fruticeta prona valido inmanium fortiter'
        title: 'Cum Homerici cum Roma nobilium'
    start_buy: Kaufstart
    start_sale: Verkaufsstart
    ticket_admin: 'Liste der Tickets (Administrator)'
    ticket_anonymous: 'Ein Ticket als anonymer Benutzer erstellen'
    ticket_buyer: 'Liste der Tickets'
    title: Start
    video_src: MECmgIz36nU
    why_buy:
        certified: Überprüft
        certified_text: 'Ein Katalog mit von StationOne überprüfen Qualitätslieferanten'
        fast: Schnell
        fast_text: 'Eine Internetseite und tausende Referenzen, um Ihre Lieferungen zu optimieren'
        simple: Einfach
        simple_text: 'Ein sicheres Zahlungssystem und garantierte Lieferfristen'
        title: 'Gründe für den Einkauf auf StationOne'
illegal_content:
    form:
        ko: 'Beim Senden Ihres illegalen Inhalts ist ein Fehler aufgetreten'
        ok: 'Ihre Anfrage wurde erfolgreich versendet'
        save: Senden
        title: 'Beschreiben Sie den illegalen Inhalt, den Sie entdeckt haben'
import:
    csv:
        invalid_separator: 'Listentrennzeichen nicht erkannt'
        no_error: 'Import ohne Fehler durchgeführt'
        not_all_lines_were_imported: 'Nicht alle Zeilen wurden importiert'
        not_enough_lines: 'Nicht genügend Zeilen in dieser Datei'
invoice:
    detail:
        already_invoiced: 'Bereits in Rechnung gestellt :'
        credit_note: Gutschrift
        frame_contract: Rahmenvertrag
        go_back: 'Zurück zur Bestellnummer:'
        including_taxes: 'einschließlich Steuern'
        invoice: Rechnung
        invoice_amount: Betrag
        invoice_date: Datum
        invoice_due_date: Fälligkeitsdatum
        invoice_payment: Zahlung
        not_paid: 'Nicht bezahlt'
        not_yet_invoiced: 'Noch nicht in Rechnung gestellt:'
        order_amount: 'Gesamt:'
        order_date: 'Datum:'
        order_id: 'Unterauftrags-ID:'
        order_payment_status: 'Zahlungsstatus:'
        order_seller: 'Verkäufer:'
        order_status: 'Status:'
        paid: Bezahlt
        payment: Zahlung
        refund: Rückerstattung
        remaining_to_pay: 'Verbleibender Betrag'
        title: Rechnungen
        total: Summe
        total_invoiced: 'Rechnungsbetrag und Gutschrift :'
        total_paid: 'Ganz bezahlt :'
        total_remaining_to_pay: 'Verbleibender Gesamtbetrag:'
    export:
        invoice_amount: Rechnungsbetrag
        invoice_creation_date: Rechnungserstellungsdatum
        invoice_number: Rechnungsnummer
        order_creation_date: 'Datum der Auftragserstellung'
        order_number: Best.-Nr.
        payment_status: Rechnungsstatus
        vendor_name: Herstellername
    list:
        amount: Betrag
        date: Datum
        due_date: Fälligkeitsdatum
        due_date_total_eur: 'Diesen Monat bezahlen (€):'
        due_date_total_usd: 'Diesen Monat bezahlen ($):'
        export: Export
        from: 'Ab Datum'
        invoice: 'Rechnung / Gutschrift'
        late_payment_eur: 'Späte Zahlung (€):'
        late_payment_usd: 'Späte Zahlung ($):'
        order: bestellen
        paid_date: Zahlungsdatum
        remain: Restbetrag
        seller: Verkäufer
        tab:
            empty: 'Sie haben in diesem Bereich keine Rechnungen.'
            not_paid: 'Nicht bezahlte Rechnungen'
            paid: 'Bezahlte Rechnungen'
        title: Rechnungen
        to: 'Bis zum Datum'
        total_to_pay_eur: 'Erhaltene Rechnungen insgesamt (€):'
        total_to_pay_usd: 'Erhaltene Rechnungen insgesamt ($):'
    reminder: Rechnungserinnerung
    seeInvoices: 'Rechnungen und Zahlungen'
key: DE
keyToIgnore: ''
label_next: Nächstes
label_previous: Voriges
login:
    companyError: 'Das Unternehmen wurde deaktiviert'
    userCreatedButCompanyError: 'Wir freuen uns, Ihnen mitteilen zu können, dass Ihr Antrag auf ein Firmenkonto eingegangen ist, und danken Ihnen für Ihr Interesse.<br />Ihr Antrag wird vom StationOne Team sobald wie möglich bestätigen.'
main_menu:
    account: 'Mein Konto'
    buyer:
        mybids: 'Meine Auktionen'
        myorders: 'Meine Bestellungen'
    categories: Kategorien
    concept: 'Das Konzept'
    contact: Kontakt
    contact_us: 'Kontaktieren Sie uns'
    dashboard: Dashboard
    eur_cart: 'WARENKORB EUR'
    explore_categories: 'Kategorien durchsuchen'
    faq: F.A.Q
    help: Hilfe
    how: 'Wie funktioniert das?'
    join_us: 'Kostenlose Registrierung'
    languages: Sprachen
    locked: 'Sie müssen den vorherigen Schritt abgeschlossen haben, um diese Seite aufzurufen'
    merchant:
        myoffers: 'Meine Angebote'
        mysales: 'Meine Verkäufe'
        offers:
            active: Aktiv
            draft: Entwürfe
            nosale: 'Ohne Transaktion'
            pending: 'Wartet auf Validierung'
    messages: Nachrichten
    offers: 'Die Angebote'
    products: Produkte
    signin: 'Sich anmelden'
    terms: 'Allgemeine Bedingungen'
    usd_cart: 'WARENKORB USD'
    e_catalog_label: 'E-Catalog'
menu:
    desktop:
        api_doc: 'Api documentation'
        company: Unternehmen
        document: Dokumente
        invoices: Rechnungen
        my_catalog: 'Mein Katalog'
        orders: Bestellungen
        payment_modes: Zahlungsarten
        pending_carts: 'Ausstehende Warenkörbe'
        profile: Profil
        purchase_request: Bestellanforderungen
        sites: Kostenzentren
        stats: Statistiken
        users: Benutzer
        wishlist: Kit-liste
merchant:
    name: Verkäufer
    update: 'Die Preisanfrage wurde mit Erfolg aktualisiert'
merchant_order:
    status:
        canceled: ''
        confirmed: ''
        finalized: ''
        initial: ''
        partially_refunded: ''
        payment_authorized: ''
        processed: ''
        received: ''
        return_in_progress: ''
        sent: ''
modal:
    cancel: Abbrechen
    confirm: Ok
    'no': Nein
    'yes': Ja
month: Monat
'no': Nein
node:
    form:
        author:
            label: Autor
        body:
            label: Corp
        checkboxLinkType:
            label: 'Redirect to external URL'
        content: Inhalt
        delete:
            content: 'Möchten Sie diese statische Seite wirklich löschen?'
            error: 'Beim Löschen ist ein Fehler aufgetreten'
            success: 'Die statische Seite wurde gelöscht'
            title: 'Löschen der statischen Seite'
        error:
            template_validation: 'An error occurred while validating content for "%locale%" language: %message%'
        header:
            edit: 'Änderung des Inhalts (%type):'
            new: 'Neuer Inhalt'
        lang:
            button: Hinzufügen
            de: Deutsch
            en: Englisch
            es: Spanisch
            fr: Französisch
            it: Italienisch
            nl: Nederlands
            select: 'Wählen Sie eine Sprache'
        sections:
            content: Inhalt
            main: Allgemein
        slug:
            help: Slug
            label: Permalink
        status:
            label: Status
        submit:
            create: Erstellen
            error:
                create: 'Bei der Erstellung ist ein Fehler aufgetreten'
                update: 'Bei der Aktualisierung ist ein Fehler aufgetreten'
            success:
                create: 'Die statische Seite wurde erstellt'
                delete: 'Die statische Seite wurde gelöscht'
                update: 'Die statische Seite wurde aktualisiert'
            update: Aktualisieren
        template:
            choices:
                default: Standardmäßig
                default_with_faq: 'Standardmäßig mit den FAQ Blöcken'
                default_with_products: 'Standardmäßig mit Produktliste'
                default_with_products_and_faq: 'Standardmäßig mit Produktliste und den FAQ-Blöcken'
                fullwidth: 'Ganze Seite'
                fullwidth_with_faq: 'Ganze Seite mit FAQ-Blöcken'
                fullwidth_with_products: 'Ganze Seite mit Produktliste'
                fullwidth_with_products_and_faq: 'Ganze Seite mit Produktliste und den FAQ-Blöcken'
            label: Kanevas
        test: 'Test emails have been successfully sent to your email address: %email%'
        textLinkExternal:
            label: 'External URL'
        title:
            label: Titel
offer_detail:
    add_error: 'Dieses Produkt befindet sich bereits in Ihrem Warenkorb'
    add_success: 'Dieses Produkt wurde erfolgreich in Ihren Warenkorb hinzugefügt.'
    add_to_cart: 'In den Warenkorb'
    add_to_wishlist: 'Zur Kit-liste hinzufügen'
    add_wishlist_success: 'Ihr Produkt wurde erfolgreich zu Ihrer Kit-liste hinzugefügt'
    anonym_contact_merchant: 'Bitte melden Sie sich an, um den Verkäufer zu kontaktieren'
    anonymous:
        add_to_cart: 'Sie müssen authentifiziert sein, um Produkte in Ihren Warenkorb hinzuzufügen.'
        not_authorized: 'Nicht berechtigt'
    ask_question: 'Verkäufer kontaktieren'
    ask_title: 'Hilfe gebraucht ?'
    ask_vendor: 'Zugriff auf Katalogpreise anfordern'
    ask_vendor_message_content: 'Hallo,\nKönnen Sie mir bitte Zugriff auf Ihre Katalogpreise gewähren?\nDankeschön\n\nHello,\nCould you please grant me access to your catalog prices ?\nThank you\n'
    ask_vendor_no_price_offer_message_content: 'Hallo,\nKönnten Sie bitte Ihren besten Preis für dieses Angebot vorschlagen?\nErwartete Menge:\nLiefertermin erwartet:\n\nDankeschön\n'
    ask_vendor_pending: 'Zugriff auf Katalogpreise angefordert'
    ask_vendor_price: 'Fordern Sie diesen Preis beim Verkäufer an'
    ask_vendor_price_reference: 'Fordern Sie einen Preis für den Artikel an %reference%'
    ask_vendor_rejeted: 'Preis nicht verfügbar'
    back_to_search: 'Zurück zur Suche'
    buy_more_for_discount: 'Mehr kaufen und einen Rabatt erhalten'
    company_code: 'Company code :'
    company_not_valid: 'Bitte vervollständigen Sie Ihre Unternehmensinformationen, bevor Sie Ihren Warenkorb auschecken'
    contact_seller:
        modal:
            send: Senden
            title: 'Den Verkäufer kontaktieren'
    contact_the_vendor: 'Sie können auf der Produktseite dem Anbieter eine Nachricht senden'
    continue_shopping: 'Einkauf fortsetzen'
    description: 'Detail eines Angebots'
    expired_offer: 'Dieses Produkt ist bei StationOne nicht mehr verfügbar. Für weitere Informationen, bitte kontaktieren Sie den Verkäufer.'
    frame_contract_valid_date: 'Gültig bis'
    from_company: 'Von:'
    in_stock: 'Auf Lager'
    login: 'Sich anmelden'
    not_available_country: 'Dieses Produkt ist in Ihrem Land nicht verfügbar. Für weitere Informationen, bitte kontaktieren Sie den Verkäufer.'
    not_batch_size_multiple: 'Die Menge muss ein Vielfaches von %batchSize% sein.'
    not_business_everywhere: 'Dieses Produkt kann in Ihrem Land nicht verkauft werden. Bitte kontaktieren Sie StationOne für weitere Informationen'
    on_demand: 'auf Nachfrage'
    on_stock: 'Auf Lager'
    out_of_stock: 'Nicht auf Lager'
    out_of_stock_description: 'Dieses Produkt ist bei StationOne ausverkauft. Für weitere Informationen, bitte kontaktieren Sie den Verkäufer.'
    price_quantity: 'Für %quantity% %unit%'
    proforma:
        0: proforma
        version: 'Proforma Version'
    proforma_pdf:
        quantity: 'Gewünschte Menge'
        title: proforma
        total_price: 'Gesamtpreis (ohne MwSt.)'
    quantity: Menge
    related_products: 'Zugehörige Dienste'
    see_cart: 'Meinen Warenkorb sehen'
    see_less: 'Weniger sehen'
    see_more: 'Mehr sehen'
    see_wishlist: 'Siehe meine kit-liste'
    seller:
        label: Herstellerpräsentation
        link_product: 'Alle Produkte anzeigen'
    sign_in_to_buy: 'Melden Sie sich an, um zu kaufen'
    similar_products: 'Zugehörige Produkte'
    title: 'Detail eines Angebots'
    too_much_quantity: 'Die gewählte Menge ist größer als der Lagerbestand (max.: %max%).'
    too_small_quantity: 'Die gewählte Menge liegt unter der Mindestbestellmenge (minimum: %min%).'
    total_price: Gesamtpreis
    wrong_quantity: 'Bitte geben Sie eine gültige Menge ein.'
    warranty_period: 'Garantie %month% Monat'
offers:
    bidbutton_msg: 'Sie können bald über Ihre bevorzugten Angebote bieten.'
    description: 'Die Angebote'
    title: 'Die Angebote'
orders:
    createCart:
        errorMoq: 'Der Artikel %reference% konnte dem Warenkorb nicht hinzugefügt werden, denn die gewünschte Menge ist geringer als die Mindestbestellmenge.'
        errorNoPrice: 'Der Artikel % reference% konnte nicht in den Warenkorb gelegt werden, da er keinen Preis hat'
        errorPrice: 'Der Artikel %reference% wurde dem Warenkorb erfolgreich hinzugefügt, allerdings hat sich sein Preis geändert.'
        errorStatus: 'Der Artikel %reference% konnte dem Warenkorb nicht hinzugefügt werden, denn der Artikel und/oder der Verkäufer sind deaktiviert.'
        errorStock: 'Der Artikel %reference% konnte dem Warenkorb nicht hinzugefügt werden, denn der vorhandene Lagerbestand für dieses Produkt ist geringer als die gewünschte Menge.'
    detail:
        go_back: 'Gehen Sie zurück zur Auftragsliste'
        shipping:
            delivered: 'Geliefert %separator%'
            last_delivery: 'Letzte Lieferung %separator%'
            title: Versandangebote
        title: Auftrag
        tracking:
            delivery_date: Lieferdatum
            error: 'Wir können momentan keine Tracking-Informationen'
            quantity: 'Menge %separator%'
            status_delivered: Geliefert
            status_pickuped: 'Abholung erfolgt'
            title: Sendungsverfolgung
            vendor_ref: 'Hersteller ref %separator%'
    empty:
        title: 'Keine Bestellung'
    export:
        address: Lieferadresse
        amount: 'Order Amount (tax excl.)'
        amountTaxIncluded: 'Order Amount (tax incl.)'
        amountVat: 'Order VAT Amount'
        buyerRef: 'Buyer Ref'
        costCenter: 'Cost center'
        currency: Currency
        date: Auftragsdatum
        documentRequirement1: 'Document requirement 1'
        documentRequirement10: 'Document requirement 10'
        documentRequirement2: 'Document requirement 2'
        documentRequirement3: 'Document requirement 3'
        documentRequirement4: 'Document requirement 4'
        documentRequirement5: 'Document requirement 5'
        documentRequirement6: 'Document requirement 6'
        documentRequirement7: 'Document requirement 7'
        documentRequirement8: 'Document requirement 8'
        documentRequirement9: 'Document requirement 9'
        expectedDeliveryDate: 'Voraussichtliches Lieferdatum'
        frameContractNumber: 'Frame contract number'
        id: 'Order id'
        internalBuyerOrderId: 'Buyer''s ERP Order id'
        itemPrice: 'Order line amount (tax excl.)'
        orderLine: OrderLine
        packagingRequirement1: 'Packaging requirement 1'
        packagingRequirement2: 'Packaging requirement 2'
        packagingRequirement3: 'Packaging requirement 3'
        paymentTerms: 'Payment Terms'
        productName: 'Product Name'
        quantity: Quantity
        status: Status
        subOrderId: 'Sub Order Id'
        unitPrice: 'Unit price (tax excl.)'
        validationNumber: 'Bestellnummer des Käufers'
        vendorName: 'Vendor Name'
        vendorRef: 'Vendor Ref'
    list:
        address: Lieferadresse
        block:
            addition_information: 'Zusätzliche Information'
            additional_information: 'Zusätzliche Information'
            address_title: 'Lieferadresse:'
            billing_address_title: 'Rechnungsadresse:'
            buyer_internal_order_id: 'Bestellnummer des Käufers'
            cost_center_title: 'Kostenstelle:'
            date_title: 'Bestelldatum:'
            expectedDate: 'Erste erwartete Lieferung:'
            iban_account_name: 'IBAN kontoname:'
            iban_number: 'IBAN-Nummer:'
            key: 'Abgleichschlüssel:'
            order_line: Bestellzeile
            order_title: 'Bestellnummer:'
            packaging_specifications: Packanleitung
            payment_information: 'Zahlungsinformationen:'
            payment_terms: 'Zahlungsbedingungen:'
            requested_documents: 'Erforderliche Dokumente'
            status: 'Status:'
            total_title: 'GESAMT:'
            validation_number_title: 'Bestellnummer des Käufers: '
        date: Bestelldatum
        detail: Detail
        download_pdf: 'PDF Herunterladen'
        export: Export
        id: 'Id der Bestellung'
        link:
            buy_again: 'Erneut kaufen'
            details: Details
            export: 'PDF Export'
            invoice: Rechnung
            refund: Rückerstattung
            document: Dokument
            documents: Dokumente
        merchant_product: Artikel
        merchant_products: Artikel
        sub_order_id: Unterauftrags-ID
        tab:
            cancelled: 'Bestellung storniert'
            empty: 'Sie haben keine Bestellung in diesem Bereich.'
            past: 'Bestellung abgeschlossen'
            running: 'Bestellung läuft'
        total: Gesamt
    status:
        pending_creation: 'Erstellung ausstehend'
        status_0: 'Ausstehende Zahlung'
        status_110: 'Problem gelöst'
        status_11111111: 'Erstellung ausstehend'
        status_2000: Abgebrochen
        status_3000: Gelöscht
        status_60: 'Bestätigung des Anbieters ausstehend'
        status_80: Bestätigt
        status_85: 'Versand & Rechnung'
payment:
    error: 'Während des Zahlungsvorgangs ist ein unerwarteter Fehler aufgetreten. Ihre Bestellung wurde storniert. Wenn der Fehler weiterhin besteht, wenden Sie sich bitte an den Support'
    form:
        not_authorized: 'Sie sind nicht berechtigt, den Zahlungsmodus anzufordern. Bitte wenden Sie sich an Ihren Manager.'
        submit: 'Beantragung Zahlung nach Rechnungsstellung'
    pre_cc_mode:
        error: 'Beim Abrufen Ihrer Bestellinformationen% id% ist ein technischer Fehler aufgetreten. Wenn das Problem weiterhin besteht, wenden Sie sich an den Support'
        ko: 'Bei der Zahlung ist ein Fehler aufgetreten: Ihre Bestellung % id% wurde storniert'
        ok: 'Ihre Zahlung wurde erfolgreich durchgeführt. Ihre Bestellung% id% ist jetzt autorisiert'
        success:
            text: 'Ihre Bestellung% id% wurde in Vorauszahlung per Kreditkarte bestätigt.'
            text_2: 'Sie können Ihren Kauf bereits auf der Bestellseite überprüfen.'
            text_3: 'Der Verkäufer wird Ihren Kauf in Kürze bestätigen.'
            title: 'Glückwünsch !'
        title: Zahlungsbestätigung
        title_error: 'Technischer Fehler'
        title_ko: Zahlungsfehler
        title_ok: 'Bezahlung bestätigt'
        waiting_status: 'Bitte warten Sie und warten Sie auf die Zahlungsautorisierung für Ihre Bestellung% id% ...'
    pre_virement_mode:
        pending:
            text: 'Ihre Bestellung wurde in Vorauskasse per Überweisung registriert.'
            text_2: 'Sie erhalten eine E-Mail mit allen Details, um mit der Zahlung fortzufahren.'
            text_3: 'Sobald Ihre Zahlung erfolgt ist, sehen die Verkäufer Ihre Bestellung und können sie bestätigen.'
            text_4: 'Bitte beachten Sie, dass die auf der Bestellung angegebene Lieferzeit etwas länger sein kann als erwartet, da sie von der Zeit abhängt, die Ihre Organisation benötigt, um die Überweisung bis zu ihrem Eingang bei StationOne auszuführen.'
            text_link1: 'Zurück zur Suchseite'
            text_link2: 'Siehe meine Bestellungen'
            title: 'Glückwünsch !'
        success:
            payment_detail_number: Transaktionsnummer
            payment_details: 'Hier ist die erforderliche Information für Ihre Banküberweisung:'
            payment_details_iban: 'IBAN-Nummer: %iban%'
            payment_details_iban_account_name: 'IBAN kontoname: %iban_account_name%'
            payment_details_key: 'Abstimmschlüssel: %key%'
            text: 'Ihre Bestellung %numOrder% wurde erfolgreich bestätigt. Sie wird nach Erhalt Ihrer Banküberweisung schnellstmöglich bearbeitet'
            text_link1: 'Zurück zur Suchseite'
            text_link2: 'Meine Bestellungen sehen'
            title: 'Bestellung bestätigt'
    select_mode:
        error:
            cc: 'Fehler beim Erstellen der Transaktion. Bitte wenden Sie sich an den Support'
        preCreditCard: 'Vorauszahlung per Kreditkarte'
        preTransferWire: 'Vorauszahlung per Banküberweisung'
        select: Auswählen
        termTransferWire: 'Term Zahlung (45 Tage Ende des Monats, nach Ausstellung der Rechnung)'
        title: 'Wahl des Zahlungsmittels'
    time_mode:
        error:
            text: 'Bei der Bestätigung Ihrer Bestellung ist ein Fehler aufgetreten. Falls das Problem weiter besteht, kontaktieren Sie bitte den Support.'
            text_link1: 'Meinen Warenkorb sehen'
            text_link2: 'Zurück zur Suchseite'
            text_link3: 'Support kontaktieren'
            title: 'Fehler bei der Bestätigung Ihrer Bestellung'
        pending:
            text: 'Ihre Bestellung wurde registriert. Zahlungsbedingung: 45 Tage Ende des Monates  nach Rechnungserstellung.'
            text_2: 'Sie erhalten eine Bestätigung Ihres Anbieters direkt auf Ihrer Bestellseite.'
            text_3: 'Sobald Sie die Rechnung erhalten haben, können Sie Ihre Zahlung verwalten.'
            text_link1: 'Zurück zur Suchseite'
            text_link2: 'Siehe meine Bestellungen'
            title: 'Glückwunsch !'
        succes:
            text: 'Ihre Bestellung %numOrder% wurde erfolgreich erstellt.'
            text_2: 'Sie erhalten eine E-Mail mit den Zahlungsangaben (IBAN und Abstimmschlüssel), wenn die Rechnung ausgestellt wird.'
            text_link1: 'Zurück zur Suche'
            text_link2: 'Meine Bestellungen sehen'
            title: 'Bestätigung Ihrer Bestellung'
payment_mode:
    Prepayment_creditcard: 'Vorauszahlung per Kreditkarte'
    Prepayment_moneytransfert: 'Vorauszahlung per Banküberweisung'
    Termpayment_moneytransfert: 'Term Zahlung (45 Tage Ende des Monats, nach Ausstellung der Rechnung)'
    ask_for_term_error: 'Fehler bei der Bearbeitung des Antrags auf Genehmigung von Ratenzahlung'
    click_button: 'Klicken Sie auf die nachfolgende Schaltfläche, um die Ratenzahlung verwenden zu können'
    enabled: 'Ihr Konto ist auch berechtigt, die Zahlungsmethode per Banküberweisung zu verwenden.'
    info: 'Die standardmäßig genehmigten Prepay-Zahlungsmittel sind Kreditkarte und Banküberweisung.'
    pending: 'Ein Antrag auf Genehmigung von Ratenzahlung wird derzeit analysiert'
    save_error: 'Bei der Aktualisierung der Zahlungsmittel ist ein Fehler aufgetreten'
    saved: 'Die Zahlungsmittel wurden aktualisiert'
    title: Zahlungsarten
product:
    about_seller: 'Über den Verkäufer'
    application_categories: Kategorien
    buy: Kaufen
    buyer_reference: 'Referenz des Käufers'
    cart_item_comment: 'Kommentar'
    comparison: Vergleicher
    converted_price: 'Basierend auf dem täglichen Wechselkurs'
    delivery_time: Lieferzeit
    description: Beschreibung
    info_button_buy: 'Die Produkte werden in Kürze zum Kauf erhältlich sein. Wenn noch nicht der Fall, erstellen Sie bitte ein Konto, um schnellstmöglich informiert zu werden!'
    info_converted_price: 'Nur zu Informationszwecken.'
    logistics_informations: Logistikinformationen
    made_in: Herstellungsland
    manufacturer: Hersteller
    manufacturer_reference: Herstellerreferenz
    private: Privat
    quantity: 'Verfügbare Anzahl'
    max_quantity: 'Kaufbare Menge'
    seller: Verkäufer
    seller_reference: 'Verkäufer referenz'
    technical:
        bearing_type: Radlagertyp
        code_command_rs: RS-Bestellcode
        conditioning: Verpackung
        dtr: DTR
        flange_outer_diameter: 'Außendurchmesser des Flansches'
        inner_diameter: Innendurchmesser
        manufacturer_ref: Herstellerartikelnummer
        marketplace_id: Marktplatz-ID
        material: Material
        min_order_quantity: Mindestmenge
        nb_row: 'Anzahl von Reihen'
        outer_diameter: Außendurchmesser
        rated_static_load: 'Statische Nennlast'
        ring_width: 'Breite des Ringspiels'
        seller_name: 'Name des Verkäufers'
        seller_ref: 'Referenznummer des Verkäufers'
        termination_type: 'Art des Abschlusses'
        trendmark: Marke
    technical_detail: 'Technische Beschreibung'
    technical_details: 'Technische Beschreibung'
products:
    all_offers: 'Alle Angebote sehen'
    home_list_header: 'Liste der Angebote'
    home_product_list_header: 'Meistgesuchte Produkte'
profile:
    form:
        email: E-Mail-Adresse
        firstname: Vorname
        lastname: Nachname
        main_phone_number: Telefonnummer
        optional_phone_number: 'Optionale Telefonnummer'
        submit: Speichern
        update:
            error: 'Bei der Aktualisierung Ihrer persönlichen Informationen ist ein Fehler aufgetreten'
            success: 'Ihre persönlichen Informationen wurden aktualisiert.'
    password:
        help: ''
        submit: Anwenden
        title: 'Ihr Passwort ändern'
proforma:
    address: Address
    address_title: 'Proforma from STATION ONE in the name and on behalf of'
    billing_address: 'Billing address'
    capital: 'to the capital of'
    condition:
        adress: 'StationOne, capital 20.000 Euros, 69/73 Boulevard Victor Hugo, 93400 Saint-Ouen (France). VAT NUMBER: FR18752364885'
        payment: 'Payment to be made to our representative'
        purchase: 'Purchase conditions according to General Terms of Use of Station One and then to the online sales conditions of %vendor_name% or to the contract number if any'
        title: 'Conditions of this offer'
    date: 'Proforma generated on'
    rcs: RCS
    siret: SIRET
    text: 'This proforma is only valid at the time of generation of this pdf. StationOne does not commit to holding this price over time.'
    title: proforma
    vat_number: 'VAT Number'
purchase_request:
    cart:
        add: 'In den Warenkorb'
        remove: 'Aus dem Wagen nehmen'
    clear: 'Seite löschen'
    detail: Bestellanforderungs
    export: 'Nicht gefundene Referenzen senden'
    exportFound: 'Export found references'
    import:
        error:
            csv_format: 'Ungültiges Dateiformat. Es wird nur das CSV-Format akzeptiert'
            csv_size: 'Die Dateigröße überschreitet das Limit. Die maximale Größe pro Datei beträgt 2 MB'
            internal_error: 'Fehler beim Starten der Anfrage, bitte wenden Sie sich an StationOne'
        title: Importieren
    import_waiting: 'Bitte importieren Sie eine neue Datei'
    instruction: 'Die importierte Datei muss das folgende Format haben: <a href="%link%" target="_blank"> format_import_demande_achat.csv </a>'
    offer:
        batch_price: 'Preis für 50 Artikel'
        error_bafv: 'Dieser Produktpreis ist nicht verfügbar, da Sie keinen Zugriff auf die Katalogpreise dieses Anbieters haben. Um den Zugriff anzufordern, klicken Sie auf den Produktnamen und klicken Sie auf der Produktdetailseite auf "Zugriff auf Katalogpreise anfordern".'
        error_businesseverywhere: 'Dieses Produkt kann in Ihrem Land nicht verkauft werden. Grund: Mindestens ein Stakeholder muss in Europa ansässig sein'
        error_noprice: 'Dieses Produkt ist auf Preisanfrage erhältlich'
        manufacturer: Hersteller
        merchant: Verkäufer
        quantity: Menge
        quantity_available: 'Verfügbare Anzahl'
        see_more: 'Mehr sehen'
        select: wählen
        selected: Ausgewählt
        sku_price: 'Preis für'
        unit_price: Einheitspreis
    pr_item:
        buyer_order_number: 'Bestellnummer des Käufers'
        buyer_reference: 'Käufer referenz'
        cost_center: Kostenzentrum
        details: Produktinformationen
        expected_delivery_date: 'Voraussichtliches Datum'
        manufacturer_name: Herstellername
        manufacturer_reference: Herstellerartikelnummer
        merchant: Verkäufer
        no_ref: 'Keine Referenz gefunden'
        order_line: Bestellzeile
        product_name: Produktname
        purchase_request_number: Bestellanforderungsnummer
        quantity: Menge
        quantity_expected: 'Erwartete Menge'
        ref: Referenz
        see_more: 'Mehr sehen'
        unit_price: Einheitspreis
        unit_price_of_reference: Referenzstückpreis
    title: Bestellanforderungs
redirect:
    form:
        delete:
            error: 'Beim Löschen der Umleitung ist ein Fehler aufgetreten'
            success: 'Die Umleitung wurde gelöscht'
        destination:
            help: 'Überprüfen Sie, ob die Url existiert'
            label: Destination
        header:
            edit: 'Änderung der Umleitung:'
            new: 'Neue Umleitung'
        origin:
            help: Slug
            label: Ursprung
        submit:
            error:
                create: 'Bei der Erstellung ist ein Fehler aufgetreten'
                update: 'Bei der Aktualisierung ist ein Fehler aufgetreten'
            success:
                create: 'Die Umleitung wurde erstellt'
                update: 'Die Umleitung wurde aktualisiert'
        type:
            help: '301 = permanent / 302 = vorübergehend'
            label: 'Art der Umleitung'
redislist:
    alert:
        delete_all: 'All Redis keys have been deleted.'
        error: 'An error has occurred ! The key %key% has not been deleted.'
        success: 'The Key %key% has been deleted !'
    delete_all_keys: 'Delete All'
    delete_all_keys_title: 'Delete all keys'
    delete_title: 'Delete the key %key%'
    no_key: 'No Redis Key stored.'
registration:
    buyer: Käufer
    error:
        identification_already_used: 'Diese Identifikation wurde auf diesem System bereits verwendet'
        identification_already_used_alert: 'Ihr Unternehmen hat bereits ein Konto bei StationOne. Der Inhaber des Kontos kann Ihnen Zugriff gewähren. Wenn Sie den Kontoinhaber nicht kennen, senden Sie uns bitte eine Anfrage mit dem Kontaktformular. Möchten Sie zum Kontaktformular gehen?'
        technical: 'Bei der Erstellung Ihres Kontos ist ein technischer Fehler aufgetreten. Bitte kontaktieren Sie den Support.'
        userDisabled: 'Der Benutzer wurde deaktiviert. Wenn Sie dieses Benutzerkonto erneut aktivieren möchten, kontaktieren Sie bitte StationOne über das Formular <a href=''%url%''>contact</a>>.'
    label: 'Ich möchte als Mitglied mitmachen'
    selectType: 'Konto auswählen'
    vendor: Verkäufer
resetting:
    check_email: '| Eine E-Mail wurde gesendet. Sie enthält ein Link, auf das Sie klicken müssen, um Ihr Passwort zurückzusetzen.'
    newpwd: 'Änderung Ihres Passworts'
    request:
        submit: 'Passwort zurücksetzen'
    reset:
        submit: Ändern
search:
    advanced_search:
        submit: Suchen
        title: 'Erweiterte Suche'
    compatible_products_with: 'Kompatible Produkte mit'
    departments:
        all_departments: ' Alle Kategorien'
        bearing: Lager
        brake_disc: Bremsscheibe
        camera: Kamera
        filter: Filter
        glazing: Verglasung
        screen: Bildschirm
    filters: Filter
    help: Hilfe
    in_compatible_products: 'Suche in kompatiblen Produkten'
    no_custom_ref_found: 'In Ihrem Katalog wurde keine Referenz gefunden.'
    no_offer: 'Entschuldigung, aber wir konnten keine Ergebnisse finden.'
    no_offer_in_catalog: 'Es gibt kein Ergebnis, das Ihrer Suche entspricht.'
    page: Seite
    pagination:
        next: Nächstes
        'on': auf
        previous: Voriges
    products: Produkte
    result_label: 'Ihre Suchergebnisse (%total% Ergebnisse)'
    results: Ergebnisse
    results_for: für
    searchbar:
        advanced_search: 'Erweiterte Suche +'
        custom_search: 'In meinem Katalog suchen'
        in_catalog: 'Mein Katalog'
        in_marketplace: Marketplace
        in_product_compatibility: 'Kompatible Produkte'
        mobile_placeholder: 'Ein Produkt suchen'
        placeholder: 'Beginnen Sie zu schreiben, um ein Produkt zu suchen'
    show: Anzeigen
    sidebar:
        category: kategorie
        commons: Gemeinsam
        departments: Kategorie
        refine_by: 'Verfeinern nach'
        search: 'Einen Begriff suchen'
        see_more: 'Mehr sehen'
        specific: Spezifisch
        up: '< Zurück'
    sort_by: 'Sortieren nach'
    sort_form:
        delivery_time: 'Lieferfristen (aufsteigend)'
        newest: 'Neueste (an erster Stelle)'
        price_max: 'Preis (absteigend)'
        price_min: 'Preis (aufsteigend)'
        relevance: Relevanz
    title: Suchen
security:
    login:
        create_account: 'Ein Konto erstellen'
        fail: 'Fehlerhafte Zugangsdaten.'
        footer_text: 'Noch nicht Mitglied?'
        login: 'Sich anmelden'
        role_denied: ''
        title: 'Käufer Login'
shipping:
    option:
        cheapest: 'Preis optimiert'
        fastest: Dringend
        no_merchant_shipping: 'Dieser Händler bietet keinen Versandservice über StationOne an'
        no_shipping_available: 'Kein Transportservice verfügbar. Für weitere Informationen wenden Sie sich bitte an StationOne'
        noshipping: 'Kein Versand'
shipping_point:
    form:
        address:
            label: Adresse
        comment:
            label: 'Kommentare (Öffnungszeiten an der Warenrezeption, Besonderheiten, um die Rezeption zu erreichen,…)'
        contact:
            accountant: Buchhaltung
            label: 'Kontakt bei Warenannahme'
        documents_requests:
            label: 'Erforderliche Unterlagen für den Wareneingang'
        first: Hauptlieferadresse
        name: 'Name des Lieferorts'
        packaging_request:
            label: Packanleitung
            tips: 'max char = 255'
        save: Speichern
        title_common: Lieferadresse
        title_edit: Ändern
        title_new: Neue
site:
    form:
        accountant_email: 'Rechnungen werden an die unten angegebene E-Mail-Adresse gesendet'
        add: 'Ein Kostenzentrum hinzufügen'
        add_contact: 'Einen Kontakt hinzufügen'
        add_modal: 'Neues Kostenzentrum'
        add_shipping_address: 'Eine Lieferadresse hinzufügen'
        adresse: Adresse
        afternoon:
            end: 'Nachmittags geschlossen'
            start: 'Nachmittags geöffnet'
        authorization: 'Präfektorale Genehmigung'
        cancel: Abbrechen
        comment: Kommentar
        complement: Zusatz
        copy: Kopieren
        corporate_name: Firmenbezeichnung
        cpZipCode: 'Postleitzahl / Stadt'
        create: Erstellen
        created: 'Ihre Kostenzentrums wurde erstellt'
        days: Tage
        default_user:
            label: ''
            placeholder: ''
        delete:
            content: 'Möchten Sie dieses Kostenzentrum wirklich löschen?'
            shipping_point: 'Möchten Sie diese Lieferadresse wirklich löschen?'
        delete_shipping_address: Löschen
        documentation_request_1: 'Benötigtes Dokument 1'
        documentation_request_10: 'Benötigtes Dokument 10'
        documentation_request_2: 'Benötigtes Dokument 2'
        documentation_request_3: 'Benötigtes Dokument 3'
        documentation_request_4: 'Benötigtes Dokument 4'
        documentation_request_5: 'Benötigtes Dokument 5'
        documentation_request_6: 'Benötigtes Dokument 6'
        documentation_request_7: 'Benötigtes Dokument 7'
        documentation_request_8: 'Benötigtes Dokument 8'
        documentation_request_9: 'Benötigtes Dokument 9'
        error: 'Fehler beim Erstellen Ihrer Kostenstelle ! Bitte alle Felder ausfüllen'
        friday: Freitag
        identification: Siret-Nummer
        info:
            operator_needed: 'Die Informationen Ihres Standorts können nicht mehr direkt geändert werden. Wenn Sie Änderungen vornehmen möchten, kontaktieren Sie bitte StationOne über das Formular <a href=''/ticket/create/''>contact</a>.'
        legales_doc: 'Rechtliche Dokumente'
        main_contact: Hauptkontakt
        modify_shipping_address: Ändern
        monday: Montag
        morning:
            end: 'Morgen nah'
            start: 'Morgen offen'
        name: 'Name des Kostenzentrums'
        opening_time: Öffnungszeit
        other_contacts: 'Sonstige Kontakte'
        packaging_request_1: 'Packanleitung 1'
        packaging_request_2: 'Packanleitung 2'
        packaging_request_3: 'Packanleitung 3'
        placeholder:
            name: 'Name der Kostenstelle'
        save: Speichern
        submit: Einreichen
        submitError: 'Sie müssen zuerst Ihr Unternehmen einreichen, bevor Sie Ihren Standort einreichen'
        thursday: Donnerstag
        title: Kostenzentrum
        tuesday: Dienstag
        wednesday: Mittwoch
    header:
        edit: 'Änderung des Kostenzentrums:'
        list: 'Liste der Kostenzentren'
    list:
        deactivation:
            ko: 'Fehler beim Löschen des Kostenzentrums'
            ok: 'Das Kostenzentrum wurde erfolgreich gelöscht'
            users_exist: 'Benutzer sind an diese Kostenstelle gebunden, sodass Sie diese Kostenstelle nicht löschen können'
        default_user:
            not_defined: 'No user is defined for this cost center'
            title: 'Default user for this cost center'
        no_user: 'Keine verbundenen Benutzer'
        title: 'Mit diesem Kostenzentrum verbundene Benutzer'
        users:
            action: Aktion
            firstname: Vorname
            function: Funktion
            id: N.º
            lastname: Nachname
            role: Rolle
stats:
    accrued:
        'no': Nein
        title: 'Kumulierter Betrag'
        'yes': Ja
    cost_center:
        all: Alles
        title: Kostenstelle
    orderAmount: Auftragsbetrag
    year:
        title: Jahr
status:
    draft: 'Zu ergänzen'
    pending: 'Wartet auf Validierung durch den Bediener'
    valid: 'Durch den Bediener zu validieren'
system:
    setting:
        form:
            submit: Ändern
    settings:
        homepage:
            disclaimer:
                title: 'Id des Videos'
                video_src: Video
            slider:
                title: 'Parameter des Slider'
            title: 'Parameter der Startseite'
            video_1_src: 'Youtube-Id des Videos von Slide 2'
            video_2_src: 'Youtube-Id des Videos von Slide 3'
            video_en_src: 'Video auf Englisch'
            video_fr_src: 'Video auf Französisch'
        notifications:
            command:
                title: Bestellungen
            completion_recall_number_of_day: 'Anzahl von Tagen vor der Erinnerungsnachricht zur Komplettierung'
            title: Benachrichtigungen
        offers:
            offer_1: 'Izberg Id 1. gefragtes Angebot'
            offer_2: 'Izberg Id 2. gefragtes Angebot'
            offer_3: 'Izberg Id 3. gefragtes Angebot'
            offer_4: 'Izberg Id 4. gefragtes Angebot'
            offer_5: 'Izberg Id 5. gefragtes Angebot'
            offer_6: 'Izberg Id 6. gefragtes Angebot'
            offer_7: 'Izberg identifier 7th popular offer'
            offer_8: 'Izberg identifier 8th popular offer'
            popular:
                title: 'Gefragte Angebote'
            title: Angebotsparameter
        security:
            login:
                title: 'Sicherheitsparameter des Login'
            login_attempt_max: 'Max. Anzahl von Login-Versuchen'
            login_banned_user_unlock_timeout: 'Freigabe der gesperrten Benutzer nach {x} Minuten'
            title: Sicherheitsparameter
        testimonials:
            fader_speed: 'Rotationsgeschwindigkeit der Berichte (in Millisekunden)'
            max_age: 'Maximales Alter in Monaten (darüber werden die Berichte nicht angezeigt)'
            max_items: 'Max. Anzahl der anzuzeigenden Berichte'
            parameters:
                title: Berichtsparameter
            title: Berichte
        update_error: 'Bei der Aktualisierung der Parameter ist ein Fehler aufgetreten'
        update_success: 'Parameter erfolgreich aktualisiert'
tab_infos_seller:
    cgv: 'Allgemeine Verkaufsbedingungen'
    frame_contract: 'Under particular condition of frame contract : %frame_contract%'
    minimum_order_amount: 'Mindestbestellwert für diesen Verkäufer : %amount%'
    presentation: Herstellerpräsentation
    see_all_products: 'Alle Produkte anzeigen'
ticket:
    common:
        add_file: 'Datei hinzufügen'
        administrator_user: '%fullname% (Bediener)'
        authorized_files_extensions_message: 'Es sind nur pdf, jpg, gif, png, tiff, xls, xlsx erlaubt'
        file_reset: Leeren
        message: Nachricht
        nofiles: 'Keine Datei ausgewählt'
        number: Ticketnummer
        subject: Betreff
        submit: Senden
        update: Senden
    create:
        company: Unternehmen
        email: E-Mail-Adresse
        firstname: Vorname
        foradmin: StationOne
        forvendor: 'ein Verkäufer'
        function: Funktion
        lastname: Nachname
        message_text: Nachricht
        phone: Telefon
        recipient: 'Wahl des Unternehmens'
        title: 'Sie möchten kontaktieren:'
    edit:
        add: 'Neue Antwort'
        attachment: Anhang
        attachment_button: 'Dateien hinzufügen'
        author: Autor
        close: 'Die Diskussion schließen'
        close_thread: 'This thread was closed by %firstname% %lastname% on %date%'
        close_thread_anonymous: 'This thread was closed by an anonymous user on %date%'
        closed: 'Geschlossen am'
        company: Firma
        date: 'Ticket erstellt am'
        id: ID
        link: 'Zurück zur Nachrichtenliste'
        message_label: Nachrichten
        message_placeholder: 'Die Nachricht eingeben'
        message_text: Antwort
        new_message: 'NEUE NACHRICHT:'
        operator: (Bediener)
        recipient: Empfänger
        reopen: 'Erneut öffnen'
        save_message: SPEICHERN
        subject: Betreff
        timeFormat: 'um $1h$2min$3s'
        title: Ticket
    error:
        create: 'Bei der Erstellung Ihres Tickets ist ein Fehler aufgetreten.'
        update: 'Bei der Aktualisierung Ihres Tickets ist ein Fehler aufgetreten.'
    list:
        actions: Aktionen
        add: 'Eine neue Nachricht schreiben'
        all: Alle
        author: Autor
        closed: Geschlossen
        company: Unternehmen
        createdAt: 'Erstellt am'
        empty: 'Sie haben keine Nachricht'
        export_csv: 'Als CSV exportieren'
        knp_next: Nächstes
        knp_previous: Voriges
        lastAt: 'Geändert am'
        main_contact: 'Urheber des Tickets'
        me: Sie
        nb_messages: 'Anzahl der Nachrichten'
        next: 'Die ältesten Tickets sehen'
        number: ID
        opened: Geöffnet
        previous: 'Die neuesten Tickets sehen'
        sent_to: 'Gesendet an'
        status: Status
        sujet: Betreff
        title:
            resolved: 'Bearbeitete Nachrichten'
            standard: Nachrichten
    status:
        STATUS_CLOSED: Geschlossen
        STATUS_INFORMATION_REQUESTED: Informationsanfrage
        STATUS_INVALID: Ungültig
        STATUS_IN_PROGRESS: Laufend
        STATUS_NEW: Neu
        STATUS_ON_HOLD: Anstehend
        STATUS_OPEN: Geöffnet
        STATUS_OPERATOR_RESPONDED: Geöffnet
        STATUS_RESOLVED: Geschlossen
        STATUS_USER_RESPONDED: Geöffnet
    success:
        create: 'Ihr Antrag wurde erfolgreich erstellt'
        update: 'Der Antrag wurde erfolgreich aktualisiert.'
    waylf:
        title: 'Wonach suchen Sie ?'
user:
    form:
        ROLE_API: ''
        ROLE_BUYER_ADMIN: 'Account Manager / Administrator'
        ROLE_BUYER_BUYER: Antragsteller
        ROLE_BUYER_PAYER: 'Einkäufer / Zahler'
        ROLE_OPERATOR: 'Betreiber / Operator'
        ROLE_SUPER_ADMIN: Administrator
        add: 'Einen Benutzer erstellen'
        company: Unternehmen
        email: E-Mail-Adresse
        firstname: Vorname
        function:
            0: Funktion
            label: ''
            mandatory: ''
        lastname: Nachname
        phone1: Haupttelefon
        phone2: Zweittelefon
        role: Rolle
        site: Kostenzentren
    registration:
        email: E-Mail-Adresse
    resetting:
        title: 'Änderung des Passworts'
validator:
    date: 'Bitte geben Sie ein gültiges Datum ein.'
    email: 'Bitte geben Sie eine gültige E-Mail-Adresse ein.'
    number: 'Bitte geben Sie eine gültige Nummer ein.'
    remote: 'Bitte korrigieren Sie dieses Feld.'
    required: 'Dieses Feld ist erforderlich.'
    url: 'Bitte geben Sie eine gültige URL ein.'
wishlist:
    add_new: 'Neue kit-liste hinzufügen'
    add_to_cart: 'In den Warenkorb legen'
    charge:
        confirm: 'Diese Aktion löscht den Inhalt Ihres Einkaufswagens, bevor Sie die Angebote aus der Wunschliste hinzufügen'
        error: 'Beim Hinzufügen von Angeboten zu Ihrem Einkaufswagen ist ein Fehler aufgetreten'
        success: 'Die Angebote aus Ihrer Liste wurden dem Warenkorb hinzugefügt'
    delete:
        error: 'Beim Löschen Ihrer kit-liste ist ein Fehler aufgetreten'
        success: 'Ihre liste wurde gelöscht'
    delete_confirm: 'Sind Sie sicher, dass Sie diese Wunschliste löschen möchten?'
    go_back: 'Zurück zur Liste'
    item:
        delete:
            error: 'Beim Löschen Ihres Angebots ist ein Fehler aufgetreten'
            success: 'Ihr Angebot wurde gelöscht'
        delete_confirm: 'Sind Sie sicher, dass Sie dieses Angebot löschen möchten?'
        noItem: 'Sie haben nicht in Ihrer Liste angeboten'
        update:
            error: 'Bei der Aktualisierung der Menge ist ein Fehler aufgetreten'
            success: 'Die Menge wurde aktualisiert'
    new_name: 'Name der neuen Liste'
    none: 'Sie haben keine kit-liste'
    notification_message:
        no_price_offer: 'Ungültige kit-liste'
    save: Speichern
    save_error: 'Beim Speichern der kit-liste ist ein Fehler aufgetreten'
    save_in: 'In kit-liste hinzufügen'
    save_success: 'Wunschliste erfolgreich gespeichert'
    table:
        days: Tage
        delivery_time_item: 'Lieferzeit vor Lieferung'
        delivery_time_wishlist: 'Lieferzeit der kit-liste'
    title: Kit-liste
'yes': Ja
seller:
    general_condition: 'Allgemeine Verkaufsbedingungen'
