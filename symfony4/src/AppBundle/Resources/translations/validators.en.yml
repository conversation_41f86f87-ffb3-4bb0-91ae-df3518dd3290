user:
  phone: "Please enter a valid phone number"
  current_password:
    invalid: "Wrong password"
  password:
    lower: 'Password must contain at least one lower case letter'
    upper: "Password must contain at least one capital letter."
    num: "Password must contain at least one numeric character [0-9]."
    special: "Password must contain at least one of these special characters: [!@#$%^*_-]."

form:
  company:
    ident_number:
      invalid: "Invalid format"
      unknown: "This VAT number doesn't exist"
    fileupload:
      invalid: "You have to upload a lesta one document"
  contact:
    phone_regex: "Only digits and plus [+] characters are allowed"
    zipcode_regex : "Wrong value, must be 5 digits"
  redirect:
    sameorigin: "Source and target URL must be different"
    samedest: "Source and target URL must be different"
    slug:
      invalid_format: "Invalid format"
      invalid_http: "Please remove [http(s)://] part of the url"
      invalid_exist: "This slug already exists"
  node:
    slug:
      invalid_format: "Invalid format"
      invalid_http: "Please remove [http(s)://] part of the url"
      invalid_exist: "This slug already exists"
  user:
    invalid_role: "Select a role"
    sites:
      mandatory: "Chose at least one cost center."
    function:
      mandatory: 'Job title is mandatory'
required: "Mandatory field"

disabled_company: "This company is disabled"

captcha:
  error: "Wrong captcha"
