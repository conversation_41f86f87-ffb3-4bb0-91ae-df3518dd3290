address:
    form:
        address: Adresse
        address2: 'Complément (bâtiment / Résidence / ...)'
        address2_placeholder: 'Bâtiment / Résidence / ...'
        all_country_placeholder: 'Tous les pays'
        check: Vérifier
        city: Ville
        country: Pays
        country_placeholder: 'Choisis<PERSON>z un pays'
        email: Email
        first_lastname: 'Nom et Prénom du contact'
        phone1: 'Tél principal'
        phone1_placeholder: 'Ce téléphone est requis'
        phone2: 'Tél secondaire'
        region: Région
        region_placeholder: 'Choisissez une région'
        update:
            error: 'Une erreur s''est produite lors de la mise à jour des données de la société'
            success: 'Les données de la société ont été mises à jour'
        zipcode: 'Code Postal'
address_form_all_country_placeholder: 'Tous les pays'
admin:
    logout: Déconnexion
admin_menu:
    admin_users:
        add_user: 'Ajouter un utilisateur'
        admin_list: Administrateurs
        label: 'Manage users'
        user_list: Utilisateurs
    bafv_requests_menu:
        label: 'BAFV catalog prices requests'
    companies_menu:
        all_companies: 'Tous les acheteurs'
        costs_centers: 'Centres de coûts'
        label: Acheteurs
        users: Utilisateurs
    dashboard:
        label: 'Tableau de bord'
    merchant_menu:
        label: Vendeurs
    message: Messages
    messages_menu:
        add: 'Créer une discussion'
        label: Messages
        list: 'Demandes ouvertes'
        resolved: 'Demandes résolues'
    orders: Commandes
    other_menu:
        api_doc: ''
        automatic_controls: 'Automatic Controls'
        feeback_form: 'Feedback form'
        label: Autre
        logo: 'Element de logo'
        notifications: Notifications
        payloads: ''
        redis_keys: 'Liste des clefs Redis'
        search_historization:
            label: 'Historique des recherches'
        serches_list: 'Searches list'
        slider: 'Element du slider'
        term_payment: 'Demandes de paiement a terme'
        top_mismatch_catalog_references: 'Top des références catalogue invalides'
    redirects:
        label: Redirections
    sys_parameters:
        label: 'Paramètres systeme'
    web_content:
        add: 'Ajouter web content'
        label: 'Web content'
        list: 'Tous les web contents'
        popular_offers: 'Offres populaires'
algolia:
    attributes:
        BAB10_MOQ: 'Quantité minimale'
        BAE10_Incoterm: Incoterm
        CAA10_DTR: DTR
        DQL10_Friction_coef: 'Coefficient de friction'
        Threshold_2_Prcie: 'Prix plancher'
    id: 'Identifiant Marketplace'
    merchant:
        name: Vendeur
    product:
        description: Description
        manufacturer: Fabricant
        name: Produit
back:
    admin:
        filter:
            activeBtn: 'Afficher les administrateurs actifs'
            inactiveBtn: 'Afficher les administrateurs inactifs'
    back: Retour
    back_to_list: 'Retour à la liste'
    bafv_request:
        list:
            buyer_code: 'Buyer Code'
            buyer_name: 'Buyer Name'
            status: Status
            submitted_on: 'Request Submitted On'
            vendor_company_name: 'Vendor Company Name'
    catalogue_references:
        not_found_mismatch_references: 'Aucune référence invalide'
        reference: references
        top_mismatch_references_title: 'Top des références invalides'
        total: total
    commons:
        actions: Actions
        add: Ajouter
        all: Tous
        cancel: Annuler
        delete: Supprimer
        disable: Désactiver
        edit: Éditer
        export_csv: 'Exporter en CSV'
        id: Id
        piggy: 'Log as'
        piggy_exit: 'Logout as'
        view: Visualiser
    company:
        all_term_payment: Tous
        all_term_payment_title: 'Toutes les Sociétés avec des paiments à terme (validé ou en attente)'
        companyInfo:
            activation:
                ko: 'Erreur lors de l''activation de la société'
                ok: 'La société a été activée avec succès'
            address: Adresse
            addressComplement: Complément
            billing:
                service: Service
                title: Facturation
            city: Ville
            contact:
                adv: 'Contact administration des ventes'
                billing: 'Adresse facturation'
                firstname: Prénom
                lastname: Nom
                logistic: 'Contact logistique'
                mail: Email
                main: 'Contact principal'
                noInformation: 'Pas d''information'
                phone1: 'Téléphone principal'
                phone2: 'Téléphone secondaire'
            country: Pays
            deactivate:
                content: 'Êtes-vous sûr de vouloir désactiver cette société ?'
                title: 'Désactivation de la société'
            deactivation:
                ko: 'Erreur lors de la désactivation de la société'
                ok: 'La société a été désactivée avec succès'
            deactivationReason: 'Raison de la désactivation'
            document:
                title: Document
            edit: Modifier
            endpointUrl: ''
            info:
                code: 'Code société'
                name: 'Nom société'
                title: 'Informations société'
            invalidate:
                ko: 'Erreur lors de la dévalidation de la société'
                ok: 'La société a été dévalidé avec succès'
            region: Région
            reject:
                ko: 'Erreur lors du rejet de la société'
                ok: 'La société a été rejetée avec succès'
            rejectedReason: 'Raison du rejet'
            termpayment:
                active: 'Activer le paiement à terme'
                askKo: 'Une erreur s''est produite durant la demande de paiement à termme'
                askOk: 'La demande de paiement à terme a été effectuée avec succès.'
                authorization: 'Autorisation de paiement à terme'
                deny: 'Rejeter le paiement à terme'
                disabled: 'Le paiement à terme est désactivé.'
                enabled: 'La demande a été acceptée le'
                inactive: 'Désactiver le paiement à terme'
                pending: 'La demande a été effectuée le'
                reason: 'Raison du refus'
                removeKo: 'Une erreur s''est produite durant la suppression du paiement à terme'
                removeOk: 'La suppression du paiement à terme a été effectuée avec succès'
                title: 'Date de demande du paiement à terme'
            validate:
                ko: 'Erreur lors de la validation de la société'
                ok: 'La société a été validée avec succès'
            zipCode: 'Code postal'
        endpointUrl: 'URL du client'
        export: 'Export en CSV'
        filter_clear: EFFACER
        filter_title: Filtres
        list:
            add:
                site: 'Ajouter un centre de coût'
                user: 'Ajouter un utilisateur'
            category: Catégorie
            city: Ville
            costCenters: 'Centres de coûts'
            country: Pays
            created: 'Création de compte'
            empty: 'Aucune société en attente de validation'
            filter:
                activeBuyer: 'Acheteurs actifs'
                all: 'Toutes les sociétés'
                caMax: 'et :'
                caMin: 'CA entre :'
                creationMax: 'et :'
                creationMin: 'Date de création entre :'
                disabled: Désactivées
                purchaseMax: 'et :'
                purchaseMin: 'Date de commande entre :'
                submit: FILTRER
                term_payment_date_max: 'et :'
                term_payment_date_min: 'Date de demande/acceptation entre :'
                toConfirm: 'A valider'
                tonMax: 'et :'
                tonMin: 'Tonnage entre :'
            identification: Code
            last: 'Dernière connexion'
            name: Société
            purchases: 'Achats (USD)'
            revenue: 'CA généré'
            status: Statut
            status_company:
                acceptable: 'En attente de validation'
                all: Toutes
                disabled: Désactivée
                draft: Brouillon
                initial: Initial
                pending: 'En attente de validation'
                rejected: Rejetée
                valid: Active
            termpayment_moneytransfert_accept: 'Valider les paiements à terme'
            termpayment_moneytransfert_date_accepted: Accepté
            termpayment_moneytransfert_date_requested: Demandé
            termpayment_moneytransfert_deny: 'Refuser les paiements à terme'
            termpayment_moneytransfert_enabled: Actif
            termpayment_moneytransfert_pending: 'En attente'
            tonnes: 'Tonnes au compteur'
            type_:
                1: Acheteur
                2: Vendeur
                all: Tous
                buyer: Acheteur
            user_type: 'Type d''utilisateur'
            users: Utilisateurs
        menu:
            general: Général
            messages: Messages
            orders: Commandes
            sites: 'Centre de coûts'
            users: Utilisateurs
        order:
            creation_date: 'Date de la commande'
            export: 'Export PDF'
            number: 'Numéro de commande'
            price: Prix
            shipping: 'Adresse de livraison'
            status:
                cancelled: 'Cancelled Orders'
                past: 'Past Orders'
                running: 'Running Orders'
                title: status
        pending_term_payment: 'En attente'
        pending_term_payment_title: 'Toutes les sociétés en attente de paiement à terme'
        term_payment_empty: 'Aucune société en attente de validation de paiement à terme'
        termpayment_moneytransfert_accepted: 'Vous avez accepté les paiements à terme pour cette Société'
        termpayment_moneytransfert_accepted_error: 'Erreur lors dans le traitement de l''acceptation des paiements à terme pour cette Société'
        termpayment_moneytransfert_rejected: 'Vous avez refusé les paiements à terme pour cette Société'
        termpayment_moneytransfert_rejected_error: 'Erreur lors du refus des paiements à terme pour cette Société'
        users:
            create_ticket: 'Créer un ticket'
            term_payment_empty: 'Aucune entreprise en attente de validation des paiements à terme'
    deactivate:
        ok: Ok
        reason: 'Raison ?'
    index_page:
        companies_term_payment_request_to_validate: 'Société en attente de validation de paiement à terme'
        companies_to_validate: 'Société en attente de validation'
        see_all: 'Voir tout'
        unread_messages: 'Messages non lus'
    logo:
        commons:
            add: Ajouter
            addTitle: 'Ajouter un nouveau logo'
            backgroundImage: 'Image de fond'
            create: Créer
            editTitle: 'Modifier un logo'
            link: Lien
            lock: verrouilé
            order: Ordre
            published: publié
            setTitleVisible: 'Make the title visible on the homepage'
            status: Statut
            title: Titre
            update: Modifier
        create:
            confirm: 'L''élément du logo a été ajouté avec succès.'
            error: 'Une erreur s''est produite durant la création du nouvel élément.'
        delete:
            confirm: 'L''élément du logo a été supprimé avec succès.'
            error: 'Une erreur s''est produite durant la suppression de l''élément.'
        edit:
            actualImage: 'Image actuelle'
            imageP: 'Sélectionnez une nouvelle image si vous souhaitez la modifier.'
        list:
            actions: Actions
            createdAt: 'Créé le'
            header: 'Liste des éléments du slider'
            lang: Langues
            updatedAt: 'Modifié le'
        status:
            draft: Brouillon
            published: Publié
        update:
            confirm: 'L''élément du slider a été modifié avec succès.'
            error: 'Une erreur s''est produite durant la modification de l''élément.'
    merchant:
        form:
            update:
                error: 'Error while updating the vendor'
                success: 'Vendor has been successfully updated'
        list:
            email: Email
            firstname: 'First Name'
            identification: Identification
            lastname: 'Last Name'
            list: Email
            name: Name
            status:
                accepted: Accepted
                all: All
                pending: Pending
                rejected: Rejected
                title: Status
        merchantInfo:
            country: Country
            currency:
                eur: EUR
                placeholder: 'Choose a currency'
                title: Devise
                usd: USD
            edit: Edit
            email: Email
            firstname: Firstname
            identification: Identification
            info:
                title: 'Vendor Information'
            lastModifiedAt: 'Last modified at'
            lastModifiedBy: 'Last modified by'
            lastname: Lastname
            name: Name
            password: Password
            phoneNumber: 'Phone number'
            registrationDate: 'Registration date'
            reject:
                ko: 'An error occurred while rejecting the vendor'
                ok: 'Vendor has been rejected with success'
            rejectedReason: 'Rejected reason'
            status: Status
            validate:
                ko: 'An error occurred while validating the vendor'
                ok: 'Vendor has been successfully updated'
    notification:
        edit:
            body: Corps
            confirm: 'La notification a été mis à jour avec succès'
            empty_email_error: 'Please, enter a valid email address'
            externalLinkType: 'Use an external URL'
            link: 'Lien du bouton'
            linkExternal: 'External URL'
            linkText: 'Texte du bouton'
            send_button: 'Update and send test email'
            slug: Identifiant
            test: 'Test email address'
            title: Titre
        list:
            creation: 'Créé le'
            disable: 'Activé : cliquez pour désactiver'
            enable: 'Désactivé : cliquez pour activer'
            header: 'Templates de mail'
            lang: Langues
            slug: Identifiant
            title: Sujet
            update: 'Mis à jour le'
    page:
        add: 'Créer une page statique'
        draft: Brouillon
        list:
            author: Auteur
            creation: 'Créé le'
            header: 'Pages statiques'
            lang: Langues
            modal:
                title: 'Choisissez la langue de visualisation'
            slug: 'Permalien (Slug)'
            status: Statut
            title: Titre
            update: 'Mis à jour le'
        modal:
            cancel_confirm: 'Êtes-vous sûr de vouloir annuler la création de ce contenu ?'
        published: Publiée
    payloads:
        detail:
            title: 'Payload : %id%'
        list:
            company_name: 'Nom de la société'
            creation: Date
            header: 'Payloads list'
            identifier: Identifier
            payload_id: 'Payload id'
            status: Status
            type: Type
        statuses:
            status_created: CREATED
            status_failed: FAILED
            status_success: SUCCESS
        types:
            cost_center_payload: 'COST CENTER'
            invoice_payload: INVOICE
            order_payload: ORDER
            purchase_request_item_payload: 'PURCHASE REQUEST'
    redirect:
        add: 'Créer une redirection'
        list:
            confirm_delete: 'Voulez vous vraiment supprimer cette redirection ?'
            confirm_delete_title: Confirmation
            creation: 'Créé le'
            destination: Destination
            header: Redirections
            origin: Origine
            type: Type
            update: 'Mis à jour le'
    search_historization:
        company_name: 'Company name'
        date: Date
        datemax: ' à '
        datemin: 'Date de recherche du'
        filter_label: Filtre
        id: Id
        is_anonymous: Anomyme
        list:
            filter:
                date: Date
        nb_hits: 'Nombre de résultat'
        offer_name: 'Product title'
        offer_sku: SKU
        searched_term: 'Terme recherché'
        user_full_name: 'User full name'
    shipping_points:
        add: 'Ajouter un point de livraison'
        delete: 'Supprimer le point de livraison'
        delete_card: Supprimer
        edit: 'Modifier le point de livraison'
        edit_card: Modifier
        error_add: 'Une erreur a eu lieu durant la création du point de livraison'
        error_edit: 'Une erreur a eu lieu durant la modification du point de livraison'
        form:
            name: 'Nom du point de livraison'
            save: Enregistrer
        shipping_points: 'Points de livraison'
        success_add: 'Le point de livraison a été créé avec succès'
        success_delete: 'Le point de livraison a été supprimé avec succès'
        success_edit: 'Le point de livraison a été modifié avec succès'
    site:
        activation:
            ko: 'Erreur lors de l''activation du centre de coût'
            ok: 'Le centre de coût a été activé avec succès'
        add: 'Ajouter un centre de coût'
        chargement:
            title: 'Sites de chargement'
        deactivate:
            content: 'Êtes-vous sûr de vouloir désactiver ce site ?'
            title: 'Désactivation du site'
        deactivation:
            ko: 'Erreur lors de la désactivation du centre de coût'
            ok: 'Le centre de coût a été désactivé avec succès'
        delete: Supprimer
        filter_clear: EFFACER
        filter_title: Filtres
        form:
            address: Adresse
            address2: 'Adresse complément'
            city: Ville
            company: Société
            contact:
                email: Email
                firstname: Prénom
                function: Fonction
                lastname: Nom
                main: 'Contact principal'
                other: Contact
                phone1: 'Téléphone principal'
                phone2: 'Téléphone secondaire'
            country: Pays
            document:
                legalesDoc: 'Autorisations préfectorales'
                title: Documents
            id: ID
            info: 'Informations générales'
            name: 'Nom du centre de coût'
            operatorInfos:
                canPack: 'Possibilité de charger pour l''export'
                chargeWay: 'Moyen de chargement'
                device: Device
            region: Région
            siret: SIRET
            zipCode: 'Code postal'
        infos: 'Informations centre de coût'
        invalidate:
            ko: 'Erreur lors de la dévalidation du centre de coût'
            ok: 'Le centre de coût a été dévalidé avec succès'
        list:
            add: 'Ajouter un nouveau site'
            address: Adresse
            city: Ville
            company: Société
            contactName: Contact
            contactPhone: Téléphone
            country: Pays
            editName: 'Modifier'
            filter:
                all: 'Tous les centres de coûts'
                disabled: Désactivées
                submit: FILTRER
                toConfirmed: 'A valider'
            name: 'Nom du centre de coût'
            nbShippingPoints: 'Nb points de livraison'
            status: Statut
            status_site:
                disabled: Désactivé
                enabled: Activé
            zipCode: 'Code postal'
        livraison:
            title: 'Centres de coût'
        modification:
            edit_name: 'Modifier le centre de coût'
            ko: 'Erreur lors de la modification du centre de coût'
            ok: 'Le centre de coût à été mis à jour avec succès'
        suppression:
            ok: 'La suppression du centre de coût a été effectuée.'
        validate:
            error: 'Vous devez valider la société avant de valider ce centre de coût'
            ko: 'Erreur lors de la validation du centre de coût'
            ok: 'Le centre de coût a été validé avec succès'
    slider:
        commons:
            add: Ajouter
            addTitle: 'Ajouter un nouvel élément au slider'
            backgroundImage: 'Image de fond'
            create: Créer
            editTitle: 'Modifier un élément du slider'
            link: Lien
            lock: verrouilé
            order: Ordre
            published: publié
            setTitleVisible: 'Make the title visible on the homepage'
            status: Statut
            title: Titre
            update: Modifier
        create:
            confirm: 'L''élément du slider a été ajouté avec succès.'
            error: 'Une erreur s''est produite durant la création du nouvel élément.'
        delete:
            confirm: 'L''élément du slider a été supprimé avec succès.'
            error: 'Une erreur s''est produite durant la suppression de l''élément.'
        edit:
            actualImage: 'Image actuelle'
            imageP: 'Sélectionnez une nouvelle image si vous souhaitez la modifier.'
        list:
            actions: Actions
            createdAt: 'Créé le'
            header: 'Liste des éléments du slider'
            lang: Langues
            updatedAt: 'Modifié le'
        status:
            draft: Brouillon
            published: Publié
        update:
            confirm: 'L''élément du slider a été modifié avec succès.'
            error: 'Une erreur s''est produite durant la modification de l''élément.'
    ticket:
        filter:
            closed: Fermé
            company: Société
            creationMax: et
            creationMin: 'Date de création entre'
            export_csv: Export
            main_contact: 'Créateur du message'
            modificationMax: et
            modificationMin: 'Date de modification entre'
            object: Objet
            opened: Ouvert
            submit: Envoyer
            title: Filtre
    user:
        change_type:
            content: 'Are you sure you want to change this buyer account type ?'
            title: 'Change buyer account type'
        connection:
            browser: Navigateur
            date: Date
            hour: heure
            ip: Ip
            os: OS
            title: 'Historique des connexions'
            type: Type
            version: Version
        deactivate:
            content: 'Êtes-vous sûr de vouloir désactiver cet utilisateur ?'
            title: 'Désactivation de cet utilisateur'
        filter:
            activeBtn: 'Afficher tous les utilisateurs'
            all: 'Tous les utilisateurs'
            connectionMax: 'et :'
            connectionMin: 'Dernière connexion entre :'
            creationMax: 'et :'
            creationMin: 'Date de création entre :'
            disabled: 'Utilisateurs désactivés'
            enabled: 'Utilisateurs actifs'
            filter_clear: Effacer
            filter_title: Filtrer
            inactiveBtn: 'Afficher les utilisateurs inactifs'
            toConfirmed: 'Utilisateurs à valider'
        form:
            account: Compte
            activate: Activer
            activation:
                ko: 'Erreur lors de l''activation de l''utilisateur'
                ok: 'L''utilisateur a été activé avec succès'
            company: Société
            confirmPassword: 'Confirmation nouveau mot de passe'
            creation:
                ko: 'Erreur lors de la création de l''utilisateur'
                mailKo: 'Cette adresse mail est déjà utilisée'
                mailKoDisabled: 'Cette adresse mail est déjà utilisée pour une utilisateur désactivé, vous pouvez le ré-activer.'
                ok: 'L''utilisateur à été créé avec succès'
            deactivate: Désactiver
            deactivation:
                ko: 'Erreur lors de la désactivation de l''utilisateur'
                ok: 'L''utilisateur a été désactivé avec succès'
            edit: Modifier
            email: Email
            firstname: Prénom
            id: ID
            invalidate: Dévalider
            language: Langue
            lastname: Nom
            modification:
                ko: 'Erreur lors de la modification de l''utilisateur'
                ok: 'L''utilisateur à été mis à jour avec succès'
            password: 'Nouveau mot de passe'
            phone1: 'Téléphone principal'
            phone2: 'Téléphone secondaire'
            reject: Rejeter
            resetPassword: 'Reset mot de passe'
            resetingPassword:
                ko: 'Erreur lors de l''envoi de la demande de reset de mot de passe'
                ok: 'Votre demande de reset de mot de passe a été envoyé'
            role: Rôle
            sites: 'Centre de coûts'
            sitesUserKo: 'Un utilisateur doit être ajouté à au moins un centre de coût.'
            status: Statut
            validate: Valider
        function: Fonction
        history:
            class:
                AppBundle\Entity\Address: Adresse
                AppBundle\Entity\Company: Société
                AppBundle\Entity\ComplementaryInformation: 'Information complémentaire'
                AppBundle\Entity\Contact: 'Information de contact'
                AppBundle\Entity\Country: Pays
                AppBundle\Entity\Document: Document
                AppBundle\Entity\NodeContent\mail: 'Contenu template'
                AppBundle\Entity\NodeContent\page: 'Contenu page'
                AppBundle\Entity\Node\mail: 'Template de mail'
                AppBundle\Entity\Node\page: 'Page statique'
                AppBundle\Entity\Redirect: 'Règle de redirection'
                AppBundle\Entity\Region: Région
                AppBundle\Entity\Setting: Configuration
                AppBundle\Entity\Site: Site
                AppBundle\Entity\User: Utilisateur
                AppBundle\Entity\ZipCode: 'Zip Code'
                Open\TicketBundle\Entity\Ticket: Ticket
                Open\TicketBundle\Entity\TicketMessage: 'Message ticket'
            date: Date
            hour: heure
            id: Identifiant
            modifications: Modifications
            new: nouveau
            objet: 'Objet modifié'
            old: ancien
            operation: Opération
            title: 'Historique des actions'
            type:
                create: Création
                delete: Suppression
                update: 'Mise à jour'
        list:
            activeStatus: Actif
            allStatus: Tous
            company: Société
            creation: 'Création Compte'
            disable: Inactif
            disabled: Désactivation
            email: Email
            enable: Actif
            firstname: Prénom
            inactiveStatus: Inactif
            lastLogin: 'Dernière connexion'
            lastname: Nom
            phone: Téléphone
            role: Rôle
            sites: 'Centre de coût'
            status: Statut
        role:
            all: Tous
            main:
                ROLE_API: ''
                ROLE_BUYER_ADMIN: 'Responsable de compte'
                ROLE_BUYER_BUYER: Demandeur
                ROLE_BUYER_PAYER: Acheteur
                ROLE_OPERATOR: Opérateur
                ROLE_SUPER_ADMIN: Administrateur
            secondary:
                ROLE_API: ''
                ROLE_BUYER_ADMIN: 'Responsable de compte'
                ROLE_BUYER_BUYER: Demandeur
                ROLE_BUYER_PAYER: Acheteur
                ROLE_OPERATOR: Opérateur
                ROLE_SUPER_ADMIN: Administrateur
buyerMenu:
    complete_profile: 'Complétez les informations de votre profil'
    complete_profile_why: 'Vous devez compléter les informations ci-dessous afin de pouvoir commencer à acheter des produits.'
cart:
    accept:
        error: 'Une erreur s''est produite durant la validation de l''assignation.'
    add:
        error: 'Une erreur s''est produite lors de l''ajout du produit au panier'
        success: 'Votre produit a été ajouté avec succès au panier'
        successUpdate: 'Votre produit a été mis à jour'
    article: article
    articles: articles
    assign:
        assign_to: UTILISATEUR
        assign_to_myself: 'M''assigner le panier'
        comment: COMMENTAIRE
        error: 'Une erreur s''est produite durant l''assignation.'
        modal_btn_cancel: Annuler
        modal_btn_confirm: Assigner
        modal_title: 'Assigner mon panier'
        noUser: 'Aucun utilisateur à qui assigner le panier'
        success: 'Le panier a été assigné avec succès.'
    assignment_history: 'Historique des affectations'
    buttons:
        assign: Assigner
        back_to_cart: 'Retour au panier'
        checkout: 'Passer la commande'
        clear_cart: 'Vider le panier'
        continue_shopping: 'Continuer mes achats'
        save_in_wishlist: 'Enregistrer pour plus tard'
        see_assignment: 'Voir l''historique des affectations'
        validate: Valider
    checkout:
        accounting_email: 'E-mail comptable'
        add_new_address: 'Ajouter une nouvelle adresse'
        add_new_cost_center: 'Ajouter un nouveau centre de coût'
        address: Adresse
        address_complement: 'Complément d''adresse'
        area: Région
        assign:
            error: 'Il manque des informations pour assigner votre panier (centre de coût, adresse de livraison, adresse de facturation, email…)'
        billing_address: 'Adresse de facturation'
        city: Ville
        cost_center: 'Centre de coût'
        minimum_order_amount: 'Une ou plusieurs sous-commandes n''atteignent pas le montant minimum de commande demandé par le marchand. Veuillez modifier votre panier.'
        notReady: 'Cette fonctionnalité n''est pas encore disponible'
        payment_mode: 'Mode de paiement'
        select:
            address_placeholder: 'Choisissez une adresse'
            cost_center_placeholder: 'Choisissez un centre de coût'
            error: 'Veuillez choisir un centre de coût, une adresse de livraison et un mode de paiement'
            error_billing: 'Il manque des informations pour valider votre panier (centre de coût, adresse de livraison, adresse de facturation, email…)'
            payment_mode_placeholder: 'Choisissez un moyen de paiement'
        title: 'Passer la commande'
        validation_number: 'Numéro de commande Acheteur'
        zipcode: 'Code postal'
        term_and_conditions_must_accept: 'Toutes les conditions générales doivent être acceptées pour valider votre commande'
        buyer_need_to_accept_term_and_conditions: "L'acheteur doit accepter les conditions générales de vente du vendeur pour valider la commande"
    cost_center:
        comments: Commentaires
        contact:
            email: Email
            name: Nom
            title: 'Réception - Informations générales partagées avec le vendeur'
        error: 'Une erreur est survenue lors du rafraichissement du centre de coût'
        noSiteId: 'Veuillez sélectionner un centre de coût et une adresse'
        packaging_request:
            label: 'Instructions d''emballage'
            none: 'Aucune instruction d''emballage. Vous pouvez paramétrer vos instructions d''emballage depuis votre page Centre de coût'
        requested_documents:
            label: 'Sélectionnez les documents requis pour la livraison'
            none: 'Pas de documents requis. Vous pouvez les paramétrer depuis votre page Centre de coûts'
    days: jours
    detail: 'Détail du panier'
    empty:
        back: 'Retour sur la page de recherche'
        no_user_title: 'Centre de cout sans utilisateur'
        text: 'Votre panier d''achat est vide.</br>Continuez vos achats sur la page de recherche.'
        title: 'Votre panier est vide'
        user_list: 'Pas d''utilisateur disponible pour ce centre de coût'
    fca_info: 'Ce produit sera mis à disposition à l''adresse suivante:'
    fca_shipping: ' + Livraison'
    fca_warning: 'Pour les produits FCA, voir l''adresse de livraison du vendeur.'
    historic:
        comment: Commentaire
        date_assign: 'Date d''assignation'
        user_assigned: 'Utilisateur cible'
        user_who: 'Utilisateur source'
    lostItem:
        multiple_removed: '%number% offres ne sont plus disponible et on été enlevées de votre panier'
        single_removed: 'Une offre n''est plus disponible et a été enlevée de votre panier'
    notification_message:
        offer_price_changed: 'Prix ??de l''offre modifié'
    pending:
        ability_to_assign: 'capacité d''assigner'
        ability_to_pay: 'capacité de payer'
        amount_excl_taxes: 'Montant hors taxes'
        cart_reference: 'Référence panier'
        creation_date: 'Date de création'
        last_comment: 'Dernier commentaire'
        me: Moi
        now_assigned_to: 'Actuellement assigné à'
        number_of_products: 'Nombre de produits'
        number_of_sellers: 'Nombre de vendeurs'
        previous_assignments: 'Assignations antérieures'
        rejected: rejeté
    quotation:
        error: 'Une erreur s''est produite lors du calcul du devis'
    reject:
        comment: 'Raison du refus'
        error: 'Une erreur s''est produite durant le rejet de l''assignation.'
        success: 'Le panier a été refusé avec succès'
        title: Rejeter
    remove:
        error: 'Une erreur s''est produite durant la suppression du produit de votre panier.'
    select:
        placeholder: 'Veuillez choisir un centre de coût'
    step:
        step_1: 'Sélection des produits'
        step_2: Livraison
        step_3: Confirmation
    table_label:
        buyer_internal_reference: 'Référence acheteur'
        cart_item_comment: 'Commentaire'
        delivery_time: 'Délai de livraison'
        expected_date: 'Date de livraison prévue'
        no_vat: 'Pas de TVA pour cette transaction'
        product_detail: 'Détails du produit'
        product_name: 'Nom du produit'
        purchase_request_id: 'ID de la demande d''achat'
        quantity: Quantité
        shippingtotal: 'Total livraison (HT)'
        subtotal: 'Sous-total (hors TVA)'
        subtotal_vat: Sous-total
        taxes: TVA
        total: 'Total (hors TVA)'
        total_cart: 'Total cart (excl tax)'
        total_order: 'Commande totale'
        total_price: 'Prix total'
        total_vat: Total
        unit_no_price: 'Pas de prix'
        unit_price: 'Prix unitaire'
    update_quantity:
        error: 'Une erreur s''est produite durant la modification de la quantité.'
    warning: Attention
    wrong_price_message: 'Le prix d''un ou plusieurs articles a changé. Veuillez réattribuer le panier pour le mettre à jour.'
category:
    all_categories_placeholder: 'Toutes les catégories'
    category_bafv: 'Client bafv'
    category_new: 'Nouveau client'
    category_normal: 'Client normal'
    category_premium: 'Client premium'
    category_undefined: 'A définir'
cgu:
    accept: 'Accepter les conditions générales d''utilisation'
    error: 'Erreur lors de l''acceptation des conditions générales d''utilisation'
    errorMessage: 'Veuillez valider les CGU avant de soumettre votre société'
    read: 'Lire et accepter les conditions générales d''utilisation'
    validated: 'Vous avez accepté les <a href="%link%" target="_blank">CGU</a>.'
company:
    form:
        address: 'Adresse de la société'
        back: Précedent
        billingService: 'Nom du département'
        billing_address:
            address: 'Adresse de facturation'
            title: 'Service facturation'
            use_main: 'Utiliser l''adresse du siège social'
        businessRegistration: 'Identification de l''entreprise'
        cgu: 'Conditions générales d''utilisation'
        fullAuto: 'API order full auto'
        eCatalog: 'E-catalog'
        check: 'Adresse de facturation différente de l''adresse principale ?'
        company_info: 'Informations de la société'
        contact:
            add: 'Ajouter un contact'
            adv: 'Contact administration des ventes'
            billing: 'Contact facturation'
            check:
                adv: 'Cochez la case si vous souhaitez saisir des coordonnées différentes pour l''ADV'
                billing: 'Cochez la case si vous souhaitez saisir des coordonnées différentes pour la facturation'
                logistic: 'Cochez la case si vous souhaitez saisir des coordonnées différentes pour la logistique'
            logistic: 'Contact logistique'
            logistic_subtitle: 'Vous pourrez ajouter des contacts opérationnels pour chaque site'
            main: 'Contact principal (commercial)'
        contract: 'Renvoyer le contrat signé'
        document:
            title: 'Ajoutez vos documents'
            titleReadOnly: 'Vos documents'
        edit-site: 'Modifier le centre de coût'
        endpointUrl: ''
        finish: Terminer
        iban: 'IBAN personne morale'
        ident_number:
            invalid: Invalide
        identification: 'Numéro d''identification'
        identity: 'Pièce d''identité du signataire'
        info:
            cgu_not_accepted: 'Vous devez accepter les CGU'
            company_registration: 'Votre compte utilisateur a été créé, veuillez remplir le formulaire ci-dessous pour soumettre les informations de votre entreprise et finaliser l’enregistrement.'
            incomplete: 'Les information de la Société sont incomplètes'
            operator_needed: 'Certaines informations de votre société ne peuvent plus être modifiées directement. Si vous souhaitez apporter des modifications, veuillez contacter StationOne via le formulaire de <a href="%url%">contact</a>.'
        legal_documents: 'Documents légaux de la société'
        main_contact:
            title: Adresse
        middleware: ''
        name: 'Raison sociale'
        next: Suivant
        password:
            change: 'Modifier mon mot de passe'
        profile: 'Mon profil'
        save: Sauvegarder
        service: Service
        siren: SIREN
        siren_placeholder: '(9 chiffres)'
        social: Société
        submit: Soumettre
        submit_infos: 'Soumettre ma société'
        tax_rate: 'Taux de TVA'
        title: 'Données administratives de la société'
        type: Type
        update:
            error: 'Erreur lors de la modification de la société'
            success: 'La société à été mise à jour avec succès'
company_catalog:
    add_in_catalog: 'Ajouter ma référence'
    buyer_reference: 'Réference dans votre catalogue'
    cancel: Annuler
    custom_search:
        title: 'Rechercher dans mon catalogue'
    delete: 'Supprimer mon catalogue'
    delete_confirm: 'Êtes-vous sûr de vouloir supprimer votre catalogue ?'
    delete_confirmed: 'Catalogue supprimé'
    delete_error: 'Seuls les responsables de compte peuvent supprimer le fichier'
    delete_reference: Supprimer
    export: 'Exporter mon catalogue'
    export_matching: 'Exporter les références valides'
    export_mismatching: 'Exporter les références invalides'
    import:
        total_matching_references: 'Total des références valides du catalogue:'
        total_mismatching_references: 'Total des références non valides du catalogue:'
    import_in_progress: 'l''import de catalogue est en cours d''éxécution'
    imported_references: 'références importées'
    instruction_title: 'Consignes :'
    instructions: 'Cette fonctionnalité vous permet de charger vos propres références produits sur la Marketplace. Cela vous donne la possibilité de rechercher et de voir des produits sous votre référence, dans le menu "Commandes" et sur la plateforme en général (seuls les utilisateurs de votre société peuvent voir vos référence personnalisées).</br></br>2 options pour ajouter vos propres références :</br>- Sur la page de détails d''un produit, cliquez sur "Ajouter ma référence" pour lui associer une référence personnalisée</br>- Depuis le menu "Mon catalogue", importer vos références personnalisées en utilisant le modèle .csv disponible <a href="%link%" target="_blank">ici</a>.</br>Entrez la référence fabricant dans la première colonne et votre référence dans la seconde. Une fois terminé, chargez votre catalogue en cliquant sur le bouton "Charger mon catalogue"</br></br>Une fois votre fichier chargé, les produits peuvent être recherchés sous votre référence personnalisée.</br></br>Le catalogue peut être téléchargé à tout moment en cliquant sur le bouton "Exporter mon catalogue".</br></br>Le bouton "Exporter les références invalides" vous permet de vérifier les références ajoutées à "mon catalogue" qui ne sont pas disponibles sur StationOne Marketplace.'
    overwrite: 'Attention, envoyer un nouveau catalogue effacera l''ancien. Souhaitez-vous continuer ?'
    save: Sauvegarder
    title: Catalogue
    upload: 'Charger mon catalogue'
    upload_success: '%count% référence(s) ajoutée(s).'
    wrong_file_format: 'Format de fichier invalide.'
comparaisonSheet:
    add:
        error: 'Erreur lors de l''ajout du produit au comparateur'
        itemAlreadyexist: 'L''article est déjà dans le comparateur'
        maxItemError: 'Vous avez déjà le nombre maximal de produits dans le comparateur : %maxItem%'
        success: 'Produit ajouté au comparateur'
    back_to_home: 'Retour à la page d''accueil'
    comment: 'Ajoutez le commentaire que vous voulez voir apparaitre sur le pdf qui sera généré :'
    export: Exporter
    information: 'Cette comparaison n''est valable qu''au moment de la génération de ce pdf. StationOne ne s''engagea pas à la tenue de ces prix dans le temps.'
    no_article: 'Votre comparateur est vide'
    page:
        author: Auteur
        comment: Commentaire
        company_name: 'Nom de la société'
        date: 'Date de la fiche de comparaison'
        delivery: Livraison
        discount: Remise
        no_discount: 'pas de remise'
        no_price: 'Prix sur demande'
        price_excl_tax: 'Prix HT'
        product: Produit
        title: 'Fiche de comparaison de prix'
        unit_price: 'Prix unitaire'
        unit_price_vat: 'Prix unitaire converti'
    sticky: Comparateur
contact:
    adv: 'Contact de l''administration des ventes'
    form:
        email: Email
        firstname: Prénom
        function: Fonction
        lastname: Nom
        phone1: 'Tél principal'
        phone1_placeholder: 'Ce téléphone est requis'
        phone2: 'Tél secondaire'
    logistic: 'Contact logistique'
contactMerchant:
    add_file: 'Ajouter un fichier'
    attachment_limit: '%limit% pièces jointes maximum'
    authorized_types: '(Fichiers autorisés : pdf, jpeg, gif, png, tiff)'
    file_too_big: 'la taille maximum d''une pièce jointe est de %size-limit%'
    form:
        error: 'Erreur lors de l''envoi de votre message'
        message: Message
        object: Objet
        save: Envoyer
        success: 'Votre message a été envoyé'
    message:
        object:
            feedback: 'Commentaires de %buyer%'
            quotation: 'Demande commerciale de %buyer%'
            technical: 'Demande technique de %buyer%'
contactWAYLF:
    add_file: 'Ajouter un fichier'
    attachment_limit: 'maximum %limit% attachments'
    authorized_types: '(Fichiers autorisés : pdf, jpeg, gif, png, tiff)'
    company_name: 'Nom de l''entreprise'
    email: Email
    file_too_big: 'La taille maximale d''une pièce jointe est % size-limit%'
    first_name: Prénom
    last_name: Nom
    message: Message
cookie:
    accept: 'j''accepte'
    message: 'En cliquant sur j''accepte, vous en acceptez les conditions générales d''utilisation, et notamment l''utilisation des cookies afin de réaliser des statistiques d''audiences.'
cost_center:
    name:
        first: 'Centre de coût principal'
country:
    australia: Australie
    austria: Autriche
    azerbaijan: Azerbaïjan
    bahrain: Bahreïn
    belgium: Belgique
    brasil: Brésil
    bulgaria: Bulgarie
    canada: Canada
    canary_islands: 'Îles Canaries'
    china: Chine
    croatia: Croatie
    cyprus: Chypre
    czech_republic: 'Republique Tchèque'
    denmark: Danemark
    estonia: Estonie
    finland: Finlande
    france: France
    germany: Allemagne
    greece: Grèce
    hongkong: 'Hong Kong'
    hungary: Hongrie
    ident:
        australia: 'A.C.N. - Autralian Company Number'
        austria: 'Numero de TVA intracommunautaire'
        azerbaijan: 'Numéro d’identification fiscal'
        bahrain: 'Numéro d''identification de l''entreprise'
        belgium: 'TVABE (N° TVA Belgique)'
        brasil: 'CNPJ Number (N° de TVA)'
        bulgaria: 'reg number Bulgaria'
        canada: DUNS
        canary_islands: 'Numéro CIF'
        china: 'RNCN (reg number China)'
        croatia: 'Numéro d''identification de l''entreprise'
        cyprus: 'RNCY (reg number Cyprus)'
        czech_republic: ICO
        denmark: 'CVR Danemark- TVA DK'
        estonia: 'Trade Register - Estonia'
        finland: 'TVAFI (N° de TVA Finlande)'
        france: 'N° TVA intra'
        germany: 'Numero de TVA intracommunautaire'
        greece: 'VATGR (VAT for Greece)'
        hongkong: 'RNHK (reg number Hong Kong)'
        hungary: 'RNHU (reg number Hungary) ou VATHU'
        india: 'CRO Number for EBSG or VAT number India'
        ireland: 'Ireland company number (CNIE)'
        italy: 'COFIS ou Partita IVA'
        latvia: 'Trade Register - Latvia'
        lithuania: 'Trade Register - Lithuania'
        luxembourg: 'IBLC (Luxembourg TVA)'
        malta: 'reg number Malta'
        mexico: 'RFC (Mexique)'
        morocco: 'RNMA (reg number Morocco)'
        netherlands: 'Chambre de comm.. Pays Bas KVK nr'
        norway: 'RNNO (company registration number- RN Norvège)'
        peru: 'Registre Unique des Contribuables'
        poland: 'TVAPL (NIP TVA Pologne)'
        portugal: 'NIPC (reg number Portugal)'
        qatar: 'Numéro d''identification de l''entreprise'
        romania: 'Numéro de TVA intra-communautaire'
        russia: 'OGRN (ident RU)'
        saudi_arabia: 'Numéro d''identification de l''entreprise'
        senegal: 'Numéro d''identification de l''entreprise'
        singapore: 'CRO number or CRO Number for EBSG'
        slovak_republic: 'reg number Slovak Republic'
        slovenia: 'reg number Slovenia'
        spain: 'CIF Espagne - TVA'
        sweden: 'RNSE (RN Suède)'
        switzerland: 'Numéro de TVA Intracommunautaire'
        taiwan: 'RNTW (company registration number- reg number Taiwan)'
        thailand: 'CRO ou ROC number'
        tunisia: 'RNTN (RN Tunisie)'
        turkey: 'TAXTR (tax identifier for Turkey)'
        united_arab_emirates: 'Numéro d''identification de l''entreprise'
        united_kingdom: 'CRO (company registration office number)'
        united_states: 'DUNS Number'
    ident_helper:
        australia: '9 chiffres'
        austria: 'ATU + 8 chiffres'
        azerbaijan: '10 chiffres correspondant à votre code TIN (Tax Identification Number)'
        bahrain: '15 chiffres'
        belgium: '10 chiffres'
        brasil: '14 chiffres'
        bulgaria: '9-10 chiffres'
        canada: '9 chiffres'
        canary_islands: 'A + 8 numéros'
        china: '18 chiffres/lettres maximum'
        croatia: 'HR + 11 chiffres'
        cyprus: '5 à 6 chiffres/lettres'
        czech_republic: '8 chiffres'
        denmark: '8 chiffres'
        estonia: '8 chiffres'
        finland: '8 chiffres'
        france: FR+XX+N°SIREN
        germany: 'DE+ 9 chiffres'
        greece: '9 chiffres'
        hongkong: '4 à 8 chiffres'
        hungary: '10 chiffres ou 11 chiffres'
        india: '2 chiffres + 13 caractères'
        ireland: '7 chiffres'
        italy: '11 chiffres'
        latvia: '11 chiffres'
        lithuania: '9 chiffres'
        luxembourg: '8 chiffres'
        malta: '8 chiffres'
        mexico: '12 ou 13 caractères'
        morocco: '35 caractères maximum'
        netherlands: '8 chiffres'
        norway: '9 chiffres'
        peru: '11 chiffres'
        poland: '10 chiffres'
        portugal: '9 chiffres'
        qatar: '5 chiffres'
        romania: '"RO" + 2 à 10 chiffres'
        russia: '13 ou 15 chiffres'
        saudi_arabia: 'AS + 15 chiffres'
        senegal: '5 lettres + 4 chiffres + 1 lettre + 5 chiffres'
        singapore: '9 chiffres max + 1 lettre'
        slovak_republic: 'SK+10 chiffres'
        slovenia: '8 chiffres'
        spain: '1 lettre + 8 chiffres'
        sweden: '10 chiffres'
        switzerland: CHE-XXXXXXXXX
        taiwan: '8 chiffres'
        thailand: '13 chiffres'
        tunisia: '10-11 caractères'
        turkey: '10 chiffres'
        united_arab_emirates: '15 chiffres'
        united_kingdom: '8 chiffres'
        united_states: '9 chiffres'
    india: Inde
    ireland: 'Republique d''Irlande'
    italy: Italie
    latvia: Lettonie
    lithuania: Lituanie
    luxembourg: Luxembourg
    malta: Malte
    mexico: Mexique
    morocco: Maroc
    netherlands: 'Pays Bas'
    norway: Norvege
    peru: Pérou
    poland: Pologne
    portugal: Portugal
    qatar: Qatar
    region:
        CA-AB: Alberta
        CA-BC: Colombie-Britannique
        CA-MB: Manitoba
        CA-NB: Nouveau-Brunswick
        CA-NL: Terre-Neuve-et-Labrador
        CA-NS: Nouvelle-Écosse
        CA-NT: 'Territoires du Nord-Ouest'
        CA-NU: Nunavut
        CA-ON: Ontario
        CA-PE: Île-du-Prince-Édouard
        CA-QC: Québec
        CA-SK: Saskatchewan
        CA-YT: Yukon
        FR-ARA: Auvergne-Rhône-Alpes
        FR-BFC: Bourgogne-Franche-Comté
        FR-BRE: Bretagne
        FR-COR: Corse
        FR-CVL: 'Centre-Val de Loire'
        FR-GES: 'Grand Est'
        FR-GF: 'Guyane (française)'
        FR-GP: Guadeloupe
        FR-HDF: Hauts-de-France
        FR-IDF: Île-de-France
        FR-MQ: Martinique
        FR-NAQ: Nouvelle-Aquitaine
        FR-NOR: Normandie
        FR-OCC: Occitanie
        FR-PAC: 'Provence-Alpes-Côte d''Azur'
        FR-PDL: 'Pays de la Loire'
        FR-RE: 'La Réunion'
        FR-YT: Mayotte
    romania: Roumanie
    russia: 'Fédération de Russie'
    saudi_arabia: 'Arabie Saoudite'
    senegal: Sénégal
    singapore: Singapour
    slovak_republic: 'République slovaque'
    slovenia: Slovénie
    spain: Espagne
    sweden: Suède
    switzerland: Suisse
    taiwan: Taiwan
    thailand: Thailande
    tunisia: Tunisie
    turkey: Turquie
    united_arab_emirates: 'Emirats Arabes Unis'
    united_kingdom: Royaume-Uni
    united_states: 'Etats Unis'
currency: Devise
customs:
    info:
        domestic: Domestic
        export_EU: 'Export UE'
        export_non_EU: Export
default:
    placeholder: Indéterminé
dispute:
    create:
        ko: 'Une erreur est survenue lors de l''envoi de votre réclamation'
        ok: 'Votre réclamation a été envoyée'
    form:
        message: Commentaires
        new: Réclamation
        placeholder: 'Expliquez ici les problèmes rencontrés avec les produits selectionnés'
        products: 'Sélectionnez les produits concernés par votre réclamation'
        see: 'Voir les réclamations'
        subject: Sujet
        table:
            all: Tous
            expected_date: 'Date prévue'
            product_name: 'Nom du produit'
            quantity: Quantité
            reference: Référence
            total_price: 'Prix total'
            unit_price: 'Prix unitaire'
        title: Réclamation
    list:
        creation_date: 'Date de création'
        id: Id
        messages: Messages
        read: Lus
        receiver: Vendeur
        subject: Sujet
        unread: 'Non lus'
document:
    contract:
        download: 'Téléchargez ici le contrat pré-rempli'
    noDoc: 'Aucun fichier sélectionné -- Vous devez ajouter vos documents'
    upload:
        delete: 'Voulez-vous supprimer ce fichier ?'
        deleteError: 'Erreur à la suppression du fichier'
        error: 'Veuillez ajouter au moins un fichier.'
        ignored: '1 ou plusieurs fichiers ont été ignorés'
        ko: 'Erreur(s) lors du chargement'
        mime: 'Le type de document doit faire partie de la liste suivante : %contrainte%'
        ok: 'Chargement effectué'
        size: 'La taille du document ne doit pas exéder %contrainte% Mo'
        title: 'Sélectionner un fichier'
        type: 'Le fichier n''est pas valide'
        typeOrSizeError: 'Un problème est survenu pendant l''ajout de votre document. Le fichier n''est peut être pas valide'
        working: 'Traitement en cours ...'
error:
    forbidden:
        code: '(erreur 403)'
        description: 'Vous n''êtes pas autorisé à visualiser ce contenu. Si vous pensez qu''il s''agit d''un problème, vous pouvez contacter le support.'
        title: 'Non autorisé'
    generic:
        help: 'Les liens suivants peuvent vous aider:'
        home: 'Retour à l''accueil'
        support: 'Contacter le support'
    internal:
        code: '(erreur 500)'
        description: 'Une erreur inconnue s''est produite lors du traitement de votre requête. Si vous pensez qu''il s''agit d''un problème, vous pouvez contacter le support.'
        title: 'Une erreur interne est survenue'
    notfound:
        code: '(erreur 404)'
        description: 'La page que vous avez demandé n''existe pas. Si vous pensez qu''il s''agit d''un problème, vous pouvez contacter le support.'
        title: 'La page n''existe pas'
    zipCode: 'Le code postal doit être composé de 5 chiffres'
filter:
    all: Tous
    'no': Non
    'yes': Oui
flag: '#icon-flag-fr'
footer:
    about_us:
        code_of_conduct: 'Code de conduite'
        company: 'La société'
        cookies: Cookies
        data_privacy_chart: 'Charte de confidentialité des données'
        join_us: Rejoignez-nous
        legal_notice: 'Mentions légales'
        our_mission: 'Notre mission'
        title: 'Qui sommes-nous'
    additional:
        alstom: StationOne
        copyright: '© 2018 StationOne. Tous droits réservés.'
        purchase_conditions: 'Conditions d''achat conformes aux conditions générales d''utilisation de Station One puis aux conditions de vente de chaque fournisseur.'
    buy:
        benefits_for_buyers: 'Avantages acheteurs'
        create_an_account: 'Créer un compte'
        general_terms_and_conditions: 'Conditions générales'
        title: 'Acheter sur StationOne'
    follow_us:
        title: Suivez-nous
    help:
        call_us_at: 'Appelez-nous au:'
        contact_us: Ecrivez-nous
        illegal_content: 'Alerte de contenu illicite'
        phone_number_1: '+33 6 59 35 58 54'
        questions: FAQ
        title: Aide
    mentions: 'Mentions Légales'
    newsletter:
        placeholder: 'Adresse mail'
        title: 'Inscrivez-vous à notre newsletter'
    press:
        blog: Blog
        news: Actualités
        press_releases: 'Communiqués de presse'
        title: 'Presse & Actualités'
    sell:
        benefits_for_sellers: 'Avantages vendeurs'
        create_an_account: 'Créer un compte'
        general_terms_and_conditions: 'Conditions générales'
        title: 'Vendre sur StationOne'
    visit:
        address_1: '69-73 Boulevard Victor Hugo'
        address_2: '93400 Saint-Ouen-sur-Seine'
        address_3: FRANCE
        title: Visitez-nous
form:
    invoice:
        search: Rechercher
    order:
        search: Search
    user:
        add: 'Ajouter un utilisateur'
        definition:
            account_manager: 'Responsable de compte'
            account_manager_definition: 'Droits de l''acheteur + Administration globale du compte'
            buyer: Acheteur
            buyer_definition: 'Droits du demandeur + Approbation des commandes pour ses centres de coûts'
            requestor: Demandeur
            requestor_definition: 'Sélectionne les produits, prépare la commande et les envoie pour validation à l''acheteur'
        delete:
            content: 'Êtes-vous sûr de vouloir supprimer cet utilisateur ?'
            content_for_default_user: 'Êtes-vous sûr de vouloir supprimer cet utilisateur ? <br/> Attention, cet utilisateur est nécessaire pour le processus de commande automatique, veuillez réaffecter un autre utilisateur à la page du centre de coûts (<a href="/fr/company/sites">centres de coûts</a>)'
            title: 'Suppression d''un utilisateur'
        email: E-mail
        error_update: 'Une erreur s''est produite lors de la mise à jour, veuillez vérifier les données saisies'
        firstname: Prénom
        function: Fonction
        invalid_role: 'Ce rôle n''est pas valide'
        lastname: Nom
        myself: Moi
        role:
            admin: 'Responsable de compte'
            buy: Demandeur
            pay: Acheteur
            placeholder: 'Choisissez un rôle'
            sell: Vente
            view: Consultation
        roles: Profil
        save_edit: 'Mettre à jour'
        save_new: Sauvegarder
        sites:
            label: 'Centre de coûts :'
            mandatory: 'Vous devez selectionner au moins un centre de coût'
        success_new: 'L''utilisateur à été créé'
        success_update: 'L''utilisateur a été mis à jour'
        title: 'Liste des utilisateurs'
        title_common: 'un utilisateur'
        title_edit: Modifier
        title_new: Ajouter
front:
    site:
        no_default_user: 'No default user is assign'
generic:
    cancel: Annuler
    delete: Supprimer
    save: Sauver
header:
    address: Adresse
    buyer_id: 'ID acheteur'
    company: Société
    delivery_delay: 'Date de livraison prévisionnelle'
    incoterm: Incoterm
    purchase_order: 'Bon de commande'
    site: 'Centre de coût'
    tva_identification_number: 'Numéro de TVA'
    vendor_id: 'ID vendeur'
    vendor_ref: 'Référence vendeur'
home:
    autres_industriels: 'Autres industriels'
    collectivite_locale: 'Collectivité locale'
    dashboard_admin: 'Dashboard administrateur'
    description: 'Cum Homerici cum Roma nobilium'
    disclaimer:
        market:
            line1: 'Plus de débouchés'
            line2: 'Plus de gisements'
            line3: 'Prix du marché'
            title: 'Accès au marché'
        process:
            line1: 'Gain de temps'
            line2: 'Reporting et tableau de bord'
            line3: 'Lettre de voiture digitale'
            title: 'Un process efficient'
        transactions:
            line1: 'Transparence (photos & caractérisation)'
            line2: 'Audit des vendeurs & acheteurs'
            line3: 'Contrat de vente engageant'
            title: 'Transactions sécurisées'
        video: 'Découvrir en vidéo'
    grande_distribution: 'Grande distribution'
    insert:
        all_news: 'Voir toutes les news'
        contact: Contact
        news_description: 'Alstom will supply Île-de-France Mobilité and the RATP whith 20 MP14 metros, consisting of 5 cars each, for line 11 of the Paris metro for an amount worth 157 million euros.'
        savoir: 'En savoir +'
        sustainability_description: 'Meeting today''s needs without compromising tomorrow'
        title: faq
        title_duree: Sustainability
        title_news: 'Latest news'
    login: Login
    login_admin: 'Login administrateur/opérateur'
    logout: 'Se déconnecter'
    register: 'S''inscrire'
    register_buyer: Inscription
    register_merchant: 'Inscription vendeur'
    register_merchant_confirmation_message: 'Votre inscription s''est déroulée avec succès.'
    register_merchant_confirmation_title: Félicitations
    register_merchant_confirmation_url: 'Votre inscription s''est déroulée avec succès et va maintenant être traitée par l''équipe StationOne'
    register_merchant_error: 'Une erreur inattendue s''est produite lors de l''inscription. Si le problème persiste, veuillez contacter le support.'
    register_merchant_success: 'Votre inscription s''est déroulée avec succès'
    register_merchant_tva_not_checked: 'The identification number could not be checked for this vendor. Please proceed to a manual check'
    slider:
        subtitle: 'Fruticeta prona valido inmanium fortiter'
        title: 'Cum Homerici cum Roma nobilium'
    start_buy: 'Commencer à acheter'
    start_sale: 'Commencer à vendre'
    ticket_admin: 'Liste des tickets (administrateur)'
    ticket_anonymous: 'Créer un ticket en tant qu''utilisateur anonyme'
    ticket_buyer: 'Liste des tickets'
    title: Accueil
    video_src: MECmgIz36nU
    why_buy:
        certified: Agréé
        certified_text: 'Un catalogue de fournisseurs de qualité agrées par StationOne'
        fast: Rapide
        fast_text: 'Un site internet et des milliers de références pour optimiser vos approvisionnements'
        simple: Simple
        simple_text: 'Un système de paiement sécurisé et des délais de livraison assurés'
        title: 'Pourquoi acheter sur StationOne ?'
illegal_content:
    form:
        ko: 'Une erreur est survenue lors de l''envoi de votre contenu illicite'
        ok: 'Votre demande a été envoyée avec succès'
        save: Envoyer
        title: 'Décrivez le contenu illicite que vous avez détecté'
import:
    csv:
        invalid_separator: 'Séparateur de liste non reconnu et/ou nombre de colonnes incorrect'
        no_error: 'Importation effectuée sans erreur'
        not_all_lines_were_imported: 'toutes les lignes n''ont pas été importées'
        not_enough_lines: 'Pas assez de ligne dans ce fichier'
invoice:
    detail:
        already_invoiced: 'Déjà facturé :'
        credit_note: Avoir
        frame_contract: 'Contrat cadre'
        go_back: 'Retour à la commande ID: '
        including_taxes: TTC
        invoice: Facture
        invoice_amount: Montant
        invoice_date: Date
        invoice_due_date: Echéance
        invoice_payment: Paiement
        not_paid: Impayé
        not_yet_invoiced: 'Pas encore facturé:'
        order_amount: 'Total:'
        order_date: 'Date:'
        order_id: 'Id sous-commande:'
        order_payment_status: 'Statut paiement:'
        order_seller: 'Vendeur:'
        order_status: 'Statut:'
        paid: Payé
        payment: Paiement
        refund: Remboursement
        remaining_to_pay: 'Restant à payer'
        title: Facture
        total: Total
        total_invoiced: 'Total facturé et avoir :'
        total_paid: 'Total payé'
        total_remaining_to_pay: 'Total restant à payer:'
    export:
        invoice_amount: 'Montant de la facture'
        invoice_creation_date: 'Date de création de la facture'
        invoice_number: 'No. de facture'
        order_creation_date: 'Date de création de la commande'
        order_number: 'N ° de commande.'
        payment_status: 'État de la facture'
        vendor_name: 'Nom du vendeur'
    list:
        amount: Montant
        date: Date
        due_date: Echéance
        due_date_total_eur: 'A payer ce mois (€):'
        due_date_total_usd: 'A payer ce mois ($):'
        export: Exporter
        from: 'À partir de la date'
        invoice: 'Facture / Note de crédit'
        late_payment_eur: 'Retard de paiement (€):'
        late_payment_usd: 'Retard de paiement ($):'
        order: Commande
        paid_date: 'Date de paiement'
        remain: Solde
        seller: Vendeur
        tab:
            empty: 'Vous n''avez aucune facture dans cette section.'
            not_paid: 'Factures non payées'
            paid: 'Factures payées'
        title: Factures
        to: 'Jusqu''à la date'
        total_to_pay_eur: 'Total des factures reçues (€):'
        total_to_pay_usd: 'Total des factures reçues ($):'
    reminder: 'Rappel de facture'
    seeInvoices: 'Factures et paiements'
key: FR
keyToIgnore: ''
label_next: Suivant
label_previous: Précédent
login:
    companyError: 'La société n''est pas active'
    userCreatedButCompanyError: 'Nous avons le plaisir de vous confirmer que votre demande de création de compte acheteur a bien été soumise.<br />L''équipe StationOne confirmera votre création de compte dans les meilleurs délais.'
main_menu:
    account: 'Mon compte'
    buyer:
        mybids: 'Mes enchères'
        myorders: 'Mes commandes'
    categories: Catégories
    concept: 'Le concept'
    contact: Contact
    contact_us: Contact
    dashboard: 'Tableau de bord'
    eur_cart: 'PANIER EUR'
    explore_categories: 'Explorer les catégories'
    faq: F.A.Q
    help: Aide
    how: 'Comment ça marche ?'
    join_us: 'Inscription gratuite'
    languages: Langages
    locked: 'Vous devez avoir finalisé l''étape précédente pour acceder à cette page'
    merchant:
        myoffers: 'Mes offres'
        mysales: 'Mes ventes'
        offers:
            active: Actives
            draft: Brouillons
            nosale: 'Sans transaction'
            pending: 'En attente de validation'
    messages: Messages
    offers: 'Les offres'
    products: Produits
    signin: 'Se connecter'
    terms: 'Conditions générales'
    usd_cart: 'PANIER USD'
    e_catalog_label: 'E-Catalog'
menu:
    desktop:
        api_doc: 'Documentation de l''API'
        company: Société
        document: Documents
        invoices: Factures
        my_catalog: 'Mon catalogue'
        orders: Commandes
        payment_modes: 'Modes de paiement'
        pending_carts: 'Paniers en attente'
        profile: 'Mes informations'
        purchase_request: 'Demandes d''achat'
        sites: 'Centres de coûts'
        stats: Statistiques
        users: Utilisateurs
        wishlist: 'Listes de kits'
merchant:
    name: Vendeur
    update: 'Demander à voir le prix a été mis à jour avec succès'
merchant_order:
    status:
        canceled: Confirmé
        confirmed: Confirmed
        finalized: Finalisé
        initial: Initial
        partially_refunded: 'Partiellement remboursé'
        payment_authorized: 'Paiement autorisé'
        processed: Traité
        received: Reçue
        return_in_progress: 'Retour en cours'
        sent: Expédié
modal:
    cancel: Annuler
    confirm: Ok
    'no': Non
    'yes': Oui
month: mois
'no': Non
node:
    form:
        author:
            label: Auteur
        body:
            label: Corps
        checkboxLinkType:
            label: 'Redirect to external URL'
        content: Contenu
        delete:
            content: 'Êtes-vous sûr de vouloir supprimer cette page statique ?'
            error: 'une erreur est survenue lors de la suppression'
            success: 'La page statique à été supprimée'
            title: 'Suppression de la page statique'
        error:
            template_validation: 'Une erreur s''est produite lors de la validation du contenu de la langue "%locale%": %message%'
        header:
            edit: 'Modification du contenu (%type) : #%id'
            new: 'Nouveau contenu'
        lang:
            button: Ajouter
            de: Allemand
            en: Anglais
            es: Espagnol
            fr: Français
            it: Italien
            nl: Nederlands
            select: 'Choisissez une langue'
        sections:
            content: Contenu
            main: Général
        slug:
            help: Slug
            label: Permalien
        status:
            label: Statut
        submit:
            create: Créer
            error:
                create: 'une erreur est survenue lors de la création'
                update: 'une erreur est survenue lors de la mise à jour'
            success:
                create: 'La page statique à été créée'
                delete: 'La page statique à été supprimée'
                update: 'La page statique à été mise à jour'
            update: 'Mettre à jour'
        template:
            choices:
                default: 'Par défaut'
                default_with_faq: 'Par défaut avec les blocs FAQ'
                default_with_products: 'Par défaut avec liste des produits'
                default_with_products_and_faq: 'Par défaut avec liste des produits et les blocs FAQ'
                fullwidth: 'Pleine page'
                fullwidth_with_faq: 'Pleine page avec les blocs FAQ'
                fullwidth_with_products: 'Pleine page avec liste des produits'
                fullwidth_with_products_and_faq: 'Pleine page avec liste des produits et les blocs FAQ'
            label: Canevas
        test: 'Les emails de tests ont été envoyés avec succès à votre adresse email: %email%'
        textLinkExternal:
            label: 'External URL'
        title:
            label: Titre
offer_detail:
    add_error: 'Une erreur s''est produite lors de l''ajout de votre produit au panier. Si l''erreur persiste, merci de contacter le support'
    add_success: 'Votre produit a été correctement ajouté à votre panier.'
    add_to_cart: 'Ajouter au panier'
    add_to_wishlist: 'Ajouter à la liste de kits'
    add_wishlist_success: 'Votre produit a été correctement ajouté à votre liste de kits.'
    anonym_contact_merchant: 'Merci de vous connecter pour pouvoir contacter le vendeur.'
    anonymous:
        add_to_cart: 'Vous devez être authentifié pour ajouter des produits à votre panier.'
        not_authorized: 'Non autorisé'
    ask_question: 'Contacter le vendeur'
    ask_title: 'Besoin d''aide ?'
    ask_vendor: 'Demander l''accès aux prix du catalogue'
    ask_vendor_message_content: 'Bonjour,\nPouvez-vous me donner accès à vos prix catalogue ?\nMerci\n\nHello,\nCould you please grant me access to your catalog prices ?\nThank you\n'
    ask_vendor_no_price_offer_message_content: 'Bonjour,\nPourriez-vous s''''il vous plaît proposer votre meilleur prix pour cette offre?\nQuantité attendue:\nDate de livraison prévue:\n\nMerci\n'
    ask_vendor_pending: 'Accès aux prix du catalogue demandé'
    ask_vendor_price: 'Demander ce prix au vendeur'
    ask_vendor_price_reference: 'Demander un prix pour le produit %reference%'
    ask_vendor_rejeted: 'Prix non disponible'
    back_to_search: 'Retour vers la recherche'
    buy_more_for_discount: 'Achetez plus et obtenez une réduction'
    company_code: 'Code de l''entreprise :'
    company_not_valid: 'Veuillez remplir les informations de votre société avant de valider votre panier'
    contact_seller:
        modal:
            send: Envoyer
            title: 'Contacter le vendeur'
    contact_the_vendor: 'Vous pouvez contacter le vendeur depuis la page produit'
    continue_shopping: 'Continuer mes achats'
    description: 'Détail d''une offre'
    expired_offer: 'Ce produit n''est plus disponible sur StationOne. Pour plus d''informations, contactez le vendeur.'
    frame_contract_valid_date: 'Valable jusqu''au'
    from_company: 'De :'
    in_stock: 'En stock'
    login: 'Se connecter'
    not_available_country: 'Ce produit n''est pas disponible pour votre pays. Pour plus d''informations, contactez le vendeur.'
    not_batch_size_multiple: 'La quantité doit être un multiple de %batchSize%.'
    not_business_everywhere: 'Ce produit ne peut pas être vendu dans votre pays. Veuillez contacter StationOne pour plus d''informations'
    on_demand: 'Sur demande'
    on_stock: 'En stock'
    out_of_stock: 'En rupture de stock'
    out_of_stock_description: 'Ce produit n''est plus en stock sur StationOne. Pour plus d''informations, contactez le vendeur.'
    price_quantity: 'Pour %quantity% %unit%'
    proforma:
        0: proforma
        version: 'Proforma Version'
    proforma_pdf:
        quantity: 'Quantité demandée'
        title: proforma
        total_price: 'Prix total (hors TVA)'
    quantity: Quantité
    related_products: 'Services associés'
    see_cart: 'Voir mon panier'
    see_less: 'Voir moins'
    see_more: 'Voir plus'
    see_wishlist: 'Voir ma liste de kits'
    seller:
        label: 'Présentation du vendeur'
        link_product: 'Voir tous les produits'
    sign_in_to_buy: 'Connectez-vous pour acheter'
    similar_products: 'Produits Associés'
    title: 'Détail d''une offre'
    too_much_quantity: 'La quantité selectionnée est supérieure au stock (maximum: %max%).'
    too_small_quantity: 'La quantité selectionnée est inférieure au minimum à commander (minimum: %min%).'
    total_price: 'Prix total'
    wrong_quantity: 'Veuillez entrer une quantité valide.'
    warranty_period: 'Garantie %month% mois'
offers:
    bidbutton_msg: 'Vous pourrez bientôt enchérir sur vos offres préférées'
    description: 'Les offres'
    title: 'Les offres'
orders:
    createCart:
        errorMoq: 'L''article %reference% n''a pas pu être ajouté au panier car la quantité minimum à commander est inférieure à la quantité demandée'
        errorNoPrice: 'L''article %reference% n''a pas pu être ajouté au panier car il n''a pas de prix.'
        errorPrice: 'L''article %reference% a bien été ajouté au panier, cependant son prix a changé.'
        errorStatus: 'L''article %reference% n''a pas pu être ajouté au panier car l''article et/ou le vendeur sont désactivés.'
        errorStock: 'L''article %reference% n''a pas pu être ajouté au panier car le stock restant sur ce produit est inférieur à la quantité demandée'
    detail:
        go_back: 'Retour à la liste'
        shipping:
            delivered: 'Livré %separator%'
            last_delivery: 'Dernière livraison %separator%'
            title: 'Offres de transport'
        title: Commande
        tracking:
            delivery_date: 'Date de livraison'
            error: 'Nous ne pouvons pas afficher les informations de suivi pour le moment'
            quantity: 'Quantité %separator%'
            status_delivered: Livré
            status_pickuped: Collecté
            title: 'Suivi de livraison'
            vendor_ref: 'Référence vendeur %separator%'
    empty:
        title: 'Aucune commande'
    export:
        address: 'Adresse de livraison'
        amount: 'Order Amount (tax excl.)'
        amountTaxIncluded: 'Order Amount (tax incl.)'
        amountVat: 'Order VAT Amount'
        buyerRef: 'Buyer Ref'
        costCenter: 'Cost center'
        currency: Currency
        date: 'Date de commande'
        documentRequirement1: 'Document requirement 1'
        documentRequirement10: 'Document requirement 10'
        documentRequirement2: 'Document requirement 2'
        documentRequirement3: 'Document requirement 3'
        documentRequirement4: 'Document requirement 4'
        documentRequirement5: 'Document requirement 5'
        documentRequirement6: 'Document requirement 6'
        documentRequirement7: 'Document requirement 7'
        documentRequirement8: 'Document requirement 8'
        documentRequirement9: 'Document requirement 9'
        expectedDeliveryDate: 'Expected Delivery Date'
        frameContractNumber: 'Frame contract number'
        id: 'Order id'
        internalBuyerOrderId: 'Buyer''s ERP Order id'
        itemPrice: 'Order line amount (tax excl.)'
        orderLine: OrderLine
        packagingRequirement1: 'Packaging requirement 1'
        packagingRequirement2: 'Packaging requirement 2'
        packagingRequirement3: 'Packaging requirement 3'
        paymentTerms: 'Payment Terms'
        productName: 'Product Name'
        quantity: Quantity
        status: Statut
        subOrderId: 'Sub Order Id'
        unitPrice: 'Unit price (tax excl.)'
        validationNumber: 'Numéro de commande Acheteur'
        vendorName: 'Vendor Name'
        vendorRef: 'Vendor Ref'
    list:
        address: 'Adresse de livraison'
        block:
            addition_information: 'Informations supplémentaires'
            additional_information: 'Informations supplémentaires'
            address_title: 'Adresse de livraison:'
            billing_address_title: 'Adresse de facturation:'
            buyer_internal_order_id: 'N° de commande interne à l''acheteur'
            cost_center_title: 'Centre de coût:'
            date_title: 'Date de la commande:'
            expectedDate: '1ere livraison prévue:'
            iban_account_name: 'Nom du compte IBAN:'
            iban_number: 'Numéro IBAN:'
            key: 'Clé de réconciliation:'
            order_line: 'Ligne de commande'
            order_title: 'Numéro de commande:'
            packaging_specifications: 'Instructions d''emballage'
            payment_information: 'Informations de paiement:'
            payment_terms: 'Conditions de paiement:'
            requested_documents: 'Documents requis'
            status: 'Statut:'
            total_title: 'MONTANT TOTAL DE LA COMMANDE:'
            validation_number_title: 'Numéro de commande Acheteur : '
        date: 'Date de commande'
        detail: Détail
        download_pdf: Télécharger.PDF
        export: Exporter
        id: 'Id de la commande'
        link:
            buy_again: 'Acheter à nouveau'
            details: Details
            export: 'Export PDF'
            invoice: Invoice
            refund: Remboursement
            document: Document
            documents: Documents
        merchant_product: article
        merchant_products: articles
        sub_order_id: 'ID sous-commande'
        tab:
            cancelled: 'Commandes annulées'
            empty: 'Vous n''avez aucune commande dans cette section.'
            past: 'Commandes terminées'
            running: 'Commandes en cours'
        total: Total
    status:
        pending_creation: 'En cours de création'
        status_0: 'Attente de paiement'
        status_110: 'Incident clôturé'
        status_11111111: 'En cours de création'
        status_2000: Annulée
        status_3000: Supprimée
        status_60: 'Attente confirmation vendeur'
        status_80: Confirmée
        status_85: 'Livraison & facturation'
payment:
    error: 'Une erreur inatendue s''est produite lors du paiement. Votre commande a été annulée. Si le problème persiste, merci de contacter le support'
    form:
        not_authorized: 'Vous n''avez pas les droits pour effectuer une demande de moyen de paiement à terme. Veuillez contacter votre responsable.'
        submit: 'Demander l''autorisation pour le paiement à terme'
    pre_cc_mode:
        error: 'Une erreur technique s''est produite lors de la récupération des informations de votre commande %id%. Si le problème persiste, merci de contacter le support'
        ko: 'Une erreur s''est produite lors de votre paiement: Votre commande %id% a été annulée'
        ok: 'Votre paiement a été enregistré. Votre commande %id% est maintenant validée'
        success:
            text: 'Votre commande %id% effectuée en prépaiement par carte bancaire a bien été prise en compte'
            text_2: 'Vous pouvez dès à présent la consulter depuis le menu "Commandes".'
            text_3: 'Le(s) vendeur(s) confirmeront très bientôt votre commande'
            title: 'Félicitations !'
        title: 'Vérification du paiement'
        title_error: 'Erreur technique'
        title_ko: 'Echec de paiement'
        title_ok: 'Paiement confirmé'
        waiting_status: 'Veuillez patienter, en attendant l''autorisation de paiement pour votre commande %id% …'
    pre_virement_mode:
        pending:
            text: 'Votre commande effectuée en prépaiement par virement bancaire a bien été prise en compte.  '
            text_2: 'Vous allez recevoir un email contenant toutes les instructions nécessaires pour procéder au paiement.'
            text_3: 'Une fois le règlement effectué, le(s) vendeur(s) verront votre commande et pourront la confirmer'
            text_4: 'Veuillez tenir compte du fait que le délai affiché sur la commande peut être légèrement plus long que prévu car il dépend du temps dont votre organisation a besoin pour effectuer le virement bancaire, jusqu''à sa réception par StationOne.'
            text_link1: 'Retour à la page de recherche'
            text_link2: 'Voir mes commandes'
            title: 'Félicitations !'
        success:
            payment_detail_number: 'Numéro de transaction: %number%'
            payment_details: 'Voici les informations nécessaires pour effectuer votre virement bancaire :'
            payment_details_iban: 'IBAN: %iban%'
            payment_details_iban_account_name: 'Nom du compte IBAN: %iban_account_name%'
            payment_details_key: 'Clé de réconciliation: %key%'
            text: 'Votre commande %numOrder% a bien été créée. Elle sera traitée dès que votre virement bancaire sera reçu'
            text_link1: 'Aller à la page de recherche'
            text_link2: 'Voir mes commandes'
            title: 'Confirmation de votre commande'
    select_mode:
        error:
            cc: 'Erreur lors de la création de la transaction. Si le problème persiste, merci de contacter le support'
        preCreditCard: 'Pré-paiement par Carte bancaire'
        preTransferWire: 'Pré-paiement par virement bancaire'
        select: Choisir
        termTransferWire: 'Paiement à terme (45 jours fin de mois, date d''émission de la facture)'
        title: 'Choix du moyen de paiement'
    time_mode:
        error:
            text: 'Une erreur est survenue lors de la confirmation de votre commande. Veuillez contacter le support si le problème persiste.'
            text_link1: 'Voir mon panier'
            text_link2: 'Aller à la page de recherche'
            text_link3: 'Contacter le support'
            title: 'Erreur de confirmation de votre commande'
        pending:
            text: 'Votre commande effectuée en paiement à terme 45 jours fin de mois a bien été prise en compte.'
            text_2: 'Une confirmation vous sera envoyée par les vendeurs et sera visible sur la page détails de votre commande.'
            text_3: 'Vous pourrez effectuer le règlement de votre commande une fois la facture reçue.'
            text_link1: 'Retour à la page de recherche'
            text_link2: 'Voir mes commandes'
            title: 'Félicitations !'
        succes:
            text: 'Votre commande %numOrder% a bien été créée'
            text_2: 'Vous recevrez un email avec les détails de paiement (IBAN et clé de réconciliation) lorsque la facture sera envoyée.'
            text_link1: 'Retour à la recherche'
            text_link2: 'Voir mes commandes'
            title: 'Confirmation de votre commande'
payment_mode:
    Prepayment_creditcard: 'Pré-paiement par Carte bancaire'
    Prepayment_moneytransfert: 'Pré-paiement par virement bancaire'
    Termpayment_moneytransfert: '45 jours fin de mois, date d''émission de la facture'
    ask_for_term_error: 'Erreur dans le traitement de la demande d''autorisation de paiement à terme'
    click_button: 'Cliquez sur le bouton ci-dessous pour pouvoir utiliser le paiement à terme.'
    enabled: 'Votre compte est également autorisé à utiliser la méthode de paiement à terme par virement bancaire.'
    info: 'Les modes de pré-paiement autorisés par défaut sont la carte bancaire et le virement bancaire.'
    pending: 'Une demande d''autorisation de paiement à terme est en cours d''analyse'
    save_error: 'Erreur lors de la mise à jour des moyens de paiement'
    saved: 'Les moyens de paiements ont été mis à jour'
    title: 'Modes de paiement'
product:
    about_seller: 'A propos du vendeur'
    application_categories: Catégories
    buy: Acheter
    buyer_reference: 'Ref acheteur'
    cart_item_comment: 'Commentaire'
    comparison: Comparateur
    converted_price: 'Calculé selon le taux de change journalier'
    delivery_time: 'Délai de livraison'
    description: Description
    info_button_buy: 'Les produits seront bientôt disponibles à l’achat. Si vous ne l’avez pas encore fait, créez un compte pour être informé au plus tôt !'
    info_converted_price: 'Affiché à titre indicatif.'
    logistics_informations: 'Informations logistiques'
    made_in: 'Pays de fabrication'
    manufacturer: Fabricant
    manufacturer_reference: 'Ref fabricant'
    private: Privé
    quantity: 'Quantité disponible'
    max_quantity: 'Quantité achetable'
    seller: Vendeur
    seller_reference: 'Ref vendeur'
    technical:
        bearing_type: 'Type de roulement à billes'
        code_command_rs: 'Code command RS'
        conditioning: Conditionnement
        dtr: DTR
        flange_outer_diameter: 'Diamètre extérieur de bride'
        inner_diameter: 'Diamètre intérieur'
        manufacturer_ref: 'Référence fabricant'
        marketplace_id: 'Marketplace ID'
        material: Matériau
        min_order_quantity: 'Quantité minimum'
        nb_row: 'Nombre de rangées'
        outer_diameter: 'Diamètre extérieur'
        rated_static_load: 'Charge statique nominale'
        ring_width: 'Largeur de la bague'
        seller_name: 'Nom du vendeur'
        seller_ref: 'Référence du vendeur'
        termination_type: 'Type de terminaison'
        trendmark: Marque
    technical_detail: 'Descriptif techniques'
    technical_details: 'Descriptif techniques'
products:
    all_offers: 'Voir toutes les offres'
    home_list_header: 'Liste des offres'
    home_product_list_header: 'Produits les plus recherchés'
profile:
    form:
        email: E-mail
        firstname: Prénom
        lastname: Nom
        main_phone_number: 'Numéro de téléphone'
        optional_phone_number: 'Optional phone number'
        submit: Sauvegarder
        update:
            error: 'Une erreur est survenue lors de la mise à jour de vos informations personnelles'
            success: 'Vos informations personelles ont étés mis à jour'
    password:
        help: ''
        submit: Appliquer
        title: 'Modifier votre mot de passe'
proforma:
    address: Address
    address_title: 'Proforma from STATION ONE in the name and on behalf of'
    billing_address: 'Billing address'
    capital: 'to the capital of'
    condition:
        adress: 'StationOne, capital 20.000 Euros, 69/73 Boulevard Victor Hugo, 93400 Saint-Ouen (France). VAT NUMBER: FR18752364885'
        payment: 'Payment to be made to our representative'
        purchase: 'Purchase conditions according to General Terms of Use of Station One and then to the online sales conditions of %vendor_name% or to the contract number if any'
        title: 'Conditions of this offer'
    date: 'Proforma generated on'
    rcs: RCS
    siret: SIRET
    text: 'This proforma is only valid at the time of generation of this pdf. StationOne does not commit to holding this price over time.'
    title: proforma
    vat_number: 'VAT Number'
purchase_request:
    cart:
        add: 'Ajouter au panier'
        remove: 'Retirer du panier'
    clear: 'Effacer la page'
    detail: 'Demandes d''achat'
    export: 'Exporter les références non trouvées'
    exportFound: 'Exporter les références trouvées'
    import:
        error:
            csv_format: 'Format du fichier invalide. Seul le format .CSV est accepté'
            csv_size: 'La taille du fichier dépasse la limite autorisée. La taille maximum par fichier est de 2MB'
            internal_error: 'Erreur dans le lancement de la requête, veuillez contacter StationOne'
        title: Importer
    import_waiting: 'Veuillez importer un nouveau fichier'
    instruction: 'Le fichier importé doit obligatoirement avoir le format suivant : <a href="%link%" target="_blank">format_import_demande_achat.csv</a>'
    offer:
        batch_price: 'Prix pour 50 articles'
        error_bafv: 'Le prix de ce produit n''est pas disponible car vous n''avez pas accès aux prix du catalogue de ce vendeur. Pour demander accès, cliquez sur le nom du produit, puis cliquez sur "Demander l''accès aux prix du catalogue" sur la page produit.'
        error_businesseverywhere: 'Ce produit ne peut pas être vendu dans votre pays. Raison : au moins l''une des parties prenantes doit être localisée en Europe'
        error_noprice: 'Ce produit est disponible sur demande de prix'
        manufacturer: Fabricant
        merchant: Vendeur
        quantity: Quantité
        quantity_available: 'Quantité disponible'
        see_more: 'Voir plus'
        select: Sélectionner
        selected: Sélectionné
        sku_price: 'Prix pour'
        unit_price: 'Prix unitaire'
    pr_item:
        buyer_order_number: 'Numéro de commande acheteur'
        buyer_reference: 'Référence acheteur'
        cost_center: 'Centre de coût'
        details: Détails
        expected_delivery_date: 'Date de livraison prévue'
        manufacturer_name: 'Nom du fabricant'
        manufacturer_reference: 'Référence fabricant'
        merchant: Vendeur
        no_ref: 'Aucune reference trouvée'
        order_line: 'Ligne de commande'
        product_name: 'Nom du produit'
        purchase_request_number: 'Numéro de la demande d''achat'
        quantity: Quantité
        quantity_expected: 'Quantité attendue'
        ref: Référence
        see_more: 'Voir plus'
        unit_price: 'Prix unitaire'
        unit_price_of_reference: 'Prix unitaire de référence'
    title: 'Demandes d''achat'
redirect:
    form:
        delete:
            error: 'Une erreur est survenue lors de la suppréssion de la redirection'
            success: 'La redirection à été supprimée'
        destination:
            help: 'assurez vous que l''url existe'
            label: Destination
        header:
            edit: 'Modification de la redirection : #%id'
            new: 'Nouvelle redirection'
        origin:
            help: Slug
            label: Origine
        submit:
            error:
                create: 'une erreur est survenue lors de la création'
                update: 'une erreur est survenue lors de la mise à jour'
            success:
                create: 'La redirection à été créée'
                update: 'La redirection à été mise à jour'
        type:
            help: '301 = permanente / 302 = temporaire'
            label: 'Type de redirection'
redislist:
    alert:
        delete_all: 'Toutes les clefs Redis ont été supprimées.'
        error: 'Une erreur s''est produite ! La clef %key% n''a pas été supprimée.'
        success: 'La clef %key% a bien été supprimée !'
    delete_all_keys: 'Supprimer tout'
    delete_all_keys_title: 'Supprimer toutes les clefs'
    delete_title: 'Supprimer la clef %key%'
    no_key: 'Aucune Clef Redis de stockée.'
registration:
    buyer: Acheteur
    error:
        identification_already_used: 'Cette identification est déjà utilisée sur ce système'
        identification_already_used_alert: 'Votre entreprise a déjà un compte sur StationOne. Le propriétaire du compte peut vous donner accès. Si vous ne connaissez pas le titulaire du compte, veuillez nous envoyer une demande avec le formulaire de contact. Voulez-vous aller sur le formulaire de contact ?'
        technical: 'Une erreur technique s''est produite lors de la création de votre compte. Veuillez contacter le support.'
        userDisabled: 'L''utilisateur a été désactivé. Si vous désirez ré-activer ce compte utilisateur, veuillez contacter StationOne via le formulaire de <a href="%url%">contact</a>'
    label: 'Je veux rejoindre en tant que'
    selectType: 'Choississez votre compte'
    vendor: Vendeur
resetting:
    check_email: 'Un e-mail a été envoyé. Il contient un lien sur lequel il vous faudra cliquer pour réinitialiser votre mot de passe.</br></br>Si vous ne recevez pas un email, vérifiez votre dossier spam ou essayez à nouveau.'
    newpwd: 'Modification de votre mot de passe'
    request:
        submit: 'Réinitialiser le mot de passe'
    reset:
        submit: Modifier
search:
    advanced_search:
        submit: Rechercher
        title: 'Recherche avancée'
    compatible_products_with: 'Produits compatibles avec'
    departments:
        all_departments: Tous
        bearing: Support
        brake_disc: 'Disque de frein'
        camera: Caméra
        filter: Filtre
        glazing: Vitrage
        screen: Ecran
    filters: Filtres
    help: Aidez-moi
    in_compatible_products: 'Rechercher parmi les produits compatibles'
    no_custom_ref_found: 'Aucune référence trouvée dans votre catalogue.'
    no_offer: 'Désolé, nous ne trouvons pas de résultats correspondant à votre recherche. '
    no_offer_in_catalog: 'Il n’y a pas de résultat correspondant à votre recherche.'
    page: page
    pagination:
        next: Suivant
        'on': sur
        previous: Précédent
    products: produits
    result_label: 'Résultat de votre recherche (%total% resultats)'
    results: résultats
    results_for: pour
    searchbar:
        advanced_search: 'Recherche avancée'
        custom_search: 'Rechercher dans mon catalogue'
        in_catalog: 'Mon catalogue'
        in_marketplace: Marketplace
        in_product_compatibility: 'Produits compatibles'
        mobile_placeholder: 'Rechercher un produit'
        placeholder: 'Commencez à écrire pour rechercher un produit'
    show: Afficher
    sidebar:
        category: catégorie
        commons: Communs
        departments: Catégories
        refine_by: 'Affiner par'
        search: 'Rechercher un terme'
        see_more: 'Voir plus'
        specific: Spécifiques
        up: '< Retour'
    sort_by: 'Trier par'
    sort_form:
        delivery_time: 'Délai de livraison (croissant)'
        newest: 'Plus récentes (en premier)'
        price_max: 'Prix (décroissant)'
        price_min: 'Prix (croissant)'
        relevance: Pertinence
    title: Rechercher
security:
    login:
        create_account: 'Créer un compte'
        fail: 'Identifiants invalides.'
        footer_text: 'Pas encore membre ?'
        login: 'Se connecter'
        role_denied: 'Votre compte n''est pas autorisé à se connecter'
        title: 'Connexion acheteur'
shipping:
    option:
        cheapest: 'Prix optimisé'
        fastest: Urgent
        no_merchant_shipping: 'Ce vendeur ne propose pas d''offre de transport via StationOne'
        no_shipping_available: 'Pas de transport disponible. Pour plus d''informations, veuillez contacter StationOne'
        noshipping: 'Sans transport'
shipping_point:
    form:
        address:
            label: Adresse
        comment:
            label: 'Commentaires (horaires d''ouverture à la réception des marchandises, etc…)'
        contact:
            accountant: 'Contact Comptabilité'
            label: 'Contact Magasin'
        documents_requests:
            label: 'Documents nécessaires pour la réception de la marchandise'
        first: 'Adresse de livraison principale'
        name: 'Nom du point de livraison'
        packaging_request:
            label: 'Instructions d''emballage'
            tips: 'max char = 255'
        save: Sauvegarder
        title_common: 'Adresse de livraison'
        title_edit: 'Modifier une'
        title_new: Nouvelle
site:
    form:
        accountant_email: 'Adresse email à laquelle les factures seront transmises'
        add: 'Ajouter un centre de coût'
        add_contact: 'Ajouter un contact'
        add_modal: 'Nouveau centre de coût'
        add_shipping_address: 'Ajouter une adresse de livraison'
        adresse: Adresse
        afternoon:
            end: 'Fermeture après-midi'
            start: 'Ouverture après-midi'
        authorization: 'Autorisation préfectorale'
        cancel: Annuler
        comment: Commentaire
        complement: Complément
        copy: copier
        corporate_name: 'Raison sociale'
        cpZipCode: 'Code postal / ville'
        create: Créer
        created: 'Votre centre de coût a été créé'
        days: Jours
        default_user:
            label: 'L''utilisateur doit attribuer un panier automatique'
            placeholder: 'L''utilisateur doit attribuer un panier automatique'
        delete:
            content: 'Êtes-vous sûr de vouloir supprimer ce centre de coût ?'
            shipping_point: 'Êtes-vous sûr de vouloir supprimer cette adresse de livraison ?'
        delete_shipping_address: Supprimer
        documentation_request_1: 'Document requis 1'
        documentation_request_10: 'Document requis 10'
        documentation_request_2: 'Document requis 2'
        documentation_request_3: 'Document requis 3'
        documentation_request_4: 'Document requis 4'
        documentation_request_5: 'Document requis 5'
        documentation_request_6: 'Document requis 6'
        documentation_request_7: 'Document requis 7'
        documentation_request_8: 'Document requis 8'
        documentation_request_9: 'Document requis 9'
        error: 'Erreur lors de la création du centre de coût ! Veuillez compléter tous les champs'
        friday: Vendredi
        identification: 'Numéro de Siret'
        info:
            operator_needed: 'Les informations de votre site ne peuvent plus être modifiées directement. Si vous souhaitez apporter des modifications, veuillez contacter StationOne via le formulaire de <a href="/ticket/create/">contact</a>.'
        legales_doc: 'Documents légaux'
        main_contact: 'Contact principal'
        modify_shipping_address: Modifier
        monday: Lundi
        morning:
            end: 'Fermeture matin'
            start: 'Ouverture matin'
        name: 'Nom du centre de coût'
        opening_time: 'Horaires d''ouverture'
        other_contacts: 'Autres contacts'
        packaging_request_1: 'Instructions d''emballage 1'
        packaging_request_2: 'Instructions d''emballage 2'
        packaging_request_3: 'Instructions d''emballage 3'
        placeholder:
            name: 'Nom du centre de coûts'
        save: Sauvegarder
        submit: Soumettre
        submitError: 'Vous devez soumettre votre société avant de soumettre votre site'
        thursday: Jeudi
        title: 'centre de coût'
        tuesday: Mardi
        wednesday: Mercredi
    header:
        edit: 'Modification de centre de coût : #%id'
        list: 'Liste des centres de coût'
    list:
        deactivation:
            ko: 'Erreur lors de la suppression du centre de coût'
            ok: 'Le centre de coût a été supprimé avec succès'
            users_exist: 'des utilisateurs osnt attachés à ce centre de coût, ce qui vous empêche de le supprimer'
        default_user:
            not_defined: 'No user is defined for this cost center'
            title: 'Default user for this cost center'
        no_user: 'No user affiliated'
        title: 'Affiliated users to this cost center'
        users:
            action: Action
            firstname: Firstname
            function: Function
            id: '#'
            lastname: Lastname
            role: Role
stats:
    accrued:
        'no': Non
        title: 'Montant cumulé'
        'yes': Oui
    cost_center:
        all: Tous
        title: 'Centre de coût'
    orderAmount: 'Montant des commandes'
    year:
        title: Année
status:
    draft: 'A compléter'
    pending: 'En attente de validation par l''opérateur'
    valid: 'Valider par l''opérateur'
system:
    setting:
        form:
            submit: Modifier
    settings:
        homepage:
            disclaimer:
                title: 'Id de la vidéo'
                video_src: Vidéo
            slider:
                title: 'Paramètres du slider'
            title: 'Paramètres de la page d''accueil'
            video_1_src: 'Id Youtube de la video du Slide 2'
            video_2_src: 'Id Youtube de la video du Slide 3'
            video_en_src: 'Vidéo en anglais'
            video_fr_src: 'Vidéo en français'
        notifications:
            command:
                title: Commandes
            completion_recall_number_of_day: 'Nombre de jour avant la notification de rappel de complétion'
            title: Notifications
        offers:
            offer_1: 'Identifiant Izberg 1ière Offre populaire'
            offer_2: 'Identifiant Izberg 2ième Offre populaire'
            offer_3: 'Identifiant Izberg 3ième Offre populaire'
            offer_4: 'Identifiant Izberg 4ième Offre populaire'
            offer_5: 'Identifiant Izberg 5ième Offre populaire'
            offer_6: 'Identifiant Izberg 6ième Offre populaire'
            offer_7: 'Identifiant Izberg 7ième Offre populaire'
            offer_8: 'Identifiant Izberg 8ième Offre populaire'
            popular:
                title: 'Offres populaires'
            title: 'Paramètre des offres'
        security:
            login:
                title: 'Paramètres de sécurité du Login'
            login_attempt_max: 'Nombre maximum de tentative de Login'
            login_banned_user_unlock_timeout: 'Déblocage des utilisateurs bannis après {x} minutes'
            title: 'Paramètres de Sécurité'
        testimonials:
            fader_speed: 'Vitesse de rotation des témoignages (en millisecondes)'
            max_age: 'Age maximum en mois (au delà, les témoiganges ne seront pas affichés)'
            max_items: 'Nombre maximal de témoignages à afficher'
            parameters:
                title: 'Paramètres des témoignages'
            title: Témoignages
        update_error: 'Une erreur est survenue lors de la mise à jour des paramètres'
        update_success: 'Paramètres mis à jour avec succès'
tab_infos_seller:
    cgv: 'Conditions générales de ventes'
    frame_contract: 'Under particular condition of frame contract : %frame_contract%'
    minimum_order_amount: 'Montant minimum de la commande dans le panier pour ce vendeur : %amount%'
    presentation: 'Présentation du vendeur'
    see_all_products: 'Voir tous les produits'
ticket:
    common:
        add_file: 'Ajouter un fichier'
        administrator_user: '%fullname% (opérateur)'
        authorized_files_extensions_message: 'Seuls les formats pdf, jpg, gif, png, tiff, xls, xlsx sont autorisés'
        file_reset: Vider
        message: Message
        nofiles: 'Pas de fichier sélectionné'
        number: 'Numéro de ticket'
        subject: Objet
        submit: Envoyer
        update: Envoyer
    create:
        company: Entreprise
        email: Email
        firstname: Prénom
        foradmin: StationOne
        forvendor: 'un vendeur'
        function: Fonction
        lastname: Nom
        message_text: Message
        phone: Téléphone
        recipient: 'Choix de la société'
        title: 'Vous souhaitez contacter :'
    edit:
        add: 'Nouvelle réponse'
        attachment: 'Pièce jointe'
        attachment_button: 'Ajouter des fichiers'
        author: Auteur
        close: 'Fermer la discussion'
        close_thread: 'This thread was closed by %firstname% %lastname% on %date%'
        close_thread_anonymous: 'This thread was closed by an anonymous user on %date%'
        closed: 'Fermé le'
        company: Société
        date: 'Ticket créé le'
        id: ID
        link: 'Retour à la liste des messages'
        message_label: Messages
        message_placeholder: 'Saisir le message'
        message_text: Réponse
        new_message: 'NOUVEAU MESSAGE :'
        operator: Opérateur
        recipient: Destinataire
        reopen: Réouvrir
        save_message: ENREGISTRER
        subject: Objet
        timeFormat: ' à $1h$2mn$3s'
        title: Ticket
    error:
        create: 'Une erreur s''est produite lors de la création de votre ticket'
        update: 'Une erreur s''est produite lors de la mise à jour de votre ticket'
    list:
        actions: Actions
        add: 'Écrire un nouveau message'
        all: Tous
        author: 'Créé par'
        closed: Fermés
        company: Société
        createdAt: 'Créé le'
        empty: 'Vous n''avez aucun message'
        export_csv: 'Export en CSV'
        knp_next: Suivant
        knp_previous: Précédent
        lastAt: 'Modifié le'
        main_contact: 'Créateur du ticket'
        me: Vous
        nb_messages: 'Nombre de messages'
        next: 'Voir les tickets plus anciens'
        number: ID
        opened: Ouverts
        previous: 'Voir les tickets plus récents'
        sent_to: 'Envoyé à'
        status: Statut
        sujet: Objet
        title:
            resolved: 'Messages résolus'
            standard: Messages
    status:
        STATUS_CLOSED: Résolu
        STATUS_INFORMATION_REQUESTED: 'Demande d''information'
        STATUS_INVALID: Invalide
        STATUS_IN_PROGRESS: 'En cours'
        STATUS_NEW: Nouveau
        STATUS_ON_HOLD: 'En attente'
        STATUS_OPEN: Ouvert
        STATUS_OPERATOR_RESPONDED: Ouvert
        STATUS_RESOLVED: Résolu
        STATUS_USER_RESPONDED: Ouvert
    success:
        create: 'Votre demande a été créée avec succès'
        update: 'La demande a été mise à jour avec succès'
    waylf:
        title: 'Que recherchez-vous ?'
user:
    form:
        ROLE_API: ''
        ROLE_BUYER_ADMIN: 'Responsable de compte'
        ROLE_BUYER_BUYER: Demandeur
        ROLE_BUYER_PAYER: Acheteur
        ROLE_OPERATOR: Opérateur
        ROLE_SUPER_ADMIN: Administrateur
        add: 'Créer un utilisateur'
        company: Société
        email: Email
        firstname: Prénom
        function:
            0: Fonction
            label: ''
            mandatory: ''
        lastname: Nom
        phone1: 'Téléphone principal'
        phone2: 'Téléphone secondaire'
        role: Rôle
        site: 'Centres de coût'
    registration:
        email: Email
    resetting:
        title: 'Modification du mot de passe'
validator:
    date: 'Veuillez fournir une date valide.'
    email: 'Veuillez fournir une adresse électronique valide.'
    number: 'Veuillez fournir un numéro valide.'
    remote: 'Veuillez corriger ce champ.'
    required: 'Ce champ est obligatoire.'
    url: 'Veuillez fournir une adresse URL valide.'
wishlist:
    add_new: 'Ajouter une nouvelle liste de kits'
    add_to_cart: 'Ajouter au panier'
    charge:
        confirm: 'Cette action supprimera le contenu de votre panier avant d''ajouter les offres de cette liste'
        error: 'Une erreur est survenue pendant l''ajout des offres dans votre panier'
        success: 'Les offres de votre liste ont été ajoutées au panier'
    delete:
        error: 'Une erreur est survenue pendant la suppression de votre liste'
        success: 'Votre liste a été supprimée'
    delete_confirm: 'Êtes-vous sûr de vouloir supprimer cette liste ?'
    go_back: 'Retour à la liste'
    item:
        delete:
            error: 'Une erreur est survenue pendant la suppression de votre offre'
            success: 'Votre offre a été supprimée de la liste'
        delete_confirm: 'Êtes-vous sûr de vouloir supprimer cette offre de la liste ?'
        noItem: 'Vous n''avez pas d''offre dans votre liste'
        update:
            error: 'Une erreur est survenue pendant la mise à jour de la quantité'
            success: 'La quantité a été mise à jour'
    new_name: 'Nom de la nouvelle liste'
    none: 'Vous n''avez pas de liste de kits'
    notification_message:
        no_price_offer: 'Liste de kits invalide'
    save: Sauvegarder
    save_error: 'Votre liste n''a pu être enregistrée'
    save_in: 'Ajouter dans une liste de kits'
    save_success: 'Votre liste a été enregistrée'
    table:
        days: jours
        delivery_time_item: 'Délai de livraison'
        delivery_time_wishlist: 'Délai de livraison de la liste'
    title: 'Liste de kits'
'yes': Oui
seller:
    general_condition: 'Conditions générales de vente'
