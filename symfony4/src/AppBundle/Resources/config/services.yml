services:

    AppBundle\Repository\MerchantOrderRepository:
        class: AppBundle\Repository\MerchantOrderRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\MerchantOrder' ]

    AppBundle\Repository\OrderItemRepository:
        class: AppBundle\Repository\OrderItemRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\OrderItem' ]

    AppBundle\Repository\CompanyCatalogRepository:
        class: AppBundle\Repository\CompanyCatalogRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\CompanyCatalog' ]

    AppBundle\Repository\CompanyRepository:
        class: AppBundle\Repository\CompanyRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\Company' ]

    AppBundle\Repository\OrderRepository:
        class: AppBundle\Repository\OrderRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\Order' ]

    AppBundle\Repository\AddressRepository:
        class: AppBundle\Repository\AddressRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\Address' ]

    AppBundle\Repository\BafvRequestRepository:
        class: AppBundle\Repository\BafvRequestRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\BafvRequest' ]
        calls:
            -   method: "setPaginator"
                arguments: [ "@knp_paginator" ]

    AppBundle\Repository\UserBafvMerchantListRepository:
        class: AppBundle\Repository\UserBafvMerchantListRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\UserBafvMerchantList' ]

    AppBundle\Repository\ShippingPointRepository:
        class: AppBundle\Repository\ShippingPointRepository
        public: true
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\ShippingPoint' ]

    AppBundle\Repository\TvaGroupRepository:
        class: AppBundle\Repository\TvaGroupRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\TvaGroup' ]

    AppBundle\Repository\TvaRateRepository:
        class: AppBundle\Repository\TvaRateRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\TvaRate' ]

    AppBundle\Repository\UserRepository:
        class: AppBundle\Repository\UserRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\User' ]

    AppBundle\Repository\VendorRepository:
        class: AppBundle\Repository\VendorRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\Vendor' ]

    AppBundle\Repository\WishListRepository:
        class: AppBundle\Repository\WishListRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\WishList' ]

    AppBundle\Repository\WishListItemRepository:
        class: AppBundle\Repository\WishListItemRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\WishListItem' ]

    AppBundle\Repository\MerchantRepository:
        class: AppBundle\Repository\VendorRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\Merchant' ]
        calls:
            -   method: "setPaginator"
                arguments: [ "@knp_paginator" ]

    AppBundle\Repository\InvoiceRepository:
        class: AppBundle\Repository\InvoiceRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\Invoice' ]

    AppBundle\Repository\CreditNoteRepository:
        class: AppBundle\Repository\CreditNoteRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\CreditNote' ]

    AppBundle\Repository\CartRepository:
        class: AppBundle\Repository\CartRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\Cart' ]

    AppBundle\Repository\BddFileRepository:
        class: AppBundle\Repository\BddFileRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\BddFile' ]

    AppBundle\Repository\CartShippingOptionRepository:
        class: AppBundle\Repository\CartShippingOptionRepository
        factory: [ '@doctrine.orm.default_entity_manager', getRepository ]
        arguments: [ 'AppBundle\Entity\CartShippingOption' ]

    AppBundle\Repository\StorageRepository:
        class: AppBundle\Repository\StorageRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\Storage' ]

    AppBundle\Repository\CountryRepository:
        class: AppBundle\Repository\CountryRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\Country' ]

    AppBundle\Repository\PurchaseRequestRepository:
        class: AppBundle\Repository\PurchaseRequestRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\PurchaseRequest' ]

    AppBundle\Repository\PurchaseRequestItemRepository:
        class: AppBundle\Repository\PurchaseRequestItemRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\PurchaseRequestItem' ]

    # Provide repository to fetch node entities
    AppBundle\Repository\SettingRepository:
        class: AppBundle\Repository\SettingRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\Setting' ]

    AppBundle\Util\SettingsProvider:
        autowire: false
        class: AppBundle\Util\SettingsProvider
        arguments:
            - "%app.settings%"

    # Provide a mapper for the company identification validation
    AppBundle\Form\Rule\Mapping\CompanyIdentificationRule:
        class: AppBundle\Form\Rule\Mapping\CompanyIdentificationRule
        tags:
            - { name: form_rule_constraint_mapper }

    # Provide repository to fetch redirect entities
    AppBundle\Repository\RedirectRepository:
        class: AppBundle\Repository\RedirectRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\Redirect' ]

    # Provide repository to fetch node entities
    AppBundle\Repository\NodeRepository:
        class: AppBundle\Repository\NodeRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\Node' ]

    AppBundle\EventSubscriber\BuyerRegistrationSubscriber:
        class: AppBundle\EventSubscriber\BuyerRegistrationSubscriber
        arguments:
            - "@router"
            - "@security.token_storage"
        tags:
            - { name: kernel.event_subscriber }

    AppBundle\EventSubscriber\KernelEventSubscriber:
        class: AppBundle\EventSubscriber\KernelEventSubscriber
        autowire: true
        tags:
            - { name: kernel.event_subscriber }

    AppBundle\EventListener\SessionExpiredListener:
        class: AppBundle\EventListener\SessionExpiredListener
        autowire: true
        tags:
            - {
                name: kernel.event_listener,
                event: kernel.request,
                method: onKernelRequest
              }
