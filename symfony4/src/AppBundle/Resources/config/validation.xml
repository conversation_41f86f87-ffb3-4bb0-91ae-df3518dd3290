<constraint-mapping xmlns="http://symfony.com/schema/dic/constraint-mapping"
                    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                    xsi:schemaLocation="http://symfony.com/schema/dic/constraint-mapping
        http://symfony.com/schema/dic/constraint-mapping/constraint-mapping-1.0.xsd">
  <class name="AppBundle\Entity\User">
    <property name="plainPassword">
      <constraint name="NotBlank">
        <option name="message">fos_user.password.blank</option>
        <option name="groups">
          <value>Registration</value>
          <value>ResetPassword</value>
          <value>ChangePassword</value>
          <value>merchant_registration</value>
        </option>
      </constraint>
      <constraint name="Length">
        <option name="min">8</option>
        <option name="max">4096</option>
        <option name="minMessage">fos_user.password.short</option>
        <option name="groups">
          <value>Registration</value>
          <value>ResetPassword</value>
          <value>ChangePassword</value>
          <value>merchant_registration</value>
        </option>
      </constraint>
      <constraint name="AppBundle\Validator\Constraints\SecurePassword">
          <option name="messageLower">user.password.lower</option>
          <option name="messageUpper">user.password.upper</option>
          <option name="messageNumeric">user.password.num</option>
          <option name="messageSpecial">user.password.special</option>
          <option name="groups">
              <value>Registration</value>
              <value>ResetPassword</value>
              <value>ChangePassword</value>
              <value>merchant_registration</value>
          </option>
      </constraint>
    </property>
    <property name="email">
      <constraint name="NotBlank">
        <option name="message">fos_user.email.blank</option>
        <option name="groups">
          <value>merchant_registration</value>
          <value>user_registration</value>
        </option>
      </constraint>
      <constraint name="Length">
        <option name="min">10</option>
        <option name="minMessage">fos_user.email.short</option>
        <option name="max">180</option>
        <option name="maxMessage">fos_user.email.long</option>
        <option name="groups">
          <value>Registration</value>
          <value>merchant_registration</value>
          <value>user_registration</value>
        </option>
      </constraint>
      <constraint name="Email">
        <option name="message">fos_user.email.invalid</option>
        <option name="groups">
          <value>merchant_registration</value>
          <value>user_registration</value>
        </option>
      </constraint>
    </property>
    <property name="lastname">
      <constraint name="NotBlank">
        <option name="message">user.lastname.blank</option>
        <option name="groups">
          <value>Registration</value>
          <value>merchant_registration</value>
        </option>
      </constraint>
      <constraint name="Length">
        <option name="min">1</option>
        <option name="minMessage">user.lastname.short</option>
        <option name="max">50</option>
        <option name="maxMessage">user.lastname.long</option>
        <option name="groups">
          <value>Registration</value>
          <value>merchant_registration</value>
        </option>
      </constraint>
    </property>
    <property name="firstname">
      <constraint name="NotBlank">
        <option name="message">user.firstname.blank</option>
        <option name="groups">
          <value>Registration</value>
          <value>merchant_registration</value>
        </option>
      </constraint>
      <constraint name="Length">
        <option name="min">1</option>
        <option name="minMessage">user.firstname.short</option>
        <option name="max">50</option>
        <option name="maxMessage">user.firstname.long</option>
        <option name="groups">
          <value>Registration</value>
          <value>merchant_registration</value>
        </option>
      </constraint>
    </property>
  </class>
</constraint-mapping>
