AppBundle\Entity\User:
    user_{1..100}:
        username: <name()>
        username_canonical: <name()>
        email: <email()>
        email_canonical: <email()>
        firstname: <firstname()>
        lastname: <lastname()>
        mainPhoneNumber: <phoneNumber()>
        optionalPhoneNumber: <phoneNumber()>
        enabled: <boolean()>
        emailConfirmed: <boolean()>
        salt:
        password: 'pascal'
        last_login:
        confirmation_token:
        password_requested_at:
        roles: ['ROLE_USER']
        company: '@company*'








