<?php
namespace AppBundle\Doctrine;

use Doctrine\ORM\Mapping\ClassMetadata;
use Doctrine\ORM\Query\Filter\SQLFilter;

class NodeLanguageFilter extends SQLFilter
{
    /**
     * Filter to collect only nodes in specific language
     *
     * @param ClassMetaData $targetEntity
     * @param string $targetTableAlias
     *
     * @return string The constraint SQL if there is available, empty string otherwise.
     */
    public function addFilterConstraint(ClassMetadata $targetEntity, $targetTableAlias)
    {
        if ($targetEntity->getReflectionClass()->name != 'AppBundle\Entity\NodeContent') {
            return '';
        }

		$lang = $this->getParameter('lang');

        return sprintf('%s.lang = %s', $targetTableAlias, $lang);
    }
}
