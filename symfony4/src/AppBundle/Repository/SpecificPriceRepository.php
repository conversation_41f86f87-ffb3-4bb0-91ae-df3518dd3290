<?php
/**
 * Created by PhpStorm.
 * User: PCH07650
 * Date: 15/05/2018
 * Time: 16:25
 */

namespace AppBundle\Repository;


use Doctrine\ORM\EntityRepository;

class SpecificPriceRepository extends EntityRepository
{
    public function findExistingPrice($buyerReference, $vendorRef, $incoterm, $country)
    {
        $query = $this->createQueryBuilder('sp');
        $query
            ->where('sp.companyIdentification = :buyerReference')
            ->andWhere('sp.vendorReference = :vendorRef')
            ->andWhere('sp.incoterm = :incoterm')
            ->andWhere('sp.country = :country')
            ->setParameter('buyerReference', $buyerReference)
            ->setParameter('vendorRef', $vendorRef)
            ->setParameter('incoterm', $incoterm)
            ->setParameter('country', $country);

        return $query->getQuery()->getResult();

    }

}
