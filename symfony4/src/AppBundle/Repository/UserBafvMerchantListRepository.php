<?php

namespace AppBundle\Repository;

use AppBundle\Entity\UserBafvMerchantList;
use Doctrine\ORM\EntityRepository;

class UserBafvMerchantListRepository extends EntityRepository
{
    public function save(UserBafvMerchantList $bafvMerchantList)
    {
        $this->_em->persist($bafvMerchantList);
        $this->_em->flush();
    }

    public function delete(UserBafvMerchantList $bafvMerchantList) {
        $this->_em->remove($bafvMerchantList);
        $this->_em->flush();
    }

    public function isMerchantAuthorizedForCompany($company, int $merchantId = null): bool
    {
        $q = $this->_em
            ->createQueryBuilder()
            ->select('m.merchantId')
            ->from(\AppBundle\Entity\UserBafvMerchantList::class, 'm','m.merchantId')
            ->where('m.company = :company')->setParameter('company', $company)
            ->andWhere('m.merchantId = :merchantId')->setParameter('merchantId', $merchantId);

        $merchant = $q->getQuery()->getOneOrNullResult();

        return ($merchant !== null);
    }
}
