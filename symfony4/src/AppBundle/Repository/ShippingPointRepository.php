<?php

namespace AppBundle\Repository;

use AppBundle\Entity\ShippingPoint;
use Doctrine\ORM\EntityRepository;

class ShippingPointRepository extends EntityRepository
{
    /**
     * Retrieve shipping points for a company
     *
     * @param int $companyId company ID
     * @return array contains shipping points for company
     */
    public function getShippingPointsByCompany(int $companyId)
    {
        $qb = $this->createQueryBuilder('sp');
        $qb->leftJoin("sp.site", 's');
        $qb->join('sp.address', 'a');
        $qb->leftJoin('s.company', 'c');
        $qb->where('c.id=:companyId');
        $qb->setParameter('companyId', $companyId);
        return $qb->getQuery()->getResult();
    }

    public function byId(int $id, int $siteId): ShippingPoint
    {
        $shippingPoint = $this->createQueryBuilder('sp')
            ->leftJoin('sp.site', 's')
            ->join('sp.address', 'a')
            ->where('sp.address = :addressId')
            ->andWhere('sp.site = :siteId')
            ->setParameters(['addressId' => $id, 'siteId' => $siteId])
            ->getQuery()
            ->getSingleResult()
        ;

        if (null === $shippingPoint) {
            throw new \Exception(sprintf('The shipping point is not found for the %d id', $id));
        }

        return $shippingPoint;
    }
}
