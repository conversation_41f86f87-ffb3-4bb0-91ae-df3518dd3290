<?php

namespace AppBundle\Repository;
use AppBundle\Entity\Company;
use Doctrine\ORM\EntityNotFoundException;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\QueryBuilder;

class CompanyRepository extends EntityRepository
{
    /**
     * @param $companyId
     * @param $userId
     * @return int|mixed|string|null
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function isUserExistsInCompany($companyId, $userId) {
        /**
         * @var QueryBuilder
         */
        $qb =
            $this->createQueryBuilder('c')
                ->select('c')
                ->join('c.users', 'u')
                ->where("u.id = :userId")
                ->andWhere("u.company = :companyId")
                ->setParameter('userId', $userId)
                ->setParameter('companyId', $companyId);




        return $qb->getQuery()->getOneOrNullResult();
    }

    /**
     * * get a company by its izberg user id
     * @param int $izbergUserId
     * @return Company|null
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function findCompanyByIzbergUserId(int $izbergUserId): ?Company
    {
        return $this->getEntityManager()->createQueryBuilder()
            ->select("c")
            ->from(Company::class, "c")
            ->where("c.izbergUserId = :izbergUserId")
            ->setParameter("izbergUserId", $izbergUserId)
            ->getQuery()->getOneOrNullResult();
    }

    public function findAllInformationOfUser(int $userId): ?Company
    {
        return $this->createQueryBuilder('c')
            ->addSelect('u')
            ->addSelect('s')
            ->join('c.users', 'u')
            ->leftJoin('c.sites', 's')
            ->where('u.id = :userId')
            ->setParameter('userId', $userId)
            ->getQuery()
            ->getOneOrNullResult()
         ;
    }

    public function getCompanyById(int $izbergId): ?Company
    {
        $company = $this->findOneBy(['izbergUserId' => $izbergId]);
        if (!$company instanceof Company) {
            throw new EntityNotFoundException();
        }

        return $company;
    }
}
