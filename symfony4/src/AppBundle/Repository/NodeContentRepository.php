<?php
/**
 * Created by PhpStorm.
 * User: CMA15450
 * Date: 18/01/2018
 * Time: 11:07
 */

namespace AppBundle\Repository;


use AppBundle\Entity\Node;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\NonUniqueResultException;

class NodeContentRepository extends EntityRepository
{

    public function findPageBySlugAndLanguage ($slug, $lang){
        $qb = $this->createQueryBuilder('c');
        $qb->leftJoin('c.node', 'n')
            ->where('n.slug = :slug and c.lang = :lang and n.type = :type')
            ->setParameter('slug', $slug)
            ->setParameter('lang', $lang)
            ->setParameter('type', Node::TYPE_PAGE);

        try {
            return $qb->getQuery()->getOneOrNullResult();
        }catch(NonUniqueResultException $e){
            return null;
        }

    }
}
