<?php

namespace AppBundle\Repository;

use Doctrine\ORM\EntityRepository;
use AppBundle\Entity\Node;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\QueryBuilder;

class NodeRepository extends EntityRepository
{
    const YM = 'Ym';

    /**
     * find all node for the specified type
     * @param $type
     * @return \Generator
     */
    public function findAllByType($type){
        $iterator = $this->createQueryBuilder("n")->where("n.type = :type")
            ->setParameter("type", $type)->getQuery()->iterate();
        foreach ($iterator as $row){
            yield $row[0];
        }
    }

    /**
     * Find a content by its slug
     * @param $slug
     * @return mixed
     */
    public function findBySlug($slug)
    {
        $query =
            $this->createQueryBuilder('n')
                ->where('n.slug = :slug')
                ->setMaxResults(1)
                ->setParameter('slug', $slug);
        return $query->getQuery()->getOneOrNullResult();
    }

    /**
     * Find nodes by the type
     * @param $node_type
     * @return array
     */
    public function findByType($node_type)
    {
        $query =
            $this->createQueryBuilder('n')
                ->where('n.type = :type')
                ->setParameter('type', $node_type); // Use a constant Node::Status_Published or something

        return $query->getQuery()->getResult();
    }

    /**
     * find slides to display on the homepage
     * @return array
     */
    public function findSlidesHomepage()
    {
        $query =
            $this->createQueryBuilder('n')
                ->where('n.type = :type and n.status = :status')
                ->setParameter('type', 'slider')
                ->setParameter('status', Node::STATUS_PUBLISHED)
                ->orderBy('n.orderNode', 'ASC');
        return $query->getQuery()->getResult();
    }

    /**
     * find logos to display on the homepage
     * @return array
     */
    public function findLogosHomepage()
    {
        $query =
            $this->createQueryBuilder('n')
                ->where('n.type = :type and n.status = :status')
                ->setParameter('type', 'logo')
                ->setParameter('status', Node::STATUS_PUBLISHED)
                ->orderBy('n.orderNode', 'ASC');
        return $query->getQuery()->getResult();
    }

    /**
     * Find nodes of type 'testimonial', ordered by date, filtered by max_age, only the max_items one
     * @param $max_age
     * @param $max_items
     * @return array
     */
    public function findTestimonialsByAgeAndMax($max_age, $max_items)
    {
        /* Get published + testimonials types Nodes */
        $query =
            $this->createQueryBuilder('n')
                ->leftJoin('n.testimonialDetails', 'd')
                ->where(' n.type = :type and n.status = :status and ( d.publishedDate >= DATE_SUB(CURRENT_DATE(), :max_age, \'MONTH\') )')
                ->setParameter('type', Node::TYPE_TESTIMONIAL)
                ->setParameter('status', Node::STATUS_PUBLISHED)
                ->setParameter('max_age', $max_age)
                ->orderBy('d.publishedDate', 'DESC')
                ->setMaxResults($max_items);

        return $query->getQuery()->getResult();
    }


    /**
     * find a node object that represents an email
     * @param string $slug the identifier of the email
     * @param string $lang the language of the email
     * @return Node[]
     */
    public function findEmailBySlugAndLanguage ($slug, $lang): array
    {
        $qb = $this->queryFindEmailBySlugAndLanguage($slug, $lang);
        return $qb->getQuery()->getResult();
    }

    public function findOneEmailBySlugAndLanguage($slug, $lang)
    {
        $qb = $this->queryFindEmailBySlugAndLanguage($slug, $lang);

        try {
            return $qb->getQuery()->getOneOrNullResult();
        }catch(NonUniqueResultException $e){
            return null;
        }
    }

    private function queryFindEmailBySlugAndLanguage($slug, $lang): QueryBuilder
    {
        $qb = $this->createQueryBuilder('n');
        $qb->leftJoin('n.content', 'c')
            ->where('n.slug = :slug and c.lang = :lang and n.type = :type')
            ->setParameter('slug', $slug)
            ->setParameter('lang', $lang)
            ->setParameter('type', Node::TYPE_EMAIL);

        return $qb;
    }
}
