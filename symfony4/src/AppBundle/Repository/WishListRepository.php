<?php

namespace AppBundle\Repository;

use AppBundle\Entity\WishList;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\EntityRepository;

class WishListRepository extends EntityRepository
{
    /**
     * @param $wishList
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function save(WishList $wishList)
    {
        $this->getEntityManager()->persist($wishList);
        $this->getEntityManager()->flush();
    }

    /**
     * @param $wishList
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function delete(WishList $wishList)
    {
        $this->getEntityManager()->remove($wishList);
        $this->getEntityManager()->flush();
    }

    public function getUserWishList($userId, ?string $currency = null)
    {
        $qb =
            $this->createQueryBuilder('w')
                ->select('w')
                ->where("w.user =:userId")
                ->setParameter('userId', $userId);

        if ($currency) {
            $qb->andWhere('w.currency = :currency')
                ->setParameter('currency', $currency, Types::STRING);
        }

        return $qb->getQuery()->getResult();
    }
}
