<?php
/**
 * Created by PhpStorm.
 * User: AQU04740
 * Date: 27/03/2017
 * Time: 10:32
 */

namespace AppBundle\Repository;


class CityRepository extends \Doctrine\ORM\EntityRepository
{
    public function findByCityLike($term, $country)
    {
        return $this->createQueryBuilder('z')
            ->select('distinct z.city, z.label, z.zipcode')
            ->where('z.label LIKE :label and z.country =:country')
            ->setParameter('label', "%".$term . '%')
            ->setParameter('country', strtolower($country))
            ->getQuery()
            ->getResult();

    }

    public function deleteCountry($country)
    {
        return $this->createQueryBuilder('z')
            ->delete()
            ->where('z.country LIKE :country')
            ->setParameter('country', $country)
            ->getQuery()
            ->getResult();
    }
}
