<?php

namespace AppBundle\Repository;

use AppBundle\Entity\CreditNote;
use Doctrine\ORM\EntityRepository;

class CreditNoteRepository extends EntityRepository
{
    /**
     * @param $creditNote
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function save(CreditNote $creditNote)
    {
        $this->_em->persist($creditNote);
        $this->_em->flush();
    }
}
