<?php
/**
 * Created by PhpStorm.
 * User: AQU04740
 * Date: 29/11/2017
 * Time: 15:08
 */

namespace AppBundle\Repository;

use Doctrine\ORM\EntityRepository;


class AddressRepository  extends EntityRepository
{

    /**
     * Get cost center by izberg address id
     *
     * @param $izbAddressId
     * @return mixed
     */
    public function getCostCenterByIzbergAddressId($izbAddressId)
    {
        $qb =
            $this->getEntityManager()
                ->createQueryBuilder()
                ->select('s')
                ->from(\AppBundle\Entity\ShippingPoint::class, 's')
                ->join("s.address", 'a')
                ->where("a.izbergAddressId = :izbergAddressId")
                ->setParameter('izbergAddressId', $izbAddressId);

        return $qb->getQuery()->getOneOrNullResult();
    }
}
