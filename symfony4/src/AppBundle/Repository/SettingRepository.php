<?php
/**
 * Created by PhpStorm.
 * User: CMA15450
 * Date: 08/03/2017
 * Time: 12:09
 */

namespace AppBundle\Repository;


use Doctrine\ORM\EntityRepository;


class SettingRepository  extends EntityRepository
{

    const DOMAIN = 'domain';
    const DOMAIN_PARAM = 's.domain = :domain';


    public function findByDomain($domain) {
        $query =
            $this
                ->createQueryBuilder('s')
                ->where(self::DOMAIN_PARAM)
                ->orderBy('s.id', 'asc')
                ->setParameter(self::DOMAIN, $domain); // Use a constant Node::Status_Published or something

        return $query->getQuery()->getResult();
    }

    public function findByDomainAndKey($domain, $key) {
        $query =
            $this
                ->createQueryBuilder('s')
                ->where(self::DOMAIN_PARAM)
                ->andWhere('s.name = :name')
                ->setMaxResults(1)
                ->setParameter(self::DOMAIN, $domain)
                ->setParameter('name', $key);

        return $query->getQuery()->getOneOrNullResult();
    }

    public function findByDomainAndKeys($domain, $keys = array()) {
        $query =
            $this
                ->createQueryBuilder('s')
                ->where(self::DOMAIN_PARAM)
                ->andWhere('s.name in (:names)')
                ->orderBy('s.id', 'asc')
                ->setParameter(self::DOMAIN, $domain)
                ->setParameter('names', $keys);

        return $query->getQuery()->getResult();
    }

    public function findByDomainAndTag($domain, $tag) {
        $query =
            $this
                ->createQueryBuilder('s')
                ->where(self::DOMAIN_PARAM)
                ->andWhere('s.tag LIKE %:tag%')
                ->orderBy('s.id', 'asc')
                ->setParameter(self::DOMAIN, $domain)
                ->setParameter('names', $tag);

        return $query->getQuery()->getResult();
    }
}
