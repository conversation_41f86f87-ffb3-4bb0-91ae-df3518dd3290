<?php

namespace AppBundle\Repository;

use AppBundle\Entity\Cart;
use Doctrine\ORM\EntityRepository;

/**
 * Class CartRepository
 * @package AppBundle\Repository
 */
class CartRepository extends EntityRepository
{
    public function getCartByWPSTransactionId($transactionId){
        return $this->getEntityManager()->createQueryBuilder()
            ->select("c")
            ->from(\AppBundle\Entity\Cart::class, "c")
            ->where("c.wpsTransactionId = :transactionId")
            ->setParameter("transactionId", $transactionId)
            ->getQuery()->getOneOrNullResult();
    }

    public function getCartByOrderId($orderId){
        return $this->getEntityManager()->createQueryBuilder()
            ->select("c")
            ->from(\AppBundle\Entity\Cart::class, "c")
            ->where("c.orderId = :orderId")
            ->setParameter("orderId", $orderId)
            ->getQuery()->getOneOrNullResult();
    }


    public function getPendingCartByCompanyId($companyId){
        return $this->getEntityManager()->createQueryBuilder()
            ->select("c")
            ->from(\AppBundle\Entity\Cart::class, "c")
            ->leftJoin('c.currentUser', 'u')
            ->leftJoin('u.company', 'co')
            ->where('co.id = :companyId')
            ->andWhere('c.status = :statusAssign OR c.status = :statusRejected')
            ->setParameter('companyId', $companyId)
            ->setParameter('statusAssign', Cart::STATUS_ASSIGN)
            ->setParameter('statusRejected', Cart::STATUS_REJECTED)
            ->getQuery()->getResult();
    }

    public function getPendingCartByUserId($userId){
        return $this->getEntityManager()->createQueryBuilder()
            ->select("c")
            ->from(\AppBundle\Entity\Cart::class, "c")
            ->innerJoin('c.currentUser', 'u')
            ->where('u.id = :userId')
            ->andWhere('c.status = :statusAssign or c.status = :statusRejected')
            ->setParameter('userId', $userId)
            ->setParameter('statusAssign', Cart::STATUS_ASSIGN)
            ->setParameter('statusRejected', Cart::STATUS_REJECTED)
            ->getQuery()->getResult();
    }

    public function saveCartOrder(\AppBundle\Model\Cart\Cart $cart): bool
    {
        /** @var Cart $cartEntity */
        $cartEntity = $this->find($cart->getId());

        if (!$cartEntity || !$cart->getOrder()) {
            return false;
        }

        $cartEntity->setOrderId($cart->getOrder()->getIzbergId());
        $this->getEntityManager()->flush();

        return true;
    }

    public function getReference(int $cartId)
    {
        return $this->getEntityManager()->getReference(Cart::class,$cartId);
    }

    public function save(Cart $cart): bool
    {
        $this->getEntityManager()->persist($cart);
        $this->getEntityManager()->flush();

        return true;
    }
}
