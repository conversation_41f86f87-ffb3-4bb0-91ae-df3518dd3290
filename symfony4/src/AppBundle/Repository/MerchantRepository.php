<?php

namespace AppBundle\Repository;

use AppBundle\Entity\Merchant;
use AppBundle\Entity\User;
use Doctrine\ORM\EntityRepository;
use Knp\Component\Pager\Pagination\PaginationInterface;
use Knp\Component\Pager\PaginatorInterface;

class MerchantRepository extends EntityRepository
{
    private ?PaginatorInterface $paginator = null;

    /**
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function save(Merchant $merchant)
    {
        $this->getEntityManager()->persist($merchant);
        $this->getEntityManager()->flush();
    }

    /**
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function update(Merchant $merchant, ?User $author = null)
    {
        if ($author) {
            $merchant->setActionBy($author);
            $merchant->setUpdatedAt(new \DateTimeImmutable());
        }

        $this->getEntityManager()->merge($merchant);
        $this->getEntityManager()->flush();
    }

    public function paginatedMerchants($data, int $page = 0, int $numberPerPage = 10): PaginationInterface
    {
        $qb = $this->getEntityManager()->createQueryBuilder();
        $qb->select('e')
            ->from($this->getEntityName(), 'e')
            ->where("1=1")
            ->addOrderBy("e.status", 'DESC')
            ->addOrderBy("e.registrationDate", "ASC");

        if (!empty($data['status']) && $data['status'] != 'all') {
            $qb->andWhere('e.status = :status');
            $qb->setParameter('status', $data['status']);
        }

        if(!empty($data['id'])){
            $qb->andWhere('e.id = :id');
            $qb->setParameter('id', $data['id']);
        }

        if(!empty($data['name'])){
            $qb->andWhere('e.name LIKE :name');
            $qb->setParameter('name', '%'.$data['name'].'%');
        }

        if(!empty($data['identification'])){
            $qb->andWhere('e.identification LIKE :identification');
            $qb->setParameter('identification', '%'.$data['identification'].'%');
        }

        if(!empty($data['firstname'])){
            $qb->andWhere('e.firstname LIKE :firstname');
            $qb->setParameter('firstname', '%'.$data['firstname'].'%');
        }

        if(!empty($data['lastname'])){
            $qb->andWhere('e.lastname LIKE :lastname');
            $qb->setParameter('lastname', '%'.$data['lastname'].'%');
        }

        return $this->paginator->paginate($qb->getQuery(), $page, $numberPerPage, []);
    }

    public function setPaginator(PaginatorInterface $paginator)
    {
        $this->paginator = $paginator;
    }

    public function getReference(int $merchantId)
    {
        return $this->getEntityManager()->getReference(Merchant::class,$merchantId);
    }
}
