<?php
/**
 * Created by PhpStorm.
 * User: CMA15450
 * Date: 28/04/2017
 * Time: 10:08
 */

namespace AppBundle\Repository;

use Doctrine\ORM\EntityRepository;


class MenuRepository  extends EntityRepository
{

    public function findByName($name)
    {
        $query =
            $this
                ->createQueryBuilder('m')
                ->where('m.name = :name')
                ->setMaxResults(1)
                ->setParameter('name', $name);

        return $query->getQuery()->getOneOrNullResult();
    }

}
