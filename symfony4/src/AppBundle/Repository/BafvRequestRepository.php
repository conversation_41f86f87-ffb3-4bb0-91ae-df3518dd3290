<?php

namespace AppBundle\Repository;

use AppBundle\Entity\BafvRequest;
use AppBundle\Entity\Company;
use Doctrine\ORM\EntityRepository;
use Knp\Component\Pager\Pagination\PaginationInterface;
use Knp\Component\Pager\Paginator;
use Knp\Component\Pager\PaginatorInterface;

class BafvRequestRepository extends EntityRepository
{
    private ?PaginatorInterface $paginator = null;

    public function findByFilter(int $merchantId = null, string $companyName = null)
    {
        /** @psalm-suppress TooManyArguments */
        $q = $this->createQueryBuilder('b')
            ->select('c.identification', 'c.name', 'b.createdAt', 'b.status', 'b.id')
            ->leftJoin('b.company', 'c');

            $q->where('b.merchantId = :merchantId')->setParameter('merchantId', $merchantId);


        if (!empty($companyName)) {
            $q->andWhere('c.name LIKE :companyName')->setParameter('companyName', '%' . $companyName . '%');
        }

        $results = $q->getQuery()->getResult();

        return $results;
    }

    public function save(BafvRequest $bafvRequest)
    {
        $entityManager = $this->getEntityManager();
        $entityManager->persist($bafvRequest);
        $entityManager->flush();
    }

    public function paginatedRequests($data, int $page=0, int $numberPerPage = 10): PaginationInterface
    {

        $qb = $this->getEntityManager()->createQueryBuilder()
            ->select('r')
            ->from(BafvRequest::class, 'r')
            ->join(Company::class, 'c')
            ->where("1=1")
            ->addOrderBy("r.id", 'DESC');

        if (array_key_exists('status', $data) && $data['status'] !== 'all') {
            $qb->andWhere('r.status = :status');
            $qb->setParameter('status', $data['status']);
        }

        if(!empty($data['id'])){
            $qb->andWhere('r.id = :id');
            $qb->setParameter('id', $data['id']);
        }

        if(!empty($data['vendor_company_name'])){
            $qb->andWhere('r.merchantName LIKE :vendor_company_name');
            $qb->setParameter('vendor_company_name', '%'.$data['vendor_company_name'].'%');
        }

        if(!empty($data['buyer_code'])){
            $qb->andWhere('r.company = c');
            $qb->andWhere('c.identification LIKE :identification');
            $qb->setParameter('identification', '%'.$data['buyer_code'].'%');
        }

        if(!empty($data['buyer_name'])){
            $qb->andWhere('r.company = c');
            $qb->andWhere('c.name LIKE :buyer_name');
            $qb->setParameter('buyer_name', '%'.$data['buyer_name'].'%');
        }

        return $this->paginator->paginate($qb->getQuery(), $page, $numberPerPage, []);
    }

    public function setPaginator(PaginatorInterface $paginator)
    {
        $this->paginator = $paginator;
    }
}
