<?php

namespace AppBundle\Repository;

use AppBundle\Entity\Company;
use Doctrine\ORM\EntityRepository;

class DocumentRepository extends EntityRepository
{
    /**
     * get the owner company or site for a document: used for security checks
     * @param int $docId the id of the document
     * @return mixed the site or the company
     */
    public function getSiteOrCompanyByTypeAndId($docId)
    {
        $qb = $this->getEntityManager()->createQueryBuilder()
            ->select("c")
            ->from(Company::class, "c")
            ->leftJoin('c.documents', 'docs')
            ->andWhere("docs.id = :docId")
            ->setParameter("docId", $docId);

        return $qb->getQuery()->getResult();
    }
}
