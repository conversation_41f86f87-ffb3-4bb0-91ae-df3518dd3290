<?php
/**
 * Created by PhpStorm.
 * User: PCH07650
 * Date: 25/05/2018
 * Time: 14:02
 */

namespace AppBundle\Repository;


use AppBundle\Entity\Cart;
use Doctrine\ORM\EntityRepository;

class CartHistoricRepository extends EntityRepository
{
    /**
     * Get historic from a cart ID with order by date DESC
     * @param int $cartId
     * @return array
     */
    public function getCartHistoric($cartId): array {
        $qb = $this->createQueryBuilder('ch')
            ->leftJoin('ch.cart', 'c')
            ->where('c.id=:cartId')
            ->orderBy('ch.date', 'ASC')
            ->setParameter('cartId', $cartId);
        return $qb->getQuery()->getResult();
    }

    public function getCartAssignmentHistoric($cartId): array{
        $qb = $this->createQueryBuilder('ch')
            ->leftJoin('ch.cart', 'c')
            ->where('c.id=:cartId')
            ->andWhere('ch.status = :status')
            ->setParameter('status', Cart::STATUS_ASSIGN)
            ->orderBy('ch.date', 'ASC')
            ->setParameter('cartId', $cartId);
        return $qb->getQuery()->getResult();
    }

    /**
     * return all the cart for the specified company
     * @param int $companyId the identifier of the company
     * @return array cart history for the company
     */
    public function getAllPendingCartForCompany(int $companyId): array{
        return $this->createQueryBuilder('ch')
            ->select("cart.id")
            ->leftJoin('ch.cart', 'cart')
            ->leftJoin('cart.site', 'site')
            ->leftJoin('site.company', 'company')
            ->where('company.id=:companyId')
            ->andWhere('cart.status=:assignStatus or cart.status=:rejectedStatus')
            ->setParameter('companyId', $companyId)
            ->setParameter('assignStatus', Cart::STATUS_ASSIGN)
            ->setParameter('rejectedStatus', Cart::STATUS_REJECTED)
            ->orderBy('cart.id', 'DESC')
            ->distinct()
            ->getQuery()
            ->getResult();
    }

    /**
     * return all the cart id for the specified user
     * @param int $userId the identifier of the user
     * @return array cart id for the user
     */
    public function getAllPendingCartForUser(int $userId): array{
        return $this->createQueryBuilder('ch')
            ->select('cart.id')
            ->leftJoin('ch.user', 'user')
            ->leftJoin('ch.assignedUser', 'assignedUser')
            ->leftJoin('ch.cart', 'cart')
            ->where('cart.status=:assignStatus OR cart.status=:rejectedStatus')
            ->andWhere("ch.user = :userId OR ch.assignedUser = :userId")
            ->setParameter('rejectedStatus', Cart::STATUS_REJECTED)
            ->setParameter('assignStatus', Cart::STATUS_ASSIGN)
            ->setParameter("userId", $userId)
            ->orderBy('cart.id', 'DESC')
            ->distinct()
            ->getQuery()
            ->getResult();
    }
}
