<?php

namespace AppBundle\Repository;

use AppBundle\Entity\Company;
use AppBundle\Entity\CompanyCatalog;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\ParameterType;
use Doctrine\ORM\EntityRepository;

class CompanyCatalogRepository  extends EntityRepository
{
    public function chunk(array $criteria, $limit, $callback)
    {
        $offset = 0;
        while($result = $this->findByCriteriaArrayResult($criteria, $limit, $offset)) {
            array_walk($result, $callback);
            $offset = $offset + $limit;
            unset($result);
        }
    }

    public function findByCriteriaArrayResult(array $criteria, int $limit = 10, int $offset = 10)
    {
        $connection = $this->getConnection();

        $where = [];
        $bindValues = [];

        if (key_exists('company', $criteria)) {
            $company = $criteria['company'];
            $where[] = 'company_id = :company_id';
            $bindValues[] = ['company_id', $company->getId(), ParameterType::INTEGER];
        }

        if (key_exists('valid', $criteria)) {
            $where[] = 'valid = :valid';
            $bindValues[] = ['valid', $criteria['valid'], ParameterType::BOOLEAN];
        }

        $sql = 'SELECT external_ref, ref, manufacturer_name, designation_reference FROM company_catalog';
        if (count($where)) {
            $sql .= ' WHERE '. implode(' AND ', $where);
        }
        $sql .= ' LIMIT :offset, :limit';

        $statement = $connection->prepare($sql);
        foreach ($bindValues as [$parameter, $value, $parameterType]) {
            $statement->bindValue($parameter, $value, $parameterType);
        }
        $statement->bindValue('offset', $offset, ParameterType::INTEGER);
        $statement->bindValue('limit', $limit, ParameterType::INTEGER);
        $query = $statement->executeQuery();

        $result = $query->fetchAll();
        unset($statement);
        return $result;
    }

    public function findByCompanyIdAndReference(int $companyId, string $companyProductReference, bool $strictSearch = true): array
    {
        $queryBuilder = $this->getEntityManager()->createQueryBuilder()
            ->select('referenceMapping')
            ->from(CompanyCatalog::class, 'referenceMapping')
            ->where('referenceMapping.company = :companyId');

        $whereReferenceClause = 'referenceMapping.ref = :companyProductReference';
        if (!$strictSearch) {
            $whereReferenceClause = 'referenceMapping.ref like :companyProductReference';
            $companyProductReference = '%'.$companyProductReference.'%';
        }

        return $queryBuilder->andWhere($whereReferenceClause)
            ->setParameters(['companyId' => (string)$companyId, 'companyProductReference' => $companyProductReference])
            ->getQuery()
            ->getResult();
    }

    public function findTopMismatchReferences(int $offset = 0, int $limit = 100): array
    {
        $connection = $this->getConnection();

        $sql = '
          SELECT external_ref, COUNT(external_ref) as total
          FROM company_catalog
          WHERE valid = 0
          GROUP BY external_ref
          ORDER BY total DESC
          LIMIT :offset, :limit
        ';

        $statement = $connection->prepare($sql);
        $statement->bindValue('offset', $offset, ParameterType::INTEGER);
        $statement->bindValue('limit', $limit, ParameterType::INTEGER);
        $query = $statement->executeQuery();

        return $query->fetchAll();
    }

    /**
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function findByCompanyIdAndCatalogReference(int $companyId, array $catalogReferences):? CompanyCatalog
    {
        foreach($catalogReferences as $catalogReference) {
            $result = $this->getEntityManager()->createQueryBuilder()
                ->select('referenceMapping')
                ->from(CompanyCatalog::class, 'referenceMapping')
                ->where('referenceMapping.company = :companyId')
                ->andWhere('referenceMapping.externalRef = :catalogReference')
                ->setParameters(['companyId' => (string)$companyId, 'catalogReference' => $catalogReference])
                ->setMaxResults(1)
                ->getQuery()
                ->getOneOrNullResult();

            if ($result) {
                return $result;
            }
        }

        return null;
    }

    public function getConnection(): Connection
    {
        return $this->getEntityManager()->getConnection();
    }

    public function deleteCompanyCatalog(int $companyId)
    {
        return $this->getEntityManager()->createQueryBuilder()
            ->delete(CompanyCatalog::class, 'cc')
            ->where('cc.company = :company')
            ->setParameter('company', $companyId)
            ->getQuery()
            ->execute();
    }

    /**
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function deleteCatalogReference(CompanyCatalog $catalogReference)
    {
        $entityManager = $this->getEntityManager();
        $entityManager->remove($catalogReference);
        $entityManager->flush();
    }

    /**
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function saveCatalogReference(CompanyCatalog $catalogReference)
    {
        $entityManager = $this->getEntityManager();
        $entityManager->persist($catalogReference);
        $entityManager->flush();
    }
}
