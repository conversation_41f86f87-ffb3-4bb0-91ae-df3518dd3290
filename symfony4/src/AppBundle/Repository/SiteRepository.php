<?php

namespace AppBundle\Repository;

use AppBundle\Entity\Site;
use AppBundle\Util\TransportService;
use Doctrine\ORM\EntityRepository;


/**
 * MethanationSiteRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class SiteRepository extends EntityRepository
{
    public function deleteOrphelin()
    {
        $em = $this->getEntityManager();

        $sql = "select site.id from methanation_site site " .
            "where site.company_id is null " .
            "order by site.id ";

        $stmt = $em->getConnection()->prepare($sql);
        $query = $stmt->executeQuery();
        $result = $query->fetchAll();
        $ids = $this->getIdToDelete($result);
        $this->deleteIds($this->getIdToDelete($result));
        return count($ids);

    }

    public function deleteIds($ids)
    {
        if ($ids != null && count($ids) > 0) {
            $em = $this->getEntityManager();
            $sql = "delete from methanation_site where methanation_site.id in (" . implode(",", $ids) . ")";
            $stmt = $em->getConnection()->prepare($sql);
            return $stmt->execute();
        }
    }

    private function getIdToDelete($results)
    {
        $ids = array();
        if ($results != null) {
            foreach ($results as $row) {
                $ids[] = $row["id"];
            }
        }
        return $ids;
    }

    /**
     * Retrieve sites for a company
     * @param int $companyId company ID
     * @return array contains sites for company
     */
    public function getSitesByCompany($companyId) {
        $qb = $this->createQueryBuilder('s');
        $qb->leftJoin('s.company', 'c');
        $qb->where('c.id=:companyId');
        $qb->setParameter('companyId', $companyId);
        return $qb->getQuery()->getResult();
    }

    /**
     * Returns the sites of the user defined by default
     *
     * @return Site[]
     */
    public function findAllSiteOfDefaultUser(int $userId): array
    {
        return $this->createQueryBuilder('s')
            ->addSelect('u')
            ->leftJoin('s.users', 'u')
            ->where('s.defaultUser = :userId')
            ->setParameter('userId', $userId)
            ->getQuery()
            ->getResult()
        ;
    }
}
