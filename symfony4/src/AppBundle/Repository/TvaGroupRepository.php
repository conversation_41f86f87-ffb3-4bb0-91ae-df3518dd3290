<?php

namespace AppBundle\Repository;

use Doctrine\ORM\EntityRepository;

class TvaGroupRepository extends EntityRepository
{
    public function findByGroupName($groupName){
        $qb = $this->getEntityManager()->createQueryBuilder()
            ->select('t')
            ->from(\AppBundle\Entity\TvaGroup::class, 't')
            ->where("t.groupName = :groupName")
            ->setParameter('groupName', $groupName);
        return $qb->getQuery()->getOneOrNullResult();
    }
}
