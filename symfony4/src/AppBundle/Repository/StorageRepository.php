<?php

namespace AppBundle\Repository;

use AppBundle\Entity\Storage;
use Doctrine\ORM\AbstractQuery;
use Doctrine\ORM\EntityRepository;

class StorageRepository extends EntityRepository
{
    public function persist(Storage $storage): void
    {
        $this->_em->persist($storage);
    }

    public function flush(): void
    {
        $this->_em->flush();
    }

    public function remove(Storage $storage): void
    {
        $this->_em->remove($storage);
        $this->_em->flush();
    }

    public function getStorageValueFromKey(string $key) :?string
    {
        $queryBuilder = $this->createQueryBuilder('s')
            ->select('s.value')
            ->where('s.key = :key')->setParameter('key', $key);

        return $queryBuilder->getQuery()->getOneOrNullResult(AbstractQuery::HYDRATE_SINGLE_SCALAR);
    }
}
