<?php

namespace AppBundle\Repository;

use AppBundle\Entity\CartShippingOption;
use Doctrine\ORM\EntityRepository;

class CartShippingOptionRepository extends EntityRepository
{
    public function save(CartShippingOption $cartShippingOption): bool
    {
        $this->getEntityManager()->persist($cartShippingOption);
        $this->getEntityManager()->flush();

        return true;
    }

    public function getShippingOption(int $cartId)
    {

        return $this->getEntityManager()->createQueryBuilder()
            ->select('c')
            ->from(CartShippingOption::class,'c')
            ->where('c.cart = :cartId')
            ->setParameter('cartId',$cartId)
            ->getQuery()->getResult();
    }
}
