<?php

namespace AppBundle\Repository;

use AppBundle\Entity\Order;
use Doctrine\ORM\EntityRepository;

class OrderRepository extends EntityRepository
{
    /**
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function save(Order $order)
    {
        $this->_em->persist($order);
        $this->_em->flush();
    }

    public function getStats($companyId, $currency, $year, $costCenterId = null){

        $qb = $this->getEntityManager()->createQueryBuilder()
            ->select('sum(o.amountVatIncluded) as amount, SUBSTRING(o.createdOn, 6, 2) as month')
            ->from(\AppBundle\Entity\Order::class, 'o')
            ->where("o.company = :companyId")
            ->setParameter('companyId', $companyId)
            ->andWhere('SUBSTRING(o.createdOn, 1, 4) = :year')
            ->setParameter('year', $year)
            ->andWhere('o.currency = :currency')
            ->setParameter('currency', $currency);

        if(null !== $costCenterId){
            $qb->andWhere('o.site = :siteId')
                ->setParameter('siteId', $costCenterId);
        }

        $qb->groupBy('month');

        return $qb->getQuery()->getResult();
    }

    public function getYears($companyId){
        $qb = $this->getEntityManager()->createQueryBuilder()
            ->select('SUBSTRING(o.createdOn, 1, 4) as year')
            ->from(\AppBundle\Entity\Order::class, 'o')
            ->where("o.company = :companyId")
            ->setParameter('companyId', $companyId)
            ->orderBy('year', 'desc');

        $qb->groupBy('year');

        return $qb->getQuery()->getResult();
    }

    public function fetchByCartId(int $cartId): ?Order
    {
        return $this->findOneBy(['cartId' => $cartId]);
    }

    public function getOrderByIzbergId(int $izbergId): ?Order
    {
        return $this->findOneBy(['izbergId' => $izbergId]);
    }

    /**
     * @return Order[]
     */
    public function fetchLastOrders(int $companyId, int $count = 3): array
    {
        return $this->createQueryBuilder('o')
            ->where('o.company = :companyId')
            ->setParameter('companyId', $companyId)
            ->setMaxResults($count)
            ->orderBy('o.id', 'DESC')
            ->getQuery()
            ->getResult()
        ;
    }
}
