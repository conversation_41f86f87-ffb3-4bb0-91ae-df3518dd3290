<?php

namespace AppBundle\Repository;

use AppBundle\Entity\ThreadParentMessage;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Exception;


class ThreadParentMessageRepository extends EntityRepository
{

	public function persist(ThreadParentMessage $entity): void
	{
		$this->_em->persist($entity);
	}

	public function flush(): void
	{
		$this->_em->flush();
	}

    public function add(ThreadParentMessage $entity, bool $flush = true): void
    {
        $this->_em->persist($entity);
        if ($flush) {
            $this->_em->flush();
        }
    }

    public function remove(ThreadParentMessage $entity, bool $flush = true): void
    {
        $this->_em->remove($entity);
        if ($flush) {
            $this->_em->flush();
        }
    }

    /**
     * @param $izbergId string identifier used to find the TransactionalEmail
     * @return ThreadParentMessage|null Returns a ThreadParentMessage object by izbergId
     */
    public function findOneByIzbergId(string $izbergId): ?ThreadParentMessage
    {
        $qb = $this->getEntityManager()->createQueryBuilder()
            ->select("tpm")
            ->from(ThreadParentMessage::class, "tpm")
            ->where('tpm.izbergId = :val')
            ->setParameter('val', $izbergId)
            ->setMaxResults(1)
        ;

		try {
			return $qb->getQuery()->getOneOrNullResult();
		} catch (NonUniqueResultException $e) {
            return null;
        }
    }

    public function updateThreadParentMessage(ThreadParentMessage $threadParentMessage) {
        $this->persist($threadParentMessage);
        $this->flush();
    }

}
