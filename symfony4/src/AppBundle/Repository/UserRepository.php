<?php

namespace AppBundle\Repository;

use AppBundle\Entity\Site;
use AppBundle\Entity\User;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\QueryBuilder;

class UserRepository  extends EntityRepository
{
    /**
     * get list of enabled operators
     * @return mixed
     */
    public function findOperator (){
        /**
         * @var QueryBuilder
         */
        $qb =
            $this->createQueryBuilder('u')
                ->select('u')
                ->where("u.roles like '%OPERATOR%' and u.enabled = 1");

        return $qb->getQuery()->getResult();
    }

    /**
     * find all users that are not operators neither administrator
     */
    public function findNonAdministratorUsers(){
        return $this->createQueryBuilder('u')
            ->where ("u.roles NOT LIKE '%OPERATOR%'")
            ->andWhere("u.roles NOT LIKE '%SUPER_ADMIN%'");
    }

    /**
     * Find users authorized to buy and valid carts for a company
     */
    public function findBuyersForCompany($companyId) {
        $qb = $this->createQueryBuilder('u')
            ->join('u.company', 'c')
            ->where ("u.roles LIKE '%BUYER_PAYER%'")
            ->orWhere("u.roles LIKE '%BUYER_ADMIN%'")
            ->andWhere('c.id = :companyId');
        $qb->setParameter('companyId', $companyId);
        return $qb->getQuery()->getResult();
    }

    /**
     * @param $siteId
     * @param $userId
     * Find users authorized to buy and valid carts for a code center (other than current user)
     * @return mixed
     */
    public function findUsersForSite($siteId, $userId) {
        $qb = $this->createQueryBuilder('u')
            ->join('u.sites', 's')
            ->where('s.id = :siteId')
          ->andWhere('u.id != :userId')
          ->andWhere('u.enabled <> 0');
        $qb->setParameter('siteId', $siteId);
        $qb->setParameter('userId', $userId);

        return $qb->getQuery()->getResult();
    }

    public function getReference(int $userId)
    {
        return $this->getEntityManager()->getReference(User::class,$userId);
    }

    /**
     * Create a queryBuilder that returns all active users, from the same company and who have the role "Buyer"
     * or "Account manager" of the site
     */
    public function createQueryBuilderUserBySite(Site $site): QueryBuilder
    {
        return $this->createQueryBuilder('u')
            ->addSelect('s')
            ->join('u.sites', 's')
            ->where("u.roles LIKE '%BUYER_PAYER%'")
            ->orWhere("u.roles LIKE '%BUYER_ADMIN%'")
            ->andWhere('u.enabled <> 0')
            ->andWhere('u.company = :company')
            ->andWhere('s.id = :siteId')
            ->setParameter('siteId', $site->getId())
            ->setParameter('company', $site->getCompany())
        ;
    }

    public function save(User $user)
    {
        $this->getEntityManager()->persist($user);
        $this->getEntityManager()->flush();
    }

}
