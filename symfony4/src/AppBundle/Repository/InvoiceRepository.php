<?php

namespace AppBundle\Repository;

use AppBundle\Entity\Company;
use AppBundle\Entity\Invoice;
use AppBundle\Entity\Order;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\EntityRepository;

class InvoiceRepository extends EntityRepository
{
    /**
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function save(Invoice $invoice)
    {
        $this->_em->persist($invoice);
        $this->_em->flush();
    }

    public function findByCompany(Company $company, ?\DateTimeImmutable $fromDate, ?\DateTimeImmutable $toDate)
    {
        $queryBuilder = $this->_em
            ->createQueryBuilder()
            ->select('i')
            ->from(\AppBundle\Entity\Invoice::class, 'i')
            ->where('i.company = :company')->setParameter('company', $company, Types::OBJECT)
        ;

        if ($fromDate !== null) {
            $queryBuilder->andWhere('i.createdOn > :fromDate')
                ->setParameter('fromDate', $fromDate, Types::DATE_IMMUTABLE);
        }

        if ($toDate !== null) {
            $queryBuilder->andWhere('i.createdOn < :toDate')
                ->setParameter('toDate', $toDate, Types::DATE_IMMUTABLE);
        }

        $query = $queryBuilder->getQuery();

        return $query->getResult();
    }
}
