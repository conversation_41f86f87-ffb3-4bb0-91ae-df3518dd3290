<?php

namespace AppBundle\Repository;

use AppBundle\Entity\ThreadMessage;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Exception;


class ThreadMessageRepository extends EntityRepository
{

	public function persist(ThreadMessage $entity): void
	{
		$this->_em->persist($entity);
	}

	public function flush(): void
	{
		$this->_em->flush();
	}

    public function add(ThreadMessage $entity, bool $flush = true): void
    {
        $this->_em->persist($entity);
        if ($flush) {
            $this->_em->flush();
        }
    }

    public function remove(ThreadMessage $entity, bool $flush = true): void
    {
        $this->_em->remove($entity);
        if ($flush) {
            $this->_em->flush();
        }
    }

    /**
     * @param $izbergId string
     * @return ThreadMessage|null Returns a ThreadParentMessage object by izbergId
     */
    public function findOneByIzbergId(string $izbergId): ?ThreadMessage
    {
        $qb = $this->getEntityManager()->createQueryBuilder()
            ->select("tm")
            ->from(ThreadMessage::class, "tm")
            ->where('tm.izbergId = :val')
            ->setParameter('val', $izbergId)
            ->setMaxResults(1)
        ;

		try {
			return $qb->getQuery()->getOneOrNullResult();
		} catch (NonUniqueResultException $e) {
            return null;
        }
    }

    /**
     * Get all ThreadMessages by izbergUserId.
     *
     * @param string $izbergUserId The izbergUserId to search for.
     * @return array Returns an array of ThreadParentMessage objects.
     */
    public function findAllByIzbergUserId(string $izbergUserId): array
    {
        $qb = $this->createQueryBuilder('tm')
            ->where('tm.izbergUserId = :izbergUserId')
            ->setParameter('izbergUserId', $izbergUserId);

        return $qb->getQuery()->getResult();
    }

    public function updateHasUnreadMessagesByIzbergId($izbergId, $unreadStatus)
    {
        $qb = $this->createQueryBuilder('tm')
            ->update()
            ->set('tm.hasUnreadMessages', ':unreadStatus')
            ->where('tm.izbergId = :izbergId')
            ->setParameter('izbergId', $izbergId)
            ->setParameter('unreadStatus', $unreadStatus);

        return $qb->getQuery()->execute();
    }

    public function countUnreadMessagesByUserId($izbergUserId)
    {
        return $this->createQueryBuilder('tm')
            ->select('count(tm.id)')
            ->where('tm.izbergUserId = :izbergUserId')
            ->andWhere('tm.hasUnreadMessages = 1')
            ->setParameter('izbergUserId', $izbergUserId)
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function updateThreadMessage(ThreadMessage $threadMessage) {
        $this->persist($threadMessage);
        $this->flush();
    }

}
