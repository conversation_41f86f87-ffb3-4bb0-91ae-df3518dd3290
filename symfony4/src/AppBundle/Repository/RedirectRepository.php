<?php
namespace AppBundle\Repository;

use Doctrine\ORM\EntityRepository;


class RedirectRepository extends EntityRepository
{

    /**
     * Find a content by its slug
     * @param $origin
     * @return mixed
     */
    public function findByOrigin($origin) {
        $query =
            $this->createQueryBuilder('r')
                 ->where('r.origin = :origin')
                 ->setMaxResults(1)
                 ->setParameter('origin', $origin);

        return $query->getQuery()->getOneOrNullResult();
    }

    /**
     * Find all redirections going to $destination (Many To One)
     * @param $destination
     * @return array
     */
    public function findByDestination($destination) {
        $query =
            $this->createQueryBuilder('r')
                ->where('r.destination = :destination')
                ->setMaxResults(1)
                ->setParameter('destination', $destination);

        return $query->getQuery()->getResult();
    }


}
