<?php

namespace AppBundle\Repository;

use AppBundle\Entity\TvaRate;
use Doctrine\ORM\EntityRepository;

class TvaRateRepository extends EntityRepository
{
    public function getTaxRateFromTaxGroupAndDate($taxGroup, $date): ?TvaRate
    {
        $qb = $this->createQueryBuilder('rate');
        $qb->leftJoin('rate.group', 'g');
        $qb->where('g.groupName = :taxGroup');
        $qb->andWhere('rate.fromDate < :date');
        $qb->setParameter('taxGroup', $taxGroup);
        $qb->setParameter('date', $date);
        $qb->orderBy('rate.fromDate', 'DESC');
        $result =  $qb->getQuery()->getResult();
        if(count($result) > 0){
            return $result[0];
        }

        return null;
    }
}
