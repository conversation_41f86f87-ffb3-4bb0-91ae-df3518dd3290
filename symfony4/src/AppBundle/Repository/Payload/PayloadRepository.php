<?php

declare(strict_types=1);

namespace AppBundle\Repository\Payload;

use AppBundle\Entity\Middleware\AbstractPayload;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

final class PayloadRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AbstractPayload::class);
    }

    public function save(AbstractPayload $payload): void
    {
        $this->getEntityManager()->persist($payload);
        $this->getEntityManager()->flush();
    }
}
