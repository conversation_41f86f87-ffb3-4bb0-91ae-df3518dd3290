<?php

namespace AppBundle\Repository;

use AppBundle\Entity\MerchantOrder;
use AppBundle\Entity\OrderItem;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\EntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method OrderItem|null find($id, $lockMode = null, $lockVersion = null)
 * @method OrderItem|null findOneBy(array $criteria, array $orderBy = null)
 */
class OrderItemRepository extends EntityRepository
{

    public function save(OrderItem $orderItem)
    {
        $this->_em->persist($orderItem);
        $this->_em->flush();
    }

    public function getByIzbergId(int $izbergId): ?OrderItem
    {
        return $this->findOneBy(['izbergId' => $izbergId]);
    }
}
