<?php

namespace AppBundle\Repository;

class ContactRepository extends \Doctrine\ORM\EntityRepository
{

    public function getQBfindByCompany($companyId)
    {
        $em = $this->getEntityManager();
        $sub = $em->createQuery("SELECT comp.name FROM " . \AppBundle\Entity\Company::class . " comp WHERE comp.id=:companyId and (comp.mainContact=c.id or comp.billingContact=c.id)");

        $qb = $this->createQueryBuilder('c');
        $orX = $qb->expr()->orX();
        $orX->add($qb->expr()->isNotNull('c.fullName'));
        $orX->add($qb->expr()->isNotNull('c.city'));
        return $qb
            ->where($qb->expr()->exists($sub->getDQL()))
            ->andWhere($orX)
            ->setParameter('companyId', $companyId);

    }

    private function createOrphelinForOrphelinSite()
    {
        $em = $this->getEntityManager();
        $sql = "update methanation_site  set contact_id = NULL where methanation_site.company_id is null";
        $stmt = $em->getConnection()->prepare($sql);
        $stmt->executeQuery();
    }

    public function deleteOrphelin()
    {
        $this->createOrphelinForOrphelinSite();
        $em = $this->getEntityManager();

        $sql = "select contact.id from contact " .
            "left join  company on company.contact_main_id = contact.id  or company.contact_billing_id = contact.id " .
            "left join methanation_site on methanation_site.contact_id = contact.id " .
            "where company.id is null and methanation_site.id is null " .
            "order by contact.id ";

        $stmt = $em->getConnection()->prepare($sql);
        $query = $stmt->executeQuery();
        $result = $query->fetchAll();
        $ids = $this->getIdToDelete($result);
        $this->deleteIds($this->getIdToDelete($result));
        return count($ids);

    }

    public function deleteIds($ids)
    {
        if ($ids != null && count($ids) > 0) {
            $em = $this->getEntityManager();
            $sql = "delete from contact where contact.id in (" . implode(",", $ids) . ")";
            $stmt = $em->getConnection()->prepare($sql);
            return $stmt->execute();
        }
    }

    private function getIdToDelete($results)
    {
        $ids = array();
        if ($results != null) {
            foreach ($results as $row) {
                $ids[] = $row["id"];
            }
        }
        return $ids;
    }
}
