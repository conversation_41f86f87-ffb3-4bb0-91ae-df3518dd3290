<?php

namespace AppBundle\Repository;

use AppBundle\Entity\WishListItem;
use Doctrine\ORM\EntityRepository;

class WishListItemRepository extends EntityRepository
{

    /**
     * @param $wishListItem
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function save(WishListItem $wishListItem)
    {
        $this->getEntityManager()->persist($wishListItem);
        $this->getEntityManager()->flush();
    }

    /**
     * @param WishListItem $wishListItem
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function delete(WishListItem $wishListItem)
    {
        $this->getEntityManager()->remove($wishListItem);
        $this->getEntityManager()->flush();
    }
}
