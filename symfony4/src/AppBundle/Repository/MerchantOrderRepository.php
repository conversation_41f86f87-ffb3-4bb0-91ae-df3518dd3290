<?php

namespace AppBundle\Repository;

use AppBundle\Entity\MerchantOrder;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\EntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method MerchantOrder|null find($id, $lockMode = null, $lockVersion = null)
 * @method MerchantOrder|null findOneBy(array $criteria, array $orderBy = null)
 */
class MerchantOrderRepository extends EntityRepository
{
    public function save(MerchantOrder $merchant)
    {
        $this->_em->persist($merchant);
        $this->_em->flush();
    }
}
