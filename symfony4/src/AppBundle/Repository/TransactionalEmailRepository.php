<?php

namespace AppBundle\Repository;

use AppBundle\Entity\TransactionalEmail;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Exception;


class TransactionalEmailRepository extends EntityRepository
{



	public function persist(TransactionalEmail $entity): void
	{
		$this->_em->persist($entity);
	}

	public function flush(): void
	{
		$this->_em->flush();
	}

    public function add(TransactionalEmail $entity, bool $flush = true): void
    {
        $this->_em->persist($entity);
        if ($flush) {
            $this->_em->flush();
        }
    }

    public function remove(TransactionalEmail $entity, bool $flush = true): void
    {
        $this->_em->remove($entity);
        if ($flush) {
            $this->_em->flush();
        }
    }

    /**
     * @param $emailIdentifier string identifier used to find the TransactionalEmail
     * @return TransactionalEmail|null Returns a TransactionalEmail object by emailIdentifier
     */
    public function findOneByEmailIdentifier(string $emailIdentifier): ?TransactionalEmail
    {
        $qb = $this->getEntityManager()->createQueryBuilder()
            ->select("te")
            ->from(TransactionalEmail::class, "te")
            ->where('te.emailIdentifier = :val')
            ->setParameter('val', $emailIdentifier)
            ->setMaxResults(1)
        ;

		try {
			return $qb->getQuery()->getOneOrNullResult();
		} catch (NonUniqueResultException $e) {
            return null;
        }
    }

    public function updateTransactionalEmail(TransactionalEmail $transactionalEmail) {
        $this->persist($transactionalEmail);
        $this->flush();
    }

}
