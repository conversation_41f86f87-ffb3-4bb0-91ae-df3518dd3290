<?php

namespace AppBundle\DependencyInjection;

use Symfony\Component\Config\Definition\Builder\ArrayNodeDefinition;
use Symfony\Component\Config\Definition\Builder\TreeBuilder;
use Symfony\Component\Config\Definition\ConfigurationInterface;
use Symfony\Component\Config\Definition\Exception\Exception;

class Configuration implements ConfigurationInterface
{
    public function getConfigTreeBuilder(): TreeBuilder
    {
        $treeBuilder = new TreeBuilder('app');

		$rootNode = $treeBuilder->getRootNode();
        if (!$rootNode instanceof ArrayNodeDefinition){
            throw new Exception("Wrong root node type");
        }
        /**
         * @psalm-suppress PossiblyUndefinedMethod
         * @psalm-suppress PossiblyNullReference
         */
		$rootNode
			->children()
			->integerNode('default_redirect_type')
			->defaultValue(301)
			->end()
			->variableNode('settings')

			->end();


        return $treeBuilder;
    }
}
