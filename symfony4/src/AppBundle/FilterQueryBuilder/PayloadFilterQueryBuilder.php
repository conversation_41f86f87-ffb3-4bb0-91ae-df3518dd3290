<?php

declare(strict_types=1);

namespace AppBundle\FilterQueryBuilder;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\QueryBuilder;
use Open\BackBundle\Dto\FilterPayloadData;

final class PayloadFilterQueryBuilder implements FilterQueryBuilderInterface
{
    /**
     * @param QueryBuilder $qb
     * @param FilterPayloadData $data
     */
    public function build(QueryBuilder $qb, $data): void
    {
        if (!empty($data->identifier)) {
            $qb
                ->andWhere('p.identifier LIKE :identifier')
                ->setParameter('identifier', sprintf('%%%s%%', $data->identifier));
        }

        if (!empty($data->companyName)) {
            $qb
                ->andWhere('p.userType LIKE :userType ')
                ->setParameter('userType', sprintf('%%%s%%', $data->companyName));
        }

        if (!empty($data->payloadId)) {
            $qb
                ->andWhere('p.entityId = :entityId ')
                ->setParameter('entityId', $data->payloadId);
        }

        if (!empty($data->date) && (isset($data->date['min']) && isset($data->date['max']))) {
            $dateMin = $data->getMinDate()->setTime(0, 0, 0);
            $dateMax = $data->getMaxDate()->setTime(23, 59, 59);

            /** @psalm-suppress TooManyArguments */
            $qb->andWhere(
                $qb->expr()->andX(
                    $qb->expr()->gte('p.createdAt', ':dateFrom'),
                    $qb->expr()->lte('p.createdAt', ':dateTo')
                )
            )
            ->orderBy('p.createdAt', 'DESC')
            ->setParameter('dateFrom', $dateMin, Types::DATE_IMMUTABLE)
            ->setParameter('dateTo', $dateMax, Types::DATE_IMMUTABLE);
        }

        if (!empty($data->type)) {
            $qb->andWhere($qb->expr()->isInstanceOf('p', $data->type));
        }

       $qb->addOrderBy('p.createdAt', 'DESC');
    }
}
