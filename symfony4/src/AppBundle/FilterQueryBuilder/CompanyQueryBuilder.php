<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 30/11/2017
 * Time: 15:24
 */

namespace AppBundle\FilterQueryBuilder;


use Doctrine\ORM\QueryBuilder;
use AppBundle\Entity\Company;

class CompanyQueryBuilder extends EntityQueryBuilder implements FilterQueryBuilderInterface
{
    private const QUERY_COMPANY_STATUS = 'status';

    public function build(QueryBuilder $qb, $data)
    {
        $this->addWhereClause($qb, 'id',  'id', $data, '=', 'e', true);
        $this->addWhereClause($qb, 'name',  'name', $data, self::LIKE_OPERATOR, 'e', false);
        $this->addWhereClause($qb, 'identification',  'identification', $data, self::LIKE_OPERATOR, 'e', false);
        $this->addWhereClause($qb, 'city',  'city', $data, self::LIKE_OPERATOR, 'a', false);
        $this->addWhereClause($qb, 'id',  'country', $data, '=', 'co', true);

        if (!empty($data['category']) && $data['category'] !== 'all') {
            $qb->andWhere('ca.id = :category');
            $qb->setParameter('category', $data['category']);
        }

        if (!empty($data[self::QUERY_COMPANY_STATUS])) {
            if ( $data[self::QUERY_COMPANY_STATUS] != 'all') {
                if($data[self::QUERY_COMPANY_STATUS] === 'rejected'){
                    $qb->andWhere('e.enabled = false');
                    $qb->andWhere('e.rejected = true');
                } else {
                    switch ($data[self::QUERY_COMPANY_STATUS]){
                        case Company::STATUS_INITIAL : { // should not happens
                            $qb->andWhere('e.enabled = true');
                            $qb->andWhere($qb->expr()->isNull('e.mainContact'));
                            /** @psalm-suppress TooManyArguments */
                            $qb->andWhere(
                                $qb->expr()->orX('e.cgu <> 1',  $qb->expr()->isNull('do.id'))
                            );
                            break;
                        }
                        case Company::STATUS_VALID : {
                            $qb->andWhere('e.enabled = true');
                            $qb->andWhere($qb->expr()->isNotNull('e.mainContact'));
                            $qb->andWhere($qb->expr()->isNotNull('e.mainAddress'));
                            $qb->andWhere('e.cgu = 1');
                            break;
                        }
                        case Company::STATUS_PENDING : { // No doc OR no CGU accepted
                            $qb->andWhere('e.enabled = false');
                            $qb->andWhere('e.rejected = 0');
                            $qb->andWhere($qb->expr()->isNotNull('e.mainContact'));
                            $qb->andWhere($qb->expr()->isNotNull('e.mainAddress'));
                            $qb->andWhere('e.cgu = 1');
                            break;
                        }
                        case Company::STATUS_ACCEPTABLE : {
                            $qb->andWhere('e.cgu = 0');
                            $qb->andWhere('e.enabled = false');
                            $qb->andWhere('e.rejected = false');
                        }

                    }
                }
            }
        }

        $this->addWhereClause($qb, 'totalSales',  'caMin', $data, '>=', 'e', true);
        $this->addWhereClause($qb, 'totalSales',  'caMax', $data, '<=', 'e', true);

        if ($this->isValidDate($data, 'creationMin')) {
            $and = $qb->expr()->andX();
            $data['creationMin']->setTime(0, 0, 0);
            $and->add($qb->expr()->gte('e.createdAt', ':dateFrom'));
            $qb->setParameter('dateFrom', $data['creationMin']);
            $qb->andWhere($and);
        }

        if ($this->isValidDate($data, 'creationMax')) {
            $and = $qb->expr()->andX();
            $data['creationMax']->setTime(23, 59, 59);
            $and->add($qb->expr()->lte('e.createdAt', ':dateTo'));
            $qb->setParameter('dateTo', $data['creationMax']);
            $qb->andWhere($and);
        }
        if ($this->isValidDate($data, 'term_payment_date_min')) {
            $and = $qb->expr()->andX();
            $data['term_payment_date_min']->setTime(0, 0, 0);
            $and->add($qb->expr()->gte('e.termpaymentMoneyTransfertRequestDate', ':dateFrom'));
            $qb->setParameter('dateFrom', $data['term_payment_date_min']);
            $qb->andWhere($and);
        }

        if ($this->isValidDate($data, 'term_payment_date_max')) {
            $and = $qb->expr()->andX();
            $data['term_payment_date_max']->setTime(23, 59, 59);
            $and->add($qb->expr()->lte('e.termpaymentMoneyTransfertRequestDate', ':dateTo'));
            $qb->setParameter('dateTo', $data['term_payment_date_max']);
            $qb->andWhere($and);
        }
    }

    private function isValidDate($data, $index)
    {
        return array_key_exists($index, $data) && ! is_null($data[$index]) && $data[$index] instanceof \DateTime;
    }


}
