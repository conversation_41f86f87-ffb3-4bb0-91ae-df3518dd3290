<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 29/11/2017
 * Time: 18:09
 */

namespace AppBundle\FilterQueryBuilder;


use Doctrine\ORM\QueryBuilder;

class UserQueryBuilder extends EntityQueryBuilder implements FilterQueryBuilderInterface
{
    public function build(QueryBuilder $qb, $data)
    {
        if (! empty($data['role']) && $data['role'] !== 'ALL') {
            if ($data['role'] === 'ACCOUNT_ADMIN') {
                $qb->andWhere('e.roles LIKE :buyer_admin');
                $qb->setParameter(':buyer_admin', '%ROLE_BUYER_ADMIN%');
            } elseif ($data['role'] === 'VIEWER') {
                $qb->andWhere('e.roles LIKE :role');
                $qb->setParameter(':role', '%BUYER_PAYER%');
            } elseif ($data['role'] === 'BUYER') {
                $qb->andWhere('e.roles LIKE :role');
                $qb->setParameter(':role', '%BUYER_BUYER%');
            } else {
                $qb->andWhere('e.roles LIKE :role');
                $qb->setParameter(':role', '%' . $data['role'] . '%');
            }
        }

        $this->addWhereClause($qb, 'id',  'id', $data, '=', 'e', true);
        $this->addWhereClause($qb, 'firstname',  'firstname', $data, self::LIKE_OPERATOR, 'e', false);
        $this->addWhereClause($qb, 'lastname',  'lastname', $data, self::LIKE_OPERATOR, 'e', false);
        $this->addWhereClause($qb, 'email',  'email', $data, self::LIKE_OPERATOR, 'e', false);
        $this->addWhereClause($qb, 'username',  'username', $data, self::LIKE_OPERATOR, 'e', false);
        $this->addWhereClause($qb, 'function',  'function', $data, self::LIKE_OPERATOR, 'e', false);
        $this->addWhereClause($qb, 'mainPhoneNumber',  'mainPhoneNumber', $data, self::LIKE_OPERATOR, 'e', false);
        $this->addWhereClause($qb, 'name',  'company', $data, self::LIKE_OPERATOR, 'c', false);
        $this->addWhereClause($qb, 'enabled', 'enabled', $data, '=', 'e', true);
        // creation date
        if($this->isValidDate($data, 'creationMin')) {
            $and = $qb->expr()->andX();
            $data['creationMin']->setTime(0, 0, 0);
            $and->add($qb->expr()->gte('e.createdAt', ':createFrom'));
            $qb->setParameter('createFrom', $data['creationMin']);
            $qb->andWhere($and);
        }

        if($this->isValidDate($data, 'creationMax')) {
            $and = $qb->expr()->andX();
            $data['creationMax']->setTime(23, 59, 59);
            $and->add($qb->expr()->lte('e.createdAt', ':createTo'));
            $qb->setParameter('createTo', $data['creationMax']);
            $qb->andWhere($and);
        }

        // last connexion
        if($this->isValidDate($data, 'connectionMin')) {
            $and = $qb->expr()->andX();
            $data['connectionMin']->setTime(0, 0, 0);
            $and->add($qb->expr()->gte('e.lastLogin', ':connectFrom'));
            $qb->setParameter('connectFrom', $data['connectionMin']);
            $qb->andWhere($and);
        }

        if($this->isValidDate($data, 'connectionMax')) {
            $and = $qb->expr()->andX();
            $data['connectionMax']->setTime(23, 59, 59);
            $and->add($qb->expr()->lte('e.lastLogin', ':connectTo'));
            $qb->setParameter('connectTo', $data['connectionMax']);
            $qb->andWhere($and);
        }

        if (!empty($data['status']) && $data['status'] != 'all') {
            if($data['status'] == 'enabled'){
                $qb->andWhere('e.enabled = true');
            }else{
                $qb->andWhere('e.enabled = false');
            }
        }
    }

    private function isValidDate($data, $index)
    {
        return array_key_exists($index, $data) && ! is_null($data[$index]) && $data[$index] instanceof \DateTime;
    }

}
