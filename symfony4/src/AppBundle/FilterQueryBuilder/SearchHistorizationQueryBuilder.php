<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 30/11/2017
 * Time: 15:24
 */

namespace AppBundle\FilterQueryBuilder;


use Doctrine\ORM\QueryBuilder;

class SearchHistorizationQueryBuilder extends EntityQueryBuilder implements FilterQueryBuilderInterface
{
    private const IS_ANONYMOUS = 'isAnonymous';
    private const DATEMAX = 'datemax';
    private const DATEMIN = 'datemin';


    public function build(QueryBuilder $qb, $data)
    {

        if(isset($data['searchedTerm'])){
            $data['searchedTerm'] = strtolower($data['searchedTerm']);
        }
        $this->addWhereClause($qb, 'id',  'id', $data, '=', 'e', true);
        $this->addWhereClause($qb, 'is_anonymous',  self::IS_ANONYMOUS, $data, '=', 'e', true);
        $this->addWhereClause($qb, 'search_term',  'searchedTerm', $data, self::LIKE_OPERATOR, 'e', false);
        $this->addWhereClause($qb, 'company_name',  'companyName', $data, self::LIKE_OPERATOR, 'e', false);
        $this->addWhereClause($qb, 'user_full_name',  'userFullName', $data, self::LIKE_OPERATOR, 'e', false);
        $this->addWhereClause($qb, 'nb_hits',  'nbHits', $data, '=', 'e', true);
        $this->addWhereClause($qb, 'filter',  'filter', $data, self::LIKE_OPERATOR, 'e', false);

        if(! empty($data['isAnonymous']) && $data['isAnonymous'] !== 'All'){
            $qb->andWhere($qb->expr()->eq('e.is_anonymous', ':isAnonymous'));;
            if($data['isAnonymous'] === 'Yes'){
                $qb->setParameter('isAnonymous', true);
            } else if($data['isAnonymous'] === 'No') {
                $qb->setParameter('isAnonymous', false);
            }
        }


        if ($this->isValidDate($data, self::DATEMIN)) {
            $and = $qb->expr()->andX();
            $data[self::DATEMIN]->setTime(0, 0, 0);
            $and->add($qb->expr()->gte('e.createdAt', ':dateFrom'));
            $qb->setParameter('dateFrom', $data[self::DATEMIN]);
            $qb->andWhere($and);
        }

        if ($this->isValidDate($data, self::DATEMAX)) {
            $and = $qb->expr()->andX();
            $data[self::DATEMAX]->setTime(23, 59, 59);
            $and->add($qb->expr()->lte('e.createdAt', ':dateTo'));
            $qb->setParameter('dateTo', $data[self::DATEMAX]);
            $qb->andWhere($and);
        }
    }

    private function isValidDate($data, $index)
    {
        return array_key_exists($index, $data) && ! is_null($data[$index]) && $data[$index] instanceof \DateTime;
    }
}
