<?php
/**
 * Created by PhpStorm.
 * User: QAR14123
 * Date: 23/01/2018
 * Time: 15:42
 */

namespace AppBundle\FilterQueryBuilder;

use Doctrine\ORM\QueryBuilder;

class SiteQueryBuilder extends EntityQueryBuilder implements FilterQueryBuilderInterface
{



    public function build(QueryBuilder $qb, $data)
    {
        $qb->leftJoin('e.shippingPoints', 'sp')
            ->left<PERSON>oin('sp.address', 'a')
            ->left<PERSON>oin('a.country', 'co')
            ->left<PERSON>oin('sp.contact', 'm');

        $this->addWhereClause($qb, 'id',  'id', $data, '=', 'e', true);
        $this->addWhereClause($qb, 'name',  'name', $data, self::LIKE_OPERATOR, 'e', false);
        $this->addWhereClause($qb, 'name',  'company', $data, self::LIKE_OPERATOR, 'c', false);
        $this->addWhereClause($qb, 'address',  'address', $data, self::LIKE_OPERATOR, 'a', false);
        $this->addWhereClause($qb, 'zipCode',  'zipCode', $data, self::LIKE_OPERATOR, 'a', false);
        $this->addWhereClause($qb, 'city',  'city', $data, self::LIKE_OPERATOR, 'a', false);
        $this->addWhereClause($qb, 'id',  'country', $data, '=', 'co', true);
        $this->addWhereClause($qb, 'firstName',  'contactName', $data, self::LIKE_OPERATOR, 'm', false);
        $this->addWhereClause($qb, 'phone1',  'contactPhone', $data, self::LIKE_OPERATOR, 'm', false);

        if (!empty($data[self::STATUS])) {
            if ( $data[self::STATUS] === 'all') {
                $qb->andWhere('e.enabled = true');
            }else{
                if ($data[self::STATUS] === 'disabled'){
                    $qb->andWhere('e.enabled = false');
                }
                else {
                    $qb->andWhere('e.enabled = true');
                }
            }
        }
    }
}
