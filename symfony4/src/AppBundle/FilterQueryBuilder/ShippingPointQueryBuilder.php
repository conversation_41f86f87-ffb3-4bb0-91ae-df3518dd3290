<?php

namespace AppBundle\FilterQueryBuilder;

use Doctrine\ORM\QueryBuilder;

class ShippingPointQueryBuilder extends EntityQueryBuilder implements FilterQueryBuilderInterface
{

  /***
   * @param \Doctrine\ORM\QueryBuilder $qb
   * @param $data
   */
    public function build(QueryBuilder $qb, $data)
    {

        $this->addWhereClause($qb, 'id',  'id', $data, '=', 'e', true);
        $this->addWhereClause($qb, 'name',  'name', $data, self::LIKE_OPERATOR, 'e', false);
        $this->addWhereClause($qb, 'createdAt', 'creationMin', $data, '>=', 'e', true);
        $this->addWhereClause($qb, 'createdAt',  'creationMax', $data, '<=', 'e', true);

    }
}
