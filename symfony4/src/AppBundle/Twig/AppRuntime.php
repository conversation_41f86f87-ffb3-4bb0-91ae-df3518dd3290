<?php

namespace AppBundle\Twig;

use AppBundle\Entity\User;
use AppBundle\Model\Cart\Cart;
use AppBundle\Services\ParameterService;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Twig\Extension\RuntimeExtensionInterface;

class AppRuntime implements RuntimeExtensionInterface
{
    private $parameterService;
    private $tokenStorage;
    private $params;
    private $defaultFlag;
    private $flags;

    public function __construct(ParameterService $parameterService, TokenStorageInterface $tokenStorage, ParameterBagInterface $params)
    {
        $this->parameterService = $parameterService;
        $this->tokenStorage     = $tokenStorage;
        $this->params = $params;
        $this->defaultFlag = 'en';

        $flags = [
            'fr',
            'en',
            'es',
            'it',
            'de',
            'nl',
        ];

        $this->flags = array_filter(
            $parameterService->supportedLocales(),
            function($locale) use ($flags) {
                return (in_array($locale, $flags));
            }
        );
    }

    public function currentFlag($locale)
    {
        return (in_array($locale, $this->flags)) ? $locale : $this->defaultFlag;
    }

    public function flags()
    {
        return $this->flags;
    }

    public function firstLevelCategoryOnly()
    {
        return $this->parameterService->displayFirstLevelCategoryOnly();
    }

    public function userCanModifyShippingPoint(User $user, Cart $cart): bool
    {
        return (
            in_array(
                $cart->getStatus(),
                [
                    \AppBundle\Entity\Cart::STATUS_CREATE,
                    \AppBundle\Entity\Cart::STATUS_REJECTED,
                ]
            )
            && $cart->isCartCreator($user)
        );
    }

    public function userCanModifyCartValidationNumber(User $user, Cart $cart): bool
    {
        return ($cart->getStatus() === \AppBundle\Entity\Cart::STATUS_CREATE && $cart->isCartCreator($user));
    }

    public function userCanRejectCart(User $user, Cart $cart): bool
    {
        if (
            $cart->getStatus() === \AppBundle\Entity\Cart::STATUS_ASSIGN
            && $cart->isCartAssignedTo($user)
            && !$cart->isCartCreator($user)
        ) {
            return true;
        }

        if (
            $cart->getStatus() === \AppBundle\Entity\Cart::STATUS_ORDER
            && $cart->isCartAssignedTo($user)
            && !$cart->isCartCreator($user)
        ) {
            return true;
        }
        return $cart->getStatus() !== \AppBundle\Entity\Cart::STATUS_CREATE
        && $cart->isCartAssignedTo($user)
        && !$cart->isCartCreator($user)
        && ($user->hasRole(User::ROLE_BUYER_ADMIN) || $user->hasRole(User::ROLE_BUYER_PAYER));
    }

    public function userCanValidateCart(User $user, Cart $cart): bool
    {
        if (
            $cart->getStatus() === \AppBundle\Entity\Cart::STATUS_CREATE
            && $cart->isCartCreator($user)
            && ($user->hasRole(User::ROLE_BUYER_ADMIN) || $user->hasRole(User::ROLE_BUYER_PAYER))
        ) {
            return true;
        }

        if (
            $cart->getStatus() === \AppBundle\Entity\Cart::STATUS_ASSIGN
            && $cart->isCartAssignedTo($user)
            && ($user->hasRole(User::ROLE_BUYER_ADMIN) || $user->hasRole(User::ROLE_BUYER_PAYER))
        ) {
            return true;
        }

        if (
            $cart->getStatus() === \AppBundle\Entity\Cart::STATUS_ORDER
            && ($cart->isCartAssignedTo($user) || $cart->isCartCreator($user))
            && ($user->hasRole(User::ROLE_BUYER_ADMIN) || $user->hasRole(User::ROLE_BUYER_PAYER))
        ) {
            return true;
        }
        return $cart->getStatus() !== \AppBundle\Entity\Cart::STATUS_CREATE
        && $cart->isCartAssignedTo($user)
        && ($user->hasRole(User::ROLE_BUYER_ADMIN) || $user->hasRole(User::ROLE_BUYER_PAYER));
    }

    public function userCanAssignCart(User $user, Cart $cart): bool
    {
        if (
            $cart->getStatus() === \AppBundle\Entity\Cart::STATUS_CREATE
            && $cart->isCartCreator($user)
        ) {
            return true;
        }

        if (
            $cart->getStatus() === \AppBundle\Entity\Cart::STATUS_ASSIGN
            && $cart->isCartAssignedTo($user)
        ) {
            return true;
        }

        if (
            $cart->getStatus() === \AppBundle\Entity\Cart::STATUS_ORDER
            && ($cart->isCartAssignedTo($user) || $cart->isCartCreator($user))
        ) {
            return true;
        }
        return $cart->getStatus() !== \AppBundle\Entity\Cart::STATUS_CREATE
        && $cart->isCartAssignedTo($user);
    }

    public function getECatalogUrl()
    {
        $token = $this->tokenStorage->getToken();
        /** @var User|null $user */
        $user = $token ? $token->getUser() : null;
        $eCatalogUrl = null;
        if (!empty($user?->getCompany()?->getECatalog()) && !empty($this->params->get('e_catalog_url'))) {
            $eCatalogUrl = $this->params->get('e_catalog_url');
        }
        return $eCatalogUrl;
    }
}
