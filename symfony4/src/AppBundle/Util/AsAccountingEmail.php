<?php

declare(strict_types=1);

namespace AppBundle\Util;

use Doctrine\ORM\Mapping as ORM;

trait AsAccountingEmail
{
    #[ORM\Column(name: 'accounting_email', type: 'string', nullable: true)]
    private ?string $accountingEmail = null;

    public function getAccountingEmail(): ?string
    {
        return $this->accountingEmail;
    }

    public function setAccountingEmail(?string $accountingEmail): self
    {
        $this->accountingEmail = $accountingEmail;

        return $this;
    }
}
