<?php

declare(strict_types=1);

namespace AppBundle\Util;

abstract class Str
{
    /**
     * @example property is "documentationRequested1" => ['name' => 'value', 'id' => 1]
     */
    public static function extractValuesOfNumberedProperty(object $object, string $pattern): array
    {
        $class = new \ReflectionClass($object);

        $properties = array_filter(
            $class->getProperties(),
            fn (\ReflectionProperty $property) =>
                preg_match($pattern, $property->getName()) === 1
        );

        return array_values(
            array_filter(
                array_map(function (\ReflectionProperty $property) use ($object): ?array  {
                        $propertyName = $property->getName();
                        $getter = sprintf('get%s', ucfirst($propertyName));
                        $id = substr($propertyName, -1); // Get the number of the string
                        $value = $object->{$getter}();
                        if ($value !== null) {
                            return [
                                'name' => $value,
                            ];
                        }

                        return null;
        }, $properties)));
    }
}
