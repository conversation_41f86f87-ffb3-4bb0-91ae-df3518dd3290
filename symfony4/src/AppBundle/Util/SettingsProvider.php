<?php

namespace AppBundle\Util;

use AppBundle\Repository\SettingRepository;
use AppBundle\Exception\InvalidSettingException;
use Symfony\Contracts\Cache\ItemInterface;
use Symfony\Contracts\Cache\TagAwareCacheInterface;

class SettingsProvider
{
    private TagAwareCacheInterface $cache;
    private SettingRepository $settingsRepository;
    private array $settingsConfig;

    public function __construct($sc, SettingRepository $sr, TagAwareCacheInterface $cache)
    {
        $this->cache = $cache;
        $this->settingsRepository = $sr;

        $this->settingsConfig = array();

        foreach ($sc as $domain => $groups) {
            $this->settingsConfig[$domain] = array();

            foreach ($groups as $keys) {
                foreach ($keys as $key_name => $setting) {
                    $this->settingsConfig[$domain][$key_name] = $setting;
                }
            }
        }
    }

    /**
     * Get setting from config
     * @param $domain
     * @param $key
     * @return mixed
     * @throws InvalidSettingException
     */
    private function getSettingConfig($domain, $key)
    {
        if (!isset($this->settingsConfig[$domain][$key])) {
            throw new InvalidSettingException('Invalid Setting');
        }

        return $this->settingsConfig[$domain][$key];
    }


    /**
     * Return a setting by it's fullname
     * @param $key
     * @param string|null $domain
     * @return mixed|null
     */
    public function get($key, ?string $domain = null)
    {
        // If domain provided then generate the cache key
        if (!empty($domain)) {
            $cache_key = $domain . '.' . $key;
        } else {
            $cache_key = $key;

            preg_match("/^(.*?)\\.(.*)$/i", $key, $matches);

            $domain = $matches[1];
            $key = $matches[2];
        }

        $settingConfig = $this->getSettingConfig($domain, $key);

        return $this->cache->get($cache_key, function(ItemInterface $item) use ($domain, $key, $settingConfig) {
            $value = $settingConfig['default_value'];

            $setting = $this->settingsRepository->findByDomainAndKey($domain, $key);

            if ($setting) {
                if ($setting->getValue() !== null) {
                    $value = $setting->getValue();
                }

                $tags = array_merge(explode(',', $setting->getTags()), array($key, $domain));
                $tags = array_unique($tags);
                $item->tag($tags);
            }

            return $value;
        });
    }
}
