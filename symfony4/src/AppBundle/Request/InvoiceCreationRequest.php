<?php

namespace AppBundle\Request;

use AppBundle\Model\Shipping\ShipmentOrderItem;

final class InvoiceCreationRequest
{
    /**
     * @var int
     */
    private $shipmentId;

    /**
     * @var int
     */
    private $merchantOrderId;

    /**
     * @var ShipmentOrderItem[]
     */
    private $orderItems;

    public function __construct()
    {
        $this->orderItems = [];
    }

    /**
     * @return int
     */
    public function getShipmentId(): int
    {
        return $this->shipmentId;
    }

    /**
     * @param int $shipmentId
     * @return $this
     */
    public function setShipmentId(int $shipmentId): self
    {
        $this->shipmentId = $shipmentId;
        return $this;
    }

    public function getMerchantOrderId(): int
    {
        return $this->merchantOrderId;
    }

    public function setMerchantOrderId(int $merchantOrderId): self
    {
        $this->merchantOrderId = $merchantOrderId;
        return $this;
    }

    public function getOrderItems(): array
    {
        return $this->orderItems;
    }

    public function addOrderItem(ShipmentOrderItem $orderItem): self
    {
        $this->orderItems[] = $orderItem;
        return $this;
    }
}
