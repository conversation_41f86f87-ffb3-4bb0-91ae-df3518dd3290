<?php

namespace AppBundle\Request;

use AppBundle\Model\Shipping\ShipmentOrderItem;
use Upela\Api\ProductApi;
use Upela\Model\Product;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Upela\Api\Exception;

final class InvoiceCreationRequestBuilder
{
    /**
     * @var ProductApi
     */
    private $productApi;

    public function __construct(ProductApi $productApi)
    {
        $this->productApi = $productApi;
    }

    /**
     * @param int $shipmentId
     * @return array
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws Exception
     */
    public function build(int $shipmentId): array
    {
        $invoiceCreationRequests = [];

        $products = $this->productApi->fetchProductsByShipmentId($shipmentId);


        /** @var Product $product */
        foreach($products as $product) {
            $merchantOrderId = $product->getPreOrder()->getMerchantOrderId();
            // retrieve the quantity of the given product shipped with the shipment on webhook payload
            $quantity = array_reduce(
                $product->getShipments(),
                function(int $quantity, \Upela\Model\Shipment $shipment) use ($shipmentId): int {
                    if ($shipmentId == $shipment->getShipmentId()) {
                        $quantity = $shipment->getQuantityProduct();
                    }

                    return $quantity;
                },
                0
            );
            $shipmentOrderItem = (new ShipmentOrderItem())
                ->setId(intval($product->getProductId()))
                ->setQuantity($quantity);

            if (!isset($invoiceCreationRequests[$merchantOrderId])) {
                $invoiceCreationRequests[$merchantOrderId] = (new InvoiceCreationRequest())
                    ->setShipmentId($shipmentId)
                    ->setMerchantOrderId($merchantOrderId);
            }

            /** @var InvoiceCreationRequest $invoiceCreationRequest */
            $invoiceCreationRequest = $invoiceCreationRequests[$merchantOrderId];
            $invoiceCreationRequest->addOrderItem($shipmentOrderItem);
            $invoiceCreationRequests[$merchantOrderId] = $invoiceCreationRequest;
        }

        return $invoiceCreationRequests;
    }
}
