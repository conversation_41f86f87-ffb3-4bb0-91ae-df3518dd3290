<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20201116205548 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('DROP TABLE IF EXISTS jms_job_statistics');
        $this->addSql('DROP TABLE IF EXISTS jms_job_dependencies');
        $this->addSql('DROP TABLE IF EXISTS jms_job_related_entities');
        $this->addSql('DROP TABLE IF EXISTS jms_cron_jobs');
        $this->addSql('DROP TABLE IF EXISTS jms_jobs');
        $this->addSql('ALTER TABLE node CHANGE template template VARCHAR(40) DEFAULT NULL');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('CREATE TABLE jms_cron_jobs (id INT UNSIGNED AUTO_INCREMENT NOT NULL, command VARCHAR(200) CHARACTER SET utf8 NOT NULL COLLATE `utf8_unicode_ci`, lastRunAt DATETIME NOT NULL, UNIQUE INDEX UNIQ_55F5ED428ECAEAD4 (command), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('ALTER TABLE `node` CHANGE template template VARCHAR(40) CHARACTER SET utf8 NOT NULL COLLATE `utf8_unicode_ci`');
    }
}
