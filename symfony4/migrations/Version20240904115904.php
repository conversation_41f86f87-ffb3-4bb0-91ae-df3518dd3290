<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240904115904 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add index for created_at field in search_historization table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `search_historization` ADD INDEX `i_created_at_1` (`created_at`);');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `search_historization` DROP INDEX `i_created_at_1`;');
    }
}
