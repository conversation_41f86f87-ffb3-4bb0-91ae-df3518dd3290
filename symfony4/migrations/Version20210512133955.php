<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210512133955 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE order_item ADD izberg_id INT NOT NULL, DROP payment_terms');
        $this->addSql('ALTER TABLE orders ADD payment_terms VARCHAR(255) NULL');
        $this->addSql('ALTER TABLE merchant_order CHANGE izberg_id izberg_id INT DEFAULT NULL, CHANGE vendor_name vendor_name VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE order_item CHANGE product_name product_name VARCHAR(255) DEFAULT NULL, CHANGE unit_price unit_price DOUBLE PRECISION DEFAULT NULL, CHANGE quantity quantity INT DEFAULT NULL, CHANGE currency currency VARCHAR(255) DEFAULT NULL, CHANGE order_line order_line INT DEFAULT NULL, CHANGE expected_delivery_date expected_delivery_date DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', CHANGE vendor_reference vendor_reference VARCHAR(255) DEFAULT NULL, CHANGE izberg_id izberg_id INT DEFAULT NULL');

    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE merchant_order CHANGE izberg_id izberg_id INT NOT NULL, CHANGE vendor_name vendor_name VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`');
        $this->addSql('ALTER TABLE order_item CHANGE product_name product_name VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, CHANGE unit_price unit_price DOUBLE PRECISION NOT NULL, CHANGE quantity quantity INT NOT NULL, CHANGE currency currency VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, CHANGE order_line order_line INT NOT NULL, CHANGE expected_delivery_date expected_delivery_date DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', CHANGE vendor_reference vendor_reference VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, CHANGE izberg_id izberg_id INT NOT NULL');
        $this->addSql('ALTER TABLE order_item ADD payment_terms VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, DROP izberg_id');
        $this->addSql('ALTER TABLE orders DROP payment_terms');

    }
}
