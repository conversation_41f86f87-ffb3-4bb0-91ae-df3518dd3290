<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240701940807 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE IF NOT EXISTS `thread_parent_message` (id_thread_message int(11) AUTO_INCREMENT NOT NULL,  izberg_id int(11) DEFAULT NULL, from_email VARCHAR(255) DEFAULT NULL,  PRIMARY KEY(id_thread_message), UNIQUE  (izberg_id))');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE IF EXISTS `thread_parent_message`');
    }
}
