<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210512083208 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE merchant_order (id INT AUTO_INCREMENT NOT NULL, order_id INT NOT NULL, izberg_id INT NOT NULL, vendor_name VARCHAR(255) NOT NULL, vendor_reference VARCHAR(255) NOT NULL, merchant_order_status INT DEFAULT NULL, INDEX IDX_62BA49DF8D9F6D38 (order_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE order_item (id INT AUTO_INCREMENT NOT NULL, product_name VARCHAR(255) NOT NULL, unit_price DOUBLE PRECISION NOT NULL, quantity INT NOT NULL, currency VARCHAR(255) NOT NULL, order_line INT NOT NULL, expected_delivery_date DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', payment_terms VARCHAR(255) NOT NULL, merchantOrder_id INT NOT NULL, INDEX IDX_52EA1F09D99F9B88 (merchantOrder_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE merchant_order ADD CONSTRAINT FK_62BA49DF8D9F6D38 FOREIGN KEY (order_id) REFERENCES orders (id)');
        $this->addSql('ALTER TABLE order_item ADD CONSTRAINT FK_52EA1F09D99F9B88 FOREIGN KEY (merchantOrder_id) REFERENCES merchant_order (id)');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE order_item DROP FOREIGN KEY FK_52EA1F09D99F9B88');
        $this->addSql('DROP TABLE merchant_order');
        $this->addSql('DROP TABLE order_item');
    }
}
