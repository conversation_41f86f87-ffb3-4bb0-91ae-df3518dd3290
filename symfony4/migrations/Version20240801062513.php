<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240801062513 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE IF NOT EXISTS `thread_messages` (
            id int(11) AUTO_INCREMENT NOT NULL,
            izberg_user_id int(11) NOT NULL,
            izberg_id int(11) NOT NULL,
            subject VARCHAR(220) DEFAULT NULL,
            sender_resource_uri VARCHAR(100) DEFAULT NULL,
            from_display_name VARCHAR(50) DEFAULT NULL,
            to_display_name VARCHAR(50) DEFAULT NULL,
            original_created_date VARCHAR(50),
            last_modified_date VARCHAR(50),
            has_unread_messages TINYINT(1) DEFAULT 0 NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY(id), UNIQUE (izberg_id)
        )');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE IF EXISTS `thread_messages`');
    }
}
