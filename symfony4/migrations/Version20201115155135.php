<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20201115155135 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        $sql = <<<SQL

-- Server version	5.6.49

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `action_historization`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `action_historization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ref_entity_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `type` varchar(10) COLLATE utf8_unicode_ci NOT NULL,
  `class_name` varchar(100) COLLATE utf8_unicode_ci NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `change_set` longtext COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:array)',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=237662 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `address`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `address` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `country_id` int(11) DEFAULT NULL,
  `region_id` int(11) DEFAULT NULL,
  `address` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `address2` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `city` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `zip_code` varchar(10) COLLATE utf8_unicode_ci DEFAULT NULL,
  `longitude` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
  `latitude` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
  `region_text` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `izberg_address_id` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `technical_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_D4E6F81F92F3E70` (`country_id`),
  KEY `IDX_D4E6F8198260155` (`region_id`),
  CONSTRAINT `FK_D4E6F8198260155` FOREIGN KEY (`region_id`) REFERENCES `region` (`id`),
  CONSTRAINT `FK_D4E6F81F92F3E70` FOREIGN KEY (`country_id`) REFERENCES `country` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `bafv_request`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `bafv_request` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `company_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `status` smallint(6) NOT NULL,
  `merchant_id` int(11) NOT NULL,
  `merchant_name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `rejection_reason` longtext COLLATE utf8_unicode_ci,
  PRIMARY KEY (`id`),
  KEY `IDX_5B5B02E3979B1AD6` (`company_id`),
  KEY `IDX_5B5B02E3A76ED395` (`user_id`),
  CONSTRAINT `FK_5B5B02E3979B1AD6` FOREIGN KEY (`company_id`) REFERENCES `company` (`id`),
  CONSTRAINT `FK_5B5B02E3A76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cart`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `cart` (
  `id` int(11) NOT NULL,
  `created_user_id` int(11) DEFAULT NULL,
  `current_user_id` int(11) DEFAULT NULL,
  `site_id` int(11) DEFAULT NULL,
  `address_id` int(11) DEFAULT NULL,
  `billing_address_id` int(11) DEFAULT NULL,
  `status` varchar(20) COLLATE utf8_unicode_ci NOT NULL,
  `payment_mode` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `payment_term` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `payment_method` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `payment_type` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `payment_term_izberg_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `order_id` int(11) DEFAULT NULL,
  `wps_transaction_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `wps_reconciliation_key` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `validation_number` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `buyer_order_id` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `documents_request` longtext COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:array)',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `technical_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_BA388B7E104C1D3` (`created_user_id`),
  KEY `IDX_BA388B7D635610` (`current_user_id`),
  KEY `IDX_BA388B7F6BD1646` (`site_id`),
  KEY `IDX_BA388B7F5B7AF75` (`address_id`),
  KEY `IDX_BA388B779D0C0E4` (`billing_address_id`),
  CONSTRAINT `FK_BA388B779D0C0E4` FOREIGN KEY (`billing_address_id`) REFERENCES `address` (`id`),
  CONSTRAINT `FK_BA388B7D635610` FOREIGN KEY (`current_user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `FK_BA388B7E104C1D3` FOREIGN KEY (`created_user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `FK_BA388B7F5B7AF75` FOREIGN KEY (`address_id`) REFERENCES `address` (`id`),
  CONSTRAINT `FK_BA388B7F6BD1646` FOREIGN KEY (`site_id`) REFERENCES `site` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cart_historic`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `cart_historic` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `user_assigned_id` int(11) NOT NULL,
  `cart_id` int(11) NOT NULL,
  `status` varchar(20) COLLATE utf8_unicode_ci NOT NULL,
  `comment` varchar(1024) COLLATE utf8_unicode_ci NOT NULL,
  `date` datetime NOT NULL,
  `technical_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_33CDA03DA76ED395` (`user_id`),
  KEY `IDX_33CDA03D484BD390` (`user_assigned_id`),
  KEY `IDX_33CDA03D1AD5CDBF` (`cart_id`),
  CONSTRAINT `FK_33CDA03D1AD5CDBF` FOREIGN KEY (`cart_id`) REFERENCES `cart` (`id`),
  CONSTRAINT `FK_33CDA03D484BD390` FOREIGN KEY (`user_assigned_id`) REFERENCES `users` (`id`),
  CONSTRAINT `FK_33CDA03DA76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=32 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cart_shipping_option`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `cart_shipping_option` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cart_id` int(11) NOT NULL,
  `merchant_id` int(11) NOT NULL,
  `flag` smallint(6) NOT NULL,
  `offers` longtext COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:json)',
  `price` double DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_8AA430F1AD5CDBF` (`cart_id`),
  CONSTRAINT `FK_8AA430F1AD5CDBF` FOREIGN KEY (`cart_id`) REFERENCES `cart` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `category`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `category` (
  `id` int(11) NOT NULL,
  `label` varchar(50) COLLATE utf8_unicode_ci NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `technical_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `company`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `company` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `contact_main_id` int(11) DEFAULT NULL,
  `contact_billing_id` int(11) DEFAULT NULL,
  `contact_adv_id` int(11) DEFAULT NULL,
  `contact_logistic_id` int(11) DEFAULT NULL,
  `address_main_id` int(11) DEFAULT NULL,
  `address_billing_id` int(11) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL,
  `name` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `ident` varchar(100) COLLATE utf8_unicode_ci NOT NULL,
  `comment` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `total_sales` decimal(10,0) DEFAULT NULL,
  `last_connexion` datetime DEFAULT NULL,
  `enabled` tinyint(1) NOT NULL,
  `cgu` tinyint(1) NOT NULL,
  `billing_service` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `izberg_email` varchar(180) COLLATE utf8_unicode_ci NOT NULL,
  `izberg_username` varchar(180) COLLATE utf8_unicode_ci DEFAULT NULL,
  `izberg_user_id` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `prepayment_creditcard_enabled` tinyint(1) NOT NULL DEFAULT '1',
  `prepayment_moneytransfert_enabled` tinyint(1) NOT NULL DEFAULT '0',
  `termpayment_moneytransfert_enabled` tinyint(1) NOT NULL DEFAULT '0',
  `termpayment_moneytransfert_pending` tinyint(1) NOT NULL DEFAULT '0',
  `termpayment_moneytransfert_request_date` datetime DEFAULT NULL,
  `termpayment_moneytransfert_accept_date` datetime DEFAULT NULL,
  `termpayment_money_transfert_deny_date` datetime DEFAULT NULL,
  `termpayment_money_transfert_deny_reason` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `termpayment_moneytransfert_izb_id` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `id_customer_wps` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `wps_code` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `rejected` tinyint(1) NOT NULL,
  `rejected_reason` varchar(512) COLLATE utf8_unicode_ci DEFAULT NULL,
  `deactivation_reason` varchar(512) COLLATE utf8_unicode_ci DEFAULT NULL,
  `custom_attributes_exist` tinyint(1) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `technical_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_4FBF094FF851860B` (`contact_main_id`),
  UNIQUE KEY `UNIQ_4FBF094FFCBB0510` (`contact_billing_id`),
  UNIQUE KEY `UNIQ_4FBF094F591E7832` (`contact_adv_id`),
  UNIQUE KEY `UNIQ_4FBF094FE12DD63F` (`contact_logistic_id`),
  UNIQUE KEY `UNIQ_4FBF094F399B5012` (`address_main_id`),
  UNIQUE KEY `UNIQ_4FBF094F439FD419` (`address_billing_id`),
  KEY `IDX_4FBF094F12469DE2` (`category_id`),
  CONSTRAINT `FK_4FBF094F12469DE2` FOREIGN KEY (`category_id`) REFERENCES `category` (`id`),
  CONSTRAINT `FK_4FBF094F399B5012` FOREIGN KEY (`address_main_id`) REFERENCES `address` (`id`),
  CONSTRAINT `FK_4FBF094F439FD419` FOREIGN KEY (`address_billing_id`) REFERENCES `address` (`id`),
  CONSTRAINT `FK_4FBF094F591E7832` FOREIGN KEY (`contact_adv_id`) REFERENCES `contact` (`id`),
  CONSTRAINT `FK_4FBF094FE12DD63F` FOREIGN KEY (`contact_logistic_id`) REFERENCES `contact` (`id`),
  CONSTRAINT `FK_4FBF094FF851860B` FOREIGN KEY (`contact_main_id`) REFERENCES `contact` (`id`),
  CONSTRAINT `FK_4FBF094FFCBB0510` FOREIGN KEY (`contact_billing_id`) REFERENCES `contact` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `company_catalog`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `company_catalog` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `company_id` int(11) DEFAULT NULL,
  `ref` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `external_ref` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `manufacturer_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `designation_reference` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `valid` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `IDX_3B7E2890979B1AD6` (`company_id`),
  KEY `ref_idx` (`ref`),
  CONSTRAINT `FK_3B7E2890979B1AD6` FOREIGN KEY (`company_id`) REFERENCES `company` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `connection`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `connection` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `ip` varchar(46) COLLATE utf8_unicode_ci DEFAULT NULL,
  `connected_at` datetime DEFAULT NULL,
  `ua` varchar(200) COLLATE utf8_unicode_ci DEFAULT NULL,
  `resolution` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `browser` varchar(200) COLLATE utf8_unicode_ci DEFAULT NULL,
  `platform` varchar(200) COLLATE utf8_unicode_ci DEFAULT NULL,
  `browser_version` varchar(200) COLLATE utf8_unicode_ci DEFAULT NULL,
  `device_type` varchar(200) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_29F77366A76ED395` (`user_id`),
  CONSTRAINT `FK_29F77366A76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=61 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `contact`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `contact` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `first_name` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `last_name` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `phone1` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
  `phone2` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
  `email` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `function` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `technical_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `contact_mode`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `contact_mode` (
  `id` int(11) NOT NULL,
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `technical_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `country`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `country` (
  `id` int(11) NOT NULL,
  `enabled` tinyint(1) NOT NULL DEFAULT '1',
  `code` varchar(30) COLLATE utf8_unicode_ci NOT NULL,
  `izb_fca_country` varchar(30) COLLATE utf8_unicode_ci NOT NULL,
  `company_ident_regex` varchar(50) COLLATE utf8_unicode_ci NOT NULL,
  `site_ident_regex` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `buyer` tinyint(1) NOT NULL,
  `vendor` tinyint(1) NOT NULL,
  `locale` varchar(5) COLLATE utf8_unicode_ci NOT NULL,
  `izberg_code` varchar(3) COLLATE utf8_unicode_ci NOT NULL,
  `izberg_id` int(11) NOT NULL,
  `eu` tinyint(1) NOT NULL,
  `business_everywhere` tinyint(1) NOT NULL,
  `legal_product_export_EU` varchar(1024) COLLATE utf8_unicode_ci DEFAULT NULL,
  `legal_product_export_NONEU` varchar(1024) COLLATE utf8_unicode_ci DEFAULT NULL,
  `legal_service_export_EU` varchar(1024) COLLATE utf8_unicode_ci DEFAULT NULL,
  `legal_service_export_NONEU` varchar(1024) COLLATE utf8_unicode_ci DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `technical_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_5373C96677153098` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `credit_notes`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `credit_notes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `company_id` int(11) DEFAULT NULL,
  `izberg_id` int(11) NOT NULL,
  `number_id` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `pdf_file` tinyint(1) NOT NULL,
  `issuer_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `created_on` datetime NOT NULL COMMENT '(DC2Type:datetime_immutable)',
  `order_id` int(11) DEFAULT NULL,
  `num_order` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `total_amount_with_taxes` double DEFAULT NULL,
  `currency` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `payment_status` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `status` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_59742822979B1AD6` (`company_id`),
  CONSTRAINT `FK_59742822979B1AD6` FOREIGN KEY (`company_id`) REFERENCES `company` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `doctrine_migration_versions`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `doctrine_migration_versions` (
  `version` varchar(191) COLLATE utf8_unicode_ci NOT NULL,
  `executed_at` datetime DEFAULT NULL,
  `execution_time` int(11) DEFAULT NULL,
  PRIMARY KEY (`version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `document`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `document` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `company_id` int(11) DEFAULT NULL,
  `size` int(11) DEFAULT NULL,
  `filename` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `mimetype` varchar(30) COLLATE utf8_unicode_ci NOT NULL,
  `type` varchar(30) COLLATE utf8_unicode_ci NOT NULL,
  `binary_file` longblob,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `technical_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_D8698A76979B1AD6` (`company_id`),
  CONSTRAINT `FK_D8698A76979B1AD6` FOREIGN KEY (`company_id`) REFERENCES `company` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `file`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `file` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `type` varchar(50) COLLATE utf8_unicode_ci NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `data` longblob NOT NULL,
  `size` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `token` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `health_check`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `health_check` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `status` tinyint(1) NOT NULL,
  `check_date` datetime NOT NULL,
  `extra_info` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `images`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `images` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `blob_` longblob NOT NULL,
  `img_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `img_mime` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `technical_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `invoices`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `invoices` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `company_id` int(11) DEFAULT NULL,
  `izberg_id` int(11) NOT NULL,
  `number_id` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `pdf_file` tinyint(1) NOT NULL,
  `issuer_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `created_on` datetime NOT NULL COMMENT '(DC2Type:datetime_immutable)',
  `order_id` int(11) DEFAULT NULL,
  `order_created_on` datetime DEFAULT NULL COMMENT '(DC2Type:datetime_immutable)',
  `num_order` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `total_amount_with_taxes` double DEFAULT NULL,
  `currency` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `due_on` datetime NOT NULL COMMENT '(DC2Type:datetime_immutable)',
  `remaining_amount` double DEFAULT NULL,
  `payment_status` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `status` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_6A2F2F95979B1AD6` (`company_id`),
  CONSTRAINT `FK_6A2F2F95979B1AD6` FOREIGN KEY (`company_id`) REFERENCES `company` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=148 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `jms_cron_jobs`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `jms_cron_jobs` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `command` varchar(200) COLLATE utf8_unicode_ci NOT NULL,
  `lastRunAt` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_55F5ED428ECAEAD4` (`command`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `jms_job_dependencies`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `jms_job_dependencies` (
  `source_job_id` bigint(20) unsigned NOT NULL,
  `dest_job_id` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`source_job_id`,`dest_job_id`),
  KEY `IDX_8DCFE92CBD1F6B4F` (`source_job_id`),
  KEY `IDX_8DCFE92C32CF8D4C` (`dest_job_id`),
  CONSTRAINT `FK_8DCFE92C32CF8D4C` FOREIGN KEY (`dest_job_id`) REFERENCES `jms_jobs` (`id`),
  CONSTRAINT `FK_8DCFE92CBD1F6B4F` FOREIGN KEY (`source_job_id`) REFERENCES `jms_jobs` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `jms_job_related_entities`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `jms_job_related_entities` (
  `job_id` bigint(20) unsigned NOT NULL,
  `related_class` varchar(150) COLLATE utf8_unicode_ci NOT NULL,
  `related_id` varchar(100) COLLATE utf8_unicode_ci NOT NULL,
  PRIMARY KEY (`job_id`,`related_class`,`related_id`),
  KEY `IDX_E956F4E2BE04EA9` (`job_id`),
  CONSTRAINT `FK_E956F4E2BE04EA9` FOREIGN KEY (`job_id`) REFERENCES `jms_jobs` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `jms_job_statistics`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `jms_job_statistics` (
  `job_id` bigint(20) unsigned NOT NULL,
  `characteristic` varchar(30) COLLATE utf8_unicode_ci NOT NULL,
  `createdAt` datetime NOT NULL,
  `charValue` double NOT NULL,
  PRIMARY KEY (`job_id`,`characteristic`,`createdAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `jms_jobs`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `jms_jobs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `state` varchar(15) COLLATE utf8_unicode_ci NOT NULL,
  `queue` varchar(50) COLLATE utf8_unicode_ci NOT NULL,
  `priority` smallint(6) NOT NULL,
  `createdAt` datetime NOT NULL,
  `startedAt` datetime DEFAULT NULL,
  `checkedAt` datetime DEFAULT NULL,
  `workerName` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `executeAfter` datetime DEFAULT NULL,
  `closedAt` datetime DEFAULT NULL,
  `command` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `args` longtext COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:json_array)',
  `output` longtext COLLATE utf8_unicode_ci,
  `errorOutput` longtext COLLATE utf8_unicode_ci,
  `exitCode` smallint(5) unsigned DEFAULT NULL,
  `maxRuntime` smallint(5) unsigned NOT NULL,
  `maxRetries` smallint(5) unsigned NOT NULL,
  `stackTrace` longblob COMMENT '(DC2Type:jms_job_safe_object)',
  `runtime` smallint(5) unsigned DEFAULT NULL,
  `memoryUsage` int(10) unsigned DEFAULT NULL,
  `memoryUsageReal` int(10) unsigned DEFAULT NULL,
  `originalJob_id` bigint(20) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_704ADB9349C447F1` (`originalJob_id`),
  KEY `cmd_search_index` (`command`),
  KEY `sorting_index` (`state`,`priority`,`id`),
  CONSTRAINT `FK_704ADB9349C447F1` FOREIGN KEY (`originalJob_id`) REFERENCES `jms_jobs` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `menu`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `menu` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(20) COLLATE utf8_unicode_ci NOT NULL,
  `label` varchar(50) COLLATE utf8_unicode_ci NOT NULL,
  `description` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_7D053A935E237E06` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `menu_item`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `menu_item` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `menu_id` int(11) NOT NULL,
  `label` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `link` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `open_in_new_window` tinyint(1) NOT NULL DEFAULT '0',
  `weight` int(11) NOT NULL DEFAULT '0',
  `active` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `IDX_D754D550CCD7E912` (`menu_id`),
  CONSTRAINT `FK_D754D550CCD7E912` FOREIGN KEY (`menu_id`) REFERENCES `menu` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `merchant`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `merchant` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `country_id` int(11) DEFAULT NULL,
  `action_by_id` int(11) DEFAULT NULL,
  `firstname` varchar(50) COLLATE utf8_unicode_ci NOT NULL,
  `lastname` varchar(50) COLLATE utf8_unicode_ci NOT NULL,
  `phone_number` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `currency` varchar(50) COLLATE utf8_unicode_ci NOT NULL,
  `name` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `ident` varchar(100) COLLATE utf8_unicode_ci NOT NULL,
  `email` varchar(100) COLLATE utf8_unicode_ci NOT NULL,
  `password` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `status` varchar(50) COLLATE utf8_unicode_ci NOT NULL,
  `registration_date` datetime DEFAULT NULL,
  `action_at` datetime DEFAULT NULL,
  `izberg_id` int(11) DEFAULT NULL,
  `rejected_reason` varchar(512) COLLATE utf8_unicode_ci DEFAULT NULL,
  `tva_checked` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `IDX_74AB25E1F92F3E70` (`country_id`),
  KEY `IDX_74AB25E14D29ECB8` (`action_by_id`),
  CONSTRAINT `FK_74AB25E14D29ECB8` FOREIGN KEY (`action_by_id`) REFERENCES `users` (`id`),
  CONSTRAINT `FK_74AB25E1F92F3E70` FOREIGN KEY (`country_id`) REFERENCES `country` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `monitoring`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `monitoring` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `redisStatus` tinyint(1) DEFAULT NULL,
  `algoliaStatus` tinyint(1) DEFAULT NULL,
  `izbAdminAccountStatus` tinyint(1) DEFAULT NULL,
  `izbConsoleAccountStatus` tinyint(1) DEFAULT NULL,
  `izbAsyncStatus` tinyint(1) DEFAULT NULL,
  `failedJobs` int(11) NOT NULL,
  `successJobs` int(11) NOT NULL,
  `checkDate` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_BA4F975D120D0D73` (`checkDate`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `node`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `node` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `author` int(11) DEFAULT NULL,
  `image_id` int(11) DEFAULT NULL,
  `type` varchar(20) COLLATE utf8_unicode_ci NOT NULL,
  `template` varchar(40) COLLATE utf8_unicode_ci DEFAULT NULL,
  `slug` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `title_visible` tinyint(1) NOT NULL,
  `link_external` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `external_link_type` tinyint(1) NOT NULL,
  `status` varchar(20) COLLATE utf8_unicode_ci NOT NULL,
  `order_node` int(11) DEFAULT NULL,
  `published_at` datetime DEFAULT NULL,
  `ended_at` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `technical_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_857FE8453DA5256D` (`image_id`),
  KEY `IDX_857FE845BDAFD8C8` (`author`),
  CONSTRAINT `FK_857FE8453DA5256D` FOREIGN KEY (`image_id`) REFERENCES `images` (`id`),
  CONSTRAINT `FK_857FE845BDAFD8C8` FOREIGN KEY (`author`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=182 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `node_content`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `node_content` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `node_id` int(11) NOT NULL,
  `lang` varchar(2) COLLATE utf8_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `body` longtext COLLATE utf8_unicode_ci,
  `link` longtext COLLATE utf8_unicode_ci,
  `link_external` longtext COLLATE utf8_unicode_ci,
  `external_link_type` tinyint(1) NOT NULL,
  `linkText` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `technical_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_58CD75DB460D9FD7` (`node_id`),
  CONSTRAINT `FK_58CD75DB460D9FD7` FOREIGN KEY (`node_id`) REFERENCES `node` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=906 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `opening_time`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `opening_time` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `morning_start` varchar(15) COLLATE utf8_unicode_ci DEFAULT NULL,
  `morning_end` varchar(15) COLLATE utf8_unicode_ci DEFAULT NULL,
  `afternoon_start` varchar(15) COLLATE utf8_unicode_ci DEFAULT NULL,
  `afternoon_end` varchar(15) COLLATE utf8_unicode_ci DEFAULT NULL,
  `day` int(11) DEFAULT NULL,
  `technical_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `orders`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `address_id` int(11) DEFAULT NULL,
  `company_id` int(11) DEFAULT NULL,
  `site_id` int(11) DEFAULT NULL,
  `shipping_point_id` int(11) DEFAULT NULL,
  `izberg_id` int(11) DEFAULT NULL,
  `number_id` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `created_on` datetime NOT NULL COMMENT '(DC2Type:datetime_immutable)',
  `status` int(11) DEFAULT NULL,
  `izberg_status` int(11) DEFAULT NULL,
  `amount` double DEFAULT NULL,
  `amount_vat_included` double DEFAULT NULL,
  `payment_code` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `validation_number` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `currency` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `cart_id` int(11) DEFAULT NULL,
  `user_email` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_E52FFDEEF5B7AF75` (`address_id`),
  KEY `IDX_E52FFDEE979B1AD6` (`company_id`),
  KEY `IDX_E52FFDEEF6BD1646` (`site_id`),
  KEY `IDX_E52FFDEE5CC7800D` (`shipping_point_id`),
  CONSTRAINT `FK_E52FFDEE5CC7800D` FOREIGN KEY (`shipping_point_id`) REFERENCES `shipping_points` (`id`),
  CONSTRAINT `FK_E52FFDEE979B1AD6` FOREIGN KEY (`company_id`) REFERENCES `company` (`id`),
  CONSTRAINT `FK_E52FFDEEF5B7AF75` FOREIGN KEY (`address_id`) REFERENCES `address` (`id`),
  CONSTRAINT `FK_E52FFDEEF6BD1646` FOREIGN KEY (`site_id`) REFERENCES `site` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1241 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `purchase_request`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `purchase_request` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `status` varchar(50) COLLATE utf8_unicode_ci NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `import_file_id` int(11) DEFAULT NULL,
  `not_found_items_file_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_204D45E680DBD080` (`import_file_id`),
  UNIQUE KEY `UNIQ_204D45E6A2FD3058` (`not_found_items_file_id`),
  KEY `IDX_204D45E6A76ED395` (`user_id`),
  CONSTRAINT `FK_204D45E680DBD080` FOREIGN KEY (`import_file_id`) REFERENCES `file` (`id`),
  CONSTRAINT `FK_204D45E6A2FD3058` FOREIGN KEY (`not_found_items_file_id`) REFERENCES `file` (`id`),
  CONSTRAINT `FK_204D45E6A76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `purchase_request_item`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `purchase_request_item` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `purchase_request_id` int(11) DEFAULT NULL,
  `buyer_reference` varchar(250) COLLATE utf8_unicode_ci DEFAULT NULL,
  `manufacturer_reference` varchar(250) COLLATE utf8_unicode_ci DEFAULT NULL,
  `offer_id` int(11) DEFAULT NULL,
  `quantity` int(11) DEFAULT NULL,
  `cart_id` int(11) DEFAULT NULL,
  `manufacturer_name` varchar(250) COLLATE utf8_unicode_ci DEFAULT NULL,
  `product_name` varchar(250) COLLATE utf8_unicode_ci DEFAULT NULL,
  `quantity_expected` varchar(250) COLLATE utf8_unicode_ci DEFAULT NULL,
  `unit_price_of_reference` varchar(250) COLLATE utf8_unicode_ci DEFAULT NULL,
  `currency_of_reference` varchar(250) COLLATE utf8_unicode_ci DEFAULT NULL,
  `expected_delivery_date` varchar(250) COLLATE utf8_unicode_ci DEFAULT NULL,
  `cost_center` varchar(250) COLLATE utf8_unicode_ci DEFAULT NULL,
  `purchase_request_number` varchar(250) COLLATE utf8_unicode_ci DEFAULT NULL,
  `buyer_order_number` varchar(250) COLLATE utf8_unicode_ci DEFAULT NULL,
  `order_line` varchar(250) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_A4A6F5444E4DEF6F` (`purchase_request_id`),
  CONSTRAINT `FK_A4A6F5444E4DEF6F` FOREIGN KEY (`purchase_request_id`) REFERENCES `purchase_request` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `redirects`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `redirects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `origin` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `destination` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `type` int(11) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `technical_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `region`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `region` (
  `id` int(11) NOT NULL,
  `country_id` int(11) DEFAULT NULL,
  `code` varchar(20) COLLATE utf8_unicode_ci NOT NULL,
  `technical_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_F62F176F92F3E70` (`country_id`),
  CONSTRAINT `FK_F62F176F92F3E70` FOREIGN KEY (`country_id`) REFERENCES `country` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `search_historization`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `search_historization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created_at` datetime DEFAULT NULL,
  `is_anonymous` tinyint(1) DEFAULT '1',
  `company_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `user_full_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `search_term` varchar(1024) COLLATE utf8_unicode_ci DEFAULT NULL,
  `filter` varchar(4096) COLLATE utf8_unicode_ci DEFAULT NULL,
  `nb_hits` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `settings`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `domain` varchar(20) COLLATE utf8_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `value_type` varchar(50) COLLATE utf8_unicode_ci NOT NULL,
  `value` text COLLATE utf8_unicode_ci,
  `required` tinyint(1) NOT NULL,
  `tags` longtext COLLATE utf8_unicode_ci COMMENT '(DC2Type:simple_array)',
  `technical_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_setting` (`domain`,`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `shipping_points`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `shipping_points` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `address_id` int(11) DEFAULT NULL,
  `contact_id` int(11) DEFAULT NULL,
  `site_id` int(11) DEFAULT NULL,
  `name` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `comment` varchar(512) COLLATE utf8_unicode_ci DEFAULT NULL,
  `accountant_email` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `packaging_request_1` longtext COLLATE utf8_unicode_ci,
  `packaging_request_2` longtext COLLATE utf8_unicode_ci,
  `packaging_request_3` longtext COLLATE utf8_unicode_ci,
  `documentation_request_1` longtext COLLATE utf8_unicode_ci,
  `documentation_request_2` longtext COLLATE utf8_unicode_ci,
  `documentation_request_3` longtext COLLATE utf8_unicode_ci,
  `documentation_request_4` longtext COLLATE utf8_unicode_ci,
  `documentation_request_5` longtext COLLATE utf8_unicode_ci,
  `documentation_request_6` longtext COLLATE utf8_unicode_ci,
  `documentation_request_7` longtext COLLATE utf8_unicode_ci,
  `documentation_request_8` longtext COLLATE utf8_unicode_ci,
  `documentation_request_9` longtext COLLATE utf8_unicode_ci,
  `documentation_request_10` longtext COLLATE utf8_unicode_ci,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_CE04B66F5B7AF75` (`address_id`),
  KEY `IDX_CE04B66E7A1254A` (`contact_id`),
  KEY `IDX_CE04B66F6BD1646` (`site_id`),
  CONSTRAINT `FK_CE04B66E7A1254A` FOREIGN KEY (`contact_id`) REFERENCES `contact` (`id`),
  CONSTRAINT `FK_CE04B66F5B7AF75` FOREIGN KEY (`address_id`) REFERENCES `address` (`id`),
  CONSTRAINT `FK_CE04B66F6BD1646` FOREIGN KEY (`site_id`) REFERENCES `site` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `site`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `site` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `company_id` int(11) DEFAULT NULL,
  `name` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `enabled` tinyint(1) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `technical_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_694309E4979B1AD6` (`company_id`),
  CONSTRAINT `FK_694309E4979B1AD6` FOREIGN KEY (`company_id`) REFERENCES `company` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `specific_prices`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `specific_prices` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `company_ident` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `threshold1` int(11) DEFAULT NULL,
  `price1` double DEFAULT NULL,
  `threshold2` int(11) DEFAULT NULL,
  `price2` double DEFAULT NULL,
  `threshold3` int(11) DEFAULT NULL,
  `price3` double DEFAULT NULL,
  `threshold4` int(11) DEFAULT NULL,
  `price4` double DEFAULT NULL,
  `IZB_merchant_id` int(11) NOT NULL,
  `moq` int(11) DEFAULT NULL,
  `basic_price` double DEFAULT NULL,
  `vendor_reference` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `incoterm` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `country` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `companyIdentification_idx` (`company_ident`),
  KEY `IZB_merchant_id_idx` (`IZB_merchant_id`),
  KEY `vendor_reference_idx` (`vendor_reference`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `storage`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `storage` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `value` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ticket`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `ticket` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `company_recipient_id` int(11) DEFAULT NULL,
  `company_author_id` int(11) DEFAULT NULL,
  `company_id` int(11) DEFAULT NULL,
  `last_message_id` int(11) DEFAULT NULL,
  `ticket_number` varchar(100) COLLATE utf8_unicode_ci NOT NULL,
  `is_created_by_admin` tinyint(1) DEFAULT NULL,
  `suject` varchar(200) COLLATE utf8_unicode_ci DEFAULT NULL,
  `status` smallint(6) DEFAULT NULL,
  `anonymous_email` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `anonymous_first_name` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `anonymous_company` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `anonymous_function` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `anonymous_last_name` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `anonymous_phone` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `anonymous_locale` varchar(5) COLLATE utf8_unicode_ci DEFAULT NULL,
  `operator_read` tinyint(1) NOT NULL,
  `customer_read` tinyint(1) NOT NULL,
  `customer_replied` tinyint(1) NOT NULL,
  `operator_replied` tinyint(1) NOT NULL,
  `operator_new` tinyint(1) NOT NULL,
  `customer_new` tinyint(1) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `technical_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_97A0ADA3BA0E79C3` (`last_message_id`),
  KEY `IDX_97A0ADA317E245B6` (`company_recipient_id`),
  KEY `IDX_97A0ADA3C56E79EB` (`company_author_id`),
  KEY `IDX_97A0ADA3979B1AD6` (`company_id`),
  CONSTRAINT `FK_97A0ADA317E245B6` FOREIGN KEY (`company_recipient_id`) REFERENCES `company` (`id`),
  CONSTRAINT `FK_97A0ADA3979B1AD6` FOREIGN KEY (`company_id`) REFERENCES `company` (`id`),
  CONSTRAINT `FK_97A0ADA3BA0E79C3` FOREIGN KEY (`last_message_id`) REFERENCES `ticket_message` (`id`),
  CONSTRAINT `FK_97A0ADA3C56E79EB` FOREIGN KEY (`company_author_id`) REFERENCES `company` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ticket_message`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `ticket_message` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ticket_id` int(11) DEFAULT NULL,
  `content` longtext COLLATE utf8_unicode_ci,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `technical_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_BA71692D700047D2` (`ticket_id`),
  CONSTRAINT `FK_BA71692D700047D2` FOREIGN KEY (`ticket_id`) REFERENCES `ticket` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ticket_message_attachment`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `ticket_message_attachment` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `message_id` int(11) DEFAULT NULL,
  `filename` varchar(260) COLLATE utf8_unicode_ci NOT NULL,
  `mimetype` varchar(30) COLLATE utf8_unicode_ci NOT NULL,
  `size` int(11) NOT NULL,
  `binary_file` longblob NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_DA00D950537A1329` (`message_id`),
  CONSTRAINT `FK_DA00D950537A1329` FOREIGN KEY (`message_id`) REFERENCES `ticket_message` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ticket_user`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `ticket_user` (
  `ticket_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  PRIMARY KEY (`ticket_id`,`user_id`),
  UNIQUE KEY `UNIQ_BF48C371700047D2` (`ticket_id`),
  KEY `IDX_BF48C371A76ED395` (`user_id`),
  CONSTRAINT `FK_BF48C371700047D2` FOREIGN KEY (`ticket_id`) REFERENCES `ticket_message` (`id`),
  CONSTRAINT `FK_BF48C371A76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tva_group`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `tva_group` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `country_id` int(11) DEFAULT NULL,
  `group_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_5A8250CBF92F3E70` (`country_id`),
  CONSTRAINT `FK_5A8250CBF92F3E70` FOREIGN KEY (`country_id`) REFERENCES `country` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tva_rate`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `tva_rate` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_id` int(11) DEFAULT NULL,
  `from_date` datetime DEFAULT NULL,
  `rate` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_45C3C1D2FE54D947` (`group_id`),
  CONSTRAINT `FK_45C3C1D2FE54D947` FOREIGN KEY (`group_id`) REFERENCES `tva_group` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_bafv_merchant_list`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `user_bafv_merchant_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `company_id` int(11) DEFAULT NULL,
  `merchant_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_E855F2EF979B1AD6` (`company_id`),
  CONSTRAINT `FK_E855F2EF979B1AD6` FOREIGN KEY (`company_id`) REFERENCES `company` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_history`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `user_history` (
  `history_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  PRIMARY KEY (`history_id`,`user_id`),
  UNIQUE KEY `UNIQ_7FB76E411E058452` (`history_id`),
  KEY `IDX_7FB76E41A76ED395` (`user_id`),
  CONSTRAINT `FK_7FB76E411E058452` FOREIGN KEY (`history_id`) REFERENCES `action_historization` (`id`),
  CONSTRAINT `FK_7FB76E41A76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `users`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `company_id` int(11) DEFAULT NULL,
  `username` varchar(180) COLLATE utf8_unicode_ci NOT NULL,
  `username_canonical` varchar(180) COLLATE utf8_unicode_ci NOT NULL,
  `email` varchar(180) COLLATE utf8_unicode_ci NOT NULL,
  `email_canonical` varchar(180) COLLATE utf8_unicode_ci NOT NULL,
  `enabled` tinyint(1) NOT NULL,
  `salt` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `password` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `last_login` datetime DEFAULT NULL,
  `confirmation_token` varchar(180) COLLATE utf8_unicode_ci DEFAULT NULL,
  `password_requested_at` datetime DEFAULT NULL,
  `roles` longtext COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:array)',
  `firstname` varchar(50) COLLATE utf8_unicode_ci NOT NULL,
  `lastname` varchar(50) COLLATE utf8_unicode_ci NOT NULL,
  `optional_phone_number` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `main_phone_number` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `email_confirmed` tinyint(1) NOT NULL,
  `login_attempt` int(11) NOT NULL,
  `locale` varchar(2) COLLATE utf8_unicode_ci NOT NULL,
  `last_failed_login` datetime DEFAULT NULL,
  `civ` varchar(2) COLLATE utf8_unicode_ci NOT NULL,
  `disabled_at` datetime DEFAULT NULL,
  `function_` varchar(50) COLLATE utf8_unicode_ci NOT NULL,
  `comparison_sheet_eur_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `comparison_sheet_usd_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `item_in_comparison_sheet` int(11) DEFAULT '0',
  `cart_eur_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `cart_usd_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `item_in_cart_eur` int(11) DEFAULT '0',
  `item_in_cart_usd` int(11) DEFAULT '0',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `technical_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_1483A5E992FC23A8` (`username_canonical`),
  UNIQUE KEY `UNIQ_1483A5E9A0D96FBF` (`email_canonical`),
  UNIQUE KEY `UNIQ_1483A5E9C05FB297` (`confirmation_token`),
  KEY `IDX_1483A5E9979B1AD6` (`company_id`),
  CONSTRAINT `FK_1483A5E9979B1AD6` FOREIGN KEY (`company_id`) REFERENCES `company` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `users_sites`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `users_sites` (
  `user_id` int(11) NOT NULL,
  `site_id` int(11) NOT NULL,
  PRIMARY KEY (`user_id`,`site_id`),
  KEY `IDX_5B770E2AA76ED395` (`user_id`),
  KEY `IDX_5B770E2AF6BD1646` (`site_id`),
  CONSTRAINT `FK_5B770E2AA76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_5B770E2AF6BD1646` FOREIGN KEY (`site_id`) REFERENCES `site` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `vendors`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `vendors` (
  `id` int(11) NOT NULL,
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `slug` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `prefered_language` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `company_name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  PRIMARY KEY (`id`,`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wishList`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `wishList` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `name` varchar(50) COLLATE utf8_unicode_ci NOT NULL,
  `currency` varchar(50) COLLATE utf8_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_3CD3850FA76ED395` (`user_id`),
  CONSTRAINT `FK_3CD3850FA76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wishListItem`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `wishListItem` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `offer_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL,
  `wishList_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_85CEA94634336D51` (`wishList_id`),
  CONSTRAINT `FK_85CEA94634336D51` FOREIGN KEY (`wishList_id`) REFERENCES `wishList` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zipcode`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE IF NOT EXISTS `zipcode` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `country_id` int(11) DEFAULT NULL,
  `zipcode` varchar(7) COLLATE utf8_unicode_ci NOT NULL,
  `city` varchar(50) COLLATE utf8_unicode_ci NOT NULL,
  `insee_code` varchar(7) COLLATE utf8_unicode_ci DEFAULT NULL,
  `gps` varchar(35) COLLATE utf8_unicode_ci DEFAULT NULL,
  `label` varchar(70) COLLATE utf8_unicode_ci NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `technical_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_550C01C2F92F3E70` (`country_id`),
  CONSTRAINT `FK_550C01C2F92F3E70` FOREIGN KEY (`country_id`) REFERENCES `country` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=235207 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
SQL;

        $this->addSql($sql);
    }

    public function down(Schema $schema) : void
    {
    }
}
