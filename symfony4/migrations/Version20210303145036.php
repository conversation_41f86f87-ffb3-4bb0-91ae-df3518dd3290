<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210303145036 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE ticket_message_attachment CHANGE mimetype mimetype VARCHAR(75) NOT NULL');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE ticket_message_attachment CHANGE mimetype mimetype VARCHAR(70) CHARACTER SET utf8 NOT NULL COLLATE `utf8_unicode_ci`');
    }
}
