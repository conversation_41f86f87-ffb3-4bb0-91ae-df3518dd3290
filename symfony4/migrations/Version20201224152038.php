<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20201224152038 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('UPDATE specific_prices SET specific_prices.created_at = "1900-01-01 00:00:00" WHERE specific_prices.created_at IS NULL');
        $this->addSql('ALTER TABLE specific_prices ADD validity_date DATETIME DEFAULT NULL, ADD delay_of_delivery INT DEFAULT NULL, CHANGE company_ident company_ident VARCHAR(255) NOT NULL, CHANGE vendor_reference vendor_reference VARCHAR(255) NOT NULL, CHANGE incoterm incoterm VARCHAR(255) NOT NULL, CHANGE country country VARCHAR(255) NOT NULL, CHANGE created_at created_at DATETIME NOT NULL');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE specific_prices DROP validity_date, DROP delay_of_delivery, CHANGE company_ident company_ident VARCHAR(255) CHARACTER SET utf8 DEFAULT NULL COLLATE `utf8_unicode_ci`, CHANGE vendor_reference vendor_reference VARCHAR(255) CHARACTER SET utf8 DEFAULT NULL COLLATE `utf8_unicode_ci`, CHANGE incoterm incoterm VARCHAR(255) CHARACTER SET utf8 DEFAULT NULL COLLATE `utf8_unicode_ci`, CHANGE country country VARCHAR(255) CHARACTER SET utf8 DEFAULT NULL COLLATE `utf8_unicode_ci`, CHANGE created_at created_at DATETIME DEFAULT NULL');
    }
}
