<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210602160234 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE merchant_order ADD packaging_request1 LONGTEXT DEFAULT NULL, ADD packaging_request2 LONGTEXT DEFAULT NULL, ADD packaging_request3 LONGTEXT DEFAULT NULL, ADD documentation_request1 LONGTEXT DEFAULT NULL, ADD documentation_request2 LONGTEXT DEFAULT NULL, ADD documentation_request3 LONGTEXT DEFAULT NULL, ADD documentation_request4 LONGTEXT DEFAULT NULL, ADD documentation_request5 LONGTEXT DEFAULT NULL, ADD documentation_request6 LONGTEXT DEFAULT NULL, ADD documentation_request7 LONGTEXT DEFAULT NULL, ADD documentation_request8 LONGTEXT DEFAULT NULL, ADD documentation_request9 LONGTEXT DEFAULT NULL, ADD documentation_request10 LONGTEXT DEFAULT NULL');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE merchant_order DROP packaging_request1, DROP packaging_request2, DROP packaging_request3, DROP documentation_request1, DROP documentation_request2, DROP documentation_request3, DROP documentation_request4, DROP documentation_request5, DROP documentation_request6, DROP documentation_request7, DROP documentation_request8, DROP documentation_request9, DROP documentation_request10');
    }
}
