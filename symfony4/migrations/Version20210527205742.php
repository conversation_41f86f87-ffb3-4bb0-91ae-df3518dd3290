<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210527205742 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE merchant_order ADD buyer_internal_order_id VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE order_item ADD frame_contract_number VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE orders ADD amount_vat DOUBLE PRECISION DEFAULT NULL');
        $this->addSql('ALTER TABLE order_item CHANGE order_line order_line VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE merchant_order DROP buyer_internal_order_id');
        $this->addSql('ALTER TABLE order_item DROP frame_contract_number');
        $this->addSql('ALTER TABLE orders DROP amount_vat');
        $this->addSql('ALTER TABLE order_item CHANGE order_line order_line INT DEFAULT NULL');
    }
}
