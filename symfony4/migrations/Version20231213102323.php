<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231213102323 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP TABLE IF EXISTS `transactional_email`');
        $this->addSql('CREATE TABLE `transactional_email` (id_transactional_email INT AUTO_INCREMENT NOT NULL, email_identifier VARCHAR(255) NOT NULL, active TINYINT(1) NOT NULL, PRIMARY KEY(id_transactional_email)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql("INSERT INTO `transactional_email` (`email_identifier`, `active`) SELECT `slug`, '1' FROM `node` WHERE `type` = 'email'");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP TABLE IF EXISTS `transactional_email`');
    }
}
