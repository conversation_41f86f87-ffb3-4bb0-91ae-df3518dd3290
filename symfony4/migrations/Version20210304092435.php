<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210304092435 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE merchant ADD izb_status VARCHAR(50) NOT NULL, ADD izb_merchant_name VARCHAR(255) NOT NULL');
        $this->addSql('UPDATE merchant SET `izb_status` = "active" WHERE (`status` = "accepted")');
        $this->addSql('UPDATE merchant SET `izb_merchant_name` = `name`');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE merchant DROP izb_status, DROP izb_merchant_name');
    }
}
