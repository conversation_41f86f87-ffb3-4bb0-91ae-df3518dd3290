<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220408132624 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE site ADD default_user_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE site ADD CONSTRAINT FK_694309E455EB82D0 FOREIGN KEY (default_user_id) REFERENCES `users` (id)');
        $this->addSql('CREATE INDEX IDX_694309E455EB82D0 ON site (default_user_id)');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE site DROP FOREIGN KEY FK_694309E455EB82D0');
        $this->addSql('DROP INDEX IDX_694309E455EB82D0 ON site');
        $this->addSql('ALTER TABLE site DROP default_user_id');
    }
}
