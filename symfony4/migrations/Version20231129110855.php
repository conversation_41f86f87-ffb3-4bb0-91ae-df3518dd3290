<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231129110855 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE IF NOT EXISTS `transactional_email` (id_transactional_email INT AUTO_INCREMENT NOT NULL, email_identifier VARCHAR(255) NOT NULL, active TINYINT(1) NOT NULL, PRIMARY KEY(id_transactional_email)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql("INSERT IGNORE INTO `transactional_email` (`email_identifier`, `active`) VALUES ('TICKET_THREAD_UPDATED_BY_VENDOR_TO_BUYER', 1)");
        $this->addSql("INSERT IGNORE INTO `transactional_email` (`email_identifier`, `active`) VALUES ('ORDER_CONFIRMED_BY_VENDOR_TO_BUYER', 1)");
        $this->addSql("INSERT IGNORE INTO `transactional_email` (`email_identifier`, `active`) VALUES ('ORDER_CONFIRMATION_TO_BUYER', 1)");
        $this->addSql("INSERT IGNORE INTO `transactional_email` (`email_identifier`, `active`) VALUES ('ORDER_CONFIRMED_BY_VENDOR_TO_BUYER', 1)");
        $this->addSql("INSERT IGNORE INTO `transactional_email` (`email_identifier`, `active`) VALUES ('ORDER_PROCESSED_BY_VENDOR_TO_BUYER', 1)");
        $this->addSql("INSERT IGNORE INTO `transactional_email` (`email_identifier`, `active`) VALUES ('SEND_INVOICE_TO_BUYER', 1)");
        $this->addSql("INSERT IGNORE INTO `transactional_email` (`email_identifier`, `active`) VALUES ('ORDER_REFUSED_BY_VENDOR_PRE_BANK_TRANSFER_TO_BUYER', 1)");
        $this->addSql("INSERT IGNORE INTO `transactional_email` (`email_identifier`, `active`) VALUES ('PAYMENT_REMINDER_LATE_TO_BUYER', 1)");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP TABLE IF EXISTS `transactional_email`');
    }
}
