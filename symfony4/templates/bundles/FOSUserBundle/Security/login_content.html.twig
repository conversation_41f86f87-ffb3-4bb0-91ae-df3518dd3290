{#{% block body %}#}
<div class="Page-outer">

    <div class="Page-inner">
        {% if error %}
            <ul class="Messages">
                <li class="Message-item Message--error">
                    {{ error.messageKey|trans(error.messageData, 'security') }}
                </li>
            </ul>
        {% endif %}

        <div class="LoginForm">

            <div class="LoginForm-section Section--form">
                <form id="js-login-form" class="LoginForm-form" action="{{ path("login_check") }}" method="post" id="js-form-login" autocomplete="off">

                    <input type="hidden" name="_csrf_token" value="{{ fos_csrf_provider.refreshToken('authenticate') }}" />


                    <h2 class="LoginForm-header">{{ 'security.login.title'|trans([], 'AppBundle') }}</h2>

                    <div class="LoginForm-fields">
                        <div class="form-row">
                            <div class="label required">{{ 'buyer.security.login.email'|trans|upper }}*</div>
                            <input id="InputEmail" type="email" name="_username" placeholder="{{ 'buyer.security.login.email'|trans }}" required  autocomplete="off"/>
                        </div>

                        <div class="form-row">
                            <div class="label required">{{ 'buyer.security.login.password'|trans|upper }}*</div>
                            <input id="inputPassword" name="_password" placeholder="{{ 'buyer.security.login.password'|trans }}" required type="password" autocomplete="off"/>
                        </div>

                        {% if (app.session.get('SESSION_USE_TOKEN', false) == true) %}
                            <div class="form-row">
                                <div class="label required">{{ 'buyer.security.login.admin_token'|trans|upper }}</div>
                                <input id="inputAdminToken" name="_admin_token" placeholder="{{ 'buyer.security.login.admin_token'|trans }}"  type="password" autocomplete="off"/>
                            </div>
                        {% endif %}

                        <div class="form-row">
                            <div id="g-recaptcha" data-sitekey="{{ captcha_public }}"></div>
                        </div>

                        <div class="form-row login-submit">
                            <button id="_submit" name="_submit" type="submit" class="btn-primary"  value="{{ 'security.login.login'|trans([], 'AppBundle') }}">{{ 'security.login.login'|trans([], 'AppBundle') }}</button>
                            <div class="LoginForm-forgotPassword">
                                <a href="{{ path('fos_user_resetting_request') }}">{{ 'buyer.security.login.forgot'|trans({},'messages') }}</a>
                            </div>
                        </div>
                    </div>
                    <div class="footer-login">
                        <h2 class="LoginForm-header">
                            {{ 'merchant.registration.alreadySeller'|trans }}
                        </h2>
                        <a href="{{ seller_domain_url }}" target="_blank" class="btn btn-link seller-login-btn btn-lg btn-block">{{ 'merchant.registration.clickHereLogin'|trans }}</a>
                    </div>

                    <div class="LoginForm-footer">
                        <div class="text">{{ 'security.login.footer_text'|trans([], 'AppBundle') }}</div>
                        <a href="{{ path('fos_user_registration_register') }}">{{ 'security.login.create_account'|trans([], 'AppBundle')|upper }}</a>
                    </div>
                </form>
            </div>

            {#{% include '@OpenFront/shared/_registration_boxes.html.twig' %}#}

        </div>
    </div>
</div>
{#  {% endblock %}
{% block javascripts %}
<script>
    // Instanciate jQuery validator
    document.addEventListener('DOMContentLoaded', function() {

        // Clear inputs to prevent
      //document.getElementById('inputPassword').value = '';
      //document.getElementById('InputEmail').value = '';

      // validation
      $('#js-login-form').validate();

        // Fix label display for Chrome saved data autofill
        setTimeout(function() {
            $('input:-webkit-autofill').each(function() {
                $(this).addClass('has-text');
            });
        }, 100);
    });
</script>
{% endblock %}#}
