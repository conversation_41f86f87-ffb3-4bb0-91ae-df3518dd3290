{% trans_default_domain 'AppBundle' %}
{% extends '@OpenFront/base.html.twig' %}

{% block body %}
    {% block fos_user_content %}
<div class="Page-inner">
    <div class="ResetForm Form Reset--request">
        <div class="Page-header">
            <h1>{{ 'user.resetting.title'|trans }}</h1>
        </div>

        <form id="js-reset-form" action="{{ path('fos_user_resetting_send_email') }}" method="POST" class="fos_user_resetting_request">
            <div class="Form-group">
                <label for="username" class="Form-label">{{ 'user.registration.email'|trans }}</label>
                <input type="email" id="username" name="username" required="required" class="Form-control" />
            </div>
            <div class="Form-group Buttons-group">
                <button type="submit" class="Button button_margin Button-primary" value="{{ 'resetting.request.submit'|trans }}" >{{ 'resetting.request.submit'|trans }}</button>
            </div>
        </form>
    </div>
</div>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        jQuery('#js-reset-form').validate();
    });
</script>
{% endblock fos_user_content %}
{% endblock %}
