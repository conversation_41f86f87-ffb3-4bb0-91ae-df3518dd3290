{% trans_default_domain 'AppBundle' %}
{% form_theme form 'Form/appli_layout.html.twig' %}
{% extends '@OpenFront/base.html.twig' %}

{% block body %}
    {% block fos_user_content %}
<div class="Page-inner">
    <div class="ResetForm Form Reset--confirm">

        <div class="Page-header">
            <h1>{{ 'resetting.newpwd'|trans }}</h1>
        </div>
        {{ form_start(form, { 'action': path('fos_user_resetting_reset', {'token': token}), 'attr': { 'class': 'fos_user_resetting_reset' } }) }}
        {{ form_widget(form) }}
        <div class="password-help">
            {{ 'profile.password.help'|trans }}
        </div>
        <div class="Form-group Buttons-group clearfix">
            <button type="submit" value="{{ 'resetting.reset.submit'|trans }}" class="Button Button-primary">
                {{ 'resetting.reset.submit'|trans }}
            </button>
        </div>
        {{ form_end(form) }}
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                {{ form_jquery_validation(form) }}
            });
        </script>
    </div>
</div>
    {% endblock fos_user_content %}
{% endblock %}
