{% extends '@OpenFront/base.html.twig' %}

{% block body %}
    {% block fos_user_content %}
{% if app.user.company is not null %}
    {% set companyId = app.user.company.id %}
    {% set companyStatus = app.user.company.status %}
    {% set sites = app.user.company.sites %}
    {% set users = app.user.company.users %}
    {% set sitesCount = app.user.company.sites|length  %}
    {% set usersCount = app.user.company.users|length - 1  %}
{% else %}
    {% set companyId = null %}
    {% set companyStatus = 'initial' %}
    {% set sites = [] %}
    {% set users = [] %}
    {% set sitesCount = 0 %}
    {% set usersCount = 0 %}
{% endif %}

{% set isAdmin = false %}
{% set isBuyer = false %}
{% for role in ['ROLE_BUYER_ADMIN'] %}
    {% if role in app.user.roles %}
        {% set isAdmin = true %}
    {% endif %}
{% endfor %}
{% for role in ['ROLE_BUYER_BUYER', 'ROLE_BUYER_ADMIN'] %}
    {% if role in app.user.roles %}
        {% set isBuyer = true %}
    {% endif %}
{% endfor %}

<div class="Page-inner">

    {% if (companyStatus == 'initial' and sitesCount == 0 and usersCount == 0 and isAdmin) %}
        {% set head_txt = 'profile.header_initial'  %}
    {% else %}
        {% set head_txt = 'profile.header_default' %}
    {% endif %}
    <h1>{{ head_txt|trans }}</h1>

    <div class="ProfileIndex" >
        <p class="ProfileIndex-title">
        {%  if companyStatus == 'initial' and sitesCount == 0 and usersCount == 0 and isAdmin %}
            {{ 'profile.title_initial'|trans }}
        {% else %}
            {{ 'profile.title'|trans }}
        {% endif %}
        </p>

        {% if isAdmin %}

        <h2>{{ 'profile.sections.data'|trans }}</h2>
        <div class="Form-group">
            <a href="{{ path('front.company.profile') }}">
                <span class="Form-label">
                    {% if companyStatus == 'valid' and companyStatus != 'pending' %}
                        {{ 'profile.sections.buttons.view'|trans }}
                    {% elseif companyStatus == 'pending' %}
                        {{ 'profile.sections.buttons.pending'|trans }}
                    {% else %}
                        {{ 'profile.sections.buttons.initial'|trans }}
                    {% endif  %}
                </span>
                {% if companyStatus == 'valid' or companyStatus == 'pending' %}
                    <svg class="Icon">
                        <use xlink:href="#icon-eye"></use>
                    </svg>
                {% else %}
                    <svg class="Icon">
                        <use xlink:href="#icon-incomplete"></use>
                    </svg>
                {% endif %}
            </a>
        </div>

        <h2 class="ProfileIndex-sectionTitle">
            <a href="{{ path('front.site.new') }}">
                <span>{{ 'profile.sections.sites'|trans }}</span>
                <svg class="Icon Icon--plus">
                    <use xlink:href="#icon-plus"></use>
                </svg>
            </a>
        </h2>
    {% if sitesCount == 0 %}
        <h3>{{ 'profile.sections.sites_initial'|trans }}</h3>
    {% endif %}


    {% for site in sites %}
        <div class="Form-group">
            <a href="{{ path('front.site.edit',{id:site.id}) }}">
                <span class="Form-label">
                    {{ site.name }}
                    {% if site.status == 'pending' %}
                        <small>({{ 'profile.sections.site_pending'|trans }})</small>
                    {% elseif site.status != 'valid' %}
                        <small>({{ 'profile.sections.site_draft'|trans }})</small>
                    {% endif %}
                </span>
                {% if site.status == 'valid' or site.status == 'pending'%}
                    <svg class="Icon">
                        <use xlink:href="#icon-eye"></use>
                    </svg>
                {% else %}
                    <svg class="Icon">
                        <use xlink:href="#icon-incomplete"></use>
                    </svg>
                {% endif %}
            </a>
        </div>
    {% endfor %}


        <h2 class="ProfileIndex-sectionTitle">
            <a href="{{ path('front.user.new') }}">
                <span>
                    {% if usersCount ==0 %}
                       {{ 'profile.sections.userss_initial'|trans }}
                    {% else %}
                       {{ 'profile.sections.users'|trans }}
                    {% endif %}
                </span>
                <svg class="Icon Icon--plus">
                    <use xlink:href="#icon-plus"></use>
                </svg>
            </a>
        </h2>
        {% if usersCount == 0 %}
            <h3>{{ 'profile.subsections.users.initial'|trans }}</h3>
        {% endif %}

    {% for user in users %}
        {% if user.id != app.user.id %}
            <div class="Form-group {{ user.emailConfirmed ? '': 'Profile--emailConfirm' }}">
                <a href="{{ path('front.user.edit',{id:user.id}) }}">
                <span class="Form-label">
                    {{ user.firstName ~ ' ' ~ user.lastName }}
                    {% if not user.emailConfirmed %}
                        <small>({{ 'profile.subsections.user.email_confirm'|trans  }})</small>
                    {% endif %}
                </span>
                <svg class="Icon">
                    <use xlink:href="#icon-pencil"></use>
                </svg>
                </a>
            </div>
        {% endif %}
    {% endfor %}

        {% endif %}


        <h2 class="ProfileIndex-account">
            <a href="{{ path('fos_user_profile_edit') }}">
                <span>{{ 'profile.account'|trans  }}</span>
                <svg class="Icon">
                    <use xlink:href="#icon-pencil"></use>
                </svg>
            </a>
        </h2>


    {% if isBuyer %}
        <div class="ProfileIndex-orders">
            <h2>
                <span>{{ 'profile.in_process_bid.title'|trans  }}</span>
            </h2>
            {% if inProcessBids is empty %}
                <h3>{{ 'profile.in_process_bid.empty'|trans }}</h3>
            {% else %}
                {% for bid in inProcessBids %}
                    <div class="Form-group">
                        <a href="{{ path('front.product.index', {'siteId':bid.siteId,'id':bid.offerId }) }}">
                            <span class="Form-label">
                                <span class="ProfileIndex-ordersItem">{{ bid.offerTitle }}</span>
                                {% if bid.lastBidPrice < 0 %}
                                    {% set priceText = 'profile.order.receive' %}
                                {% else %}
                                    {% set priceText = 'profile.order.pay' %}
                                {% endif %}

                                <span class="ProfileIndex-ordersPrice">{{ priceText|trans }} : {{ bid.lastBidPrice|abs|number_format(0) }}{{ 'profile.order.currency'|trans  }}{{ 'profile.order.unit'|trans  }}</span>
                                <span class="ProfileIndex-ordersDate">{{ bid.lastBidDate|date("d/m/Y") }}</span>
                            </span>
                        </a>
                    </div>
                {% endfor %}
            {% endif %}
        </div>
        <div class="ProfileIndex-orders">
            <h2>
                <span>{{ 'profile.lost_bid.title'|trans  }}</span>
            </h2>
            {% for lostBid in lostBids %}
                <div class="Form-group">
                    <a href="{{ path('front.lost_bid.detail', {id:lostBid.bidId}) }}">
                        <span class="Form-label">
                            <span class="ProfileIndex-ordersItem">{{ lostBid.offerTitle }}</span>
                            {% if lostBid.lastBidPrice < 0 %}
                                {% set priceText = 'profile.order.receive' %}
                            {% else %}
                                {% set priceText = 'profile.order.pay' %}
                            {% endif %}
                            <span class="ProfileIndex-ordersPrice">{{ priceText|trans }} : {{ lostBid.lastBidPrice|abs|number_format(0) }}{{ 'profile.order.currency'|trans  }}{{ 'profile.order.unit'|trans  }}</span>
                            <span class="ProfileIndex-ordersDate">{{ lostBid.lastBidDate|date("d/m/Y") }}</span>
                        </span>
                    </a>
                </div>
            {% else %}
                <h3>{{ 'profile.lost_bid.empty'|trans }}</h3>
            {% endfor %}
        </div>
        <div class="ProfileIndex-orders">
            <h2>
                <span>{{ 'profile.order.title'|trans  }}</span>
            </h2>
            {% if orders is empty %}
                <h3>{{ 'profile.order.empty'|trans }}</h3>
            {% else %}
                {% for order in orders %}
                    <div class="Form-group">
                        <a href="{{ path('front.order.detail', {id:order.order.id}) }}">
                            <span class="Form-label">
                                <span class="ProfileIndex-ordersItem">{{ order.items.0.name }}</span>
                                {% if order.price < 0 %}
                                {% set priceText = 'profile.order.receive' %}
                                {% else %}
                                    {% set priceText = 'profile.order.pay' %}
                                {% endif %}
                                <span class="ProfileIndex-ordersPrice">{{ priceText|trans }} : {{ order.price|abs|number_format(0) }}{{ 'profile.order.currency'|trans  }}{{ 'profile.order.unit'|trans  }}</span>
                                <span class="ProfileIndex-ordersDate">{{ order.order.createdOn|date("d/m/Y") }}</span>
                            </span>
                        </a>
                    </div>
                {% endfor %}
            {% endif %}
        </div>
    {% endif %}

    </div>


</div>
{% endblock fos_user_content %}
{% endblock %}
