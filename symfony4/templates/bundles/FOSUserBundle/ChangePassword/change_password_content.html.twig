{% form_theme form 'Form/appli_layout.html.twig' %}

{% extends '@OpenFront/base.html.twig' %}

{% block body %}
    {% block fos_user_content %}
    <div class="Page-inner">

        <div class="ChangePasswordForm">
        {{ form_start(form, { 'action': path('fos_user_change_password'), 'attr': { 'class': 'fos_user_change_password', 'autocomplete' : 'off' } }) }}
            <h1>{{ 'profile.password.title'|trans({}, 'AppBundle') }}</h1>
            <div class="flex-container">
                {{ form_row(form.current_password, {'attr': { 'autocomplete' : 'off' } }) }}
            </div>
            <div class="flex-container">
                {{ form_row(form.plainPassword) }}
                <div class="password-help">
                    {{ 'profile.password.help'|trans({},'AppBundle') }}
                </div>
            </div>
            {{ form_widget(form) }}
            <div class="form-group">
                <button class="button_margin" type="submit">
                    {{ 'profile.password.submit'|trans({},'AppBundle') }}
                </button>
            </div>
        {{ form_end(form) }}
        </div>
        <script>
            {{ form_jquery_validation(form) }}
        </script>
    </div>
    {% endblock fos_user_content %}
{% endblock %}
