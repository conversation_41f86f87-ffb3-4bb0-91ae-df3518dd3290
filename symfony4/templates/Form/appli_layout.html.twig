{% use "form_div_layout.html.twig" %}


{% block form_row -%}
    <div class="form-row">
        {{- form_widget(form) -}}
        {{- form_label(form) -}}
        <small class="error">{{- form_errors(form) -}}</small>
    </div>
{%- endblock form_row %}

{%- block form_errors -%}
    {%- if errors|length > 0 -%}
        <ul>
            {%- for error in errors -%}
                <li class="Message--error">{{ error.message }}</li>
            {%- endfor -%}
        </ul>
    {%- endif -%}
{%- endblock form_errors -%}

{%- block form_label -%}
    {% set label_attr = label_attr|merge({'class': 'form-control-label'}) %}
    {% if label is not same as(false) -%}
        {% if not compound -%}
            {% set label_attr = label_attr|merge({'for': id}) %}
        {%- endif -%}
        {% if required -%}
            {% set label_attr = label_attr|merge({'class': (label_attr.class|default('') ~ ' required')|trim}) %}
        {%- endif -%}
        {% if label is empty -%}
            {%- if label_format is not empty -%}
                {% set label = label_format|replace({
                    '%name%': name,
                    '%id%': id,
                }) %}
            {%- else -%}
                {% set label = name|humanize %}
            {%- endif -%}
        {%- endif -%}

        {% set text = (translation_domain is same as(false)) ? label : label|trans({}, translation_domain) %}
        {% if capitalized is defined and capitalized %}
            {% set text = text | capitalize %}
        {% endif %}
        <{{ element|default('label') }} {% if label_attr %}{% with { attr: label_attr } %}{{ block('attributes') }}{% endwith %}{% endif %}>
            {{ text }}
        </{{ element|default('label') }}>
    {%- endif -%}
{%- endblock form_label -%}

{%- block checkbox_widget -%}
    <input type="checkbox" {{ block('widget_attributes') }}{% if value is defined %} value="{{ value }}"{% endif %}{% if checked %} checked="checked"{% endif %} />
    {#<label for="{{ id }}"></label>#}
{%- endblock checkbox_widget -%}

{%- block form_widget_simple -%}
    {%- set type = type|default('text') -%}
    <input type="{{ type }}" {{ block('widget_attributes') }} {% if value is not empty %} value="{{ value }}" {% endif %} />
{%- endblock form_widget_simple -%}

{%- block widget_attributes -%}
id="{{ id }}" name="{{ full_name }}"
   {%- if value is not empty -%} class="has-text" {%- endif -%}
   {%- if disabled -%} disabled="disabled" {%- endif -%}
   {%- if required -%} required="required" {%- endif -%}
    {{ block('attributes') }}
{%- endblock widget_attributes -%}

{%- block choice_widget_collapsed -%}
    {%- if required and placeholder is none and not placeholder_in_choices and not multiple and (attr.size is not defined or attr.size <= 1) -%}
        {% set required = false %}
    {%- endif -%}

    <select {{ block('widget_attributes') }}{% if multiple %} multiple="multiple"{% endif %}>
        {%- if placeholder is not none -%}
            <option value="" data-placeholder="true" selected="selected">{{ placeholder != '' ? (translation_domain is same as(false) ? placeholder : placeholder|trans({}, translation_domain)) }}</option>
        {%- endif -%}
        {%- if preferred_choices|length > 0 -%}
            {% set options = preferred_choices %}
            {{- block('choice_widget_options') -}}
            {%- if choices|length > 0 and separator is not none -%}
                <option disabled="disabled">{{ separator }}</option>
            {%- endif -%}
        {%- endif -%}
        {%- set options = choices -%}
        {{- block('choice_widget_options') -}}
    </select>

{%- endblock choice_widget_collapsed -%}

{% block file_widget %}
    {% if form.vars.name in ['attachmentFile', 'attachments'] %}
        {% set buttonText = 'ticket.edit.attachment_button' %}
    {% else %}
        {% set buttonText = 'document.upload.title' %}
    {% endif %}
    {% apply spaceless %}
        {% set type = type|default('file') %}
        <button id="js-button-{{ form.vars.id }}" type="button" class="Button Button--upload js-button-upload" {{ block('widget_attributes') }}>
            <svg class="Icon">
                <use xlink:href="#icon-upload"></use>
            </svg>
            {{ buttonText|trans({},'AppBundle')|upper }}
        </button>
        <input type="{{ type }}" {{ block('widget_attributes') }} />
    {% endapply %}
{% endblock file_widget %}
