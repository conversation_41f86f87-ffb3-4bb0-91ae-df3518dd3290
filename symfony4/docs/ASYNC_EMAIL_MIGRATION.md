# Async Email Migration Guide

This document explains the migration from synchronous to asynchronous email sending using Symfony Messenger queues.

## Overview

The email system has been enhanced to support asynchronous email sending through Symfony Messenger queues while maintaining backward compatibility with synchronous sending.

## Key Features

- **Asynchronous Processing**: Emails are queued and processed by background workers
- **Priority-based Routing**: High-priority emails (user-facing, time-sensitive) go to the high-priority queue
- **Backward Compatibility**: Existing code continues to work with optional async parameter
- **Error Handling**: Comprehensive logging and error handling for queued emails
- **Scalability**: Better performance for bulk email operations

## Priority Classification

### High Priority Queue (`async_priority_high`)
- User registration confirmations
- Password reset emails
- Order confirmations and updates
- Payment notifications
- Support ticket notifications
- Account validation/rejection emails

### Low Priority Queue (`async_priority_low`)
- Bulk notifications
- Reports and summaries
- Non-urgent administrative emails
- System maintenance notifications

## Usage

### Asynchronous Email Sending (Default)

```php
// Send email asynchronously (default behavior)
$mailService->sendEmailMessage(
    'USER_RESET_MDP',
    'en',
    '<EMAIL>',
    ['firstName' => '<PERSON>', 'lastName' => 'Doe', 'url' => $resetUrl]
);

// Explicitly async
$mailService->sendEmailMessage(
    'USER_RESET_MDP',
    'en',
    '<EMAIL>',
    ['firstName' => 'John', 'lastName' => 'Doe', 'url' => $resetUrl],
    null,
    null,
    [],
    true // async = true
);
```

### Synchronous Email Sending (Legacy)

```php
// Send email synchronously (immediate sending)
$mailService->sendEmailMessage(
    'USER_RESET_MDP',
    'en',
    '<EMAIL>',
    ['firstName' => 'John', 'lastName' => 'Doe', 'url' => $resetUrl],
    null,
    null,
    [],
    false // async = false
);

// Or use the dedicated sync method
$mailService->sendEmailMessageSync(
    'USER_RESET_MDP',
    'en',
    '<EMAIL>',
    ['firstName' => 'John', 'lastName' => 'Doe', 'url' => $resetUrl]
);
```

## Queue Processing

### Development Environment

Start the messenger worker to process queued emails:

```bash
php bin/console messenger:consume async_priority_high async_priority_low -vv
```

### Production Environment

The production environment uses Supervisor to manage messenger workers:

```bash
# Check worker status
sudo supervisorctl status messenger-consume:*

# Restart workers
sudo supervisorctl restart messenger-consume:*

# View logs
sudo supervisorctl tail -f messenger-consume:messenger-consume_00
```

## Testing

### Test Command

Use the test command to verify async email functionality:

```bash
# Test high-priority async email
php bin/console app:test-async-email --email=<EMAIL> --high-priority

# Test low-priority async email
php bin/console app:test-async-email --email=<EMAIL>

# Test synchronous email
php bin/console app:test-async-email --email=<EMAIL> --sync
```

### Unit Tests

Run the email service tests:

```bash
php bin/phpunit tests/AppBundle/Services/MailServiceAsyncTest.php
```

## Migration Strategy

### Phase 1: Gradual Migration (Recommended)
1. Keep existing code unchanged (will use async by default)
2. Monitor queue processing and performance
3. Identify any issues with specific email types

### Phase 2: Optimization
1. Review email priority classifications
2. Adjust queue worker configuration if needed
3. Optimize for your specific email volume

### Phase 3: Full Migration
1. Remove any explicit `async = false` parameters where appropriate
2. Update documentation and training materials

## Monitoring and Troubleshooting

### Queue Status

Check queue status and failed messages:

```bash
# View queue statistics
php bin/console messenger:stats

# Process failed messages
php bin/console messenger:failed:show
php bin/console messenger:failed:retry
```

### Logging

Email processing is logged with the following events:
- `EMAIL_SENT`: Successful email processing
- `EMAIL_ERROR`: Email processing errors
- Queue dispatch and processing events

### Common Issues

1. **Worker Not Running**: Ensure messenger workers are active
2. **Database Connection**: Workers need database access for email templates
3. **Memory Limits**: Monitor worker memory usage for bulk operations
4. **Failed Messages**: Check failed message queue for processing errors

## Configuration

### Messenger Configuration

The routing configuration in `config/packages/messenger.yaml`:

```yaml
routing:
    'AppBundle\Message\SendEmailMessage': 
        - when: 'message.isHighPriority()'
          send-to: 'async_priority_high'
        - send-to: 'async_priority_low'
```

### Service Configuration

The MailService is configured with MessageBusInterface dependency in `config/services/app_bundle.yaml`.

## Benefits

1. **Performance**: Non-blocking email sending improves response times
2. **Reliability**: Failed emails can be retried automatically
3. **Scalability**: Handle high email volumes without blocking the application
4. **Monitoring**: Better visibility into email processing status
5. **Resource Management**: Dedicated workers for email processing

## Backward Compatibility

All existing email sending code continues to work without changes. The system defaults to asynchronous sending but can be configured per call if needed.
