{"devDependencies": {"@babel/preset-env": "^7.9.5", "@symfony/webpack-encore": "^0.28.3", "bazinga-translator": "^3.0.1", "blueimp-file-upload": "9.25.0", "breakpoint-sass": "^2.7.1", "handlebars": "^4.7.6", "jquery": "^3.5.0", "jquery-timepicker": "^1.3.3", "jquery-ui-dist": "^1.12.1", "jquery-validation": "^1.19.1", "js-cookie": "^2.2.1", "jsonexport": "^3.0.1", "lightbox2": "^2.11.1", "node-sass": "^4.13.1", "normalize.css": "^8.0.1", "sass-loader": "^7.0.1", "slider-pro": "^1.5.0", "vue-loader": "^15.0.11", "vue-template-compiler": "^2.6.12"}, "dependencies": {"bootstrap": "^4.5.3", "bootstrap-select": "^1.13.18", "popper.js": "^1.16.1", "vue": "^2.6.12"}, "scripts": {"dev-server": "encore dev-server", "dev": "./node_modules/@symfony/webpack-encore/bin/encore.js dev", "watch": "./node_modules/@symfony/webpack-encore/bin/encore.js dev --watch", "build": "./node_modules/@symfony/webpack-encore/bin/encore.js production --progress"}}