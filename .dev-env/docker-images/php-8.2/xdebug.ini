; Xdebug v3 configuration for Station One development environment
; This file is mounted into the container at /usr/local/etc/php/conf.d/xdebug.ini

; Ensure the xdebug extension is enabled
zend_extension=xdebug

; Core Xdebug settings
[xdebug]
; Enable debugger and developer helpers (stack traces, etc.)
xdebug.mode=${XDEBUG_MODE}
; Start a debugging session for every request unless explicitly disabled
xdebug.start_with_request=${XDEBUG_START_WITH_REQUEST}
; Where the IDE is running. We resolve to host.docker.internal which we map in docker-compose
xdebug.client_host=${XDEBUG_CLIENT_HOST}
; Xdebug v3 default port
xdebug.client_port=${XDEBUG_CLIENT_PORT}
; Set to 0 because with nginx->php-fpm in Docker the discovered IP would be the nginx container, not the host
xdebug.discover_client_host=0
; IDE key for debugging sessions
xdebug.idekey=${XDEBUG_IDEKEY}
; Optional logging (set XDEBUG_LOG_LEVEL=7 to debug issues)
xdebug.log_level=${XDEBUG_LOG_LEVEL}
; Uncomment the next line to enable Xdebug logging for troubleshooting
; xdebug.log=/tmp/xdebug.log

; Quality of life improvements
xdebug.max_nesting_level=1024
; Show better stack traces
xdebug.show_error_trace=1
; Show local variables in stack traces
xdebug.show_local_vars=1
