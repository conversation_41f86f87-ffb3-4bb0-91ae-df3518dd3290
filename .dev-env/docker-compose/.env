# project info
PROJECT_NAME='station-one'
PROJECT_DIRECTORY='./../..'
PROJECT_REGISTRY=gitlab.lateos.net:5050/station_one/station-one

# nginx vars
NGINX_HOST_SYMFONY4="alstom.local www.alstom.local"
NGINX_FPM_SYMFONY4=symfony4
NGINX_CERT_CRT_SYMFONY4=/etc/nginx/certs/cert.crt
NGINX_CERT_KEY_SYMFONY4=/etc/nginx/certs/cert.key
NGINX_PROXY_PASS_SYMFONY4=https://nginx-symfony4
NGINX_AUTH_BASIC_SYMFONY4=""
NGINX_AUTH_BASIC_USER_FILE_SYMFONY4=""
#NGINX_AUTH_BASIC_SYMFONY4="auth_basic           \"Simple website Area\";"
#NGINX_AUTH_BASIC_USER_FILE_SYMFONY4="auth_basic_user_file /etc/nginx/.htpasswd;"

# Xdebug configuration
XDEBUG_MODE=debug
XDEBUG_START_WITH_REQUEST=yes
XDEBUG_CLIENT_HOST=host.docker.internal
XDEBUG_CLIENT_PORT=9003
XDEBUG_IDEKEY=PHPSTORM
XDEBUG_LOG_LEVEL=0

