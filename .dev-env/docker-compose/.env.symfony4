# bdd vars
DB_HOST=mariadb-symfony4
DB_PORT=3306
DB_NAME=prod_stationone
DB_USER=root
DB_PASS=password
DB_ROOT_PASSWORD=password
DB_SERVER_VERSION=mariadb-10.8.3
DB_CHARSET=utf8

#mariadb vars
MARIADB_DATABASE=symfony4
MARIADB_USER=symfony4
MARIADB_PASSWORD=symfony4-password
MARIADB_ROOT_PASSWORD=password

# Xdebug configuration
XDEBUG_MODE=debug
XDEBUG_START_WITH_REQUEST=yes
XDEBUG_CLIENT_HOST=host.docker.internal
XDEBUG_CLIENT_PORT=9003
XDEBUG_IDEKEY=PHPSTORM
XDEBUG_LOG_LEVEL=0
