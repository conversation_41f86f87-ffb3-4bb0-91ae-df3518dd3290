version: "3.3"

services:
  symfony4:
    image: ${PROJECT_REGISTRY}/php-dev:8.2
    container_name: "${PROJECT_NAME}-symfony4"
    user: '1000'
    working_dir: /var/www/symfony4
    env_file: .env.symfony4
    environment:
      - XDEBUG_MODE=${XDEBUG_MODE}
      - XDEBUG_START_WITH_REQUEST=${XDEBUG_START_WITH_REQUEST}
      - XDEBUG_CLIENT_HOST=${XDEBUG_CLIENT_HOST}
      - XDEBUG_CLIENT_PORT=${XDEBUG_CLIENT_PORT}
      - XDEBUG_IDEKEY=${XDEBUG_IDEKEY}
      - XDEBUG_LOG_LEVEL=${XDEBUG_LOG_LEVEL}
    volumes:
      - "${PROJECT_DIRECTORY}/symfony4:/var/www/symfony4"
      - "${PROJECT_DIRECTORY}/.dev-env/docker-images/php-8.2/xdebug.ini:/usr/local/etc/php/conf.d/xdebug.ini"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    depends_on:
      - mariadb-symfony4
      - mailhog
    networks:
      - dev

  symfony4-webpack:
    image: node:14.13.0-alpine3.10
    container_name: "${PROJECT_NAME}-symfony4-webpack"
    env_file: .env
    working_dir: /app
    user: '1000'
    command: sh -c "yarn && yarn encore dev"
    volumes:
      - "${PROJECT_DIRECTORY}/symfony4:/app"

  nginx-symfony4:
    image: nginx
    container_name: "${PROJECT_NAME}-symfony4-nginx"
    env_file: .env
    ports:
      - "80:80"
      - "443:443"
    restart: on-failure
    volumes:
      - "${PROJECT_DIRECTORY}/symfony4/public:/var/www/symfony4/public"
      - "${PROJECT_DIRECTORY}/.dev-env/docker-images/nginx/templates/symfony4.conf.template:/etc/nginx/templates/symfony4.conf.template"
      - "${PROJECT_DIRECTORY}/.dev-env/docker-images/nginx/templates/default.conf.template:/etc/nginx/templates/default.conf.template"
      - "${PROJECT_DIRECTORY}/.dev-env/docker-images/nginx/certs:/etc/nginx/certs"
    command: [ nginx-debug, '-g', 'daemon off;' ]
    depends_on:
      - symfony4
    networks:
      - dev

  mariadb-symfony4:
    image: mariadb:10.8.3
    restart: always
    container_name: "${PROJECT_NAME}-symfony4-mariadb"
    ports:
      - "3306:3306"
    env_file: .env.symfony4
    volumes:
      - symfony4-database:/var/lib/mysql
    networks:
      - dev

  redis-symfony4:
    image: redis
    container_name: "${PROJECT_NAME}-symfony4-redis"
    networks:
      - dev

volumes:
  symfony4-database:
